const { chromium } = require('playwright');

class UltimateTrader {
    constructor(port = 9222) {
        this.browser = null;
        this.page = null;
        this.port = port;
    }

    async connectToBrowser() {
        console.log(`🔗 Connecting to browser on port ${this.port}...`);
        
        try {
            this.browser = await chromium.connectOverCDP(`http://localhost:${this.port}`);
            const contexts = this.browser.contexts();
            
            if (contexts.length > 0) {
                const pages = contexts[0].pages();
                this.page = pages.length > 0 ? pages[0] : await contexts[0].newPage();
            } else {
                const context = await this.browser.newContext();
                this.page = await context.newPage();
            }
            
            console.log(`✅ Connected to browser on port ${this.port}`);
            return true;
        } catch (error) {
            console.error(`❌ Connection failed to port ${this.port}:`, error.message);
            return false;
        }
    }

    async cleanupPage() {
        console.log('🧹 Cleaning up page (removing popups, clearing fields)...');
        
        try {
            // Step 1: Close any existing popups
            const popupCloseSelectors = [
                'button:has-text("Confirm")',
                'button:has-text("Cancel")',
                'button:has-text("Close")',
                '.modal-close',
                '.popup-close',
                '[class*="close"]',
                '.ant-modal-close'
            ];

            for (const selector of popupCloseSelectors) {
                try {
                    const elements = await this.page.locator(selector).all();
                    for (const element of elements) {
                        const isVisible = await element.isVisible({ timeout: 100 });
                        if (isVisible) {
                            await element.click({ timeout: 200 });
                            console.log(`✅ Closed popup with: ${selector}`);
                            await this.page.waitForTimeout(200);
                        }
                    }
                } catch (error) {
                    // Continue to next selector
                }
            }

            // Step 2: Clear any existing quantity fields
            const quantitySelectors = [
                'text=Quantity(USDT) >> xpath=following::input[1]',
                'text=Quantity >> xpath=following::input[1]',
                'input[placeholder*="quantity"]',
                'input[placeholder*="amount"]',
                'input[type="number"]'
            ];

            for (const selector of quantitySelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    const isVisible = await element.isVisible({ timeout: 200 });
                    if (isVisible) {
                        await element.click({ timeout: 200 });
                        await element.fill('');
                        console.log(`✅ Cleared quantity field: ${selector}`);
                        break;
                    }
                } catch (error) {
                    // Continue to next selector
                }
            }

            console.log('✅ Page cleanup completed');
        } catch (error) {
            console.log('⚠️ Cleanup had issues, continuing anyway...');
        }
    }

    async retryFindElement(selectors, maxRetries = 5, timeout = 300) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            for (const selector of selectors) {
                try {
                    const element = this.page.locator(selector).first();
                    const isVisible = await element.isVisible({ timeout });
                    
                    if (isVisible) {
                        console.log(`✅ Found element: ${selector} (attempt ${attempt})`);
                        return element;
                    }
                } catch (error) {
                    continue;
                }
            }
            
            if (attempt < maxRetries) {
                console.log(`⚠️ Retry ${attempt}/${maxRetries} - Element not found, retrying...`);
                
                // On retry, try cleanup in case there are blocking popups
                if (attempt === 2) {
                    console.log('🧹 Attempting cleanup on retry...');
                    await this.cleanupPage();
                }
                
                await new Promise(resolve => setTimeout(resolve, 200));
            }
        }
        
        throw new Error(`Element not found after ${maxRetries} attempts`);
    }

    async executeOrder(orderType) {
        const startTime = Date.now();
        console.log(`🎯 EXECUTING ${orderType.toUpperCase()} ORDER...`);
        
        try {
            // Step 0: Initial cleanup
            await this.cleanupPage();

            // Ensure we're on the right page
            const url = this.page.url();
            if (!url.includes('mexc.com/futures/TRU_USDT')) {
                console.log('🌐 Navigating to TRU_USDT...');
                await this.page.goto('https://www.mexc.com/futures/TRU_USDT');
                await this.page.waitForTimeout(2000);
            }

            // Step 1: Set correct mode based on order type
            if (orderType.includes('Close')) {
                console.log('🔄 Step 1: Switching to Close mode...');
                const closeModeSelectors = ['button:has-text("Close")', 'text=Close'];
                
                try {
                    const closeModeButton = await this.retryFindElement(closeModeSelectors, 3);
                    await closeModeButton.click();
                    console.log('✅ Switched to Close mode');
                    await this.page.waitForTimeout(500);
                } catch (error) {
                    console.log('⚠️ Close mode button not found, assuming already in close mode');
                }
            } else {
                console.log('🔄 Step 1: Ensuring Open mode...');
                const openModeSelectors = ['button:has-text("Open")', 'text=Open'];
                
                try {
                    const openModeButton = await this.retryFindElement(openModeSelectors, 3);
                    await openModeButton.click();
                    console.log('✅ Switched to Open mode');
                    await this.page.waitForTimeout(500);
                } catch (error) {
                    console.log('⚠️ Open mode button not found, assuming already in open mode');
                }
            }

            // Step 2: Fill Quantity with retry and cleanup
            console.log('🔢 Step 2: Filling quantity...');
            const quantitySelectors = [
                // Prioritize number inputs to avoid checkboxes
                'input[type="number"]',
                'input[placeholder*="quantity"]',
                'input[placeholder*="amount"]',
                'input[placeholder*="size"]',
                // Only use XPath as last resort and specify number type
                'text=Quantity(USDT) >> xpath=following::input[@type="number"][1]',
                'text=Quantity >> xpath=following::input[@type="number"][1]'
            ];

            const quantityField = await this.retryFindElement(quantitySelectors);
            
            // Clear existing value first
            await quantityField.click();
            await quantityField.selectText();
            await quantityField.fill('0.3600');
            console.log('✅ Quantity filled: 0.3600');

            // Step 3: Click the appropriate button
            console.log(`📊 Step 3: Clicking ${orderType} button...`);
            let buttonSelectors = [];
            
            switch (orderType) {
                case 'Open Long':
                    buttonSelectors = ['button:has-text("Open Long")', 'text=Open Long'];
                    break;
                case 'Open Short':
                    buttonSelectors = ['button:has-text("Open Short")', 'text=Open Short'];
                    break;
                case 'Close Long':
                    buttonSelectors = ['button:has-text("Close Long")', 'text=Close Long'];
                    break;
                case 'Close Short':
                    buttonSelectors = ['button:has-text("Close Short")', 'text=Close Short'];
                    break;
                default:
                    throw new Error(`Unknown order type: ${orderType}`);
            }

            const orderButton = await this.retryFindElement(buttonSelectors);
            await orderButton.click();
            console.log(`✅ ${orderType} button clicked`);

            // Step 4: Handle popup (only for Open orders)
            const needsPopup = orderType.includes('Open');
            
            if (needsPopup) {
                console.log('✅ Step 4: Handling popup...');
                const confirmClicked = await this.handleConfirmPopup(orderType);
                if (!confirmClicked) {
                    console.log('⚠️ Confirm popup not found, but continuing...');
                }
            } else {
                console.log('✅ Step 4: No popup needed for Close orders');
            }

            const executionTime = Date.now() - startTime;

            // Step 5: Verify success
            const verified = await this.verifyTrade(orderType);

            console.log(`⚡ ${orderType} completed in ${executionTime}ms`);
            console.log(`🎉 ${orderType} verified: ${verified ? '✅ YES' : '❌ NO'}`);

            return {
                success: verified,
                executionTime,
                verified,
                orderType,
                targetAchieved: executionTime < 2000
            };

        } catch (error) {
            const executionTime = Date.now() - startTime;
            console.error(`❌ ${orderType} failed after ${executionTime}ms:`, error.message);
            
            // Try emergency cleanup and retry once
            console.log('🚨 Attempting emergency cleanup and retry...');
            try {
                await this.cleanupPage();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // One more attempt at verification
                const verified = await this.verifyTrade(orderType);
                if (verified) {
                    console.log('✅ Emergency verification successful!');
                    return {
                        success: true,
                        executionTime,
                        verified: true,
                        orderType,
                        emergencyRecovery: true
                    };
                }
            } catch (retryError) {
                console.log('❌ Emergency retry also failed');
            }
            
            return { 
                success: false, 
                executionTime, 
                error: error.message, 
                orderType 
            };
        }
    }

    async handleConfirmPopup(orderType) {
        const confirmSelectors = [
            'button:has-text("Confirm")',
            'text=Confirm',
            '.confirm-btn',
            'button[class*="confirm"]'
        ];

        // Aggressive popup handling with retry
        for (let attempt = 1; attempt <= 20; attempt++) {
            for (const selector of confirmSelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    const isVisible = await element.isVisible({ timeout: 100 });
                    
                    if (isVisible) {
                        await element.click();
                        console.log(`✅ ${orderType}: Confirm clicked (attempt ${attempt})`);
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }
            await new Promise(resolve => setTimeout(resolve, 50));
        }

        console.log(`⚠️ ${orderType}: Confirm button not found after 20 attempts`);
        return false;
    }

    async verifyTrade(orderType) {
        try {
            const successSelectors = [
                'text=Purchased successfully',
                'text=Success',
                'text=success',
                'text=completed',
                '.success',
                '.toast-success'
            ];

            for (const selector of successSelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    const isVisible = await element.isVisible({ timeout: 500 });
                    if (isVisible) {
                        const text = await element.textContent();
                        console.log(`✅ ${orderType} success: ${text}`);
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }
            return false;
        } catch (error) {
            return false;
        }
    }
}

async function executeCommand(orderType, port) {
    const trader = new UltimateTrader(port);
    
    try {
        console.log('🎯 MEXC ULTIMATE TRADER');
        console.log('========================');
        console.log(`📊 Order: ${orderType}`);
        console.log(`🌐 Port: ${port}`);
        console.log('⚡ Target: <2 seconds per trade');
        console.log('🧹 Auto-cleanup: Enabled');
        console.log('🔄 Retry logic: Enhanced');
        console.log('');

        const connected = await trader.connectToBrowser();
        if (!connected) {
            throw new Error(`Failed to connect to browser on port ${port}`);
        }

        const result = await trader.executeOrder(orderType);

        // Final results
        if (result.success) {
            if (result.targetAchieved) {
                console.log('\n🏆 TARGET ACHIEVED!');
                console.log(`⚡ Executed in ${result.executionTime}ms (<2 seconds)`);
            } else {
                console.log('\n✅ Trade completed successfully!');
                console.log(`⏱️ Time: ${result.executionTime}ms`);
            }
            
            if (result.emergencyRecovery) {
                console.log('🚨 Recovered via emergency cleanup!');
            }
        } else {
            console.log('\n❌ Trade failed');
            if (result.error) console.log(`Error: ${result.error}`);
        }

        // Save results
        require('fs').writeFileSync(`ultimate-results-${orderType.replace(' ', '')}-${port}.json`, JSON.stringify(result, null, 2));
        
        process.exit(result.success ? 0 : 1);
        
    } catch (error) {
        console.error('💥 Ultimate trader failed:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    const orderType = process.argv[2];
    const port = parseInt(process.argv[3]) || (orderType && orderType.includes('Close') ? 9223 : 9222);
    
    const validOrders = ['Open Long', 'Open Short', 'Close Long', 'Close Short'];
    
    if (!orderType || !validOrders.includes(orderType)) {
        console.log('🎯 MEXC ULTIMATE TRADER');
        console.log('========================');
        console.log('📋 USAGE:');
        console.log('node ultimate-trader.js "Open Long" [port]    - Open Long position');
        console.log('node ultimate-trader.js "Open Short" [port]   - Open Short position');
        console.log('node ultimate-trader.js "Close Long" [port]   - Close Long position');
        console.log('node ultimate-trader.js "Close Short" [port]  - Close Short position');
        console.log('');
        console.log('🌐 Default Ports:');
        console.log('- Open orders: 9222');
        console.log('- Close orders: 9223');
        console.log('');
        console.log('📋 Examples:');
        console.log('node ultimate-trader.js "Open Long"     # Uses port 9222');
        console.log('node ultimate-trader.js "Close Long"    # Uses port 9223');
        console.log('node ultimate-trader.js "Open Short" 9222');
        console.log('');
        console.log('🚀 Prerequisites:');
        console.log('chrome.exe --remote-debugging-port=9222 --user-data-dir="./browser_data"');
        console.log('chrome.exe --remote-debugging-port=9223 --user-data-dir="./browser_data2"');
        process.exit(1);
    }
    
    executeCommand(orderType, port);
}

module.exports = UltimateTrader;
