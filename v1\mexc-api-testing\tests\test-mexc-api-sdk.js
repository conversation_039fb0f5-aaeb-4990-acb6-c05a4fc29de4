require('dotenv').config();

async function testMexcApiSDK() {
    console.log('\n=== Testing mexc-api-sdk ===');
    
    try {
        // Try to import the package
        let MexcApiSDK;
        try {
            MexcApiSDK = require('mexc-api-sdk');
        } catch (importError) {
            console.log('⚠ Import failed, trying alternative import methods...');
            try {
                const pkg = require('mexc-api-sdk');
                MexcApiSDK = pkg.default || pkg.MexcApiSDK || pkg;
            } catch (altError) {
                throw new Error(`Failed to import mexc-api-sdk: ${importError.message}`);
            }
        }

        console.log('✓ mexc-api-sdk imported successfully');

        // Initialize the SDK
        let client;
        try {
            client = new MexcApiSDK({
                apiKey: process.env.MEXC_API_KEY,
                apiSecret: process.env.MEXC_API_SECRET,
            });
        } catch (initError) {
            // Try alternative initialization
            client = MexcApiSDK({
                apiKey: process.env.MEXC_API_KEY,
                apiSecret: process.env.MEXC_API_SECRET,
            });
        }

        console.log('✓ SDK client initialized');

        // Test connection
        console.log('\n1. Testing connection...');
        
        // Try to get server time
        try {
            const serverTime = await client.getServerTime();
            console.log('✓ Server time retrieved:', serverTime);
        } catch (error) {
            console.log('⚠ Server time failed:', error.message);
        }

        // Try to get account info
        try {
            const accountInfo = await client.getAccountInfo();
            console.log('✓ Account info retrieved');
        } catch (error) {
            console.log('⚠ Account info failed:', error.message);
        }

        // Test market data
        console.log('\n2. Testing market data...');
        try {
            const symbol = process.env.TEST_SYMBOL || 'BTCUSDT';
            const ticker = await client.getTicker(symbol);
            console.log('✓ Ticker data retrieved for', symbol);
        } catch (error) {
            console.log('⚠ Market data failed:', error.message);
        }

        // Test futures functionality if available
        console.log('\n3. Testing futures functionality...');
        try {
            const positions = await client.getFuturesPositions();
            console.log('✓ Futures positions retrieved:', positions.length, 'positions');
        } catch (error) {
            console.log('⚠ Futures positions failed:', error.message);
        }

        // Test order placement (dry run)
        console.log('\n4. Testing order creation (dry run)...');
        try {
            const orderParams = {
                symbol: process.env.TEST_SYMBOL || 'BTCUSDT',
                side: process.env.TEST_SIDE || 'buy',
                type: 'LIMIT',
                quantity: process.env.TEST_QUANTITY || '0.001',
                price: '30000',
                timeInForce: 'GTC'
            };

            console.log('Order parameters:', orderParams);
            
            // Note: Uncomment to actually place order
            // const order = await client.createOrder(orderParams);
            console.log('✓ Order parameters validated (order not placed)');
            
        } catch (error) {
            console.log('⚠ Order creation test failed:', error.message);
        }

        console.log('\n✅ mexc-api-sdk test completed');
        return { success: true, library: 'mexc-api-sdk', features: ['spot', 'futures_possible'] };

    } catch (error) {
        console.error('❌ mexc-api-sdk test failed:', error.message);
        return { success: false, library: 'mexc-api-sdk', error: error.message };
    }
}

if (require.main === module) {
    testMexcApiSDK().then(result => {
        console.log('\nResult:', result);
        process.exit(result.success ? 0 : 1);
    });
}

module.exports = testMexcApiSDK;
