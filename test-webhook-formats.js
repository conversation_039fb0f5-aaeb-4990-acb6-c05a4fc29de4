#!/usr/bin/env node

/**
 * Test Webhook Formats
 * Tests the new TradingView webhook format handling
 */

const axios = require('axios');

class WebhookFormatTester {
    constructor() {
        this.webhookUrl = 'http://localhost:80/webhook';
        this.testResults = [];
    }

    async runTests() {
        console.log('📡 Testing TradingView Webhook Formats...\n');

        // Test 1: Buy signal (should map to Open Long)
        await this.testBuySignal();

        // Test 2: Sell signal (should map to Open Short)
        await this.testSellSignal();

        // Test 3: Close signal (should be ignored)
        await this.testCloseSignal();

        // Test 4: Invalid format
        await this.testInvalidFormat();

        // Test 5: Missing fields
        await this.testMissingFields();

        // Test 6: Both leverage spellings
        await this.testLeverageSpellings();

        // Print results
        this.printResults();
    }

    async testBuySignal() {
        console.log('🟢 Testing BUY signal...');
        
        const buySignal = {
            symbol: "TRUUSDT",
            trade: "buy",
            last_price: "0.000012064",
            leverege: "2"
        };

        try {
            const response = await axios.post(this.webhookUrl, buySignal, { timeout: 10000 });
            
            const success = response.data.success && 
                           response.data.processedSignal?.orderType === 'Open Long';
            
            this.addResult('Buy Signal → Open Long', success, {
                mapped_to: response.data.processedSignal?.orderType,
                message: response.data.message,
                execution_time: response.data.executionTime
            });

        } catch (error) {
            this.addResult('Buy Signal → Open Long', false, {
                error: error.response?.data?.error || error.message
            });
        }
    }

    async testSellSignal() {
        console.log('🔴 Testing SELL signal...');
        
        const sellSignal = {
            symbol: "TRUUSDT",
            trade: "sell",
            last_price: "0.000012064",
            leverege: "2"
        };

        try {
            const response = await axios.post(this.webhookUrl, sellSignal, { timeout: 10000 });
            
            const success = response.data.success && 
                           response.data.processedSignal?.orderType === 'Open Short';
            
            this.addResult('Sell Signal → Open Short', success, {
                mapped_to: response.data.processedSignal?.orderType,
                message: response.data.message,
                execution_time: response.data.executionTime
            });

        } catch (error) {
            this.addResult('Sell Signal → Open Short', false, {
                error: error.response?.data?.error || error.message
            });
        }
    }

    async testCloseSignal() {
        console.log('⏹️ Testing CLOSE signal (should be ignored)...');
        
        const closeSignal = {
            symbol: "TRUUSDT",
            trade: "close",
            last_price: "0.000012064",
            leverege: "2"
        };

        try {
            const response = await axios.post(this.webhookUrl, closeSignal, { timeout: 10000 });
            
            const ignored = response.data.message && 
                           response.data.message.includes('ignored');
            
            this.addResult('Close Signal Ignored', ignored, {
                message: response.data.message,
                success: response.data.success
            });

        } catch (error) {
            this.addResult('Close Signal Ignored', false, {
                error: error.response?.data?.error || error.message
            });
        }
    }

    async testInvalidFormat() {
        console.log('❌ Testing invalid format...');
        
        const invalidSignal = {
            invalid: "data",
            missing: "required_fields"
        };

        try {
            const response = await axios.post(this.webhookUrl, invalidSignal, { timeout: 5000 });
            
            // Should fail validation
            const properlyRejected = !response.data.success;
            
            this.addResult('Invalid Format Rejected', properlyRejected, {
                error: response.data.error,
                success: response.data.success
            });

        } catch (error) {
            // This is expected - should be rejected
            this.addResult('Invalid Format Rejected', true, {
                error: error.response?.data?.error || error.message
            });
        }
    }

    async testMissingFields() {
        console.log('⚠️ Testing missing required fields...');
        
        const incompleteSignal = {
            symbol: "TRUUSDT",
            trade: "buy"
            // Missing last_price and leverege
        };

        try {
            const response = await axios.post(this.webhookUrl, incompleteSignal, { timeout: 5000 });
            
            const properlyRejected = !response.data.success;
            
            this.addResult('Missing Fields Rejected', properlyRejected, {
                error: response.data.error,
                success: response.data.success
            });

        } catch (error) {
            this.addResult('Missing Fields Rejected', true, {
                error: error.response?.data?.error || error.message
            });
        }
    }

    async testLeverageSpellings() {
        console.log('🔤 Testing both leverage spellings...');
        
        // Test correct spelling
        const correctSpelling = {
            symbol: "TRUUSDT",
            trade: "buy",
            last_price: "0.000012064",
            leverage: "2"
        };

        try {
            const response1 = await axios.post(this.webhookUrl, correctSpelling, { timeout: 10000 });
            this.addResult('Correct "leverage" Spelling', response1.data.success, {
                message: response1.data.message
            });
        } catch (error) {
            this.addResult('Correct "leverage" Spelling', false, {
                error: error.response?.data?.error || error.message
            });
        }

        // Test typo spelling
        const typoSpelling = {
            symbol: "TRUUSDT",
            trade: "buy",
            last_price: "0.000012064",
            leverege: "2"
        };

        try {
            const response2 = await axios.post(this.webhookUrl, typoSpelling, { timeout: 10000 });
            this.addResult('Typo "leverege" Spelling', response2.data.success, {
                message: response2.data.message
            });
        } catch (error) {
            this.addResult('Typo "leverege" Spelling', false, {
                error: error.response?.data?.error || error.message
            });
        }
    }

    addResult(testName, passed, details) {
        this.testResults.push({
            name: testName,
            passed,
            details,
            timestamp: new Date().toISOString()
        });
    }

    printResults() {
        console.log('\n📊 WEBHOOK FORMAT TEST RESULTS');
        console.log('=' .repeat(60));

        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;

        console.log(`\n🎯 Results: ${passed}/${total} tests passed (${Math.round(passed/total*100)}%)\n`);

        this.testResults.forEach((result, index) => {
            const status = result.passed ? '✅' : '❌';
            console.log(`${status} ${index + 1}. ${result.name}`);
            
            if (result.details) {
                Object.entries(result.details).forEach(([key, value]) => {
                    if (value !== undefined && value !== null) {
                        console.log(`   ${key}: ${value}`);
                    }
                });
            }
            console.log('');
        });

        if (passed === total) {
            console.log('🎉 All webhook format tests passed!');
        } else {
            console.log('⚠️ Some webhook format tests failed.');
        }
    }
}

// Run tests if called directly
if (require.main === module) {
    const tester = new WebhookFormatTester();
    tester.runTests().catch(error => {
        console.error('❌ Test execution failed:', error);
        process.exit(1);
    });
}

module.exports = WebhookFormatTester;
