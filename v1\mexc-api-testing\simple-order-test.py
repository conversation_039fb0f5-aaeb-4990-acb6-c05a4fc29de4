#!/usr/bin/env python3
"""
Simple MEXC order placement test using different approaches
"""

import os
import sys
import json
import time
import requests
import hmac
import hashlib
from dotenv import load_dotenv

load_dotenv()

API_KEY = os.getenv('MEXC_API_KEY')
API_SECRET = os.getenv('MEXC_API_SECRET')

def test_spot_order():
    """Try placing a spot order instead of futures"""
    print("🎯 Testing SPOT order placement...")
    
    try:
        import ccxt
        
        exchange = ccxt.mexc({
            'apiKey': API_KEY,
            'secret': API_SECRET,
            'sandbox': False,
            'enableRateLimit': True,
        })
        
        # Load markets
        markets = exchange.load_markets()
        
        # Try spot TRU/USDT
        spot_symbol = 'TRU/USDT'
        if spot_symbol not in markets:
            print(f"❌ Spot symbol {spot_symbol} not found")
            return False
        
        print(f"✅ Found spot symbol: {spot_symbol}")
        
        # Get balance
        balance = exchange.fetch_balance()
        usdt_balance = balance.get('USDT', {}).get('free', 0)
        print(f"💰 USDT balance: {usdt_balance}")
        
        if usdt_balance < 0.1:
            print("❌ Insufficient USDT balance for spot trading")
            return False
        
        # Get current price
        ticker = exchange.fetch_ticker(spot_symbol)
        current_price = ticker['last']
        print(f"📊 Current price: {current_price}")
        
        # Calculate order size (use $0.5 for spot)
        order_value = 0.5
        quantity = order_value / current_price
        
        print(f"🎯 Placing spot market buy order:")
        print(f"   Symbol: {spot_symbol}")
        print(f"   Quantity: {quantity:.6f}")
        print(f"   Value: ${order_value}")
        
        # Place spot market order
        order = exchange.create_market_buy_order(spot_symbol, quantity)
        
        print("🎉 SPOT ORDER PLACED SUCCESSFULLY!")
        print(f"   Order ID: {order['id']}")
        print(f"   Status: {order['status']}")
        print(f"   Amount: {order['amount']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Spot order failed: {e}")
        return False

def test_direct_api_call():
    """Try direct API call with different parameters"""
    print("\n🎯 Testing direct API call...")
    
    try:
        # Try the spot API instead
        base_url = 'https://api.mexc.com'
        endpoint = '/api/v3/order'
        
        timestamp = int(time.time() * 1000)
        
        params = {
            'symbol': 'TRUUSDT',
            'side': 'BUY',
            'type': 'MARKET',
            'quoteOrderQty': '0.5',  # Buy $0.5 worth
            'timestamp': timestamp
        }
        
        # Create query string
        query_string = '&'.join([f"{k}={v}" for k, v in sorted(params.items())])
        
        # Generate signature
        signature = hmac.new(
            API_SECRET.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        params['signature'] = signature
        
        headers = {
            'X-MEXC-APIKEY': API_KEY,
            'Content-Type': 'application/json'
        }
        
        print(f"📡 Making request to: {base_url}{endpoint}")
        print(f"📋 Parameters: {params}")
        
        response = requests.post(
            f"{base_url}{endpoint}",
            json=params,
            headers=headers,
            timeout=30
        )
        
        result = response.json()
        
        if response.status_code == 200 and result.get('orderId'):
            print("🎉 DIRECT API ORDER PLACED SUCCESSFULLY!")
            print(f"   Order ID: {result['orderId']}")
            print(f"   Status: {result.get('status', 'Unknown')}")
            return True
        else:
            print(f"❌ Direct API call failed: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Direct API call failed: {e}")
        return False

def test_futures_with_different_params():
    """Try futures with different parameters"""
    print("\n🎯 Testing futures with different parameters...")
    
    try:
        import ccxt
        
        exchange = ccxt.mexc({
            'apiKey': API_KEY,
            'secret': API_SECRET,
            'sandbox': False,
            'enableRateLimit': True,
            'timeout': 60000,  # Increase timeout
            'options': {
                'defaultType': 'swap',
            }
        })
        
        # Load markets
        markets = exchange.load_markets()
        
        futures_symbol = 'TRU/USDT:USDT'
        
        # Get current price
        ticker = exchange.fetch_ticker(futures_symbol)
        current_price = ticker['last']
        print(f"📊 Current price: {current_price}")
        
        # Try smaller quantity
        quantity = 10  # Much smaller
        
        print(f"🎯 Placing small futures order:")
        print(f"   Symbol: {futures_symbol}")
        print(f"   Quantity: {quantity}")
        print(f"   Type: market")
        
        # Try with explicit parameters
        order = exchange.create_order(
            symbol=futures_symbol,
            type='market',
            side='buy',
            amount=quantity,
            price=None,
            params={
                'timeInForce': 'IOC',  # Immediate or Cancel
            }
        )
        
        print("🎉 FUTURES ORDER PLACED SUCCESSFULLY!")
        print(f"   Order ID: {order['id']}")
        print(f"   Status: {order['status']}")
        print(f"   Amount: {order['amount']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Futures order failed: {e}")
        return False

def main():
    print("🚀 Simple MEXC Order Placement Test")
    print("===================================")
    print(f"API Key: {API_KEY[:10]}..." if API_KEY else "No API Key")
    print("")
    
    print("⚠️  WARNING: This will attempt to place REAL orders!")
    print("⚠️  Using small amounts (~$0.5)")
    print("⚠️  Starting in 3 seconds... Press Ctrl+C to cancel")
    
    try:
        time.sleep(3)
    except KeyboardInterrupt:
        print("\n❌ Test cancelled by user")
        return False
    
    success = False
    
    # Try spot order first (usually more reliable)
    if test_spot_order():
        success = True
        print("\n🎉 SUCCESS! Spot order worked!")
    
    # Try direct API call
    if test_direct_api_call():
        success = True
        print("\n🎉 SUCCESS! Direct API call worked!")
    
    # Try futures with different params
    if test_futures_with_different_params():
        success = True
        print("\n🎉 SUCCESS! Futures order worked!")
    
    if success:
        print("\n✅ MISSION ACCOMPLISHED!")
        print("✅ Successfully placed order(s) on MEXC")
        print("✅ API integration confirmed working")
    else:
        print("\n❌ All order placement attempts failed")
        print("💡 Consider checking:")
        print("   - API permissions")
        print("   - Account verification status")
        print("   - Network connectivity")
        print("   - MEXC maintenance status")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test crashed: {e}")
        sys.exit(1)
