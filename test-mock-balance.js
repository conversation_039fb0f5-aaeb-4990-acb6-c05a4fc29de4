const axios = require('axios');

async function testMockBalance() {
    console.log('🧪 Testing Mock Balance Implementation...\n');

    try {
        // Test 1: Check balance endpoint
        console.log('1️⃣ Testing balance endpoint...');
        const balanceResponse = await axios.get('http://localhost:4000/api/balance', { timeout: 10000 });
        console.log('Balance Response:', JSON.stringify(balanceResponse.data, null, 2));

        // Test 2: Send a simple webhook to see if money management uses mock balance
        console.log('\n2️⃣ Testing webhook with money management...');
        
        // First, enable money management
        const config = {
            moneyManagementEnabled: true,
            moneyManagementMode: 'percentage',
            positionSizePercentage: 50,
            maxPriceDifference: 100
        };

        // Update config file directly
        const fs = require('fs');
        const configPath = 'tradingview-webhook-listener/config.json';
        const currentConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        const newConfig = { ...currentConfig, ...config };
        fs.writeFileSync(configPath, JSON.stringify(newConfig, null, 2));
        console.log('✅ Configuration updated');

        // Wait a moment for config to reload
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Send test webhook
        const testSignal = {
            symbol: "TRUUSDT",
            trade: "buy",
            last_price: "0.03225",
            leverege: "2"
        };

        console.log('Sending test signal:', JSON.stringify(testSignal, null, 2));
        
        const webhookResponse = await axios.post('http://localhost:4000/webhook', testSignal, { 
            timeout: 15000,
            headers: { 'Content-Type': 'application/json' }
        });

        console.log('\nWebhook Response:');
        console.log('Success:', webhookResponse.data.success);
        console.log('Message:', webhookResponse.data.message);
        console.log('Processed Signal:', JSON.stringify(webhookResponse.data.processedSignal, null, 2));
        console.log('Quantity:', webhookResponse.data.processedSignal?.quantity);
        console.log('Skipped:', webhookResponse.data.skipped);
        console.log('Reason:', webhookResponse.data.reason);

        // Test 3: Check if quantity calculation is working
        const expectedQuantity = 2.29 * 0.5; // 50% of 2.29 USDT
        const actualQuantity = parseFloat(webhookResponse.data.processedSignal?.quantity || 0);
        
        console.log('\n3️⃣ Quantity Calculation Check:');
        console.log('Expected Quantity (50% of 2.29):', expectedQuantity);
        console.log('Actual Quantity:', actualQuantity);
        console.log('Match:', Math.abs(actualQuantity - expectedQuantity) < 0.01 ? '✅' : '❌');

        // Test 4: Check status endpoint for balance info
        console.log('\n4️⃣ Testing status endpoint...');
        const statusResponse = await axios.get('http://localhost:4000/api/status', { timeout: 5000 });
        console.log('Status Balance:', JSON.stringify(statusResponse.data.balance, null, 2));

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

testMockBalance();
