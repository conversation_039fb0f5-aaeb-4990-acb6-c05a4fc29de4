#!/usr/bin/env python3
"""
MEXC Signature Implementation
Implementation of the cracked signature algorithm for production use
"""

import json
import time
import hashlib
import hmac
import base64
import random
import string
from curl_cffi import requests
from dotenv import dotenv_values
from typing import Dict, Optional

class MEXCSignatureImplementation:
    """Implementation of MEXC signature algorithm"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        self.uc_token = vals.get('uc_token')
        self.session = requests.Session(impersonate='chrome124')
        
        # This will be updated when we crack the algorithm
        self.working_algorithm = None
        
        print("🔧 MEXC Signature Implementation")
        print("="*35)
    
    def set_working_algorithm(self, algorithm_data: Dict):
        """Set the working algorithm from cracked data"""
        self.working_algorithm = algorithm_data
        print(f"✅ Working algorithm set: {algorithm_data.get('algorithm', 'Unknown')}")
    
    def generate_signature(self, order_data: Dict, nonce: str) -> str:
        """Generate signature using the working algorithm"""
        
        if not self.working_algorithm:
            print("❌ No working algorithm available")
            return self._fallback_signature(order_data, nonce)
        
        try:
            algorithm = self.working_algorithm.get('algorithm', '')
            hash_function = self.working_algorithm.get('hash_function', '')
            length = self.working_algorithm.get('length', 32)
            
            # Generate content based on algorithm
            content = self._generate_content(order_data, nonce, algorithm)
            
            # Apply hash function
            signature = self._apply_hash_function(content, hash_function, nonce)
            
            # Truncate to correct length
            return signature[:length]
            
        except Exception as e:
            print(f"❌ Signature generation error: {e}")
            return self._fallback_signature(order_data, nonce)
    
    def _generate_content(self, order_data: Dict, nonce: str, algorithm: str) -> str:
        """Generate content string based on algorithm"""
        
        if algorithm == 'JSON+nonce+auth':
            json_str = json.dumps(order_data, separators=(',', ':'), sort_keys=True)
            return json_str + nonce + self.auth
        
        elif algorithm == 'auth+nonce+JSON':
            json_str = json.dumps(order_data, separators=(',', ':'), sort_keys=True)
            return self.auth + nonce + json_str
        
        elif algorithm == 'query+nonce+auth':
            query_str = self._to_query_string(order_data)
            return query_str + nonce + self.auth
        
        elif algorithm == 'auth+nonce+query':
            query_str = self._to_query_string(order_data)
            return self.auth + nonce + query_str
        
        elif algorithm == 'nonce+query+auth':
            query_str = self._to_query_string(order_data)
            return nonce + query_str + self.auth
        
        elif algorithm == 'auth+query+nonce':
            query_str = self._to_query_string(order_data)
            return self.auth + query_str + nonce
        
        else:
            # Default fallback
            json_str = json.dumps(order_data, separators=(',', ':'), sort_keys=True)
            return self.auth + nonce + json_str
    
    def _apply_hash_function(self, content: str, hash_function: str, nonce: str) -> str:
        """Apply the specified hash function"""
        
        if hash_function == 'SHA256':
            return hashlib.sha256(content.encode()).hexdigest()
        
        elif hash_function == 'MD5':
            return hashlib.md5(content.encode()).hexdigest()
        
        elif hash_function == 'HMAC-SHA256-auth':
            return hmac.new(self.auth.encode(), content.encode(), hashlib.sha256).hexdigest()
        
        elif hash_function == 'HMAC-SHA256-nonce':
            return hmac.new(nonce.encode(), content.encode(), hashlib.sha256).hexdigest()
        
        elif hash_function == 'HMAC-SHA256-combined':
            secret = (self.auth + nonce).encode()
            return hmac.new(secret, content.encode(), hashlib.sha256).hexdigest()
        
        elif hash_function == 'Base64-SHA256':
            b64_content = base64.b64encode(content.encode()).decode()
            return hashlib.sha256(b64_content.encode()).hexdigest()
        
        else:
            # Default fallback
            return hashlib.sha256(content.encode()).hexdigest()
    
    def _to_query_string(self, data: Dict) -> str:
        """Convert dict to query string"""
        params = []
        for key in sorted(data.keys()):
            params.append(f"{key}={data[key]}")
        return '&'.join(params)
    
    def _fallback_signature(self, order_data: Dict, nonce: str) -> str:
        """Fallback signature generation"""
        # Use the most common pattern as fallback
        json_str = json.dumps(order_data, separators=(',', ':'), sort_keys=True)
        content = self.auth + nonce + json_str
        return hashlib.sha256(content.encode()).hexdigest()[:32]
    
    def get_market_price(self, symbol: str) -> float:
        """Get market price"""
        
        headers = {'Accept': 'application/json', 'authorization': self.auth}
        
        try:
            r = self.session.get('https://futures.mexc.com/api/v1/contract/ticker',
                               params={'symbol': symbol}, headers=headers)
            
            if r.status_code == 200:
                data = r.json()
                if data.get('code') == 0:
                    ticker_data = data.get('data')
                    if isinstance(ticker_data, list) and ticker_data:
                        return float(ticker_data[0].get('lastPrice', 0))
                    elif isinstance(ticker_data, dict):
                        return float(ticker_data.get('lastPrice', 0))
        except Exception as e:
            print(f"❌ Market data error: {e}")
        
        return 0.0
    
    def place_order_with_signature(self, symbol: str, side: int, price: float, volume: int = 1) -> Dict:
        """Place order using the implemented signature algorithm"""
        
        print(f"🚀 Placing order with implemented signature...")
        print(f"   Symbol: {symbol}")
        print(f"   Side: {'LONG' if side == 1 else 'SHORT'}")
        print(f"   Price: ${price:,.2f}")
        print(f"   Volume: {volume}")
        
        # Generate nonce
        nonce = str(int(time.time() * 1000))
        
        # Prepare order data
        order_data = {
            'symbol': symbol,
            'side': side,
            'openType': 1,
            'type': '2',
            'vol': volume,
            'leverage': 1,
            'marketCeiling': False,
            'price': str(price),
            'priceProtect': '0'
        }
        
        # Generate opaque parameters
        p0 = hashlib.md5(f"{nonce}{json.dumps(order_data)}{self.auth}".encode()).hexdigest()
        k0 = hashlib.md5(f"{time.time()}{random.random()}".encode()).hexdigest()[:16]
        
        order_data['p0'] = p0
        order_data['k0'] = k0
        
        # Generate signature using implemented algorithm
        signature = self.generate_signature(order_data, nonce)
        
        print(f"🔐 Generated signature: {signature[:16]}...")
        print(f"🔢 Nonce: {nonce}")
        
        # Prepare headers
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Origin': 'https://futures.mexc.com',
            'Referer': 'https://futures.mexc.com/exchange',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'authorization': self.auth,
            'x-mxc-nonce': nonce,
            'x-mxc-sign': signature,
            'x-language': 'en_US',
        }
        
        if self.uc_token:
            headers['mtoken'] = self.uc_token
        
        # Place order
        try:
            mhash = ''.join(random.choices(string.ascii_lowercase + string.digits, k=32))
            url = f'https://futures.mexc.com/api/v1/private/order/create?mhash={mhash}'
            
            r = self.session.post(url, json=order_data, headers=headers)
            
            print(f"📡 Response: {r.status_code}")
            
            if r.status_code == 200:
                result = r.json()
                
                if result.get('success') and result.get('code') == 0:
                    order_id = result.get('data', {}).get('orderId')
                    print(f"✅ Order placed successfully! ID: {order_id}")
                    return {
                        'success': True,
                        'order_id': order_id,
                        'result': result
                    }
                else:
                    error_code = result.get('code')
                    error_msg = result.get('message', '')
                    print(f"❌ Order failed: {error_code} - {error_msg}")
                    
                    if error_code == 602:
                        print("   → Signature verification failed")
                        print("   → Algorithm may need adjustment")
                    
                    return {'success': False, 'error_code': error_code, 'error_msg': error_msg}
            else:
                print(f"❌ HTTP error: {r.status_code}")
                return {'success': False, 'error': f'HTTP {r.status_code}'}
                
        except Exception as e:
            print(f"❌ Order placement error: {e}")
            return {'success': False, 'error': str(e)}
    
    def cancel_order(self, order_id: str) -> Dict:
        """Cancel order"""
        
        print(f"🔄 Canceling order {order_id}...")
        
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'authorization': self.auth,
            'x-mxc-nonce': str(int(time.time() * 1000)),
        }
        
        if self.uc_token:
            headers['mtoken'] = self.uc_token
        
        try:
            r = self.session.post('https://futures.mexc.com/api/v1/private/order/cancel',
                                json=[order_id], headers=headers)
            
            if r.status_code == 200:
                result = r.json()
                if result.get('success') and result.get('code') == 0:
                    print("✅ Order canceled successfully")
                    return {'success': True}
                else:
                    print(f"❌ Cancel failed: {result.get('message', 'Unknown')}")
                    return {'success': False}
            else:
                print(f"❌ Cancel HTTP error: {r.status_code}")
                return {'success': False}
                
        except Exception as e:
            print(f"❌ Cancel error: {e}")
            return {'success': False}
    
    def test_implementation(self, symbol: str = 'BTC_USDT'):
        """Test the signature implementation"""
        
        print(f"🧪 Testing signature implementation for {symbol}...")
        
        # Get market price
        market_price = self.get_market_price(symbol)
        if market_price <= 0:
            print("❌ Could not get market price")
            return False
        
        # Calculate test price (70% below market)
        test_price = round(market_price * 0.3, 2)
        
        print(f"📊 Market price: ${market_price:,.2f}")
        print(f"🎯 Test price: ${test_price:,.2f}")
        
        # Place test order
        order_result = self.place_order_with_signature(symbol, 1, test_price, 1)
        
        if order_result.get('success'):
            order_id = order_result.get('order_id')
            print(f"🎉 SIGNATURE IMPLEMENTATION SUCCESS!")
            
            if order_id:
                # Cancel the test order
                time.sleep(2)
                cancel_result = self.cancel_order(str(order_id))
                
                if cancel_result.get('success'):
                    print(f"✅ Test order canceled")
                    print(f"\n🚀 SIGNATURE ALGORITHM FULLY WORKING!")
                    return True
                else:
                    print(f"⚠️ Order placed but cancel failed")
                    return True  # Still consider it successful
            else:
                print(f"⚠️ Order placed but no ID returned")
                return True
        else:
            print(f"❌ Signature implementation failed")
            error_code = order_result.get('error_code')
            
            if error_code == 602:
                print(f"💡 Signature verification failed - algorithm needs adjustment")
            else:
                print(f"💡 Other error: {order_result.get('error_msg', 'Unknown')}")
            
            return False

def main():
    """Main implementation test"""
    
    implementation = MEXCSignatureImplementation()
    
    # Example: Set a working algorithm (this would come from the cracker)
    # implementation.set_working_algorithm({
    #     'algorithm': 'auth+nonce+JSON',
    #     'hash_function': 'HMAC-SHA256-auth',
    #     'length': 32
    # })
    
    # Test the implementation
    success = implementation.test_implementation('BTC_USDT')
    
    if success:
        print(f"\n🎉 SIGNATURE IMPLEMENTATION READY FOR PRODUCTION!")
    else:
        print(f"\n🔧 Implementation needs refinement")

if __name__ == '__main__':
    main()
