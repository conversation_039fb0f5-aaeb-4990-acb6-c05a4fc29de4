# MEXC Futures Trader

High-speed MEXC futures trading service with sub-2 second execution target. Supports automated trading through browser automation with optimized quantity field handling and smart popup management.

## 🚀 Features

- **Sub-2 Second Execution**: Optimized for high-speed trading
- **4 Order Types**: Open Long, Open Short, Close Long, Close Short
- **Smart Quantity Handling**: Automatically clears existing values and fills new quantities
- **Popup Management**: Intelligent handling of confirmation dialogs
- **Error Recovery**: Robust error handling with automatic recovery
- **Position Validation**: Checks for open positions before close orders
- **Dual Browser Support**: Separate browsers for open (9222) and close (9223) operations
- **REST API**: HTTP endpoints for integration with other services
- **CLI Interface**: Command-line interface for direct trading
- **Comprehensive Logging**: Detailed logging for debugging and monitoring

## 📋 Prerequisites

- **Node.js** 16.0.0 or higher
- **Google Chrome** browser
- **MEXC Account** with futures trading enabled
- **Windows** environment (PowerShell)

## 🛠️ Installation

1. **Clone or navigate to the service directory:**
   ```bash
   cd mexc-futures-trader
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **<PERSON>reate logs directory:**
   ```bash
   mkdir logs
   ```

## 🌐 Browser Setup (CRITICAL)

**⚠️ IMPORTANT: Browsers must be set up manually before using the service.**

### Step 1: Create Browser Data Directories
```bash
mkdir browser_data_open
mkdir browser_data_close
```

### Step 2: Start Browsers on Required Ports

**For Open Orders (Port 9222):**
```bash
chrome.exe --remote-debugging-port=9222 --user-data-dir="./browser_data_open"
```

**For Close Orders (Port 9223):**
```bash
chrome.exe --remote-debugging-port=9223 --user-data-dir="./browser_data_close"
```

### Step 3: Manual Browser Configuration

1. **Navigate to MEXC Futures:**
   - Open both browsers
   - Go to: `https://www.mexc.com/futures/TRU_USDT`
   - Log in to your MEXC account
   - Ensure you're on the futures trading page

2. **Set Trading Modes:**
   - **Browser on port 9222**: Set to "Open" mode for opening positions
   - **Browser on port 9223**: Set to "Close" mode for closing positions

3. **Keep Browsers Running:**
   - Do NOT close these browsers while using the service
   - The service connects to these existing browser instances

## 🎯 Usage

### Method 1: Command Line Interface (CLI)

**Basic Usage:**
```bash
# Open a long position with default quantity (0.3600 USDT)
node src/cli.js "Open Long"

# Open a short position with custom quantity
node src/cli.js "Open Short" 1.5000

# Close a long position
node src/cli.js "Close Long"

# Close a short position with custom quantity
node src/cli.js "Close Short" 0.5000
```

**Get Help:**
```bash
node src/cli.js --help
```

### Method 2: REST API Server

**Start the server:**
```bash
npm start
# or
node src/server.js
```

**Server will start on port 3000 by default.**

#### API Endpoints

**Health Check:**
```bash
GET http://localhost:3000/health
```

**Service Information:**
```bash
GET http://localhost:3000/info
```

**Execute Single Trade:**
```bash
POST http://localhost:3000/trade
Content-Type: application/json

{
  "orderType": "Open Long",
  "quantity": "0.3600"
}
```

**Execute Batch Trades:**
```bash
POST http://localhost:3000/trade/batch
Content-Type: application/json

{
  "orders": [
    {"orderType": "Open Long", "quantity": "0.5000"},
    {"orderType": "Close Long", "quantity": "0.5000"}
  ]
}
```

#### API Response Format

**Successful Trade:**
```json
{
  "success": true,
  "executionTime": 1847,
  "verified": true,
  "orderType": "Open Long",
  "quantity": "0.3600",
  "targetAchieved": true,
  "timestamp": "2025-08-14T12:34:56.789Z",
  "totalServiceTime": 1923,
  "port": 9222,
  "requestId": "**********-abc123def"
}
```

**Failed Trade:**
```json
{
  "success": false,
  "error": "Close Long button not found. No open long positions to close.",
  "executionTime": 1234,
  "orderType": "Close Long",
  "quantity": "0.3600",
  "timestamp": "2025-08-14T12:34:56.789Z"
}
```

## 🧪 Testing

**Run comprehensive tests:**
```bash
npm test
# or
node tests/test-all-orders.js
```

**Test individual order types:**
```bash
node src/cli.js "Open Long"
node src/cli.js "Open Short" 
node src/cli.js "Close Long"
node src/cli.js "Close Short"
```

## 📊 Supported Order Types

| Order Type | Port | Description | Requirements |
|------------|------|-------------|--------------|
| **Open Long** | 9222 | Create new long position | Browser in Open mode |
| **Open Short** | 9222 | Create new short position | Browser in Open mode |
| **Close Long** | 9223 | Close existing long position | Browser in Close mode + Open long position |
| **Close Short** | 9223 | Close existing short position | Browser in Close mode + Open short position |

## ⚡ Performance Targets

- **Execution Time**: < 2 seconds per trade
- **Success Rate**: > 95% under normal conditions
- **Error Recovery**: Automatic retry with smart cleanup
- **Concurrent Trades**: Supports batch operations

## 🔧 Configuration

### Environment Variables

```bash
# Server port (default: 3000)
PORT=3000

# Log level (default: info)
LOG_LEVEL=info
```

### Browser Ports

- **Port 9222**: Open orders (Open Long, Open Short)
- **Port 9223**: Close orders (Close Long, Close Short)

**⚠️ These ports are fixed and cannot be changed without code modification.**

## 📝 Logging

Logs are saved to:
- `logs/combined.log` - All logs
- `logs/error.log` - Error logs only
- Console output - Real-time logs

## 🚨 Troubleshooting

### Common Issues

**1. "Failed to connect to browser on port XXXX"**
- Ensure Chrome is running with correct debugging port
- Check browser data directory permissions
- Verify no other processes are using the port

**2. "Quantity field not found"**
- Ensure you're on the correct MEXC futures page
- Check that the page is fully loaded
- Verify you're logged in to MEXC

**3. "Close Long/Short button not found"**
- This usually means no open positions exist
- Open a position first before trying to close
- Check the browser is in correct mode (Close)

**4. "Execution time > 2 seconds"**
- This is normal for complex operations
- System prioritizes reliability over speed
- Check for network latency issues

### Debug Steps

1. **Check browser connections:**
   ```bash
   # Test if browsers are accessible
   curl http://localhost:9222/json
   curl http://localhost:9223/json
   ```

2. **Verify MEXC page:**
   - Manually check both browsers
   - Ensure logged in and on futures page
   - Check for any blocking popups

3. **Run health check:**
   ```bash
   curl http://localhost:3000/health
   ```

## 🔒 Security Notes

- **Local Only**: Service runs locally, no external access
- **Browser Isolation**: Uses separate browser instances
- **No API Keys**: Uses browser session authentication
- **Secure Logging**: Sensitive data is not logged

## 📈 Integration

This service is designed to be integrated with other trading systems:

1. **Webhook Integration**: Use REST API endpoints
2. **Microservice Architecture**: Runs as independent service
3. **Batch Processing**: Supports multiple orders per request
4. **Real-time Monitoring**: Comprehensive logging and health checks

## 🎯 Next Steps

After setting up this service, you can:

1. **Create a webhook receiver** to accept TradingView signals
2. **Build a dashboard** for monitoring trades
3. **Add position management** features
4. **Implement risk management** rules
5. **Scale to multiple trading pairs**

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review log files in the `logs/` directory
3. Test with the CLI interface first
4. Verify browser setup is correct

---

**⚡ Ready to trade at lightning speed!** 🚀
