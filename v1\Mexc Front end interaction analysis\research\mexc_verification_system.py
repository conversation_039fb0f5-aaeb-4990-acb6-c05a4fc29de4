#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MEXC Verification System
Comprehensive verification to check if our automation is actually working or reporting false positives.

This script will:
1. Take detailed screenshots of the current browser state
2. Analyze all input fields and their actual values
3. Test real interactions and verify they work
4. Provide visual evidence of what's actually happening
"""

import os
import sys
import json
import time
import logging
from datetime import datetime
from typing import Dict, Any, List
from playwright.sync_api import sync_playwright

class MEXCVerificationSystem:
    """Comprehensive verification of MEXC browser automation"""
    
    def __init__(self):
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # Browser components
        self.playwright = None
        self.browser = None
        self.page = None
        
        # Verification data
        self.verification_data = {
            "timestamp": datetime.now().isoformat(),
            "screenshots": [],
            "input_analysis": [],
            "button_analysis": [],
            "interaction_tests": []
        }
        
        self.screenshot_counter = 0
    
    def take_screenshot(self, name: str, description: str = "") -> str:
        """Take a screenshot with detailed naming"""
        self.screenshot_counter += 1
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"verify_{self.screenshot_counter:03d}_{name}_{timestamp}.png"
        
        try:
            self.page.screenshot(path=filename, full_page=True)
            self.logger.info(f"📸 {filename} - {description}")
            self.verification_data["screenshots"].append({
                "filename": filename,
                "description": description,
                "timestamp": datetime.now().isoformat()
            })
            return filename
        except Exception as e:
            self.logger.error(f"Screenshot failed: {e}")
            return ""
    
    def connect_to_browser(self) -> bool:
        """Connect to browser"""
        self.logger.info("🔌 Connecting to browser for verification...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                self.logger.error("No browser contexts found")
                return False
            
            context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                self.logger.error("No MEXC page found")
                return False
            
            self.page = mexc_page
            self.logger.info(f"✅ Connected to MEXC page: {self.page.url}")
            
            # Take initial screenshot
            self.take_screenshot("initial_state", "Initial browser state before any interactions")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Browser connection failed: {e}")
            return False
    
    def analyze_all_inputs(self) -> List[Dict[str, Any]]:
        """Analyze all input fields on the page"""
        self.logger.info("🔍 Analyzing all input fields...")
        
        analysis_script = """
        () => {
            const inputs = Array.from(document.querySelectorAll('input'));
            const analysis = [];
            
            inputs.forEach((input, index) => {
                const rect = input.getBoundingClientRect();
                const isVisible = rect.width > 0 && rect.height > 0 && 
                                window.getComputedStyle(input).visibility !== 'hidden' &&
                                window.getComputedStyle(input).display !== 'none';
                
                // Get parent context
                let parentText = '';
                let parent = input.parentElement;
                while (parent && parentText.length < 100) {
                    const text = parent.textContent || '';
                    if (text.length > parentText.length) {
                        parentText = text.substring(0, 100);
                    }
                    parent = parent.parentElement;
                    if (parent === document.body) break;
                }
                
                analysis.push({
                    index: index,
                    type: input.type || 'text',
                    value: input.value || '',
                    placeholder: input.placeholder || '',
                    className: input.className || '',
                    id: input.id || '',
                    name: input.name || '',
                    isVisible: isVisible,
                    isEnabled: !input.disabled,
                    position: {
                        x: Math.round(rect.x),
                        y: Math.round(rect.y),
                        width: Math.round(rect.width),
                        height: Math.round(rect.height)
                    },
                    parentContext: parentText.trim()
                });
            });
            
            return analysis;
        }
        """
        
        try:
            input_analysis = self.page.evaluate(analysis_script)
            self.verification_data["input_analysis"] = input_analysis
            
            self.logger.info(f"📊 Found {len(input_analysis)} input fields:")
            
            for i, input_data in enumerate(input_analysis):
                if input_data['isVisible']:
                    self.logger.info(f"  Input {i+1}:")
                    self.logger.info(f"    Value: '{input_data['value']}'")
                    self.logger.info(f"    Placeholder: '{input_data['placeholder']}'")
                    self.logger.info(f"    Classes: '{input_data['className'][:50]}'")
                    self.logger.info(f"    Position: {input_data['position']}")
                    self.logger.info(f"    Context: '{input_data['parentContext'][:50]}'")
                    self.logger.info("")
            
            return input_analysis
            
        except Exception as e:
            self.logger.error(f"Input analysis failed: {e}")
            return []
    
    def analyze_order_buttons(self) -> List[Dict[str, Any]]:
        """Analyze all potential order buttons"""
        self.logger.info("🔍 Analyzing order buttons...")
        
        button_analysis_script = """
        () => {
            const buttons = Array.from(document.querySelectorAll('button'));
            const analysis = [];
            
            buttons.forEach((button, index) => {
                const rect = button.getBoundingClientRect();
                const isVisible = rect.width > 0 && rect.height > 0 && 
                                window.getComputedStyle(button).visibility !== 'hidden' &&
                                window.getComputedStyle(button).display !== 'none';
                
                const text = button.textContent || '';
                
                // Check if this looks like a trading button
                const isTradingButton = text.toLowerCase().includes('buy') || 
                                      text.toLowerCase().includes('sell') || 
                                      text.toLowerCase().includes('long') || 
                                      text.toLowerCase().includes('short') ||
                                      text.toLowerCase().includes('open') ||
                                      button.className.includes('longBtn') ||
                                      button.className.includes('shortBtn');
                
                if (isTradingButton || isVisible) {
                    analysis.push({
                        index: index,
                        text: text.trim(),
                        className: button.className || '',
                        id: button.id || '',
                        isVisible: isVisible,
                        isEnabled: !button.disabled,
                        isTradingButton: isTradingButton,
                        position: {
                            x: Math.round(rect.x),
                            y: Math.round(rect.y),
                            width: Math.round(rect.width),
                            height: Math.round(rect.height)
                        }
                    });
                }
            });
            
            return analysis;
        }
        """
        
        try:
            button_analysis = self.page.evaluate(button_analysis_script)
            self.verification_data["button_analysis"] = button_analysis
            
            self.logger.info(f"📊 Found {len(button_analysis)} relevant buttons:")
            
            for i, button_data in enumerate(button_analysis):
                if button_data['isVisible']:
                    self.logger.info(f"  Button {i+1}:")
                    self.logger.info(f"    Text: '{button_data['text']}'")
                    self.logger.info(f"    Classes: '{button_data['className'][:50]}'")
                    self.logger.info(f"    Trading Button: {button_data['isTradingButton']}")
                    self.logger.info(f"    Position: {button_data['position']}")
                    self.logger.info(f"    Enabled: {button_data['isEnabled']}")
                    self.logger.info("")
            
            return button_analysis
            
        except Exception as e:
            self.logger.error(f"Button analysis failed: {e}")
            return []
    
    def test_quantity_field_interaction(self, test_value: str = "7.5") -> Dict[str, Any]:
        """Test actual interaction with quantity field"""
        self.logger.info(f"🧪 Testing quantity field interaction with value: {test_value}")
        
        # Take before screenshot
        before_screenshot = self.take_screenshot("before_quantity_test", f"Before testing quantity field with {test_value}")
        
        test_script = f"""
        () => {{
            console.log('🧪 Testing quantity field interaction...');
            
            // Find all input fields
            const inputs = Array.from(document.querySelectorAll('input'));
            let results = [];
            
            // Test each visible input field
            inputs.forEach((input, index) => {{
                const rect = input.getBoundingClientRect();
                const isVisible = rect.width > 0 && rect.height > 0;
                
                if (isVisible && input.type === 'text') {{
                    const originalValue = input.value;
                    
                    try {{
                        // Try to fill this input
                        input.focus();
                        input.value = '{test_value}';
                        
                        // Trigger events
                        input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        
                        // Check if value stuck
                        const newValue = input.value;
                        const success = newValue === '{test_value}';
                        
                        results.push({{
                            index: index,
                            originalValue: originalValue,
                            testValue: '{test_value}',
                            resultValue: newValue,
                            success: success,
                            className: input.className,
                            position: {{
                                x: Math.round(rect.x),
                                y: Math.round(rect.y)
                            }}
                        }});
                        
                        console.log(`Input ${{index}}: ${{originalValue}} -> ${{newValue}} (success: ${{success}})`);
                        
                    }} catch (error) {{
                        console.log(`Input ${{index}} failed: ${{error.message}}`);
                    }}
                }}
            }});
            
            return results;
        }}
        """
        
        try:
            test_results = self.page.evaluate(test_script)
            
            # Take after screenshot
            after_screenshot = self.take_screenshot("after_quantity_test", f"After testing quantity field with {test_value}")
            
            # Analyze results
            successful_tests = [r for r in test_results if r['success']]
            
            result = {
                "test_value": test_value,
                "total_inputs_tested": len(test_results),
                "successful_interactions": len(successful_tests),
                "results": test_results,
                "before_screenshot": before_screenshot,
                "after_screenshot": after_screenshot
            }
            
            self.logger.info(f"📊 Quantity field test results:")
            self.logger.info(f"   Inputs tested: {len(test_results)}")
            self.logger.info(f"   Successful: {len(successful_tests)}")
            
            for test in successful_tests:
                self.logger.info(f"   ✅ Input {test['index']}: '{test['originalValue']}' -> '{test['resultValue']}'")
            
            self.verification_data["interaction_tests"].append(result)
            return result
            
        except Exception as e:
            self.logger.error(f"Quantity field test failed: {e}")
            return {"error": str(e)}
    
    def test_button_interaction(self) -> Dict[str, Any]:
        """Test actual button interaction"""
        self.logger.info("🧪 Testing button interaction...")
        
        # Take before screenshot
        before_screenshot = self.take_screenshot("before_button_test", "Before testing button interaction")
        
        test_script = """
        () => {
            console.log('🧪 Testing button interaction...');
            
            // Look for the specific button we think we found
            const targetButton = document.querySelector('button.component_longBtn__eazYU');
            
            if (targetButton) {
                const rect = targetButton.getBoundingClientRect();
                const isVisible = rect.width > 0 && rect.height > 0;
                
                return {
                    found: true,
                    text: targetButton.textContent || '',
                    className: targetButton.className || '',
                    isVisible: isVisible,
                    isEnabled: !targetButton.disabled,
                    position: {
                        x: Math.round(rect.x),
                        y: Math.round(rect.y),
                        width: Math.round(rect.width),
                        height: Math.round(rect.height)
                    },
                    // Test hover effect
                    hasHoverEffect: targetButton.className.includes('hover') || 
                                   getComputedStyle(targetButton).cursor === 'pointer'
                };
            } else {
                return {
                    found: false,
                    error: 'Target button not found'
                };
            }
        }
        """
        
        try:
            button_test = self.page.evaluate(test_script)
            
            # Take after screenshot
            after_screenshot = self.take_screenshot("after_button_test", "After testing button interaction")
            
            result = {
                "button_test": button_test,
                "before_screenshot": before_screenshot,
                "after_screenshot": after_screenshot
            }
            
            if button_test.get('found'):
                self.logger.info(f"📊 Button test results:")
                self.logger.info(f"   Found: ✅")
                self.logger.info(f"   Text: '{button_test['text']}'")
                self.logger.info(f"   Visible: {button_test['isVisible']}")
                self.logger.info(f"   Enabled: {button_test['isEnabled']}")
                self.logger.info(f"   Position: {button_test['position']}")
            else:
                self.logger.error(f"❌ Button not found: {button_test.get('error')}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Button test failed: {e}")
            return {"error": str(e)}
    
    def run_comprehensive_verification(self) -> Dict[str, Any]:
        """Run complete verification workflow"""
        self.logger.info("🔬 Starting comprehensive MEXC verification")
        
        if not self.connect_to_browser():
            return {"error": "Browser connection failed"}
        
        # Step 1: Analyze current state
        self.logger.info("📋 Step 1: Analyzing current page state")
        input_analysis = self.analyze_all_inputs()
        button_analysis = self.analyze_order_buttons()
        
        # Step 2: Test quantity field interactions
        self.logger.info("📋 Step 2: Testing quantity field interactions")
        quantity_test = self.test_quantity_field_interaction("7.5")
        
        # Step 3: Test button interactions
        self.logger.info("📋 Step 3: Testing button interactions")
        button_test = self.test_button_interaction()
        
        # Step 4: Take final state screenshot
        self.take_screenshot("final_state", "Final state after all verification tests")
        
        # Compile results
        self.verification_data.update({
            "input_analysis": input_analysis,
            "button_analysis": button_analysis,
            "quantity_test": quantity_test,
            "button_test": button_test
        })
        
        # Save verification report
        self.save_verification_report()
        
        return self.verification_data
    
    def save_verification_report(self):
        """Save comprehensive verification report"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"mexc_verification_report_{timestamp}.json"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(self.verification_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"📊 Verification report saved: {report_file}")
            
            # Print summary
            print(f"""
🔬 MEXC Verification Report Summary
==================================
Report File: {report_file}
Screenshots: {len(self.verification_data['screenshots'])}
Input Fields Analyzed: {len(self.verification_data.get('input_analysis', []))}
Buttons Analyzed: {len(self.verification_data.get('button_analysis', []))}

Quantity Field Test:
- Inputs Tested: {self.verification_data.get('quantity_test', {}).get('total_inputs_tested', 0)}
- Successful: {self.verification_data.get('quantity_test', {}).get('successful_interactions', 0)}

Button Test:
- Target Button Found: {self.verification_data.get('button_test', {}).get('button_test', {}).get('found', False)}
            """)
            
        except Exception as e:
            self.logger.error(f"Failed to save verification report: {e}")
    
    def cleanup(self):
        """Clean up resources"""
        try:
            if self.playwright:
                self.playwright.stop()
        except Exception as e:
            self.logger.error(f"Cleanup error: {e}")

def main():
    """Main verification entry point"""
    print("""
🔬 MEXC Verification System
===========================

This system will:
✅ Take detailed screenshots of current browser state
✅ Analyze all input fields and their actual values  
✅ Test real interactions with quantity fields
✅ Verify button detection and responsiveness
✅ Provide visual evidence of what's actually happening

Starting comprehensive verification...
    """)
    
    verifier = MEXCVerificationSystem()
    
    try:
        results = verifier.run_comprehensive_verification()
        
        if "error" in results:
            print(f"❌ Verification failed: {results['error']}")
        else:
            print("✅ Verification completed successfully!")
            print("📊 Check the verification report and screenshots for detailed analysis.")
    
    except KeyboardInterrupt:
        print("\n👋 Verification interrupted")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        verifier.cleanup()

if __name__ == "__main__":
    main()
