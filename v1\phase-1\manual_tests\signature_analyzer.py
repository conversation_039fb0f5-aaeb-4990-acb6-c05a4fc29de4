#!/usr/bin/env python3
"""
Signature Algorithm Analyzer
Captures multiple real requests to reverse-engineer the signature algorithm
"""

import json
import time
import hashlib
import hmac
import base64
from playwright.sync_api import sync_playwright
from dotenv import dotenv_values

class SignatureAnalyzer:
    """Analyze multiple captured signatures to reverse-engineer the algorithm"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        self.captured_requests = []
        
        print("🔬 MEXC Signature Algorithm Analyzer")
        print("="*45)
        print("🎯 Goal: Reverse-engineer the exact signature algorithm")
    
    def setup_capture_system(self):
        """Setup browser with enhanced capture system"""
        
        print("\n🌐 Setting up enhanced capture system...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                print("❌ No browser contexts found")
                return False
            
            self.context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                mexc_page = self.context.new_page()
                mexc_page.goto('https://futures.mexc.com/exchange/BTC_USDT', wait_until='domcontentloaded')
            
            self.page = mexc_page
            
            # Inject enhanced monitoring
            self.page.evaluate("""
                window.signatureCaptures = [];
                window.captureCount = 0;
                
                // Enhanced fetch override
                const originalFetch = window.fetch;
                window.fetch = function(...args) {
                    const [url, options] = args;
                    
                    if (url.includes('order/create') || url.includes('order/submit') || url.includes('order/place')) {
                        window.captureCount++;
                        
                        const capture = {
                            id: window.captureCount,
                            url: url,
                            headers: options.headers || {},
                            body: options.body,
                            timestamp: Date.now(),
                            method: 'fetch'
                        };
                        
                        window.signatureCaptures.push(capture);
                        
                        console.log(`🎯 CAPTURE #${window.captureCount}:`);
                        console.log('URL:', url);
                        console.log('Signature:', options.headers['x-mxc-sign']);
                        console.log('Nonce:', options.headers['x-mxc-nonce']);
                        console.log('Body:', options.body);
                        
                        // Show alert with capture number
                        alert(`CAPTURE #${window.captureCount} COMPLETE! Signature: ${options.headers['x-mxc-sign']?.substring(0,8)}...`);
                    }
                    
                    return originalFetch.apply(this, args);
                };
                
                // Enhanced XHR override
                const originalXHR = window.XMLHttpRequest;
                window.XMLHttpRequest = function() {
                    const xhr = new originalXHR();
                    const originalSend = xhr.send;
                    const originalOpen = xhr.open;
                    const originalSetRequestHeader = xhr.setRequestHeader;
                    
                    let requestUrl = '';
                    let requestHeaders = {};
                    
                    xhr.open = function(method, url, ...args) {
                        requestUrl = url;
                        return originalOpen.apply(this, [method, url, ...args]);
                    };
                    
                    xhr.setRequestHeader = function(name, value) {
                        requestHeaders[name] = value;
                        return originalSetRequestHeader.apply(this, [name, value]);
                    };
                    
                    xhr.send = function(body) {
                        if (requestUrl.includes('order/create') || requestUrl.includes('order/submit') || requestUrl.includes('order/place')) {
                            window.captureCount++;
                            
                            const capture = {
                                id: window.captureCount,
                                url: requestUrl,
                                headers: requestHeaders,
                                body: body,
                                timestamp: Date.now(),
                                method: 'xhr'
                            };
                            
                            window.signatureCaptures.push(capture);
                            
                            console.log(`🎯 XHR CAPTURE #${window.captureCount}:`);
                            console.log('URL:', requestUrl);
                            console.log('Signature:', requestHeaders['x-mxc-sign']);
                            console.log('Nonce:', requestHeaders['x-mxc-nonce']);
                            console.log('Body:', body);
                            
                            alert(`XHR CAPTURE #${window.captureCount} COMPLETE! Signature: ${requestHeaders['x-mxc-sign']?.substring(0,8)}...`);
                        }
                        
                        return originalSend.apply(this, [body]);
                    };
                    
                    return xhr;
                };
                
                console.log('✅ Enhanced signature capture system ready!');
                console.log('📊 Captures will be stored in window.signatureCaptures');
            """)
            
            print("✅ Enhanced capture system setup completed")
            return True
            
        except Exception as e:
            print(f"❌ Setup failed: {e}")
            return False
    
    def guide_multiple_captures(self):
        """Guide user to capture multiple signatures"""
        
        print("\n" + "="*60)
        print("MULTIPLE SIGNATURE CAPTURE SESSION")
        print("="*60)
        print()
        print("🎯 INSTRUCTIONS:")
        print("1. We need to capture 3-5 different order requests")
        print("2. Use different symbols, prices, and quantities")
        print("3. Each capture will show an alert with the capture number")
        print("4. Try these combinations:")
        print("   - BTC_USDT, price $1000, qty 1")
        print("   - ETH_USDT, price $100, qty 2") 
        print("   - TRU_USDT, price $0.01, qty 5")
        print("   - Different sides (Buy/Sell)")
        print()
        print("⚠️  SAFETY: Use very low prices to avoid accidental fills!")
        print()
        print("🔍 Monitoring for multiple captures...")
        
        target_captures = 3
        timeout = 600  # 10 minutes
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # Check captures
                captures = self.page.evaluate("() => window.signatureCaptures || []")
                capture_count = len(captures)
                
                if capture_count > 0:
                    print(f"\n📊 Captured {capture_count} signature(s) so far...")
                    
                    if capture_count >= target_captures:
                        print(f"\n🎉 SUCCESS! Captured {capture_count} signatures!")
                        self.captured_requests = captures
                        return True
                
                # Show progress
                elapsed = int(time.time() - start_time)
                if elapsed % 30 == 0 and elapsed > 0:  # Every 30 seconds
                    print(f"⏱️  Waiting for captures... ({elapsed}s elapsed, need {target_captures-capture_count} more)")
                
                time.sleep(2)
                
            except Exception as e:
                print(f"⚠️  Monitoring error: {e}")
                time.sleep(2)
        
        print(f"\n⏰ Session ended. Captured {len(self.captured_requests)} signatures.")
        return len(self.captured_requests) > 0
    
    def analyze_signature_patterns(self):
        """Analyze captured signatures to find the algorithm"""
        
        if not self.captured_requests:
            print("❌ No signatures to analyze")
            return False
        
        print(f"\n🔍 ANALYZING {len(self.captured_requests)} CAPTURED SIGNATURES")
        print("="*55)
        
        for i, request in enumerate(self.captured_requests):
            print(f"\n📋 CAPTURE #{i+1}:")
            print(f"URL: {request['url']}")
            
            headers = request.get('headers', {})
            signature = headers.get('x-mxc-sign')
            nonce = headers.get('x-mxc-nonce')
            auth = headers.get('Authorization') or headers.get('authorization')
            
            print(f"🔐 Signature: {signature}")
            print(f"🔢 Nonce: {nonce}")
            print(f"🎫 Auth: {auth[:20] if auth else 'None'}...")
            
            body = request.get('body', '{}')
            print(f"📦 Body: {body[:100]}...")
            
            # Try to reverse engineer this specific signature
            if signature and nonce and auth and body:
                self.test_signature_algorithms(signature, nonce, auth, body, i+1)
        
        return True
    
    def test_signature_algorithms(self, target_signature, nonce, auth, body, capture_num):
        """Test advanced algorithms against a specific signature"""

        print(f"\n🧪 Testing advanced algorithms for capture #{capture_num}...")

        try:
            # Parse body
            if isinstance(body, str):
                body_data = json.loads(body)
            else:
                body_data = body

            # Extract key components
            p0 = body_data.get('p0', '')
            k0 = body_data.get('k0', '')
            chash = body_data.get('chash', '')
            mtoken = body_data.get('mtoken', '')
            ts = str(body_data.get('ts', ''))
            mhash = body_data.get('mhash', '')

            print(f"   🔍 Key components:")
            print(f"      p0: {p0[:20] if p0 else 'None'}...")
            print(f"      k0: {k0[:20] if k0 else 'None'}...")
            print(f"      chash: {chash[:20] if chash else 'None'}...")
            print(f"      mtoken: {mtoken}")
            print(f"      ts: {ts}")
            print(f"      mhash: {mhash}")

            # Try different JSON serializations
            json_variants = [
                json.dumps(body_data, separators=(',', ':')),
                json.dumps(body_data, separators=(',', ':'), sort_keys=True),
                json.dumps(body_data),
                json.dumps(body_data, sort_keys=True),
                # Without p0/k0 (they might be added after signature)
                json.dumps({k: v for k, v in body_data.items() if k not in ['p0', 'k0']}, separators=(',', ':')),
                json.dumps({k: v for k, v in body_data.items() if k not in ['p0', 'k0', 'chash', 'mhash']}, separators=(',', ':'))
            ]

            algorithms_tested = 0

            # Test with individual components
            component_combinations = [
                # Basic combinations
                f"{auth}{nonce}",
                f"{nonce}{auth}",
                f"{auth}{nonce}{mtoken}",
                f"{nonce}{mtoken}{auth}",
                f"{auth}{nonce}{ts}",
                f"{nonce}{ts}{auth}",
                f"{auth}{nonce}{mhash}",
                f"{nonce}{mhash}{auth}",
                # With chash
                f"{auth}{nonce}{chash}",
                f"{nonce}{chash}{auth}",
                f"{chash}{nonce}{auth}",
                # Complex combinations
                f"{auth}{nonce}{mtoken}{ts}",
                f"{nonce}{mtoken}{ts}{auth}",
                f"{auth}{nonce}{mtoken}{mhash}",
                f"{nonce}{mtoken}{mhash}{auth}",
                # With separators
                f"{auth}|{nonce}|{mtoken}",
                f"{nonce}|{mtoken}|{auth}",
                f"{auth}&{nonce}&{mtoken}",
                f"{nonce}&{mtoken}&{auth}",
            ]

            for json_str in json_variants:
                # Add JSON-based combinations
                json_combinations = [
                    f"{auth}{nonce}{json_str}",
                    f"{nonce}{json_str}{auth}",
                    f"{json_str}{nonce}{auth}",
                    f"{auth}{json_str}{nonce}",
                    f"{nonce}{auth}{json_str}",
                    f"{json_str}{auth}{nonce}",
                ]

                all_combinations = component_combinations + json_combinations

                for content in all_combinations:
                    algorithms_tested += 1

                    # Test multiple hash algorithms
                    hash_results = [
                        ('MD5', hashlib.md5(content.encode()).hexdigest()),
                        ('SHA1', hashlib.sha1(content.encode()).hexdigest()[:32]),
                        ('SHA256', hashlib.sha256(content.encode()).hexdigest()[:32]),
                        ('SHA512', hashlib.sha512(content.encode()).hexdigest()[:32]),
                    ]

                    for hash_name, test_sig in hash_results:
                        if test_sig == target_signature:
                            print(f"🎉 ALGORITHM FOUND! (Capture #{capture_num})")
                            print(f"   Method: {hash_name}")
                            print(f"   Content: {content[:100]}...")
                            print(f"   Full content: {content}")
                            return True

                    # Test HMAC variants
                    hmac_keys = [auth, nonce, mtoken, chash]
                    hmac_messages = [content, nonce, json_str]

                    for key in hmac_keys:
                        if not key:
                            continue
                        for message in hmac_messages:
                            if not message:
                                continue
                            try:
                                for hash_func in [hashlib.md5, hashlib.sha1, hashlib.sha256]:
                                    test_sig = hmac.new(key.encode(), message.encode(), hash_func).hexdigest()[:32]
                                    if test_sig == target_signature:
                                        print(f"🎉 HMAC ALGORITHM FOUND! (Capture #{capture_num})")
                                        print(f"   Method: HMAC-{hash_func().name.upper()}")
                                        print(f"   Key: {key[:20]}...")
                                        print(f"   Message: {message[:100]}...")
                                        return True
                            except:
                                continue

            print(f"❌ No match found for capture #{capture_num} ({algorithms_tested} algorithms tested)")

            # Show some test results for debugging
            print(f"   🔍 Sample test results:")
            sample_content = f"{auth}{nonce}"
            print(f"      MD5({sample_content[:30]}...): {hashlib.md5(sample_content.encode()).hexdigest()}")
            print(f"      Target: {target_signature}")

            return False

        except Exception as e:
            print(f"❌ Analysis error for capture #{capture_num}: {e}")
            return False
    
    def describe_pattern(self, content, auth, nonce, json_str):
        """Describe the pattern used for signature generation"""
        if content == f"{auth}{nonce}{json_str}":
            return "auth + nonce + json"
        elif content == f"{nonce}{json_str}{auth}":
            return "nonce + json + auth"
        elif content == f"{json_str}{nonce}{auth}":
            return "json + nonce + auth"
        elif content == f"{auth}{json_str}{nonce}":
            return "auth + json + nonce"
        elif content == f"{nonce}{auth}{json_str}":
            return "nonce + auth + json"
        elif content == f"{json_str}{auth}{nonce}":
            return "json + auth + nonce"
        elif content == f"{auth}{nonce}":
            return "auth + nonce (no json)"
        elif content == f"{nonce}{auth}":
            return "nonce + auth (no json)"
        else:
            return "custom pattern"
    
    def run_analysis(self):
        """Run the complete signature analysis"""
        
        print("="*60)
        print("SIGNATURE ALGORITHM ANALYSIS")
        print("="*60)
        
        # Setup capture system
        if not self.setup_capture_system():
            return False
        
        try:
            # Capture multiple signatures
            if not self.guide_multiple_captures():
                print("❌ Failed to capture enough signatures")
                return False
            
            # Analyze patterns
            if not self.analyze_signature_patterns():
                print("❌ Failed to analyze signatures")
                return False
            
            print("\n🎉 SIGNATURE ANALYSIS COMPLETE!")
            return True
            
        finally:
            # Cleanup
            if hasattr(self, 'browser'):
                self.browser.close()
            if hasattr(self, 'playwright'):
                self.playwright.stop()

def main():
    """Main function"""
    
    analyzer = SignatureAnalyzer()
    success = analyzer.run_analysis()
    
    if success:
        print("\n🚀 ANALYSIS SUCCESSFUL!")
        print("Signature algorithm should now be identified!")
    else:
        print("\n🔧 Analysis incomplete - try capturing more signatures")

if __name__ == '__main__':
    main()
