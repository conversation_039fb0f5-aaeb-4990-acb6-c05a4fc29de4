const axios = require('axios');
const { spawn } = require('child_process');
const path = require('path');

class TradingExecutor {
    constructor(configManager, telegramBot = null) {
        this.configManager = configManager;
        this.telegramBot = telegramBot;
        this.mexcTraderServiceUrl = 'http://localhost:3000'; // MEXC Futures Trader service (legacy)
        this.ready = true; // Always ready for browser automation
        this.lastHealthCheck = null;
        this.cachedBalance = null;
        this.lastBalanceUpdate = null;
        this.balanceUpdateInterval = 5 * 60 * 1000; // 5 minutes
        this.isExecutingTrade = false; // Flag to prevent balance checks during trades
        this.maxRetryAttempts = 3; // Maximum retry attempts for trade execution
        this.useBrowserAutomation = true; // Use browser automation instead of service

        // Persistent background monitoring
        this.persistentTrader = null;
        this.monitoringInitialized = false;

        // Emergency stop mechanism
        this.consecutiveFailures = 0;
        this.maxConsecutiveFailures = 10; // Emergency stop after 10 consecutive failures
        this.emergencyStopActive = false;
        this.emergencyStopTime = null;
        this.emergencyStopResetTime = 10 * 60 * 1000; // 10 minutes
    }

    async checkServiceHealth() {
        try {
            // For browser automation mode, always return healthy
            if (this.useBrowserAutomation) {
                this.ready = true;
                this.lastHealthCheck = new Date().toISOString();

                return {
                    healthy: true,
                    service: 'Browser Automation',
                    timestamp: new Date().toISOString(),
                    mode: 'browser-automation',
                    circuitBreaker: { state: 'CLOSED' }
                };
            }

            // Legacy service health check (kept for compatibility)
            const response = await axios.get(`${this.mexcTraderServiceUrl}/health`, {
                timeout: 5000,
                headers: {
                    'User-Agent': 'TradingView-Webhook-Listener/1.0.0'
                }
            });

            // Check if service is healthy and circuit breaker is not open
            const isHealthy = response.data.status === 'healthy';
            const circuitBreakerOpen = response.data.circuitBreaker && response.data.circuitBreaker.state === 'OPEN';

            if (circuitBreakerOpen) {
                this.ready = false;
                this.lastHealthCheck = new Date().toISOString();
                throw new Error('MEXC Trader service circuit breaker is OPEN - service temporarily unavailable');
            }

            this.ready = isHealthy;
            this.lastHealthCheck = new Date().toISOString();

            return {
                healthy: this.ready,
                service: response.data.service,
                timestamp: response.data.timestamp,
                circuitBreaker: response.data.circuitBreaker
            };
        } catch (error) {
            // If using browser automation, don't fail on service connection errors
            if (this.useBrowserAutomation) {
                this.ready = true;
                this.lastHealthCheck = new Date().toISOString();

                return {
                    healthy: true,
                    service: 'Browser Automation (Service Unavailable)',
                    timestamp: new Date().toISOString(),
                    mode: 'browser-automation',
                    warning: 'Legacy service unavailable, using browser automation'
                };
            }

            this.ready = false;
            this.lastHealthCheck = new Date().toISOString();

            throw new Error(`MEXC Trader Service not available: ${error.message}`);
        }
    }

    async getBalance(forceRefresh = false) {
        try {
            // Use real balance from browser automation or API
            // Don't fetch balance during trade execution unless forced
            if (this.isExecutingTrade && !forceRefresh) {
                console.log('⚡ Skipping balance check during trade execution');
                if (this.cachedBalance) {
                    return this.cachedBalance;
                }
            }

            // Use cached balance if recent (within 5 minutes) and not forced
            if (!forceRefresh && this.cachedBalance && this.lastBalanceUpdate) {
                const age = Date.now() - this.lastBalanceUpdate;
                if (age < this.balanceUpdateInterval) {
                    console.log(`💰 Using cached balance: ${this.cachedBalance.balance} USDT (${Math.round(age/1000)}s old)`);
                    return this.cachedBalance;
                }
            }

            // Use persistent trader for balance if available (prioritize background monitoring)
            if (this.persistentTrader && this.monitoringInitialized) {
                // First try to get balance from background monitoring (most reliable)
                const monitoringStatus = this.getMonitoringStatus();
                if (monitoringStatus.lastBalance && monitoringStatus.balanceUpdateTime) {
                    const balanceData = {
                        success: true,
                        balance: monitoringStatus.lastBalance,
                        currency: 'USDT',
                        raw: `${monitoringStatus.lastBalance} USDT`,
                        timestamp: monitoringStatus.balanceUpdateTime,
                        cached: true,
                        source: 'background-monitoring'
                    };

                    // Cache the balance
                    this.cachedBalance = balanceData;
                    this.lastBalanceUpdate = Date.now();
                    console.log(`💰 Balance from background monitoring: ${balanceData.balance} ${balanceData.currency}`);
                    return balanceData;
                }

                // Fallback: Try to fetch fresh balance from browser
                console.log('🔄 Fetching fresh balance from browser...');
                const browserBalance = await this.persistentTrader.updateBalance();
                if (browserBalance) {
                    const balanceData = {
                        success: true,
                        balance: parseFloat(browserBalance.replace(/[^\d.]/g, '')) || 0,
                        currency: 'USDT',
                        raw: browserBalance,
                        timestamp: new Date().toISOString(),
                        cached: false,
                        source: 'browser-fresh'
                    };

                    // Cache the balance if successful
                    if (balanceData.success && balanceData.balance > 0) {
                        this.cachedBalance = balanceData;
                        this.lastBalanceUpdate = Date.now();
                        console.log(`💰 Fresh balance from browser: ${balanceData.balance} ${balanceData.currency}`);
                    }

                    return balanceData;
                }
            }

            // Fallback: No persistent trader available
            throw new Error('No balance source available - persistent trader not initialized');
        } catch (error) {
            console.error('Failed to get balance:', error.message);

            // Return error response instead of mock balance
            return {
                success: false,
                balance: 0,
                currency: 'USDT',
                error: error.message,
                timestamp: new Date().toISOString(),
                source: 'error'
            };
        }
    }

    isReady() {
        return this.ready && !this.emergencyStopActive;
    }

    getStatus() {
        return {
            ready: this.ready,
            emergencyStopActive: this.emergencyStopActive,
            consecutiveFailures: this.consecutiveFailures,
            maxConsecutiveFailures: this.maxConsecutiveFailures,
            emergencyStopTime: this.emergencyStopTime,
            emergencyStopResetTime: this.emergencyStopResetTime,
            lastHealthCheck: this.lastHealthCheck
        };
    }

    async executeTrade(tradeRequest) {
        return await this.executeTradeWithRetry(tradeRequest, this.maxRetryAttempts);
    }

    async executeTradeWithRetry(tradeRequest, maxAttempts = 3) {
        const startTime = Date.now();
        let lastError = null;

        // Check emergency stop
        if (this.emergencyStopActive) {
            const timeSinceStop = Date.now() - this.emergencyStopTime;
            if (timeSinceStop < this.emergencyStopResetTime) {
                throw new Error(`Emergency stop active - trading suspended for ${Math.ceil((this.emergencyStopResetTime - timeSinceStop) / 60000)} more minutes`);
            } else {
                // Reset emergency stop
                this.emergencyStopActive = false;
                this.consecutiveFailures = 0;
                this.emergencyStopTime = null;
                console.log('🔄 Emergency stop reset - trading resumed');
            }
        }

        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                // Set trade execution flag to prevent balance checks during trade
                this.isExecutingTrade = true;
                console.log(`🎯 Starting trade execution (attempt ${attempt}/${maxAttempts}): ${tradeRequest.orderType}`);

                // Validate trade request
                this.validateTradeRequest(tradeRequest);

                // Check if service is ready (for browser automation, always ready)
                if (!this.ready) {
                    await this.checkServiceHealth();
                }

                // Prepare the trade payload for browser automation
                const tradePayload = {
                    orderType: tradeRequest.orderType,
                    quantity: tradeRequest.quantity
                };

                console.log(`Executing trade: ${tradeRequest.orderType} with quantity ${tradeRequest.quantity}`);

                // Execute trade via browser automation (FAST & RELIABLE)
                console.log('🚀 Using browser automation for trade execution');
                const response = await this.executeTradeBrowserAutomation(tradePayload);

                const executionTime = Date.now() - startTime;

                // Process the response
                const result = {
                    success: response.data.success,
                    executionTime: response.data.executionTime || executionTime,
                    totalServiceTime: executionTime,
                    verified: response.data.verified,
                    targetAchieved: response.data.targetAchieved,
                    orderType: response.data.orderType,
                    quantity: response.data.quantity,
                    port: response.data.port,
                    requestId: response.data.requestId,
                    timestamp: response.data.timestamp,
                    tradeRequest,
                    serviceResponse: response.data,
                    attempt,
                    maxAttempts
                };

                if (!response.data.success) {
                    result.error = response.data.error || 'Trade execution failed';

                    // If this is not the last attempt, continue to retry
                    if (attempt < maxAttempts) {
                        console.log(`❌ Trade failed on attempt ${attempt}, retrying...`);
                        lastError = result.error;
                        await new Promise(resolve => setTimeout(resolve, 1000 * attempt)); // Exponential backoff
                        continue;
                    }
                }

                // Track success/failure for emergency stop
                if (result.success) {
                    this.consecutiveFailures = 0; // Reset on success
                } else {
                    this.consecutiveFailures++;

                    // Check if emergency stop should be activated
                    if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
                        this.emergencyStopActive = true;
                        this.emergencyStopTime = Date.now();
                        console.log(`🚨 EMERGENCY STOP ACTIVATED after ${this.consecutiveFailures} consecutive failures`);

                        // Send emergency alert
                        if (this.telegramBot && this.telegramBot.isReady()) {
                            await this.telegramBot.sendSystemAlert(
                                'EMERGENCY STOP ACTIVATED',
                                `Trading suspended after ${this.consecutiveFailures} consecutive failures. System will auto-resume in ${this.emergencyStopResetTime / 60000} minutes.`,
                                'CRITICAL'
                            );
                        }
                    }
                }

                // Success or final attempt - send Telegram notification if configured
                if (this.telegramBot && this.telegramBot.isReady()) {
                    await this.telegramBot.sendTradeAlert(
                        tradeRequest.orderType,
                        tradeRequest.symbol || 'TRUUSDT',
                        tradeRequest.quantity,
                        result.success,
                        result.error,
                        result.executionTime
                    );
                }

                return result;

            } catch (error) {
                const executionTime = Date.now() - startTime;

                let errorMessage = 'Trade execution failed';
                let errorDetails = {};

                if (error.response) {
                    // HTTP error response from MEXC Trader service
                    errorMessage = error.response.data?.error || error.response.statusText;
                    errorDetails = {
                        status: error.response.status,
                        statusText: error.response.statusText,
                        data: error.response.data
                    };
                } else if (error.request) {
                    // Network error
                    errorMessage = 'Unable to connect to MEXC Trader service';
                    errorDetails = {
                        code: error.code,
                        message: error.message
                    };
                } else {
                    // Other error
                    errorMessage = error.message;
                }

                lastError = errorMessage;

                // If this is not the last attempt, continue to retry
                if (attempt < maxAttempts) {
                    console.log(`❌ Trade failed on attempt ${attempt} with error: ${errorMessage}, retrying...`);
                    await new Promise(resolve => setTimeout(resolve, 1000 * attempt)); // Exponential backoff
                    continue;
                }

                // Track failure for emergency stop
                this.consecutiveFailures++;

                // Check if emergency stop should be activated
                if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
                    this.emergencyStopActive = true;
                    this.emergencyStopTime = Date.now();
                    console.log(`🚨 EMERGENCY STOP ACTIVATED after ${this.consecutiveFailures} consecutive failures`);

                    // Send emergency alert
                    if (this.telegramBot && this.telegramBot.isReady()) {
                        await this.telegramBot.sendSystemAlert(
                            'EMERGENCY STOP ACTIVATED',
                            `Trading suspended after ${this.consecutiveFailures} consecutive failures. System will auto-resume in ${this.emergencyStopResetTime / 60000} minutes.`,
                            'CRITICAL'
                        );
                    }
                }

                // Final attempt failed - send critical Telegram alert
                if (this.telegramBot && this.telegramBot.isReady()) {
                    await this.telegramBot.sendRetryLimitAlert(
                        tradeRequest.orderType,
                        tradeRequest.symbol || 'TRUUSDT',
                        maxAttempts,
                        errorMessage
                    );
                }

                return {
                    success: false,
                    error: errorMessage,
                    errorDetails,
                    executionTime,
                    tradeRequest,
                    timestamp: new Date().toISOString(),
                    attempt,
                    maxAttempts,
                    retryLimitExceeded: true
                };
            } finally {
                // Always clear trade execution flag
                this.isExecutingTrade = false;
                console.log('🏁 Trade execution completed, balance checks re-enabled');
            }
        }

        // This should never be reached, but just in case
        return {
            success: false,
            error: lastError || 'Unknown error after all retry attempts',
            executionTime: Date.now() - startTime,
            tradeRequest,
            timestamp: new Date().toISOString(),
            attempt: maxAttempts,
            maxAttempts,
            retryLimitExceeded: true
        };
    }

    validateTradeRequest(tradeRequest) {
        if (!tradeRequest || typeof tradeRequest !== 'object') {
            throw new Error('Invalid trade request: must be an object');
        }
        
        if (!tradeRequest.orderType) {
            throw new Error('Invalid trade request: orderType is required');
        }
        
        const validOrderTypes = ['Open Long', 'Open Short', 'Close Long', 'Close Short'];
        if (!validOrderTypes.includes(tradeRequest.orderType)) {
            throw new Error(`Invalid order type: ${tradeRequest.orderType}. Must be one of: ${validOrderTypes.join(', ')}`);
        }
        
        if (!tradeRequest.quantity) {
            throw new Error('Invalid trade request: quantity is required');
        }
        
        const quantity = parseFloat(tradeRequest.quantity);
        if (isNaN(quantity) || quantity <= 0) {
            throw new Error(`Invalid quantity: ${tradeRequest.quantity}. Must be a positive number`);
        }
        
        // Additional validation for symbol (if provided)
        if (tradeRequest.symbol && tradeRequest.symbol !== 'TRUUSDT') {
            throw new Error(`Unsupported symbol: ${tradeRequest.symbol}. Only TRUUSDT is supported`);
        }
    }

    async executeBatchTrades(tradeRequests) {
        if (!Array.isArray(tradeRequests) || tradeRequests.length === 0) {
            throw new Error('Invalid batch request: must be a non-empty array');
        }
        
        if (tradeRequests.length > 10) {
            throw new Error('Batch size too large: maximum 10 trades per batch');
        }
        
        const startTime = Date.now();
        const results = [];
        
        try {
            // Check service health before batch execution
            await this.checkServiceHealth();
            
            // Prepare batch payload
            const batchPayload = {
                orders: tradeRequests.map(req => ({
                    orderType: req.orderType,
                    quantity: req.quantity
                }))
            };
            
            console.log(`Executing batch of ${tradeRequests.length} trades`);
            
            // Execute batch via MEXC Futures Trader service
            const response = await axios.post(`${this.mexcTraderServiceUrl}/trade/batch`, batchPayload, {
                timeout: 60000, // 60 seconds for batch execution
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'TradingView-Webhook-Listener/1.0.0'
                }
            });
            
            const executionTime = Date.now() - startTime;
            
            return {
                success: response.data.success,
                totalOrders: response.data.totalOrders,
                successCount: response.data.successCount,
                failureCount: response.data.failureCount,
                results: response.data.results,
                executionTime,
                timestamp: response.data.timestamp
            };
            
        } catch (error) {
            const executionTime = Date.now() - startTime;
            
            return {
                success: false,
                error: error.response?.data?.error || error.message,
                executionTime,
                timestamp: new Date().toISOString()
            };
        }
    }

    async getServiceInfo() {
        try {
            const response = await axios.get(`${this.mexcTraderServiceUrl}/info`, {
                timeout: 5000
            });
            
            return response.data;
        } catch (error) {
            throw new Error(`Failed to get service info: ${error.message}`);
        }
    }

    async testConnection() {
        try {
            const healthCheck = await this.checkServiceHealth();
            const serviceInfo = await this.getServiceInfo();
            
            return {
                success: true,
                message: 'Successfully connected to MEXC Futures Trader service',
                health: healthCheck,
                serviceInfo,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    // Get trading statistics from the service
    async getTradingStatistics() {
        try {
            // This would require additional endpoints in the MEXC Trader service
            // For now, return basic info
            return {
                serviceUrl: this.mexcTraderServiceUrl,
                ready: this.ready,
                lastHealthCheck: this.lastHealthCheck,
                supportedOrderTypes: ['Open Long', 'Open Short', 'Close Long', 'Close Short'],
                supportedSymbols: ['TRUUSDT']
            };
        } catch (error) {
            throw new Error(`Failed to get trading statistics: ${error.message}`);
        }
    }

    // Initialize persistent background monitoring
    async initializePersistentMonitoring() {
        if (this.monitoringInitialized) {
            console.log('⚠️ Background monitoring already initialized');
            return true;
        }

        try {
            console.log('🔄 Initializing persistent background monitoring...');

            // Import the OptimizedMexcTrader class
            const OptimizedMexcTrader = require('../../mexc-api-testing/optimized-mexc-trader.js');

            // Create persistent trader instance
            this.persistentTrader = new OptimizedMexcTrader(9223);

            // Connect to browser
            const connected = await this.persistentTrader.connectToBrowser();
            if (!connected) {
                throw new Error('Failed to connect to browser on port 9223');
            }

            // Start background monitoring
            await this.persistentTrader.startBackgroundMonitoring();

            this.monitoringInitialized = true;
            console.log('✅ Persistent background monitoring initialized successfully');
            console.log('🧹 Panel cleanup will run every 10 seconds');
            console.log('🔄 Post-trade cleanup will run after close trades');

            return true;

        } catch (error) {
            console.error('❌ Failed to initialize persistent monitoring:', error.message);
            this.monitoringInitialized = false;
            return false;
        }
    }

    // Get monitoring status
    getMonitoringStatus() {
        return {
            initialized: this.monitoringInitialized,
            active: this.persistentTrader ? this.persistentTrader.monitoringActive : false,
            isExecutingTrade: this.persistentTrader ? this.persistentTrader.isExecutingTrade : false,
            lastBalance: this.persistentTrader ? this.persistentTrader.lastBalance : null,
            balanceUpdateTime: this.persistentTrader ? this.persistentTrader.balanceUpdateTime : null
        };
    }

    // Update service URL (for testing or different environments)
    updateServiceUrl(newUrl) {
        this.mexcTraderServiceUrl = newUrl;
        this.ready = false; // Reset ready status
    }

    // Get current service status
    getStatus() {
        return {
            serviceUrl: this.mexcTraderServiceUrl,
            ready: this.ready,
            lastHealthCheck: this.lastHealthCheck,
            monitoringStatus: this.getMonitoringStatus(),
            timestamp: new Date().toISOString()
        };
    }

    async executeTradeBrowserAutomation(tradePayload) {
        const startTime = Date.now();

        try {
            // Map the trade request to our order types
            const orderType = this.mapOrderType(tradePayload);
            const port = this.selectPort(orderType);

            console.log(`🎯 Browser automation: ${orderType} on port ${port}`);

            // Use persistent trader if available, otherwise fall back to spawned process
            let result;
            if (this.persistentTrader && this.monitoringInitialized) {
                console.log('🚀 Using persistent trader with background monitoring');
                result = await this.persistentTrader.executeOrder(orderType, tradePayload.quantity);
            } else {
                console.log('⚠️ Persistent trader not available, using spawned process');
                result = await this.executeWithWorkingTrader(orderType, port, tradePayload.quantity);
            }

            const executionTime = Date.now() - startTime;

            // Format response to match expected structure
            return {
                data: {
                    success: result.success,
                    executionTime: result.executionTime,
                    totalTime: executionTime,
                    orderType: orderType,
                    quantity: tradePayload.quantity,
                    port: port,
                    targetAchieved: result.executionTime < 2000,
                    verified: result.verified || result.success,
                    message: result.success ? 'Trade executed successfully via browser automation' : (result.error || 'Trade execution failed'),
                    timestamp: new Date().toISOString(),
                    monitoringActive: this.persistentTrader ? this.persistentTrader.monitoringActive : false
                }
            };

        } catch (error) {
            const executionTime = Date.now() - startTime;

            console.error('Browser automation error:', error.message);

            return {
                data: {
                    success: false,
                    error: error.message,
                    executionTime: executionTime,
                    message: 'Browser automation failed',
                    timestamp: new Date().toISOString()
                }
            };
        }
    }

    mapOrderType(tradePayload) {
        const orderType = tradePayload.orderType || tradePayload.action || tradePayload.trade;

        if (!orderType) {
            throw new Error('No order type specified in trade payload');
        }

        const normalizedOrderType = orderType.toLowerCase();

        // Map various formats to our standard order types
        switch (normalizedOrderType) {
            case 'buy':
            case 'long':
            case 'open long':
            case 'openlong':
                return 'Open Long';

            case 'sell':
            case 'short':
            case 'open short':
            case 'openshort':
                return 'Open Short';

            case 'close long':
            case 'closelong':
            case 'close_long':
                return 'Close Long';

            case 'close short':
            case 'closeshort':
            case 'close_short':
                return 'Close Short';

            default:
                throw new Error(`Unknown order type: ${orderType}`);
        }
    }

    selectPort(orderType) {
        // Use port 9223 since that's where your browser is running
        // Both Open and Close trades will use the same browser
        return 9223;
    }

    async executeWithWorkingTrader(orderType, port, quantity = '0.3600') {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            // Use our optimized trader from mexc-api-testing directory
            const traderPath = path.join(__dirname, '../../mexc-api-testing/optimized-mexc-trader.js');
            const args = [traderPath, orderType, port.toString()];

            console.log(`🔧 Spawning: node ${args.join(' ')}`);

            const child = spawn('node', args, {
                cwd: path.join(__dirname, '../../mexc-api-testing'),
                stdio: ['pipe', 'pipe', 'pipe']
            });

            let stdout = '';
            let stderr = '';

            child.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            child.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            child.on('close', (code) => {
                const executionTime = Date.now() - startTime;

                const result = {
                    success: code === 0,
                    executionTime: executionTime,
                    exitCode: code,
                    stdout: stdout,
                    stderr: stderr,
                    verified: code === 0
                };

                if (code === 0) {
                    console.log(`✅ Browser automation completed in ${executionTime}ms`);
                } else {
                    console.log(`❌ Browser automation failed with exit code ${code}`);
                    result.error = `Process exited with code ${code}`;
                }

                resolve(result);
            });

            child.on('error', (error) => {
                const executionTime = Date.now() - startTime;

                console.error('Browser automation process error:', error.message);

                resolve({
                    success: false,
                    executionTime: executionTime,
                    error: error.message,
                    verified: false
                });
            });

            // Timeout after 8 seconds (should be plenty for sub-2 second execution)
            setTimeout(() => {
                if (!child.killed) {
                    child.kill();
                    const executionTime = Date.now() - startTime;

                    console.log(`⏰ Browser automation timeout after ${executionTime}ms`);

                    resolve({
                        success: false,
                        executionTime: executionTime,
                        error: 'Browser automation timeout (8s)',
                        verified: false
                    });
                }
            }, 8000);
        });
    }
}

module.exports = TradingExecutor;
