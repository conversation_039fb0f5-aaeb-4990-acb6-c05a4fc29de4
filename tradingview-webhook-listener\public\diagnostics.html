<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic Admin Panel - TradingView Webhook Listener</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            margin: 20px 0;
        }

        .nav-links a {
            color: #ffffff;
            text-decoration: none;
            padding: 10px 20px;
            margin: 0 10px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        .nav-links a:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .diagnostic-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .card h3 {
            font-size: 1.4rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-icon {
            font-size: 1.2rem;
            color: #ffd700;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-label {
            font-weight: 500;
        }

        .status-value {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }

        .status-connected { background-color: #4CAF50; }
        .status-error { background-color: #f44336; }
        .status-warning { background-color: #ff9800; }
        .status-unknown { background-color: #9e9e9e; }
        .status-disabled { background-color: #607d8b; }

        .refresh-btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }

        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .test-btn {
            background: linear-gradient(45deg, #2196F3, #1976D2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .test-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(33, 150, 243, 0.4);
        }

        .alert {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: 500;
        }

        .alert-success {
            background-color: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #4CAF50;
        }

        .alert-error {
            background-color: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            color: #f44336;
        }

        .alert-warning {
            background-color: rgba(255, 152, 0, 0.2);
            border: 1px solid #ff9800;
            color: #ff9800;
        }

        .config-issues {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid #f44336;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }

        .config-issues h4 {
            color: #f44336;
            margin-bottom: 10px;
        }

        .config-issues ul {
            list-style: none;
            padding-left: 0;
        }

        .config-issues li {
            padding: 5px 0;
            color: #ffcdd2;
        }

        .config-issues li:before {
            content: "⚠️ ";
            margin-right: 8px;
        }

        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 20px 0;
            justify-content: center;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #4CAF50;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .timestamp {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.7);
            text-align: center;
            margin-top: 20px;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.7);
        }

        .response-time {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.6);
        }

        .error-details {
            font-size: 0.8rem;
            color: #ffcdd2;
            margin-top: 5px;
            word-break: break-word;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-stethoscope"></i> Diagnostic Admin Panel</h1>
            <p>Real-time system monitoring and troubleshooting for TradingView Webhook Listener</p>
            <div class="nav-links">
                <a href="/"><i class="fas fa-home"></i> Main Dashboard</a>
                <a href="/diagnostics"><i class="fas fa-chart-line"></i> Diagnostics</a>
            </div>
            <div class="auto-refresh">
                <span>Auto Refresh:</span>
                <label class="toggle-switch">
                    <input type="checkbox" id="autoRefreshToggle" checked>
                    <span class="slider"></span>
                </label>
                <button class="refresh-btn" onclick="refreshAllData()">
                    <i class="fas fa-sync-alt"></i> Refresh All
                </button>
            </div>
        </div>

        <div class="diagnostic-grid">
            <!-- Configuration Status Card -->
            <div class="card">
                <h3><i class="fas fa-cog card-icon"></i> Configuration Status</h3>
                <div id="configStatus" class="loading">Loading configuration status...</div>
            </div>

            <!-- Service Health Card -->
            <div class="card">
                <h3><i class="fas fa-heartbeat card-icon"></i> Service Health</h3>
                <div id="serviceHealth" class="loading">Loading service health...</div>
            </div>

            <!-- Data Sources Card -->
            <div class="card">
                <h3><i class="fas fa-database card-icon"></i> Data Sources</h3>
                <div id="dataSources" class="loading">Loading data sources...</div>
            </div>

            <!-- System Information Card -->
            <div class="card">
                <h3><i class="fas fa-server card-icon"></i> System Information</h3>
                <div id="systemInfo" class="loading">Loading system information...</div>
            </div>
        </div>

        <div class="timestamp" id="lastUpdate">Last updated: Never</div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let isRefreshing = false;

        // Initialize the diagnostic panel
        document.addEventListener('DOMContentLoaded', function() {
            refreshAllData();
            setupAutoRefresh();
        });

        function setupAutoRefresh() {
            const toggle = document.getElementById('autoRefreshToggle');
            
            if (toggle.checked) {
                startAutoRefresh();
            }

            toggle.addEventListener('change', function() {
                if (this.checked) {
                    startAutoRefresh();
                } else {
                    stopAutoRefresh();
                }
            });
        }

        function startAutoRefresh() {
            if (autoRefreshInterval) return;
            
            autoRefreshInterval = setInterval(() => {
                if (!isRefreshing) {
                    refreshAllData();
                }
            }, 10000); // Refresh every 10 seconds
        }

        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
        }

        async function refreshAllData() {
            if (isRefreshing) return;
            isRefreshing = true;

            try {
                await Promise.all([
                    loadConfigStatus(),
                    loadServiceHealth(),
                    loadDataSources(),
                    loadSystemInfo()
                ]);

                document.getElementById('lastUpdate').textContent =
                    `Last updated: ${new Date().toLocaleString()}`;
            } catch (error) {
                console.error('Error refreshing data:', error);
            } finally {
                isRefreshing = false;
            }
        }

        async function loadConfigStatus() {
            try {
                const response = await fetch('/api/diagnostics/config');
                const data = await response.json();

                if (data.success) {
                    renderConfigStatus(data);
                } else {
                    document.getElementById('configStatus').innerHTML =
                        `<div class="alert alert-error">Error: ${data.error}</div>`;
                }
            } catch (error) {
                document.getElementById('configStatus').innerHTML =
                    `<div class="alert alert-error">Failed to load configuration status: ${error.message}</div>`;
            }
        }

        async function loadServiceHealth() {
            try {
                const response = await fetch('/api/diagnostics/services');
                const data = await response.json();

                if (data.success) {
                    renderServiceHealth(data);
                } else {
                    document.getElementById('serviceHealth').innerHTML =
                        `<div class="alert alert-error">Error: ${data.error}</div>`;
                }
            } catch (error) {
                document.getElementById('serviceHealth').innerHTML =
                    `<div class="alert alert-error">Failed to load service health: ${error.message}</div>`;
            }
        }

        async function loadDataSources() {
            try {
                const response = await fetch('/api/diagnostics/data-sources');
                const data = await response.json();

                if (data.success) {
                    renderDataSources(data);
                } else {
                    document.getElementById('dataSources').innerHTML =
                        `<div class="alert alert-error">Error: ${data.error}</div>`;
                }
            } catch (error) {
                document.getElementById('dataSources').innerHTML =
                    `<div class="alert alert-error">Failed to load data sources: ${error.message}</div>`;
            }
        }

        async function loadSystemInfo() {
            try {
                const response = await fetch('/api/diagnostics/system');
                const data = await response.json();

                if (data.success) {
                    renderSystemInfo(data);
                } else {
                    document.getElementById('systemInfo').innerHTML =
                        `<div class="alert alert-error">Error: ${data.error}</div>`;
                }
            } catch (error) {
                document.getElementById('systemInfo').innerHTML =
                    `<div class="alert alert-error">Failed to load system information: ${error.message}</div>`;
            }
        }

        function renderConfigStatus(data) {
            const { config, validation } = data;
            let html = '';

            // Configuration validation status
            html += `<div class="status-item">
                <span class="status-label">System Configured</span>
                <span class="status-value">
                    <span class="status-indicator ${validation.isConfigured ? 'status-connected' : 'status-error'}"></span>
                    ${validation.isConfigured ? 'Yes' : 'No'}
                </span>
            </div>`;

            html += `<div class="status-item">
                <span class="status-label">Bot Active</span>
                <span class="status-value">
                    <span class="status-indicator ${config.botActive ? 'status-connected' : 'status-disabled'}"></span>
                    ${config.botActive ? 'Active' : 'Inactive'}
                </span>
            </div>`;

            html += `<div class="status-item">
                <span class="status-label">API Keys Configured</span>
                <span class="status-value">
                    <span class="status-indicator ${validation.hasApiKeys ? 'status-connected' : 'status-error'}"></span>
                    ${validation.hasApiKeys ? 'Yes' : 'No'}
                </span>
            </div>`;

            html += `<div class="status-item">
                <span class="status-label">Money Management</span>
                <span class="status-value">
                    <span class="status-indicator ${config.moneyManagementEnabled ? 'status-connected' : 'status-disabled'}"></span>
                    ${config.moneyManagementEnabled ? `Enabled (${config.moneyManagementMode})` : 'Disabled'}
                </span>
            </div>`;

            html += `<div class="status-item">
                <span class="status-label">SL/TP Automation</span>
                <span class="status-value">
                    <span class="status-indicator ${config.slTpEnabled ? 'status-connected' : 'status-disabled'}"></span>
                    ${config.slTpEnabled ? 'Enabled' : 'Disabled'}
                </span>
            </div>`;

            html += `<div class="status-item">
                <span class="status-label">Telegram Notifications</span>
                <span class="status-value">
                    <span class="status-indicator ${validation.hasTelegramConfig ? 'status-connected' : 'status-disabled'}"></span>
                    ${validation.hasTelegramConfig ? 'Configured' : 'Not Configured'}
                </span>
            </div>`;

            // Configuration issues
            if (validation.configurationIssues && validation.configurationIssues.length > 0) {
                html += `<div class="config-issues">
                    <h4>Configuration Issues</h4>
                    <ul>`;
                validation.configurationIssues.forEach(issue => {
                    html += `<li>${issue}</li>`;
                });
                html += `</ul></div>`;
            }

            document.getElementById('configStatus').innerHTML = html;
        }

        function renderServiceHealth(data) {
            const { services } = data;
            let html = '';

            Object.entries(services).forEach(([serviceName, service]) => {
                const statusClass = getStatusClass(service.status);
                const statusText = getStatusText(service.status);

                html += `<div class="status-item">
                    <span class="status-label">${formatServiceName(serviceName)}</span>
                    <span class="status-value">
                        <span class="status-indicator ${statusClass}"></span>
                        ${statusText}
                    </span>
                </div>`;

                if (service.lastError) {
                    html += `<div class="error-details">Error: ${service.lastError}</div>`;
                }

                if (service.responseTime) {
                    html += `<div class="response-time">Response time: ${service.responseTime}ms</div>`;
                }
            });

            document.getElementById('serviceHealth').innerHTML = html;
        }

        function renderDataSources(data) {
            const { dataSources } = data;
            let html = '';

            Object.entries(dataSources).forEach(([sourceName, source]) => {
                const statusClass = getStatusClass(source.status);
                const statusText = getStatusText(source.status);

                html += `<div class="status-item">
                    <span class="status-label">${formatServiceName(sourceName)}</span>
                    <span class="status-value">
                        <span class="status-indicator ${statusClass}"></span>
                        ${statusText}
                        ${source.responseTime ? `(${source.responseTime}ms)` : ''}
                    </span>
                </div>`;

                if (source.lastError) {
                    html += `<div class="error-details">Error: ${source.lastError}</div>`;
                }
            });

            document.getElementById('dataSources').innerHTML = html;
        }

        function renderSystemInfo(data) {
            const { system } = data;
            let html = '';

            html += `<div class="status-item">
                <span class="status-label">Service Version</span>
                <span class="status-value">${system.service.version}</span>
            </div>`;

            html += `<div class="status-item">
                <span class="status-label">Uptime</span>
                <span class="status-value">${formatUptime(system.service.uptime)}</span>
            </div>`;

            html += `<div class="status-item">
                <span class="status-label">Environment</span>
                <span class="status-value">${system.service.environment}</span>
            </div>`;

            html += `<div class="status-item">
                <span class="status-label">Port</span>
                <span class="status-value">${system.service.port}</span>
            </div>`;

            html += `<div class="status-item">
                <span class="status-label">Memory Usage</span>
                <span class="status-value">${formatBytes(system.memory.heapUsed)} / ${formatBytes(system.memory.heapTotal)}</span>
            </div>`;

            html += `<div class="status-item">
                <span class="status-label">Total Webhooks</span>
                <span class="status-value">${system.webhookStats.totalReceived}</span>
            </div>`;

            if (system.webhookStats.lastSignalTime) {
                html += `<div class="status-item">
                    <span class="status-label">Last Signal</span>
                    <span class="status-value">${new Date(system.webhookStats.lastSignalTime).toLocaleString()}</span>
                </div>`;
            }

            document.getElementById('systemInfo').innerHTML = html;
        }

        function getStatusClass(status) {
            switch (status) {
                case 'connected':
                case 'healthy':
                case 'configured':
                    return 'status-connected';
                case 'error':
                    return 'status-error';
                case 'not_configured':
                case 'disabled':
                    return 'status-disabled';
                default:
                    return 'status-unknown';
            }
        }

        function getStatusText(status) {
            switch (status) {
                case 'connected': return 'Connected';
                case 'healthy': return 'Healthy';
                case 'configured': return 'Configured';
                case 'error': return 'Error';
                case 'not_configured': return 'Not Configured';
                case 'disabled': return 'Disabled';
                default: return 'Unknown';
            }
        }

        function formatServiceName(name) {
            return name.replace(/([A-Z])/g, ' $1')
                      .replace(/^./, str => str.toUpperCase())
                      .replace(/Api/g, 'API');
        }

        function formatUptime(seconds) {
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);

            if (days > 0) {
                return `${days}d ${hours}h ${minutes}m`;
            } else if (hours > 0) {
                return `${hours}h ${minutes}m`;
            } else {
                return `${minutes}m`;
            }
        }

        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
