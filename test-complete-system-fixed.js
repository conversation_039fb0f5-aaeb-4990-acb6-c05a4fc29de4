#!/usr/bin/env node

/**
 * Comprehensive System Test for MEXC Trading System
 * Tests both TradingView Webhook Listener and MEXC Futures Trader services
 */

const axios = require('axios');
const { spawn } = require('child_process');
const path = require('path');

class SystemTester {
    constructor() {
        this.webhookListenerUrl = 'http://localhost:80';
        this.mexcTraderUrl = 'http://localhost:3000';
        this.testResults = [];
        this.services = {
            webhookListener: null,
            mexcTrader: null
        };
    }

    async runAllTests() {
        console.log('🚀 Starting Comprehensive System Tests...\n');

        try {
            // Test 1: Service Health Checks
            await this.testServiceHealth();

            // Test 2: Webhook Format Handling
            await this.testWebhookFormats();

            // Test 3: MEXC Trader Service
            await this.testMexcTraderService();

            // Test 4: End-to-End Integration
            await this.testEndToEndIntegration();

            // Test 5: Error Handling
            await this.testErrorHandling();

            // Print Results
            this.printTestResults();

        } catch (error) {
            console.error('❌ Test suite failed:', error.message);
            process.exit(1);
        }
    }

    async testServiceHealth() {
        console.log('🔍 Testing Service Health...');

        // Test Webhook Listener Health
        try {
            const webhookHealth = await axios.get(`${this.webhookListenerUrl}/health`, { timeout: 5000 });
            this.addTestResult('Webhook Listener Health', true, `Status: ${webhookHealth.data.status}`);
        } catch (error) {
            this.addTestResult('Webhook Listener Health', false, `Error: ${error.message}`);
        }

        // Test MEXC Trader Health
        try {
            const mexcHealth = await axios.get(`${this.mexcTraderUrl}/health`, { timeout: 5000 });
            this.addTestResult('MEXC Trader Health', true, `Status: ${mexcHealth.data.status}`);
        } catch (error) {
            this.addTestResult('MEXC Trader Health', false, `Error: ${error.message}`);
        }

        console.log('✅ Service health tests completed\n');
    }

    async testWebhookFormats() {
        console.log('📡 Testing Webhook Format Handling...');

        // Test 1: Buy signal (new format)
        const buySignal = {
            symbol: "TRUUSDT",
            trade: "buy",
            last_price: "0.000012064",
            leverege: "2"
        };

        try {
            const response = await axios.post(`${this.webhookListenerUrl}/webhook`, buySignal, { timeout: 10000 });
            this.addTestResult('Buy Signal Processing', response.data.success, 
                `Message: ${response.data.message}, Mapped to: ${response.data.processedSignal?.orderType}`);
        } catch (error) {
            this.addTestResult('Buy Signal Processing', false, `Error: ${error.response?.data?.error || error.message}`);
        }

        // Test 2: Sell signal (new format)
        const sellSignal = {
            symbol: "TRUUSDT",
            trade: "sell",
            last_price: "0.000012064",
            leverege: "2"
        };

        try {
            const response = await axios.post(`${this.webhookListenerUrl}/webhook`, sellSignal, { timeout: 10000 });
            this.addTestResult('Sell Signal Processing', response.data.success, 
                `Message: ${response.data.message}, Mapped to: ${response.data.processedSignal?.orderType}`);
        } catch (error) {
            this.addTestResult('Sell Signal Processing', false, `Error: ${error.response?.data?.error || error.message}`);
        }

        // Test 3: Close signal (should be ignored)
        const closeSignal = {
            symbol: "TRUUSDT",
            trade: "close",
            last_price: "0.000012064",
            leverege: "2"
        };

        try {
            const response = await axios.post(`${this.webhookListenerUrl}/webhook`, closeSignal, { timeout: 10000 });
            const ignored = response.data.message && response.data.message.includes('ignored');
            this.addTestResult('Close Signal Ignored', ignored, `Message: ${response.data.message}`);
        } catch (error) {
            this.addTestResult('Close Signal Ignored', false, `Error: ${error.response?.data?.error || error.message}`);
        }

        console.log('✅ Webhook format tests completed\n');
    }

    async testMexcTraderService() {
        console.log('🎯 Testing MEXC Trader Service...');

        // Test service info
        try {
            const info = await axios.get(`${this.mexcTraderUrl}/info`, { timeout: 5000 });
            this.addTestResult('MEXC Service Info', true, `Service: ${info.data.service}, Version: ${info.data.version}`);
        } catch (error) {
            this.addTestResult('MEXC Service Info', false, `Error: ${error.message}`);
        }

        // Test balance endpoint
        try {
            const balance = await axios.get(`${this.mexcTraderUrl}/balance`, { timeout: 15000 });
            this.addTestResult('Balance Retrieval', balance.data.success, 
                `Balance: ${balance.data.balance} ${balance.data.currency}, Source: ${balance.data.selector || 'API'}`);
        } catch (error) {
            this.addTestResult('Balance Retrieval', false, `Error: ${error.response?.data?.error || error.message}`);
        }

        console.log('✅ MEXC Trader tests completed\n');
    }

    async testEndToEndIntegration() {
        console.log('🔄 Testing End-to-End Integration...');

        // Test webhook listener status
        try {
            const status = await axios.get(`${this.webhookListenerUrl}/api/status`, { timeout: 10000 });
            this.addTestResult('System Status Check', true, 
                `Bot Active: ${status.data.botActive}, MEXC Connected: ${status.data.mexcConnected}, Balance: ${status.data.balance?.free || 'N/A'}`);
        } catch (error) {
            this.addTestResult('System Status Check', false, `Error: ${error.response?.data?.error || error.message}`);
        }

        // Test configuration retrieval
        try {
            const config = await axios.get(`${this.webhookListenerUrl}/api/config`, { timeout: 5000 });
            this.addTestResult('Configuration Retrieval', true, 
                `Bot Active: ${config.data.botActive}, Money Management: ${config.data.moneyManagementEnabled}`);
        } catch (error) {
            this.addTestResult('Configuration Retrieval', false, `Error: ${error.response?.data?.error || error.message}`);
        }

        console.log('✅ End-to-end integration tests completed\n');
    }

    async testErrorHandling() {
        console.log('⚠️ Testing Error Handling...');

        // Test invalid webhook payload
        try {
            const invalidPayload = { invalid: "data" };
            const response = await axios.post(`${this.webhookListenerUrl}/webhook`, invalidPayload, { timeout: 5000 });
            this.addTestResult('Invalid Payload Handling', !response.data.success, 
                `Error handled: ${response.data.error || 'No error message'}`);
        } catch (error) {
            this.addTestResult('Invalid Payload Handling', true, `Properly rejected: ${error.response?.data?.error || error.message}`);
        }

        // Test unsupported symbol
        try {
            const unsupportedSymbol = {
                symbol: "BTCUSDT",
                trade: "buy",
                last_price: "50000",
                leverege: "2"
            };
            const response = await axios.post(`${this.webhookListenerUrl}/webhook`, unsupportedSymbol, { timeout: 5000 });
            this.addTestResult('Unsupported Symbol Handling', !response.data.success, 
                `Error handled: ${response.data.error || 'No error message'}`);
        } catch (error) {
            this.addTestResult('Unsupported Symbol Handling', true, `Properly rejected: ${error.response?.data?.error || error.message}`);
        }

        console.log('✅ Error handling tests completed\n');
    }

    addTestResult(testName, passed, details) {
        this.testResults.push({
            name: testName,
            passed,
            details,
            timestamp: new Date().toISOString()
        });
    }

    printTestResults() {
        console.log('📊 TEST RESULTS SUMMARY');
        console.log('=' .repeat(80));

        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;

        console.log(`\n🎯 Overall: ${passed}/${total} tests passed (${Math.round(passed/total*100)}%)\n`);

        this.testResults.forEach((result, index) => {
            const status = result.passed ? '✅' : '❌';
            console.log(`${status} ${index + 1}. ${result.name}`);
            if (result.details) {
                console.log(`   ${result.details}`);
            }
            console.log('');
        });

        if (passed === total) {
            console.log('🎉 All tests passed! System is ready for production.');
        } else {
            console.log('⚠️ Some tests failed. Please review and fix issues before deployment.');
        }
    }
}

// Run tests if called directly
if (require.main === module) {
    const tester = new SystemTester();
    tester.runAllTests().catch(error => {
        console.error('❌ Test execution failed:', error);
        process.exit(1);
    });
}

module.exports = SystemTester;
