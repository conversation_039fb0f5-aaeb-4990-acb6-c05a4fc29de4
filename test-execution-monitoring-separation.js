#!/usr/bin/env node

/**
 * Test Execution-Monitoring Separation
 * Verifies that monitoring never interferes with trade execution performance
 */

const path = require('path');

class ExecutionMonitoringSeparationTester {
    constructor() {
        this.testResults = [];
        this.executionTimes = [];
        this.monitoringCallsDuringExecution = 0;
    }

    createMockTrader() {
        const mockTrader = {
            browser: null,
            page: null,
            port: 9223,
            isExecutingTrade: false,
            monitoringActive: true,
            monitoringInterval: null,
            lastBalance: null,
            balanceUpdateTime: null,
            
            // Track monitoring calls during execution
            monitoringCallsDuringExecution: 0,
            
            // Mock page with performance tracking
            page: {
                locator: (selector) => ({
                    first: () => ({
                        isVisible: async (options = {}) => {
                            await this.simulateDelay(10);
                            return true;
                        },
                        getAttribute: async (attr) => {
                            await this.simulateDelay(5);
                            if (selector.includes('tab-open')) {
                                return 'handle_active__Yy6UA';
                            }
                            return '';
                        },
                        click: async () => {
                            await this.simulateDelay(20);
                            console.log(`🖱️ MOCK: Clicked ${selector}`);
                        },
                        inputValue: async () => {
                            await this.simulateDelay(5);
                            return '';
                        },
                        fill: async (value) => {
                            await this.simulateDelay(15);
                            console.log(`📝 MOCK: Filled with "${value}"`);
                        },
                        textContent: async () => {
                            await this.simulateDelay(10);
                            return '2.29 USDT';
                        }
                    }),
                    all: async () => {
                        await this.simulateDelay(10);
                        return [];
                    }
                }),
                waitForTimeout: async (ms) => {
                    await this.simulateDelay(Math.min(ms, 50));
                }
            },

            // Performance-critical execution methods
            async executeOpenTrade(orderType, quantity = '0.3600') {
                const startTime = Date.now();
                this.isExecutingTrade = true;
                
                try {
                    console.log(`🎯 EXECUTING ${orderType.toUpperCase()}...`);
                    console.log(`⚡ PERFORMANCE MODE: No monitoring during execution`);

                    // Simulate trade execution steps
                    const quantityInput = this.page.locator('text=Quantity(USDT) >> xpath=following::input[1]').first();
                    await quantityInput.click();
                    await quantityInput.fill(quantity);

                    const buttonSelector = 'button.component_longBtn__eazYU div:has-text("Open Long")';
                    const orderButton = this.page.locator(buttonSelector).first();
                    await orderButton.click();

                    const executionTime = Date.now() - startTime;
                    console.log(`⚡ ${orderType} completed in ${executionTime}ms`);

                    return {
                        success: true,
                        executionTime,
                        orderType,
                        quantity,
                        targetAchieved: executionTime < 2000,
                        timestamp: new Date().toISOString()
                    };

                } catch (error) {
                    const executionTime = Date.now() - startTime;
                    return {
                        success: false,
                        error: error.message,
                        executionTime,
                        orderType,
                        quantity
                    };
                } finally {
                    this.isExecutingTrade = false;
                }
            },

            async executeCloseTrade(orderType, quantity = '0.3600') {
                const startTime = Date.now();
                this.isExecutingTrade = true;
                
                try {
                    console.log(`🎯 EXECUTING ${orderType.toUpperCase()}...`);

                    // Simulate close trade execution
                    const closeTab = this.page.locator('span[data-testid="contract-trade-order-form-tab-close"]').first();
                    await closeTab.click();
                    await this.page.waitForTimeout(200); // Reduced for testing

                    const textInputs = await this.page.locator('input[type="text"]').all();
                    // Simulate quantity filling and button clicking
                    await this.simulateDelay(50);

                    const executionTime = Date.now() - startTime;
                    console.log(`⚡ ${orderType} completed in ${executionTime}ms`);

                    // Schedule post-trade cleanup
                    this.schedulePostTradeCleanup();

                    return {
                        success: true,
                        executionTime,
                        orderType,
                        quantity,
                        targetAchieved: executionTime < 2000,
                        timestamp: new Date().toISOString()
                    };

                } catch (error) {
                    const executionTime = Date.now() - startTime;
                    return {
                        success: false,
                        error: error.message,
                        executionTime,
                        orderType,
                        quantity
                    };
                } finally {
                    this.isExecutingTrade = false;
                }
            },

            // Monitoring methods with execution protection
            async performBackgroundMaintenance() {
                if (this.isExecutingTrade) {
                    this.monitoringCallsDuringExecution++;
                    console.log('⚡ PERFORMANCE PROTECTION: Skipping maintenance during trade execution');
                    return;
                }

                try {
                    console.log('🧹 Background maintenance (between trades only)...');
                    await this.simulateDelay(100); // Simulate maintenance work
                    console.log('✅ Background maintenance completed - panel ready for next trade');
                } catch (error) {
                    console.log('⚠️ Background maintenance error:', error.message);
                }
            },

            async ensurePreExecutionReadiness() {
                if (this.isExecutingTrade) {
                    console.log('⚡ PERFORMANCE PROTECTION: Cannot prepare during active execution');
                    return false;
                }

                try {
                    console.log('🚀 Pre-execution preparation...');
                    await this.simulateDelay(30); // Quick preparation
                    console.log('✅ Pre-execution preparation complete');
                    return true;
                } catch (error) {
                    console.log('⚠️ Pre-execution preparation failed:', error.message);
                    return false;
                }
            },

            async schedulePostTradeCleanup() {
                setTimeout(async () => {
                    if (!this.isExecutingTrade && this.monitoringActive) {
                        console.log('🔄 Post-trade cleanup initiated...');
                        await this.performBackgroundMaintenance();
                    }
                }, 500);
            },

            async executeOrder(orderType, quantity = '0.3600') {
                console.log('🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel');
                
                if (orderType.includes('Open')) {
                    const ready = await this.ensurePreExecutionReadiness();
                    if (!ready) {
                        console.log('⚠️ Panel preparation failed, proceeding with execution anyway');
                    }
                }

                if (orderType.includes('Open')) {
                    return await this.executeOpenTrade(orderType, quantity);
                } else if (orderType.includes('Close')) {
                    return await this.executeCloseTrade(orderType, quantity);
                } else {
                    throw new Error(`Unknown order type: ${orderType}`);
                }
            }
        };

        return mockTrader;
    }

    async simulateDelay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async runTests() {
        console.log('🔬 Testing Execution-Monitoring Separation');
        console.log('===========================================\n');

        await this.testNoMonitoringDuringExecution();
        await this.testPreExecutionPreparation();
        await this.testPostExecutionCleanup();
        await this.testPerformanceImpact();

        this.printResults();
    }

    async testNoMonitoringDuringExecution() {
        console.log('🚫 Test 1: No Monitoring During Execution');
        console.log('-------------------------------------------');

        const trader = this.createMockTrader();

        try {
            // Start background monitoring
            const monitoringInterval = setInterval(async () => {
                await trader.performBackgroundMaintenance();
            }, 100); // Frequent monitoring for testing

            // Execute multiple trades while monitoring is running
            const trades = ['Open Long', 'Close Long', 'Open Short', 'Close Short'];
            
            for (const orderType of trades) {
                console.log(`\n🔄 Testing ${orderType}...`);
                const result = await trader.executeOrder(orderType, '1.0000');
                
                if (result.success && result.executionTime < 2000) {
                    this.addResult(`${orderType} Performance`, true, `Executed in ${result.executionTime}ms (sub-2s target met)`);
                } else {
                    this.addResult(`${orderType} Performance`, false, `Execution time: ${result.executionTime}ms or failed`);
                }
            }

            clearInterval(monitoringInterval);

            // Check if monitoring was blocked during execution
            if (trader.monitoringCallsDuringExecution === 0) {
                this.addResult('Monitoring Blocked During Execution', true, 'No monitoring calls occurred during trade execution');
            } else {
                this.addResult('Monitoring Blocked During Execution', false, `${trader.monitoringCallsDuringExecution} monitoring calls during execution`);
            }

        } catch (error) {
            this.addResult('No Monitoring During Execution', false, error.message);
        }

        console.log('');
    }

    async testPreExecutionPreparation() {
        console.log('🚀 Test 2: Pre-Execution Preparation');
        console.log('--------------------------------------');

        const trader = this.createMockTrader();

        try {
            // Test preparation when not executing
            trader.isExecutingTrade = false;
            const result1 = await trader.ensurePreExecutionReadiness();
            this.addResult('Pre-execution Preparation (Available)', result1, 'Preparation completed when not executing');

            // Test preparation blocked during execution
            trader.isExecutingTrade = true;
            const result2 = await trader.ensurePreExecutionReadiness();
            this.addResult('Pre-execution Preparation (Blocked)', !result2, 'Preparation blocked during execution');

        } catch (error) {
            this.addResult('Pre-Execution Preparation', false, error.message);
        }

        console.log('');
    }

    async testPostExecutionCleanup() {
        console.log('🔄 Test 3: Post-Execution Cleanup');
        console.log('-----------------------------------');

        const trader = this.createMockTrader();

        try {
            // Execute a close trade (triggers post-cleanup)
            const result = await trader.executeOrder('Close Long', '1.0000');
            
            if (result.success) {
                this.addResult('Post-Execution Cleanup Scheduled', true, 'Cleanup scheduled after close trade');
                
                // Wait for cleanup to trigger
                await this.simulateDelay(600);
                this.addResult('Post-Execution Cleanup Executed', true, 'Cleanup executed after delay');
            } else {
                this.addResult('Post-Execution Cleanup', false, 'Trade execution failed');
            }

        } catch (error) {
            this.addResult('Post-Execution Cleanup', false, error.message);
        }

        console.log('');
    }

    async testPerformanceImpact() {
        console.log('⚡ Test 4: Performance Impact Analysis');
        console.log('---------------------------------------');

        const trader = this.createMockTrader();

        try {
            // Execute trades and measure performance
            const orderTypes = ['Open Long', 'Open Short'];
            const executionTimes = [];

            for (const orderType of orderTypes) {
                const result = await trader.executeOrder(orderType, '1.0000');
                if (result.success) {
                    executionTimes.push(result.executionTime);
                }
            }

            const avgExecutionTime = executionTimes.reduce((a, b) => a + b, 0) / executionTimes.length;
            const maxExecutionTime = Math.max(...executionTimes);

            this.addResult('Average Execution Time', avgExecutionTime < 1500, `Average: ${avgExecutionTime.toFixed(0)}ms`);
            this.addResult('Maximum Execution Time', maxExecutionTime < 2000, `Maximum: ${maxExecutionTime.toFixed(0)}ms`);
            this.addResult('Sub-2s Target Achievement', maxExecutionTime < 2000, 'All trades under 2 seconds');

        } catch (error) {
            this.addResult('Performance Impact Analysis', false, error.message);
        }

        console.log('');
    }

    addResult(name, passed, message) {
        this.testResults.push({ name, passed, message });
    }

    printResults() {
        console.log('📊 Test Results Summary');
        console.log('========================');

        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;

        this.testResults.forEach(result => {
            const icon = result.passed ? '✅' : '❌';
            console.log(`${icon} ${result.name}: ${result.message}`);
        });

        console.log(`\n📈 Performance Summary: ${passed}/${total} tests passed`);

        if (passed === total) {
            console.log('🎉 Perfect separation achieved! Monitoring never interferes with execution.');
            console.log('⚡ Sub-2 second execution targets are protected.');
        } else {
            console.log('⚠️ Some separation issues detected. Review monitoring implementation.');
        }
    }
}

// Run tests
if (require.main === module) {
    const tester = new ExecutionMonitoringSeparationTester();
    tester.runTests().catch(error => {
        console.error('❌ Test failed:', error);
        process.exit(1);
    });
}

module.exports = ExecutionMonitoringSeparationTester;
