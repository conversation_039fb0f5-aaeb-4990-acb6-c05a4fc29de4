const axios = require('axios');
const crypto = require('crypto');
require('dotenv').config();

class CustomMexcAPI {
    constructor(apiKey, apiSecret) {
        this.apiKey = apiKey;
        this.apiSecret = apiSecret;
        this.baseURL = 'https://api.mexc.com';
        this.futuresBaseURL = 'https://contract.mexc.com';
    }

    // Generate signature for authenticated requests
    generateSignature(queryString) {
        return crypto
            .createHmac('sha256', this.apiSecret)
            .update(queryString)
            .digest('hex');
    }

    // Make authenticated request
    async makeRequest(method, endpoint, params = {}, isFutures = false) {
        const baseURL = isFutures ? this.futuresBaseURL : this.baseURL;
        const timestamp = Date.now();
        
        // Add timestamp to params
        params.timestamp = timestamp;
        
        // Create query string
        const queryString = Object.keys(params)
            .sort()
            .map(key => `${key}=${params[key]}`)
            .join('&');
        
        // Generate signature
        const signature = this.generateSignature(queryString);
        
        // Add signature to params
        params.signature = signature;
        
        const config = {
            method,
            url: `${baseURL}${endpoint}`,
            headers: {
                'X-MEXC-APIKEY': this.apiKey,
                'Content-Type': 'application/json'
            }
        };

        if (method === 'GET') {
            config.params = params;
        } else {
            config.data = params;
        }

        return axios(config);
    }

    // Test spot account info
    async getSpotAccountInfo() {
        return this.makeRequest('GET', '/api/v3/account');
    }

    // Test futures account info
    async getFuturesAccountInfo() {
        return this.makeRequest('GET', '/api/v1/private/account/assets', {}, true);
    }

    // Test futures positions
    async getFuturesPositions() {
        return this.makeRequest('GET', '/api/v1/private/position/list/history_positions', {}, true);
    }

    // Test market data (no auth required)
    async getMarketData(symbol) {
        const response = await axios.get(`${this.baseURL}/api/v3/ticker/24hr?symbol=${symbol}`);
        return response.data;
    }

    // Test futures market data
    async getFuturesMarketData(symbol) {
        const response = await axios.get(`${this.futuresBaseURL}/api/v1/contract/ticker?symbol=${symbol}`);
        return response.data;
    }

    // Test futures order placement (dry run)
    async createFuturesOrder(params) {
        return this.makeRequest('POST', '/api/v1/private/order/submit', params, true);
    }
}

async function testCustomAPI() {
    console.log('\n=== Testing Custom MEXC API Implementation ===');
    
    try {
        const client = new CustomMexcAPI(
            process.env.MEXC_API_KEY,
            process.env.MEXC_API_SECRET
        );

        console.log('✓ Custom API client initialized');

        // Test public market data first (no auth required)
        console.log('\n1. Testing public market data...');
        try {
            const symbol = process.env.TEST_SYMBOL || 'BTCUSDT';
            const marketData = await client.getMarketData(symbol);
            console.log('✓ Spot market data retrieved for', symbol);
            console.log('Price:', marketData.lastPrice);
        } catch (error) {
            console.log('⚠ Spot market data failed:', error.message);
        }

        // Test futures market data
        try {
            const symbol = process.env.TEST_SYMBOL || 'BTC_USDT';
            const futuresData = await client.getFuturesMarketData(symbol);
            console.log('✓ Futures market data retrieved for', symbol);
        } catch (error) {
            console.log('⚠ Futures market data failed:', error.message);
        }

        // Test spot account info
        console.log('\n2. Testing spot account info...');
        try {
            const response = await client.getSpotAccountInfo();
            console.log('✓ Spot account info retrieved');
            console.log('Balances count:', response.data.balances.length);
        } catch (error) {
            console.log('⚠ Spot account info failed:', error.response?.data || error.message);
        }

        // Test futures account info
        console.log('\n3. Testing futures account info...');
        try {
            const response = await client.getFuturesAccountInfo();
            console.log('✓ Futures account info retrieved');
            console.log('Account data:', response.data);
        } catch (error) {
            console.log('⚠ Futures account info failed:', error.response?.data || error.message);
        }

        // Test futures positions
        console.log('\n4. Testing futures positions...');
        try {
            const response = await client.getFuturesPositions();
            console.log('✓ Futures positions retrieved');
            console.log('Positions:', response.data);
        } catch (error) {
            console.log('⚠ Futures positions failed:', error.response?.data || error.message);
        }

        // Test futures order creation (dry run)
        console.log('\n5. Testing futures order creation (dry run)...');
        try {
            const orderParams = {
                symbol: 'BTC_USDT',
                side: 1, // 1 for buy, 2 for sell
                type: 1, // 1 for limit order
                vol: process.env.TEST_QUANTITY || '0.001',
                price: '30000',
                leverage: process.env.TEST_LEVERAGE || '1'
            };

            console.log('Order parameters:', orderParams);
            
            // Note: Uncomment to actually place order
            // const response = await client.createFuturesOrder(orderParams);
            console.log('✓ Order parameters validated (order not placed)');
            
        } catch (error) {
            console.log('⚠ Order creation test failed:', error.response?.data || error.message);
        }

        console.log('\n✅ Custom API test completed');
        return { success: true, library: 'custom-api', features: ['spot', 'futures', 'direct_api'] };

    } catch (error) {
        console.error('❌ Custom API test failed:', error.message);
        return { success: false, library: 'custom-api', error: error.message };
    }
}

if (require.main === module) {
    testCustomAPI().then(result => {
        console.log('\nResult:', result);
        process.exit(result.success ? 0 : 1);
    });
}

module.exports = testCustomAPI;
