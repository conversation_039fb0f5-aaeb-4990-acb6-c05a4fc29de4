const { chromium } = require('playwright');

async function testCloseQuantity() {
    console.log('🧪 TESTING CLOSE PANEL QUANTITY FIELD');
    console.log('======================================');
    
    try {
        const browser = await chromium.connectOverCDP('http://localhost:9223');
        const contexts = browser.contexts();
        const pages = contexts[0].pages();
        const page = pages[0];
        
        console.log('✅ Connected to browser');
        
        // Switch to Close panel
        console.log('🔄 Switching to Close panel...');
        const closeTab = page.locator('span[data-testid="contract-trade-order-form-tab-close"]').first();
        await closeTab.click();
        await page.waitForTimeout(1000);
        console.log('✅ Switched to Close panel');
        
        // Test quantity selectors one by one
        const quantitySelectors = [
            'input.ant-input.ant-input-sm',
            'input[type="text"].ant-input-sm',
            'input[type="text"]',
            '.ant-input-sm',
            '.ant-input'
        ];
        
        for (const selector of quantitySelectors) {
            console.log(`\n🧪 Testing selector: ${selector}`);
            
            try {
                const elements = await page.locator(selector).all();
                console.log(`   Found ${elements.length} elements`);
                
                for (let i = 0; i < elements.length; i++) {
                    const element = elements[i];
                    const isVisible = await element.isVisible({ timeout: 500 });
                    
                    if (isVisible) {
                        console.log(`   Element ${i + 1}: Visible`);
                        
                        try {
                            await element.click({ timeout: 1000 });
                            await element.fill('0.3600');
                            const value = await element.inputValue();
                            console.log(`   ✅ SUCCESS! Filled with: "${value}"`);
                            
                            // Clear it
                            await element.fill('');
                            console.log(`   🧹 Cleared field`);
                            
                            console.log(`\n🎉 WORKING SELECTOR FOUND: ${selector} (element ${i + 1})`);
                            return { success: true, selector: selector, elementIndex: i };
                            
                        } catch (fillError) {
                            console.log(`   ❌ Fill failed: ${fillError.message}`);
                        }
                    } else {
                        console.log(`   Element ${i + 1}: Not visible`);
                    }
                }
            } catch (error) {
                console.log(`   ❌ Selector failed: ${error.message}`);
            }
        }
        
        console.log('\n❌ NO WORKING SELECTOR FOUND');
        return { success: false };
        
    } catch (error) {
        console.error('💥 Test failed:', error.message);
        return { success: false, error: error.message };
    }
}

if (require.main === module) {
    testCloseQuantity().then(result => {
        if (result.success) {
            console.log('\n🎉 TEST PASSED!');
            console.log(`Working selector: ${result.selector}`);
            if (result.elementIndex !== undefined) {
                console.log(`Element index: ${result.elementIndex}`);
            }
        } else {
            console.log('\n❌ TEST FAILED');
            if (result.error) {
                console.log(`Error: ${result.error}`);
            }
        }
    });
}

module.exports = testCloseQuantity;
