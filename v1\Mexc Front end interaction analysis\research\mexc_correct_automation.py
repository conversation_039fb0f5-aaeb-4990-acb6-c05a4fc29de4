#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MEXC Correct Automation System
Targets the correct quantity input field that's below the price field.

KEY INSIGHT: The quantity field is directly below the price field and has no value.
Price field has value "0.03374", quantity field below it is empty.
"""

import os
import sys
import json
import time
import logging
import argparse
from datetime import datetime
from typing import Dict, Any, Optional
from dataclasses import dataclass
from playwright.sync_api import sync_playwright

@dataclass
class TradeConfig:
    symbol: str = "TRU_USDT"
    side: str = "BUY"  # BUY or SELL
    order_type: str = "MARKET"  # MARKET, LIMIT
    quantity: float = 10.0
    price: Optional[float] = None
    leverage: int = 20
    execute_real_trade: bool = False

class MEXCCorrectAutomation:
    """Automation that targets the correct quantity field"""
    
    def __init__(self, config: TradeConfig):
        self.config = config
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # Browser components
        self.playwright = None
        self.browser = None
        self.page = None
        
        # Interaction tracking
        self.screenshot_counter = 0
        
        self.logger.info(f"🎯 Correct automation initialized: {config}")
    
    def take_screenshot(self, name: str, description: str = "") -> str:
        """Take a screenshot for verification"""
        self.screenshot_counter += 1
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"correct_{self.screenshot_counter:03d}_{name}_{timestamp}.png"
        
        try:
            self.page.screenshot(path=filename, full_page=True)
            self.logger.info(f"📸 {filename} - {description}")
            return filename
        except Exception as e:
            self.logger.error(f"Screenshot failed: {e}")
            return ""
    
    def connect_to_browser(self) -> bool:
        """Connect to browser"""
        self.logger.info("🔌 Connecting to browser...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                self.logger.error("No browser contexts found")
                return False
            
            context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                self.logger.error("No MEXC page found")
                return False
            
            self.page = mexc_page
            
            # Wait for page to fully load
            time.sleep(2)
            
            self.take_screenshot("connected", "Connected to MEXC")
            self.logger.info("✅ Browser connection successful")
            return True
            
        except Exception as e:
            self.logger.error(f"Browser connection failed: {e}")
            return False
    
    def find_correct_quantity_field(self) -> bool:
        """Find the correct quantity field using JavaScript"""
        self.logger.info("🔍 Finding correct quantity field...")
        
        # Take before screenshot
        self.take_screenshot("before_find_quantity", "Before finding quantity field")
        
        find_script = f"""
        () => {{
            console.log('🔍 Analyzing input fields to find quantity field...');
            
            // Get all input elements
            const inputs = Array.from(document.querySelectorAll('input.ant-input'));
            console.log(`Found ${{inputs.length}} input elements`);
            
            let priceField = null;
            let quantityField = null;
            
            // First, find the price field (has value like "0.03374")
            for (let i = 0; i < inputs.length; i++) {{
                const input = inputs[i];
                const value = input.value || '';
                
                console.log(`Input ${{i+1}}: value="${{value}}", type="${{input.type}}", classes="${{input.className}}"`);
                
                // Check if this looks like a price field
                if (value && value.includes('.') && parseFloat(value) > 0) {{
                    priceField = input;
                    console.log(`✅ Found price field at index ${{i}}: value="${{value}}"`);
                    
                    // Look for the next empty input field (should be quantity)
                    for (let j = i + 1; j < inputs.length; j++) {{
                        const nextInput = inputs[j];
                        const nextValue = nextInput.value || '';
                        
                        // Skip checkboxes and search inputs
                        if (nextInput.type === 'checkbox' || nextInput.className.includes('search')) {{
                            continue;
                        }}
                        
                        // If it's empty or has a small value, it might be quantity
                        if (!nextValue || nextValue === '' || parseFloat(nextValue) === 0) {{
                            quantityField = nextInput;
                            console.log(`✅ Found potential quantity field at index ${{j}}: value="${{nextValue}}"`);
                            break;
                        }}
                    }}
                    break;
                }}
            }}
            
            if (quantityField) {{
                // Test fill the quantity field
                console.log('🎯 Testing quantity field...');
                
                // Focus and fill
                quantityField.focus();
                quantityField.value = '{self.config.quantity}';
                
                // Trigger events
                const inputEvent = new Event('input', {{ bubbles: true }});
                quantityField.dispatchEvent(inputEvent);
                
                const changeEvent = new Event('change', {{ bubbles: true }});
                quantityField.dispatchEvent(changeEvent);
                
                console.log(`✅ Quantity field filled with: ${{quantityField.value}}`);
                
                return {{
                    success: true,
                    priceValue: priceField ? priceField.value : 'not found',
                    quantityValue: quantityField.value,
                    quantityFieldIndex: inputs.indexOf(quantityField)
                }};
            }} else {{
                console.log('❌ Could not find quantity field');
                return {{
                    success: false,
                    error: 'Quantity field not found',
                    priceValue: priceField ? priceField.value : 'not found'
                }};
            }}
        }}
        """
        
        try:
            result = self.page.evaluate(find_script)
            
            if result.get('success'):
                price_value = result.get('priceValue')
                quantity_value = result.get('quantityValue')
                field_index = result.get('quantityFieldIndex')
                
                self.logger.info(f"✅ Found correct fields!")
                self.logger.info(f"   Price field value: {price_value}")
                self.logger.info(f"   Quantity field filled with: {quantity_value}")
                self.logger.info(f"   Quantity field index: {field_index}")
                
                # Take after screenshot
                self.take_screenshot("after_find_quantity", f"Quantity field filled: {quantity_value}")
                
                # Verify the value matches what we wanted
                if str(quantity_value) == str(self.config.quantity):
                    self.logger.info("✅ Quantity verification successful!")
                    return True
                else:
                    self.logger.warning(f"⚠️ Value mismatch but field found: expected {self.config.quantity}, got {quantity_value}")
                    return True  # Still success since we found and filled the field
            else:
                error = result.get('error', 'Unknown error')
                price_value = result.get('priceValue', 'unknown')
                self.logger.error(f"❌ Failed to find quantity field: {error}")
                self.logger.info(f"   Price field value: {price_value}")
                
        except Exception as e:
            self.logger.error(f"❌ JavaScript execution failed: {e}")
        
        self.take_screenshot("find_quantity_failed", "Failed to find quantity field")
        return False
    
    def click_order_button(self) -> bool:
        """Click the order button using JavaScript"""
        self.logger.info(f"🎯 Clicking {self.config.side} button...")
        
        # Take before screenshot
        self.take_screenshot("before_order_button", f"Before {self.config.side} button click")
        
        # Use the exact button classes we found earlier
        if self.config.side == "BUY":
            button_class = "component_longBtn__eazYU"
            button_text = "Open Long"
        else:
            button_class = "component_shortBtn__x5P3I"
            button_text = "Open Short"
        
        click_script = f"""
        () => {{
            console.log('🔍 Looking for {self.config.side} button...');
            
            // Find button by class
            let button = document.querySelector('button.{button_class}');
            
            if (button) {{
                console.log('✅ Found {self.config.side} button by class');
                console.log(`Button text: "${{button.textContent}}"`);
                
                if (!{str(self.config.execute_real_trade).lower()}) {{
                    console.log('🟡 SAFETY MODE: Button found but not clicked');
                    return {{ success: true, mode: 'safety', text: button.textContent }};
                }} else {{
                    console.log('🔴 LIVE MODE: Clicking button...');
                    button.click();
                    console.log('✅ Button clicked');
                    return {{ success: true, mode: 'live', text: button.textContent }};
                }}
            }} else {{
                console.log('❌ Button not found');
                return {{ success: false, error: 'Button not found' }};
            }}
        }}
        """
        
        try:
            result = self.page.evaluate(click_script)
            
            if result.get('success'):
                mode = result.get('mode')
                button_text = result.get('text')
                
                if mode == 'safety':
                    self.logger.info(f"✅ {self.config.side} button found: '{button_text}'")
                    self.logger.info("🟡 SAFETY MODE: Button ready but not clicked")
                    self.take_screenshot("button_ready", f"{self.config.side} button ready")
                    return True
                else:
                    self.logger.info(f"🔴 {self.config.side} button clicked: '{button_text}'")
                    self.take_screenshot("button_clicked", f"{self.config.side} button clicked")
                    
                    # Wait for any confirmation dialogs
                    time.sleep(2)
                    self.take_screenshot("after_click", "After button click")
                    return True
            else:
                error = result.get('error', 'Unknown error')
                self.logger.error(f"❌ Button click failed: {error}")
                
        except Exception as e:
            self.logger.error(f"❌ JavaScript execution failed: {e}")
        
        self.take_screenshot("button_click_failed", f"{self.config.side} button click failed")
        return False
    
    def execute_complete_trade(self) -> Dict[str, Any]:
        """Execute complete trading workflow"""
        self.logger.info("🚀 Starting complete trade execution")
        
        result = {
            "success": False,
            "steps_completed": [],
            "errors": [],
            "total_duration": 0
        }
        
        start_time = time.time()
        
        try:
            # Step 1: Connect to browser
            self.logger.info("📋 Step 1: Browser connection")
            if not self.connect_to_browser():
                result["errors"].append("Browser connection failed")
                return result
            result["steps_completed"].append("browser_connected")
            
            # Step 2: Find and fill correct quantity field
            self.logger.info("📋 Step 2: Find and fill quantity field")
            if not self.find_correct_quantity_field():
                result["errors"].append("Quantity field interaction failed")
                return result
            result["steps_completed"].append("quantity_filled")
            
            # Step 3: Click order button
            self.logger.info("📋 Step 3: Click order button")
            if not self.click_order_button():
                result["errors"].append("Order button click failed")
                return result
            result["steps_completed"].append("order_button_clicked")
            
            # Success!
            result["success"] = True
            self.logger.info("✅ Complete trade execution successful!")
            
        except Exception as e:
            self.logger.error(f"Trade execution exception: {e}")
            result["errors"].append(str(e))
        
        finally:
            result["total_duration"] = time.time() - start_time
        
        return result
    
    def cleanup(self):
        """Clean up resources"""
        try:
            if self.playwright:
                self.playwright.stop()
        except Exception as e:
            self.logger.error(f"Cleanup error: {e}")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="MEXC Correct Automation System")
    
    # Trade parameters
    parser.add_argument("--symbol", default="TRU_USDT", help="Trading symbol")
    parser.add_argument("--side", choices=["BUY", "SELL"], default="BUY", help="Order side")
    parser.add_argument("--quantity", type=float, default=10.0, help="Order quantity")
    parser.add_argument("--execute", action="store_true", help="🔴 EXECUTE REAL TRADE")
    
    args = parser.parse_args()
    
    # Create configuration
    config = TradeConfig(
        symbol=args.symbol,
        side=args.side,
        quantity=args.quantity,
        execute_real_trade=args.execute
    )
    
    print(f"""
🎯 MEXC Correct Automation System
=================================

TARGETING CORRECT FIELDS:
✅ Price field: Contains value like "0.03374"
✅ Quantity field: Empty field directly below price
✅ Order button: component_longBtn__eazYU class

Trade Configuration:
  Symbol: {config.symbol}
  Side: {config.side}
  Quantity: {config.quantity}

Execution Mode: {'🔴 LIVE TRADING' if args.execute else '🟡 SAFE MODE'}
    """)
    
    if args.execute:
        confirmation = input("⚠️ Type 'EXECUTE' to proceed with live trading: ")
        if confirmation != 'EXECUTE':
            print("❌ Live trading cancelled")
            return
    
    print("\nStarting automation...")
    
    # Initialize automation system
    automation = MEXCCorrectAutomation(config)
    
    try:
        result = automation.execute_complete_trade()
        
        print(f"""
📊 Execution Results:
====================
Success: {'✅' if result['success'] else '❌'}
Duration: {result['total_duration']:.2f}s
Steps: {', '.join(result['steps_completed'])}
        """)
        
        if result['errors']:
            print(f"Errors: {', '.join(result['errors'])}")
        
        if result['success']:
            if args.execute:
                print("🎉 LIVE TRADE EXECUTED!")
            else:
                print("✅ Ready for live trading!")
        else:
            print("❌ Execution failed")
    
    except KeyboardInterrupt:
        print("\n👋 Interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
    finally:
        automation.cleanup()

if __name__ == "__main__":
    main()
