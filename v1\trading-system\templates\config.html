{% extends "base.html" %}

{% block title %}System Configuration - MEXC Trading System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">System Configuration</h1>
                <div class="d-sm-flex align-items-center">
                    <button class="btn btn-primary btn-sm" onclick="refreshConfig()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Configuration Sections -->
    <div class="row">
        <!-- Trading Configuration -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Trading Configuration</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tbody>
                                <tr>
                                    <td><strong>Environment:</strong></td>
                                    <td>
                                        <span class="badge badge-{{ 'warning' if config.trading.environment == 'development' else 'success' }}">
                                            {{ config.trading.environment|title }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Max Concurrent Trades:</strong></td>
                                    <td>{{ config.trading.max_concurrent_trades }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Default Leverage:</strong></td>
                                    <td>{{ config.trading.default_leverage }}x</td>
                                </tr>
                                <tr>
                                    <td><strong>Risk Management:</strong></td>
                                    <td>
                                        <span class="badge badge-{{ 'success' if config.trading.enable_risk_management else 'warning' }}">
                                            {{ 'Enabled' if config.trading.enable_risk_management else 'Disabled' }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Auto Trading:</strong></td>
                                    <td>
                                        <span class="badge badge-{{ 'success' if config.trading.auto_trading_enabled else 'danger' }}">
                                            {{ 'Enabled' if config.trading.auto_trading_enabled else 'Disabled' }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Max Position Size:</strong></td>
                                    <td>${{ config.trading.max_position_size }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Stop Loss %:</strong></td>
                                    <td>{{ config.trading.default_stop_loss_percent }}%</td>
                                </tr>
                                <tr>
                                    <td><strong>Take Profit %:</strong></td>
                                    <td>{{ config.trading.default_take_profit_percent }}%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Browser Configuration -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Browser Configuration</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tbody>
                                <tr>
                                    <td><strong>Pool Size:</strong></td>
                                    <td>{{ config.browser.pool_size }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Headless Mode:</strong></td>
                                    <td>
                                        <span class="badge badge-{{ 'warning' if config.browser.headless_mode else 'info' }}">
                                            {{ 'Enabled' if config.browser.headless_mode else 'Disabled' }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Timeout:</strong></td>
                                    <td>{{ config.browser.timeout }}ms</td>
                                </tr>
                                <tr>
                                    <td><strong>Network Interception:</strong></td>
                                    <td>
                                        <span class="badge badge-{{ 'success' if config.browser.enable_network_interception else 'secondary' }}">
                                            {{ 'Enabled' if config.browser.enable_network_interception else 'Disabled' }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Stealth Mode:</strong></td>
                                    <td>
                                        <span class="badge badge-{{ 'success' if config.browser.stealth_mode else 'secondary' }}">
                                            {{ 'Enabled' if config.browser.stealth_mode else 'Disabled' }}
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Session Configuration -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Session Configuration</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tbody>
                                <tr>
                                    <td><strong>Pool Size:</strong></td>
                                    <td>{{ config.sessions.pool_size }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Session Timeout:</strong></td>
                                    <td>{{ config.sessions.session_timeout }}s</td>
                                </tr>
                                <tr>
                                    <td><strong>Health Check Interval:</strong></td>
                                    <td>{{ config.sessions.health_check_interval }}s</td>
                                </tr>
                                <tr>
                                    <td><strong>Expiry Warning Hours:</strong></td>
                                    <td>{{ config.sessions.expiry_warning_hours }}h</td>
                                </tr>
                                <tr>
                                    <td><strong>Auto Refresh:</strong></td>
                                    <td>
                                        <span class="badge badge-{{ 'success' if config.sessions.auto_refresh else 'warning' }}">
                                            {{ 'Enabled' if config.sessions.auto_refresh else 'Disabled' }}
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Configuration -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">System Configuration</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tbody>
                                <tr>
                                    <td><strong>Debug Mode:</strong></td>
                                    <td>
                                        <span class="badge badge-{{ 'warning' if config.system.debug_mode else 'success' }}">
                                            {{ 'Enabled' if config.system.debug_mode else 'Disabled' }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Log Level:</strong></td>
                                    <td>
                                        <span class="badge badge-info">{{ config.system.log_level }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Structured Logging:</strong></td>
                                    <td>
                                        <span class="badge badge-{{ 'success' if config.system.structured_logging else 'secondary' }}">
                                            {{ 'Enabled' if config.system.structured_logging else 'Disabled' }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Database URL:</strong></td>
                                    <td><small class="text-muted">{{ config.system.database_url[:50] }}...</small></td>
                                </tr>
                                <tr>
                                    <td><strong>Structured Logging:</strong></td>
                                    <td>
                                        <span class="badge badge-{{ 'success' if config.system.structured_logging else 'secondary' }}">
                                            {{ 'Enabled' if config.system.structured_logging else 'Disabled' }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Telegram Enabled:</strong></td>
                                    <td>
                                        <span class="badge badge-{{ 'success' if config.system.telegram_enabled else 'secondary' }}">
                                            {{ 'Enabled' if config.system.telegram_enabled else 'Disabled' }}
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- MEXC API Configuration -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">MEXC API Configuration</h6>
                    <button class="btn btn-sm btn-outline-success" onclick="testMexcConnection()">
                        <i class="fas fa-plug"></i> Test
                    </button>
                </div>
                <div class="card-body">
                    <form id="mexcApiForm">
                        <div class="form-group">
                            <label for="mexcApiKey">API Key</label>
                            <input type="text" class="form-control" id="mexcApiKey" placeholder="Enter MEXC API Key">
                        </div>
                        <div class="form-group">
                            <label for="mexcApiSecret">API Secret</label>
                            <input type="password" class="form-control" id="mexcApiSecret" placeholder="Enter MEXC API Secret">
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="mexcApiEnabled">
                            <label class="form-check-label" for="mexcApiEnabled">
                                Enable MEXC API Integration
                            </label>
                        </div>
                        <hr>
                        <button type="button" class="btn btn-primary" onclick="saveMexcApiConfig()">
                            <i class="fas fa-save"></i> Save API Configuration
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Money Management Configuration -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Money Management</h6>
                </div>
                <div class="card-body">
                    <form id="moneyManagementForm">
                        <div class="form-group">
                            <label for="tradingSymbol">Trading Symbol</label>
                            <select class="form-control" id="tradingSymbol">
                                <option value="TRU_USDT">TRU_USDT</option>
                                <option value="ETH_USDT">ETH_USDT</option>
                                <option value="BNB_USDT">BNB_USDT</option>
                                <option value="ADA_USDT">ADA_USDT</option>
                                <option value="SOL_USDT">SOL_USDT</option>
                            </select>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="leverage">Leverage</label>
                                <input type="number" class="form-control" id="leverage" min="1" max="100" value="1">
                            </div>
                            <div class="form-group col-md-6">
                                <label for="positionSizeType">Position Size Type</label>
                                <select class="form-control" id="positionSizeType" onchange="togglePositionSizeInputs()">
                                    <option value="percentage">Percentage</option>
                                    <option value="fixed">Fixed Amount</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6" id="percentageGroup">
                                <label for="positionSizePercentage">Position Size (%)</label>
                                <input type="number" class="form-control" id="positionSizePercentage" min="0" max="100" value="50">
                            </div>
                            <div class="form-group col-md-6" id="fixedGroup" style="display: none;">
                                <label for="positionSizeFixed">Fixed Amount (USDT)</label>
                                <input type="number" class="form-control" id="positionSizeFixed" min="0" step="0.01" value="100">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="maxPositionAmount">Maximum Position Amount (USDT)</label>
                            <input type="number" class="form-control" id="maxPositionAmount" min="0" step="0.01" value="100">
                            <small class="form-text text-muted">
                                If your balance exceeds this amount, only this amount will be used per trade
                            </small>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="useMaxPositionLimit" checked>
                            <label class="form-check-label" for="useMaxPositionLimit">
                                Use Maximum Position Limit
                            </label>
                        </div>
                        <hr>
                        <button type="button" class="btn btn-primary" onclick="saveMoneyManagementConfig()">
                            <i class="fas fa-save"></i> Save Money Management
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Configuration Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Configuration Actions</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <button class="btn btn-outline-primary btn-block" onclick="exportConfig()">
                                <i class="fas fa-download"></i> Export Configuration
                            </button>
                        </div>
                        <div class="col-md-4 mb-3">
                            <button class="btn btn-outline-warning btn-block" onclick="validateConfig()">
                                <i class="fas fa-check-circle"></i> Validate Configuration
                            </button>
                        </div>
                        <div class="col-md-4 mb-3">
                            <button class="btn btn-outline-danger btn-block" onclick="resetConfig()">
                                <i class="fas fa-undo"></i> Reset to Defaults
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshConfig() {
    location.reload();
}

function exportConfig() {
    fetch('/api/config/export')
        .then(response => response.blob())
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = 'mexc_trading_config.json';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to export configuration');
        });
}

function validateConfig() {
    fetch('/api/config/validate')
        .then(response => response.json())
        .then(data => {
            if (data.valid) {
                alert('Configuration is valid!');
            } else {
                alert('Configuration validation failed: ' + data.errors.join(', '));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to validate configuration');
        });
}

function resetConfig() {
    if (confirm('Are you sure you want to reset configuration to defaults? This action cannot be undone.')) {
        fetch('/api/config/reset', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Configuration reset successfully. Please restart the system.');
                location.reload();
            } else {
                alert('Failed to reset configuration: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error resetting configuration');
        });
    }
}

// Load current configuration
async function loadCurrentConfig() {
    try {
        const response = await fetch('/config/current');
        const data = await response.json();

        if (data.success) {
            const config = data.config;

            // Update MEXC API form
            document.getElementById('mexcApiEnabled').checked = config.mexc_api.api_enabled;

            // Update Money Management form
            document.getElementById('tradingSymbol').value = config.money_management.trading_symbol;
            document.getElementById('leverage').value = config.money_management.leverage;
            document.getElementById('positionSizeType').value = config.money_management.position_size_type;
            document.getElementById('positionSizePercentage').value = config.money_management.position_size_percentage;
            document.getElementById('positionSizeFixed').value = config.money_management.position_size_fixed;
            document.getElementById('maxPositionAmount').value = config.money_management.max_position_amount;
            document.getElementById('useMaxPositionLimit').checked = config.money_management.use_max_position_limit;

            // Toggle position size inputs
            togglePositionSizeInputs();
        }
    } catch (error) {
        console.error('Failed to load current config:', error);
    }
}

// Toggle position size inputs based on type
function togglePositionSizeInputs() {
    const positionSizeType = document.getElementById('positionSizeType').value;
    const percentageGroup = document.getElementById('percentageGroup');
    const fixedGroup = document.getElementById('fixedGroup');

    if (positionSizeType === 'percentage') {
        percentageGroup.style.display = 'block';
        fixedGroup.style.display = 'none';
    } else {
        percentageGroup.style.display = 'none';
        fixedGroup.style.display = 'block';
    }
}

// Save MEXC API configuration
async function saveMexcApiConfig() {
    const apiKey = document.getElementById('mexcApiKey').value;
    const apiSecret = document.getElementById('mexcApiSecret').value;
    const apiEnabled = document.getElementById('mexcApiEnabled').checked;

    try {
        const response = await fetch('/config/mexc-api', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                api_key: apiKey || null,
                api_secret: apiSecret || null,
                api_enabled: apiEnabled
            })
        });

        const data = await response.json();

        if (data.success) {
            alert('MEXC API configuration saved successfully!');
            // Clear sensitive fields
            document.getElementById('mexcApiKey').value = '';
            document.getElementById('mexcApiSecret').value = '';
        } else {
            alert('Failed to save MEXC API configuration: ' + (data.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error saving MEXC API config:', error);
        alert('Error saving MEXC API configuration');
    }
}

// Test MEXC connection
async function testMexcConnection() {
    try {
        const response = await fetch('/dashboard/api/test-mexc-connection');
        const data = await response.json();

        if (data.success && data.connection_test.status === 'connected') {
            alert('MEXC API connection successful!\n\nPublic API: ' + (data.connection_test.public_api ? 'OK' : 'Failed') + '\nPrivate API: ' + (data.connection_test.private_api ? 'OK' : 'Failed'));
        } else {
            alert('MEXC API connection failed:\n' + (data.connection_test?.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error testing MEXC connection:', error);
        alert('Error testing MEXC connection');
    }
}

// Save money management configuration
async function saveMoneyManagementConfig() {
    const config = {
        trading_symbol: document.getElementById('tradingSymbol').value,
        leverage: parseInt(document.getElementById('leverage').value),
        position_size_type: document.getElementById('positionSizeType').value,
        position_size_percentage: parseFloat(document.getElementById('positionSizePercentage').value),
        position_size_fixed: parseFloat(document.getElementById('positionSizeFixed').value),
        max_position_amount: parseFloat(document.getElementById('maxPositionAmount').value),
        use_max_position_limit: document.getElementById('useMaxPositionLimit').checked
    };

    // Validate inputs
    if (config.leverage < 1 || config.leverage > 100) {
        alert('Leverage must be between 1 and 100');
        return;
    }

    if (config.position_size_type === 'percentage' && (config.position_size_percentage < 0 || config.position_size_percentage > 100)) {
        alert('Position size percentage must be between 0 and 100');
        return;
    }

    if (config.position_size_fixed < 0) {
        alert('Fixed position size must be positive');
        return;
    }

    if (config.max_position_amount < 0) {
        alert('Maximum position amount must be positive');
        return;
    }

    try {
        const response = await fetch('/config/money-management', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config)
        });

        const data = await response.json();

        if (data.success) {
            alert('Money management configuration saved successfully!');
        } else {
            alert('Failed to save money management configuration: ' + (data.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error saving money management config:', error);
        alert('Error saving money management configuration');
    }
}

// Initialize configuration page
document.addEventListener('DOMContentLoaded', function() {
    loadCurrentConfig();
});
</script>
{% endblock %}
