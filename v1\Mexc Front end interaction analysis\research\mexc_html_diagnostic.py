#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MEXC HTML Diagnostic Tool
Test each element individually and inspect HTML/DOM to see what actually happens.

APPROACH:
1. Try to populate ONE field → Check HTML to see if value is actually there
2. Try to click ONE button → Check HTML to see if popup/modal appeared
3. Try to change ONE tab → Check HTML to see if content changed
4. Use JavaScript DOM inspection instead of screenshots
"""

import os
import sys
import time
import logging
import json
from datetime import datetime
from playwright.sync_api import sync_playwright

class MEXCHTMLDiagnostic:
    """HTML/DOM diagnostic tool to see what actually works"""
    
    def __init__(self):
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
        self.logger = logging.getLogger(__name__)
        
        self.playwright = None
        self.browser = None
        self.page = None
        
        self.logger.info("🔍 HTML DIAGNOSTIC TOOL INITIALIZED")
    
    def connect(self):
        """Connect to browser"""
        self.logger.info("🔌 Connecting...")
        
        self.playwright = sync_playwright().start()
        self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
        
        context = self.browser.contexts[0]
        for page in context.pages:
            if 'mexc.com' in (page.url or ''):
                self.page = page
                break
        
        self.logger.info(f"✅ Connected: {self.page.url}")
    
    def save_html_snapshot(self, name, description=""):
        """Save HTML snapshot for inspection"""
        try:
            html_content = self.page.content()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"html_{name}_{timestamp}.html"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.logger.info(f"💾 HTML saved: {filename} - {description}")
            return filename
        except Exception as e:
            self.logger.error(f"HTML save error: {e}")
            return None
    
    def test_single_field_population(self, test_value="3.5"):
        """Test populating ONE field and check HTML to see if it worked"""
        self.logger.info(f"🔍 TESTING SINGLE FIELD POPULATION with value: {test_value}")
        
        # Save HTML before
        before_html = self.save_html_snapshot("before_field_test", "Before field population")
        
        field_test_script = f"""
        () => {{
            console.log('🔍 Testing single field population...');
            
            const testValue = '{test_value}';
            const results = {{
                fields_found: [],
                population_attempts: [],
                html_verification: []
            }};
            
            // Find all visible input fields
            const inputs = document.querySelectorAll('input');
            const visibleInputs = [];
            
            inputs.forEach((input, index) => {{
                const rect = input.getBoundingClientRect();
                const style = window.getComputedStyle(input);
                
                if (rect.width > 0 && rect.height > 0 && 
                    style.display !== 'none' && 
                    !input.disabled) {{
                    
                    visibleInputs.push({{
                        element: input,
                        index: index,
                        type: input.type || 'text',
                        placeholder: input.placeholder || '',
                        name: input.name || '',
                        className: input.className || '',
                        value_before: input.value || '',
                        position: {{
                            x: Math.round(rect.x),
                            y: Math.round(rect.y)
                        }}
                    }});
                }}
            }});
            
            console.log(`Found ${{visibleInputs.length}} visible input fields`);
            
            // Test EACH field individually
            visibleInputs.forEach((fieldInfo, testIndex) => {{
                const input = fieldInfo.element;
                
                console.log(`\\n=== TESTING FIELD ${{testIndex}} ===`);
                console.log(`Type: ${{fieldInfo.type}}`);
                console.log(`Placeholder: ${{fieldInfo.placeholder}}`);
                console.log(`Name: ${{fieldInfo.name}}`);
                console.log(`Class: ${{fieldInfo.className}}`);
                console.log(`Position: ${{fieldInfo.position.x}}, ${{fieldInfo.position.y}}`);
                console.log(`Value before: "${{fieldInfo.value_before}}"`);
                
                const attempt = {{
                    field_index: testIndex,
                    field_info: fieldInfo,
                    methods_tried: [],
                    value_before: fieldInfo.value_before,
                    value_after: '',
                    success: false
                }};
                
                try {{
                    // METHOD 1: Basic fill
                    console.log('Method 1: Basic fill...');
                    input.focus();
                    input.value = '';
                    input.value = testValue;
                    input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                    input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    
                    let currentValue = input.value;
                    attempt.methods_tried.push({{
                        method: 'basic_fill',
                        result: currentValue,
                        success: currentValue === testValue
                    }});
                    console.log(`Basic fill result: "${{currentValue}}"`);
                    
                    // METHOD 2: Aggressive fill (if basic failed)
                    if (currentValue !== testValue) {{
                        console.log('Method 2: Aggressive fill...');
                        
                        input.focus();
                        input.select();
                        input.value = '';
                        
                        // Type character by character
                        for (let char of testValue) {{
                            input.value += char;
                            input.dispatchEvent(new KeyboardEvent('keydown', {{ key: char, bubbles: true }}));
                            input.dispatchEvent(new KeyboardEvent('keyup', {{ key: char, bubbles: true }}));
                            input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        }}
                        
                        input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        input.dispatchEvent(new Event('blur', {{ bubbles: true }}));
                        
                        currentValue = input.value;
                        attempt.methods_tried.push({{
                            method: 'aggressive_fill',
                            result: currentValue,
                            success: currentValue === testValue
                        }});
                        console.log(`Aggressive fill result: "${{currentValue}}"`);
                    }}
                    
                    // METHOD 3: Force attribute (if still failed)
                    if (currentValue !== testValue) {{
                        console.log('Method 3: Force attribute...');
                        
                        input.setAttribute('value', testValue);
                        input.value = testValue;
                        
                        // Force React/Vue update
                        const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set;
                        nativeInputValueSetter.call(input, testValue);
                        
                        input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        
                        currentValue = input.value;
                        attempt.methods_tried.push({{
                            method: 'force_attribute',
                            result: currentValue,
                            success: currentValue === testValue
                        }});
                        console.log(`Force attribute result: "${{currentValue}}"`);
                    }}
                    
                    attempt.value_after = currentValue;
                    attempt.success = currentValue === testValue;
                    
                    console.log(`FINAL RESULT for field ${{testIndex}}: "${{currentValue}}" (success: ${{attempt.success}})`);
                    
                }} catch (error) {{
                    console.log(`ERROR in field ${{testIndex}}: ${{error.message}}`);
                    attempt.error = error.message;
                }}
                
                results.population_attempts.push(attempt);
            }});
            
            // HTML VERIFICATION - Check actual DOM state
            console.log('\\n=== HTML VERIFICATION ===');
            visibleInputs.forEach((fieldInfo, index) => {{
                const input = fieldInfo.element;
                const verification = {{
                    field_index: index,
                    dom_value: input.value,
                    attribute_value: input.getAttribute('value'),
                    outer_html: input.outerHTML.substring(0, 200),
                    parent_html: input.parentElement ? input.parentElement.outerHTML.substring(0, 300) : 'no parent'
                }};
                
                results.html_verification.push(verification);
                console.log(`Field ${{index}} DOM verification:`);
                console.log(`  DOM value: "${{verification.dom_value}}"`);
                console.log(`  Attribute value: "${{verification.attribute_value}}"`);
                console.log(`  HTML: ${{verification.outer_html}}`);
            }});
            
            return {{
                success: true,
                results: results,
                summary: {{
                    total_fields: visibleInputs.length,
                    successful_populations: results.population_attempts.filter(a => a.success).length
                }}
            }};
        }}
        """
        
        try:
            result = self.page.evaluate(field_test_script)
            
            # Save HTML after
            after_html = self.save_html_snapshot("after_field_test", "After field population")
            
            if result.get('success'):
                summary = result.get('summary', {})
                results_data = result.get('results', {})
                
                self.logger.info(f"📊 FIELD POPULATION TEST RESULTS:")
                self.logger.info(f"   Total fields: {summary.get('total_fields', 0)}")
                self.logger.info(f"   Successful populations: {summary.get('successful_populations', 0)}")
                
                # Detailed results for each field
                for attempt in results_data.get('population_attempts', []):
                    field_idx = attempt.get('field_index', 0)
                    field_info = attempt.get('field_info', {})
                    success = attempt.get('success', False)
                    value_before = attempt.get('value_before', '')
                    value_after = attempt.get('value_after', '')
                    
                    status = "✅ SUCCESS" if success else "❌ FAILED"
                    self.logger.info(f"   Field {field_idx}: {status}")
                    self.logger.info(f"     Type: {field_info.get('type', 'unknown')}")
                    self.logger.info(f"     Placeholder: {field_info.get('placeholder', 'none')}")
                    self.logger.info(f"     Class: {field_info.get('className', 'none')}")
                    self.logger.info(f"     Before: '{value_before}' → After: '{value_after}'")
                    
                    # Show methods tried
                    for method in attempt.get('methods_tried', []):
                        method_status = "✅" if method.get('success') else "❌"
                        self.logger.info(f"       {method_status} {method.get('method')}: '{method.get('result', '')}'")
                
                # HTML verification
                self.logger.info(f"🔍 HTML VERIFICATION:")
                for verification in results_data.get('html_verification', []):
                    field_idx = verification.get('field_index', 0)
                    dom_value = verification.get('dom_value', '')
                    attr_value = verification.get('attribute_value', '')
                    
                    self.logger.info(f"   Field {field_idx} HTML state:")
                    self.logger.info(f"     DOM value: '{dom_value}'")
                    self.logger.info(f"     Attribute value: '{attr_value}'")
                    self.logger.info(f"     HTML: {verification.get('outer_html', '')[:100]}...")
                
                return {
                    'success': summary.get('successful_populations', 0) > 0,
                    'total_fields': summary.get('total_fields', 0),
                    'successful_fields': summary.get('successful_populations', 0),
                    'detailed_results': results_data,
                    'before_html': before_html,
                    'after_html': after_html
                }
            else:
                self.logger.error("❌ Field population test failed")
                return {'success': False, 'error': 'Script execution failed'}
                
        except Exception as e:
            self.logger.error(f"❌ Field population test exception: {e}")
            return {'success': False, 'error': str(e)}

    def test_single_button_click(self, button_class="component_longBtn__eazYU"):
        """Test clicking ONE button and check HTML to see if popup/modal appeared"""
        self.logger.info(f"🔍 TESTING SINGLE BUTTON CLICK: {button_class}")

        # Save HTML before
        before_html = self.save_html_snapshot("before_button_test", "Before button click")

        button_test_script = f"""
        () => {{
            console.log('🔍 Testing single button click...');

            const results = {{
                button_found: false,
                button_info: null,
                click_attempts: [],
                html_before: null,
                html_after: null,
                dom_changes: []
            }};

            // Find the target button
            const button = document.querySelector('button.{button_class}');

            if (!button) {{
                console.log('❌ Target button not found');
                return {{ success: false, error: 'Button not found', results: results }};
            }}

            results.button_found = true;
            results.button_info = {{
                text: button.textContent || '',
                className: button.className || '',
                disabled: button.disabled,
                style: button.style.cssText || '',
                position: {{
                    x: Math.round(button.getBoundingClientRect().x),
                    y: Math.round(button.getBoundingClientRect().y)
                }},
                outer_html: button.outerHTML.substring(0, 300)
            }};

            console.log('=== BUTTON INFO ===');
            console.log(`Text: "${{results.button_info.text}}"`);
            console.log(`Class: ${{results.button_info.className}}`);
            console.log(`Disabled: ${{results.button_info.disabled}}`);
            console.log(`Position: ${{results.button_info.position.x}}, ${{results.button_info.position.y}}`);
            console.log(`HTML: ${{results.button_info.outer_html}}`);

            // Record HTML state before click
            results.html_before = {{
                modals: document.querySelectorAll('.ant-modal, .modal, [role="dialog"]').length,
                notifications: document.querySelectorAll('.ant-notification, .ant-message').length,
                popups: document.querySelectorAll('.popup, .overlay').length,
                body_length: document.body.innerHTML.length,
                modal_html: Array.from(document.querySelectorAll('.ant-modal, .modal, [role="dialog"]')).map(m => m.outerHTML.substring(0, 200)),
                notification_html: Array.from(document.querySelectorAll('.ant-notification, .ant-message')).map(n => n.outerHTML.substring(0, 200))
            }};

            console.log('=== HTML STATE BEFORE CLICK ===');
            console.log(`Modals: ${{results.html_before.modals}}`);
            console.log(`Notifications: ${{results.html_before.notifications}}`);
            console.log(`Popups: ${{results.html_before.popups}}`);
            console.log(`Body length: ${{results.html_before.body_length}}`);

            // CLICK ATTEMPTS
            console.log('\\n=== CLICK ATTEMPTS ===');

            // METHOD 1: Simple click
            try {{
                console.log('Method 1: Simple click...');
                button.click();
                results.click_attempts.push({{ method: 'simple_click', success: true }});
                console.log('✅ Simple click executed');
            }} catch (error) {{
                results.click_attempts.push({{ method: 'simple_click', success: false, error: error.message }});
                console.log(`❌ Simple click failed: ${{error.message}}`);
            }}

            // METHOD 2: Event dispatch
            try {{
                console.log('Method 2: Event dispatch...');
                const clickEvent = new Event('click', {{ bubbles: true, cancelable: true }});
                button.dispatchEvent(clickEvent);
                results.click_attempts.push({{ method: 'event_dispatch', success: true }});
                console.log('✅ Event dispatch executed');
            }} catch (error) {{
                results.click_attempts.push({{ method: 'event_dispatch', success: false, error: error.message }});
                console.log(`❌ Event dispatch failed: ${{error.message}}`);
            }}

            // METHOD 3: Force click
            try {{
                console.log('Method 3: Force click...');
                button.disabled = false;
                button.style.pointerEvents = 'auto';
                button.focus();
                button.click();
                results.click_attempts.push({{ method: 'force_click', success: true }});
                console.log('✅ Force click executed');
            }} catch (error) {{
                results.click_attempts.push({{ method: 'force_click', success: false, error: error.message }});
                console.log(`❌ Force click failed: ${{error.message}}`);
            }}

            // Wait for potential responses
            return new Promise((resolve) => {{
                setTimeout(() => {{
                    console.log('\\n=== CHECKING FOR RESPONSES ===');

                    // Record HTML state after click
                    results.html_after = {{
                        modals: document.querySelectorAll('.ant-modal, .modal, [role="dialog"]').length,
                        notifications: document.querySelectorAll('.ant-notification, .ant-message').length,
                        popups: document.querySelectorAll('.popup, .overlay').length,
                        body_length: document.body.innerHTML.length,
                        modal_html: Array.from(document.querySelectorAll('.ant-modal, .modal, [role="dialog"]')).map(m => m.outerHTML.substring(0, 200)),
                        notification_html: Array.from(document.querySelectorAll('.ant-notification, .ant-message')).map(n => n.outerHTML.substring(0, 200))
                    }};

                    console.log('=== HTML STATE AFTER CLICK ===');
                    console.log(`Modals: ${{results.html_after.modals}}`);
                    console.log(`Notifications: ${{results.html_after.notifications}}`);
                    console.log(`Popups: ${{results.html_after.popups}}`);
                    console.log(`Body length: ${{results.html_after.body_length}}`);

                    // Detect changes
                    const modalChange = results.html_after.modals - results.html_before.modals;
                    const notificationChange = results.html_after.notifications - results.html_before.notifications;
                    const popupChange = results.html_after.popups - results.html_before.popups;
                    const bodyChange = results.html_after.body_length - results.html_before.body_length;

                    if (modalChange > 0) {{
                        results.dom_changes.push({{ type: 'modal', change: modalChange, new_html: results.html_after.modal_html }});
                        console.log(`✅ NEW MODALS DETECTED: ${{modalChange}}`);
                    }}
                    if (notificationChange > 0) {{
                        results.dom_changes.push({{ type: 'notification', change: notificationChange, new_html: results.html_after.notification_html }});
                        console.log(`✅ NEW NOTIFICATIONS DETECTED: ${{notificationChange}}`);
                    }}
                    if (popupChange > 0) {{
                        results.dom_changes.push({{ type: 'popup', change: popupChange }});
                        console.log(`✅ NEW POPUPS DETECTED: ${{popupChange}}`);
                    }}
                    if (Math.abs(bodyChange) > 1000) {{
                        results.dom_changes.push({{ type: 'content', change: bodyChange }});
                        console.log(`✅ SIGNIFICANT CONTENT CHANGE: ${{bodyChange}} characters`);
                    }}

                    if (results.dom_changes.length === 0) {{
                        console.log('❌ NO DOM CHANGES DETECTED');
                    }}

                    resolve({{
                        success: true,
                        results: results,
                        summary: {{
                            button_found: results.button_found,
                            click_attempts: results.click_attempts.length,
                            successful_clicks: results.click_attempts.filter(a => a.success).length,
                            dom_changes: results.dom_changes.length,
                            response_detected: results.dom_changes.length > 0
                        }}
                    }});
                }}, 3000); // Wait 3 seconds for responses
            }});
        }}
        """

        try:
            result = self.page.evaluate(button_test_script)

            # Save HTML after
            after_html = self.save_html_snapshot("after_button_test", "After button click")

            if result.get('success'):
                summary = result.get('summary', {})
                results_data = result.get('results', {})

                self.logger.info(f"📊 BUTTON CLICK TEST RESULTS:")
                self.logger.info(f"   Button found: {summary.get('button_found', False)}")
                self.logger.info(f"   Click attempts: {summary.get('click_attempts', 0)}")
                self.logger.info(f"   Successful clicks: {summary.get('successful_clicks', 0)}")
                self.logger.info(f"   DOM changes: {summary.get('dom_changes', 0)}")
                self.logger.info(f"   Response detected: {summary.get('response_detected', False)}")

                # Button info
                button_info = results_data.get('button_info', {})
                if button_info:
                    self.logger.info(f"🔘 BUTTON DETAILS:")
                    self.logger.info(f"   Text: '{button_info.get('text', '')}'")
                    self.logger.info(f"   Disabled: {button_info.get('disabled', 'unknown')}")
                    self.logger.info(f"   Position: {button_info.get('position', {})}")

                # Click attempts
                for attempt in results_data.get('click_attempts', []):
                    status = "✅" if attempt.get('success') else "❌"
                    method = attempt.get('method', 'unknown')
                    self.logger.info(f"   {status} {method}")
                    if not attempt.get('success'):
                        self.logger.info(f"     Error: {attempt.get('error', 'unknown')}")

                # DOM changes
                dom_changes = results_data.get('dom_changes', [])
                if dom_changes:
                    self.logger.info(f"🔍 DOM CHANGES DETECTED:")
                    for change in dom_changes:
                        change_type = change.get('type', 'unknown')
                        change_count = change.get('change', 0)
                        self.logger.info(f"   ✅ {change_type}: {change_count} new elements")

                        # Show HTML of new elements
                        if 'new_html' in change:
                            for html in change.get('new_html', []):
                                self.logger.info(f"     HTML: {html[:150]}...")
                else:
                    self.logger.info(f"❌ NO DOM CHANGES - Button click had no effect")

                return {
                    'success': summary.get('response_detected', False),
                    'button_found': summary.get('button_found', False),
                    'dom_changes': summary.get('dom_changes', 0),
                    'detailed_results': results_data,
                    'before_html': before_html,
                    'after_html': after_html
                }
            else:
                self.logger.error("❌ Button click test failed")
                return {'success': False, 'error': 'Script execution failed'}

        except Exception as e:
            self.logger.error(f"❌ Button click test exception: {e}")
            return {'success': False, 'error': str(e)}

    def run_html_diagnostics(self):
        """Run complete HTML diagnostics"""
        self.logger.info("🔍 STARTING HTML DIAGNOSTICS")

        try:
            self.connect()

            # Test 1: Field population
            self.logger.info("\n" + "="*50)
            self.logger.info("TEST 1: FIELD POPULATION DIAGNOSTIC")
            self.logger.info("="*50)
            field_result = self.test_single_field_population("3.5")

            # Test 2: Button click
            self.logger.info("\n" + "="*50)
            self.logger.info("TEST 2: BUTTON CLICK DIAGNOSTIC")
            self.logger.info("="*50)
            button_result = self.test_single_button_click("component_longBtn__eazYU")

            # Summary
            self.logger.info("\n" + "="*50)
            self.logger.info("DIAGNOSTIC SUMMARY")
            self.logger.info("="*50)

            field_success = field_result.get('success', False)
            button_success = button_result.get('success', False)

            self.logger.info(f"Field Population: {'✅ WORKING' if field_success else '❌ NOT WORKING'}")
            if field_success:
                self.logger.info(f"  {field_result.get('successful_fields', 0)}/{field_result.get('total_fields', 0)} fields populated")

            self.logger.info(f"Button Click: {'✅ WORKING' if button_success else '❌ NOT WORKING'}")
            if button_success:
                self.logger.info(f"  {button_result.get('dom_changes', 0)} DOM changes detected")

            if not field_success and not button_success:
                self.logger.info("❌ NOTHING IS WORKING - Need to investigate further")
            elif field_success and not button_success:
                self.logger.info("⚠️ FIELDS WORK but BUTTONS DON'T - Form validation issue?")
            elif not field_success and button_success:
                self.logger.info("⚠️ BUTTONS WORK but FIELDS DON'T - Field detection issue?")
            else:
                self.logger.info("✅ BOTH WORKING - Automation should work!")

            return {
                'field_result': field_result,
                'button_result': button_result,
                'overall_success': field_success or button_success
            }

        except Exception as e:
            self.logger.error(f"Diagnostic error: {e}")
            return {'error': str(e)}

    def cleanup(self):
        """Cleanup"""
        try:
            if self.playwright:
                self.playwright.stop()
        except:
            pass

def main():
    print("""
🔍 MEXC HTML DIAGNOSTIC TOOL
============================
NO SCREENSHOTS - JUST HTML/DOM INSPECTION

TESTS:
1. 📝 Field Population → Check HTML to see if values are actually there
2. 🔘 Button Click → Check HTML to see if popups/modals appeared
3. 📑 Tab Navigation → Check HTML to see if content changed

APPROACH:
- Test ONE element at a time
- Inspect HTML/DOM before and after each action
- Use JavaScript to see what actually happens
- Find out WHY automation isn't working
    """)

    diagnostic = MEXCHTMLDiagnostic()

    try:
        results = diagnostic.run_html_diagnostics()

        if 'error' in results:
            print(f"\n❌ Diagnostic failed: {results['error']}")
        else:
            print(f"\n🎯 DIAGNOSTIC COMPLETED - Check logs above for detailed results")

    except KeyboardInterrupt:
        print("\n👋 Diagnostic interrupted")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        diagnostic.cleanup()

if __name__ == "__main__":
    main()
