const crypto = require('crypto');
const axios = require('axios');

class MexcPermissionTester {
    constructor(apiKey, secretKey) {
        this.apiKey = apiKey;
        this.secretKey = secretKey;
        this.baseURL = 'https://api.mexc.com';
    }

    generateSignature(queryString, secretKey) {
        return crypto
            .createHmac('sha256', secretKey)
            .update(queryString)
            .digest('hex');
    }

    async testEndpoint(method, endpoint, requiresAuth = false) {
        try {
            let url = `${this.baseURL}${endpoint}`;
            let headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'MEXC-Permission-Test/1.0.0'
            };

            if (requiresAuth) {
                const timestamp = Date.now();
                const queryString = `timestamp=${timestamp}`;
                const signature = this.generateSignature(queryString, this.secretKey);
                
                url += `?${queryString}&signature=${signature}`;
                headers['X-MEXC-APIKEY'] = this.apiKey;
            }

            const response = await axios({
                method,
                url,
                headers,
                timeout: 10000
            });

            return {
                success: true,
                status: response.status,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                status: error.response?.status || 'Network Error',
                error: error.response?.data || error.message
            };
        }
    }

    async runPermissionTest() {
        console.log('🔐 MEXC API Permissions Test');
        console.log('============================');
        console.log(`API Key: ${this.apiKey}`);
        console.log(`Secret: ${this.secretKey.substring(0, 8)}...`);

        const tests = [
            // Public endpoints (no auth required)
            { name: 'Server Time', method: 'GET', endpoint: '/api/v3/time', auth: false },
            { name: 'Exchange Info', method: 'GET', endpoint: '/api/v3/exchangeInfo', auth: false },
            
            // Spot account endpoints
            { name: 'Spot Account Info', method: 'GET', endpoint: '/api/v3/account', auth: true },
            { name: 'Spot Account Status', method: 'GET', endpoint: '/api/v3/accountStatus', auth: true },
            
            // Alternative spot endpoints
            { name: 'Capital Config', method: 'GET', endpoint: '/api/v3/capital/config/getall', auth: true },
            { name: 'User Asset', method: 'GET', endpoint: '/api/v3/asset/getUserAsset', auth: true },
            
            // Futures endpoints
            { name: 'Futures Account V1', method: 'GET', endpoint: '/fapi/v1/account', auth: true },
            { name: 'Futures Account V2', method: 'GET', endpoint: '/fapi/v2/account', auth: true },
            { name: 'Futures Balance', method: 'GET', endpoint: '/fapi/v1/balance', auth: true },
            
            // Margin endpoints
            { name: 'Margin Account', method: 'GET', endpoint: '/sapi/v1/margin/account', auth: true },
            { name: 'Cross Margin Account', method: 'GET', endpoint: '/sapi/v1/margin/crossMarginData', auth: true },
            
            // Wallet endpoints
            { name: 'Wallet Status', method: 'GET', endpoint: '/sapi/v1/system/status', auth: false },
            { name: 'All Coins Info', method: 'GET', endpoint: '/sapi/v1/capital/config/getall', auth: true },
        ];

        console.log('\n🧪 Testing Endpoints...\n');

        const results = {
            public: { success: 0, total: 0 },
            private: { success: 0, total: 0 },
            details: []
        };

        for (const test of tests) {
            console.log(`📋 ${test.name}...`);
            
            const result = await this.testEndpoint(test.method, test.endpoint, test.auth);
            
            if (result.success) {
                console.log(`   ✅ Success (${result.status})`);
                
                // Show relevant data for balance-related endpoints
                if (test.endpoint.includes('account') || test.endpoint.includes('balance')) {
                    if (result.data.balances) {
                        const nonZero = result.data.balances.filter(b => 
                            parseFloat(b.free) > 0 || parseFloat(b.locked) > 0
                        );
                        console.log(`      💰 Non-zero balances: ${nonZero.length}`);
                        
                        nonZero.forEach(balance => {
                            const total = parseFloat(balance.free) + parseFloat(balance.locked);
                            console.log(`         ${balance.asset}: ${total}`);
                        });
                    }
                    
                    if (result.data.totalWalletBalance) {
                        console.log(`      💰 Total Wallet: ${result.data.totalWalletBalance} USDT`);
                    }
                    
                    if (result.data.assets) {
                        const nonZeroAssets = result.data.assets.filter(a => 
                            parseFloat(a.walletBalance) > 0
                        );
                        console.log(`      💰 Non-zero assets: ${nonZeroAssets.length}`);
                    }
                }
                
                if (test.auth) {
                    results.private.success++;
                } else {
                    results.public.success++;
                }
            } else {
                console.log(`   ❌ Failed (${result.status})`);
                console.log(`      Error: ${JSON.stringify(result.error)}`);
            }
            
            if (test.auth) {
                results.private.total++;
            } else {
                results.public.total++;
            }
            
            results.details.push({
                name: test.name,
                endpoint: test.endpoint,
                auth: test.auth,
                success: result.success,
                status: result.status,
                error: result.error
            });
        }

        console.log('\n📊 PERMISSION TEST SUMMARY');
        console.log('===========================');
        console.log(`🌐 Public Endpoints: ${results.public.success}/${results.public.total} successful`);
        console.log(`🔐 Private Endpoints: ${results.private.success}/${results.private.total} successful`);
        
        const totalSuccess = results.public.success + results.private.success;
        const totalTests = results.public.total + results.private.total;
        console.log(`📈 Overall Success: ${totalSuccess}/${totalTests}`);

        // Analyze permissions
        console.log('\n🔍 PERMISSION ANALYSIS');
        console.log('======================');
        
        if (results.public.success === results.public.total) {
            console.log('✅ Public API access: Working');
        } else {
            console.log('❌ Public API access: Issues detected');
        }
        
        if (results.private.success > 0) {
            console.log('✅ Private API access: Working (at least partially)');
        } else {
            console.log('❌ Private API access: No successful private endpoints');
        }
        
        // Check specific permissions
        const accountTest = results.details.find(r => r.endpoint === '/api/v3/account');
        if (accountTest?.success) {
            console.log('✅ Spot trading permissions: Enabled');
        } else {
            console.log('❌ Spot trading permissions: Disabled or limited');
        }
        
        const futuresTests = results.details.filter(r => r.endpoint.startsWith('/fapi/'));
        const futuresWorking = futuresTests.some(t => t.success);
        if (futuresWorking) {
            console.log('✅ Futures trading permissions: Enabled');
        } else {
            console.log('❌ Futures trading permissions: Disabled or not available');
        }

        console.log('\n💡 RECOMMENDATIONS');
        console.log('==================');
        
        if (results.private.success === 0) {
            console.log('🔧 Check API key permissions in MEXC account settings');
            console.log('🔧 Ensure "Spot Trading" permission is enabled');
            console.log('🔧 Verify API key is not expired');
        }
        
        if (!futuresWorking) {
            console.log('🔧 Enable "Futures Trading" permission if needed');
            console.log('🔧 MEXC may use different futures API endpoints');
        }
        
        console.log('🔧 Check if balance is in a different account type (Margin, Savings, etc.)');
        console.log('🔧 Verify the expected balance amount and currency');
    }
}

// Test with provided credentials
async function main() {
    const apiKey = 'mx0vgl6bPv2Frz96j0';
    const secretKey = 'ac416e95c9b34ec0ba6d3210c18a1e76';
    
    const tester = new MexcPermissionTester(apiKey, secretKey);
    await tester.runPermissionTest();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = MexcPermissionTester;
