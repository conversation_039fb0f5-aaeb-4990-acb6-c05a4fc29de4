const axios = require('axios');

async function finalSystemVerification() {
    console.log('🎯 FINAL SYSTEM VERIFICATION');
    console.log('============================');
    console.log('Verifying complete MEXC futures trading system with browser balance integration');
    console.log('');

    const results = {
        services: { mexc: false, webhook: false },
        balance: { accessible: false, source: 'unknown', value: 0 },
        webhook: { functional: false, moneyManagement: false },
        frontend: { updated: false, showsBalance: false }
    };

    try {
        // 1. Service Health Check
        console.log('1️⃣ SERVICE HEALTH CHECK');
        console.log('========================');
        
        try {
            const mexcHealth = await axios.get('http://localhost:3000/health');
            console.log(`✅ MEXC Futures Trader: ${mexcHealth.data.status} (v${mexcHealth.data.version})`);
            results.services.mexc = true;
        } catch (error) {
            console.log(`❌ MEXC Futures Trader: OFFLINE`);
            return results;
        }

        try {
            const webhookHealth = await axios.get('http://localhost:4000/health');
            console.log(`✅ TradingView Webhook Listener: ${webhookHealth.data.status} (v${webhookHealth.data.version})`);
            results.services.webhook = true;
        } catch (error) {
            console.log(`❌ TradingView Webhook Listener: OFFLINE`);
            return results;
        }

        // 2. Balance Integration Test
        console.log('\n2️⃣ BALANCE INTEGRATION TEST');
        console.log('============================');
        
        try {
            const balanceResponse = await axios.get('http://localhost:4000/api/balance');
            const balance = balanceResponse.data;
            
            console.log(`   Success: ${balance.success}`);
            console.log(`   Balance: ${balance.balance.total} USDT`);
            console.log(`   Source: ${balance.source}`);
            console.log(`   Timestamp: ${balance.timestamp}`);
            
            if (balance.balance.raw) {
                console.log(`   Raw Text: "${balance.balance.raw}"`);
            }
            
            results.balance.accessible = balance.success;
            results.balance.source = balance.source;
            results.balance.value = balance.balance.total;
            
            if (balance.source === 'frontend') {
                console.log('✅ Browser balance integration working!');
            } else if (balance.source === 'api') {
                console.log('⚠️ Using API balance (browser balance not available)');
            }
            
        } catch (error) {
            console.log(`❌ Balance integration failed: ${error.message}`);
        }

        // 3. System Status Check
        console.log('\n3️⃣ SYSTEM STATUS CHECK');
        console.log('=======================');
        
        try {
            const statusResponse = await axios.get('http://localhost:4000/api/status');
            const status = statusResponse.data;
            
            console.log(`   Bot Active: ${status.botActive}`);
            console.log(`   MEXC Connected: ${status.mexcConnected}`);
            console.log(`   Configuration: ${status.configured ? 'Complete' : 'Incomplete'}`);
            console.log(`   Balance: ${status.balance.total} USDT (${status.balance.source || 'unknown'})`);
            
            if (status.balance.raw) {
                console.log(`   Balance Raw: "${status.balance.raw}"`);
            }
            
        } catch (error) {
            console.log(`⚠️ Status check failed: ${error.message}`);
        }

        // 4. Webhook Functionality Test
        console.log('\n4️⃣ WEBHOOK FUNCTIONALITY TEST');
        console.log('==============================');
        
        const testSignal = {
            symbol: "TRUUSDT",
            trade: "open_long",
            last_price: "0.03295",
            leverage: "2"
        };

        try {
            console.log(`   Sending test signal: ${JSON.stringify(testSignal)}`);
            const webhookResponse = await axios.post('http://localhost:4000/webhook', testSignal);
            
            console.log(`   ✅ Webhook Success: ${webhookResponse.data.success}`);
            
            if (webhookResponse.data.success) {
                console.log(`   📊 Trade Details:`);
                console.log(`      Trade ID: ${webhookResponse.data.tradeId}`);
                console.log(`      Execution Time: ${webhookResponse.data.executionTime}ms`);
                console.log(`      Position Size: ${webhookResponse.data.positionSize} USDT`);
                console.log(`      Target Achieved: ${webhookResponse.data.targetAchieved ? 'YES' : 'NO'}`);
                
                if (webhookResponse.data.moneyManagement) {
                    console.log(`   💰 Money Management: ACTIVE`);
                    console.log(`      Balance Used: ${webhookResponse.data.balanceUsed || 'N/A'}`);
                    results.webhook.moneyManagement = true;
                } else {
                    console.log(`   💰 Money Management: DISABLED`);
                }
                
                results.webhook.functional = true;
            } else {
                console.log(`   ❌ Webhook Error: ${webhookResponse.data.error}`);
            }
            
        } catch (error) {
            console.log(`   ❌ Webhook test failed: ${error.message}`);
            if (error.response?.data) {
                console.log(`   Response: ${JSON.stringify(error.response.data)}`);
            }
        }

        // 5. Frontend Panel Check
        console.log('\n5️⃣ FRONTEND PANEL CHECK');
        console.log('========================');
        
        try {
            const frontendResponse = await axios.get('http://localhost:4000');
            if (frontendResponse.status === 200) {
                console.log('✅ Frontend panel accessible at http://localhost:4000');
                console.log('   Features available:');
                console.log('   - Configuration management');
                console.log('   - Balance display (browser-fetched)');
                console.log('   - Money management settings');
                console.log('   - Real-time status monitoring');
                results.frontend.updated = true;
                results.frontend.showsBalance = true;
            }
        } catch (error) {
            console.log(`❌ Frontend panel not accessible: ${error.message}`);
        }

        // 6. Final Results Summary
        console.log('\n📊 FINAL VERIFICATION RESULTS');
        console.log('==============================');
        
        const allServicesUp = results.services.mexc && results.services.webhook;
        const balanceWorking = results.balance.accessible;
        const webhookWorking = results.webhook.functional;
        const frontendWorking = results.frontend.updated;
        
        console.log(`🔧 Services: ${allServicesUp ? '✅ OPERATIONAL' : '❌ ISSUES'}`);
        console.log(`💰 Balance Integration: ${balanceWorking ? '✅ WORKING' : '❌ FAILED'}`);
        console.log(`📡 Webhook Processing: ${webhookWorking ? '✅ FUNCTIONAL' : '❌ FAILED'}`);
        console.log(`🖥️ Frontend Panel: ${frontendWorking ? '✅ ACCESSIBLE' : '❌ ISSUES'}`);
        
        if (results.balance.source === 'frontend') {
            console.log(`🌐 Browser Balance: ✅ INTEGRATED (${results.balance.value} USDT)`);
        } else {
            console.log(`🌐 Browser Balance: ⚠️ FALLBACK TO API (${results.balance.value} USDT)`);
        }
        
        if (results.webhook.moneyManagement) {
            console.log(`💼 Money Management: ✅ ACTIVE (using ${results.balance.source} balance)`);
        } else {
            console.log(`💼 Money Management: ⚠️ NEEDS CONFIGURATION`);
        }

        // 7. TradingView Setup Instructions
        console.log('\n🎯 TRADINGVIEW SETUP INSTRUCTIONS');
        console.log('==================================');
        
        if (allServicesUp && webhookWorking) {
            console.log('✅ System is READY for TradingView integration!');
            console.log('');
            console.log('📋 Setup Steps:');
            console.log('1. Open TradingView and create your strategy/indicator');
            console.log('2. Set up alerts with these settings:');
            console.log('   - Webhook URL: http://localhost:4000/webhook');
            console.log('   - Message format: {"symbol":"TRUUSDT","trade":"open_long","last_price":"{{close}}","leverage":"2"}');
            console.log('');
            console.log('📝 Available Trade Types:');
            console.log('   - "open_long"   : Open long position');
            console.log('   - "open_short"  : Open short position');
            console.log('   - "close_long"  : Close long position');
            console.log('   - "close_short" : Close short position');
            console.log('');
            console.log('⚙️ Configuration Panel: http://localhost:4000');
            console.log('📊 System Status: http://localhost:4000/api/status');
            console.log('💰 Balance Check: http://localhost:4000/api/balance');
            console.log('');
            console.log('🚀 The system will automatically:');
            console.log('   - Fetch balance from MEXC browser interface');
            console.log('   - Calculate position size based on money management');
            console.log('   - Execute trades via single browser automation');
            console.log('   - Provide sub-2 second execution times');
            console.log('   - Send Telegram alerts for login issues');
        } else {
            console.log('❌ System has issues that need to be resolved before TradingView integration');
        }

        return results;

    } catch (error) {
        console.error('❌ Verification failed:', error.message);
        return results;
    }
}

// Run verification
finalSystemVerification()
    .then(results => {
        console.log('\n🏁 VERIFICATION COMPLETE');
        console.log('=========================');
        
        const systemReady = results.services.mexc && 
                           results.services.webhook && 
                           results.balance.accessible && 
                           results.webhook.functional;
        
        if (systemReady) {
            console.log('🎉 SYSTEM IS PRODUCTION READY! 🎉');
        } else {
            console.log('⚠️ System needs attention before production use');
        }
    })
    .catch(console.error);
