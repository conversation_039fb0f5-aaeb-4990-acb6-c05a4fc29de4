const { chromium } = require('playwright');

class MEXCSpecificTrader {
    constructor() {
        this.browser = null;
        this.page = null;
        this.startTime = null;
    }

    async connectToRemoteBrowser() {
        console.log('🔗 Connecting to remote browser...');
        
        try {
            this.browser = await chromium.connectOverCDP('http://localhost:9222');
            const contexts = this.browser.contexts();
            
            if (contexts.length > 0) {
                const context = contexts[0];
                const pages = context.pages();
                this.page = pages.length > 0 ? pages[0] : await context.newPage();
            } else {
                const context = await this.browser.newContext();
                this.page = await context.newPage();
            }
            
            console.log('✅ Connected to remote browser');
            return true;
        } catch (error) {
            console.error('❌ Connection failed:', error.message);
            return false;
        }
    }

    async navigateToTRUUSDT() {
        console.log('🌐 Navigating to TRU_USDT futures page...');
        
        try {
            await this.page.goto('https://www.mexc.com/futures/TRU_USDT', {
                waitUntil: 'domcontentloaded',
                timeout: 15000
            });
            
            // Wait for page to load
            await this.page.waitForTimeout(3000);
            console.log('✅ Successfully navigated to TRU_USDT page');
            return true;
        } catch (error) {
            console.error('❌ Navigation failed:', error.message);
            return false;
        }
    }

    async executeSpecificWorkflow() {
        this.startTime = Date.now();
        console.log('🎯 Starting specific MEXC workflow...');
        
        try {
            // STEP 1: Find and populate Quantity(USDT) field
            console.log('🔍 Step 1: Looking for Quantity(USDT) field...');
            
            let quantityFieldFound = false;
            
            // Look for the field by text "Quantity(USDT)" and then find input below it
            const quantitySelectors = [
                // Look for input near "Quantity(USDT)" text
                'text=Quantity(USDT) >> .. >> input',
                'text=Quantity(USDT) >> xpath=following::input[1]',
                'text=Quantity >> .. >> input',
                // Direct input selectors that might be the quantity field
                'input[placeholder*="quantity"]',
                'input[placeholder*="amount"]',
                'input[placeholder*="USDT"]',
                // Look in common trading form areas
                '.trading-form input[type="number"]',
                '.order-form input[type="number"]',
                '.trade-panel input[type="number"]'
            ];

            for (const selector of quantitySelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    const isVisible = await element.isVisible({ timeout: 1000 });
                    
                    if (isVisible) {
                        console.log(`✅ Found quantity field with selector: ${selector}`);
                        
                        // Click and fill the field
                        await element.click();
                        await this.page.waitForTimeout(100);
                        
                        // Clear existing value and enter 0.3600
                        await element.fill('');
                        await element.type('0.3600');
                        
                        console.log('✅ Entered 0.3600 in quantity field');
                        quantityFieldFound = true;
                        break;
                    }
                } catch (error) {
                    continue;
                }
            }

            if (!quantityFieldFound) {
                console.log('⚠️ Quantity field not found, continuing anyway...');
            }

            await this.page.waitForTimeout(500);

            // STEP 2: Click "Open Long" button
            console.log('🔍 Step 2: Looking for Open Long button...');
            
            let openLongClicked = false;
            
            const openLongSelectors = [
                'button:has-text("Open Long")',
                'text=Open Long',
                '.open-long',
                '.long-btn',
                'button[class*="long"]',
                // Look for green button (Open Long is typically green)
                'button[style*="green"]',
                'button[class*="green"]'
            ];

            for (const selector of openLongSelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    const isVisible = await element.isVisible({ timeout: 1000 });
                    
                    if (isVisible) {
                        console.log(`✅ Found Open Long button with selector: ${selector}`);
                        
                        // Click the Open Long button
                        await element.click();
                        console.log('✅ Clicked Open Long button');
                        openLongClicked = true;
                        break;
                    }
                } catch (error) {
                    continue;
                }
            }

            if (!openLongClicked) {
                throw new Error('Could not find or click Open Long button');
            }

            await this.page.waitForTimeout(1000);

            // STEP 3: Wait for popup and click "Confirm"
            console.log('🔍 Step 3: Looking for insufficient margin popup...');
            
            let confirmClicked = false;
            
            // Look for the popup with "You have insufficient available margin!" text
            const popupSelectors = [
                'text=You have insufficient available margin!',
                'text=insufficient',
                'text=margin',
                '.popup',
                '.modal',
                '.dialog'
            ];

            // Wait a bit for popup to appear
            await this.page.waitForTimeout(1500);

            // Look for Confirm button in the popup
            const confirmSelectors = [
                'button:has-text("Confirm")',
                'text=Confirm',
                '.confirm-btn',
                'button[class*="confirm"]',
                // Look near the insufficient margin text
                'text=You have insufficient available margin! >> .. >> button:has-text("Confirm")',
                'text=insufficient >> .. >> button:has-text("Confirm")'
            ];

            for (const selector of confirmSelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    const isVisible = await element.isVisible({ timeout: 2000 });
                    
                    if (isVisible) {
                        console.log(`✅ Found Confirm button with selector: ${selector}`);
                        
                        // Click the Confirm button
                        await element.click();
                        console.log('✅ Clicked Confirm button');
                        confirmClicked = true;
                        break;
                    }
                } catch (error) {
                    continue;
                }
            }

            if (!confirmClicked) {
                console.log('⚠️ Confirm button not found - popup might not have appeared');
            }

            const executionTime = Date.now() - this.startTime;

            console.log('\n🏆 WORKFLOW RESULTS:');
            console.log('====================');
            console.log(`⏱️ Total execution time: ${executionTime}ms`);
            console.log(`🎯 Under 2 seconds: ${executionTime < 2000 ? '✅ YES' : '❌ NO'}`);
            console.log(`📊 Quantity field: ${quantityFieldFound ? '✅ FOUND & FILLED' : '❌ NOT FOUND'}`);
            console.log(`📈 Open Long: ${openLongClicked ? '✅ CLICKED' : '❌ NOT CLICKED'}`);
            console.log(`✅ Confirm: ${confirmClicked ? '✅ CLICKED' : '⚠️ NOT FOUND'}`);

            const result = {
                success: quantityFieldFound && openLongClicked,
                executionTime,
                quantityFieldFound,
                openLongClicked,
                confirmClicked,
                targetAchieved: executionTime < 2000,
                timestamp: new Date().toISOString()
            };

            require('fs').writeFileSync('mexc-specific-results.json', JSON.stringify(result, null, 2));

            return result;

        } catch (error) {
            const executionTime = Date.now() - this.startTime;
            console.error(`❌ Workflow failed after ${executionTime}ms:`, error.message);
            
            return {
                success: false,
                executionTime,
                error: error.message
            };
        }
    }

    async verifyWorkflowCompletion() {
        console.log('🔍 Verifying workflow completion...');
        
        try {
            // Look for any success messages or confirmations
            const successIndicators = [
                'text=success',
                'text=Success',
                'text=completed',
                'text=confirmed',
                '.success',
                '.toast-success'
            ];

            for (const indicator of successIndicators) {
                try {
                    const element = this.page.locator(indicator).first();
                    const isVisible = await element.isVisible({ timeout: 1000 });
                    if (isVisible) {
                        const text = await element.textContent();
                        console.log(`✅ Success indicator found: ${text}`);
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }

            console.log('⚠️ No specific success indicators found');
            return false;
        } catch (error) {
            console.log('❌ Verification error:', error.message);
            return false;
        }
    }
}

async function runMEXCSpecificTrader() {
    const trader = new MEXCSpecificTrader();
    
    try {
        console.log('🎯 MEXC SPECIFIC WORKFLOW TRADER');
        console.log('================================');
        console.log('📋 Workflow:');
        console.log('1. Navigate to https://www.mexc.com/futures/TRU_USDT');
        console.log('2. Find "Quantity(USDT)" field and enter 0.3600');
        console.log('3. Click "Open Long" button');
        console.log('4. Click "Confirm" on insufficient margin popup');
        console.log('');

        const connected = await trader.connectToRemoteBrowser();
        if (!connected) {
            throw new Error('Could not connect to remote browser');
        }

        const navigated = await trader.navigateToTRUUSDT();
        if (!navigated) {
            throw new Error('Could not navigate to TRU_USDT page');
        }

        console.log('🚀 Executing specific workflow...');
        
        const result = await trader.executeSpecificWorkflow();
        
        // Verify completion
        const verified = await trader.verifyWorkflowCompletion();
        
        if (result.success && result.targetAchieved) {
            console.log('\n🎯 WORKFLOW COMPLETED SUCCESSFULLY!');
            console.log('✅ All steps executed under 2 seconds!');
        } else if (result.success) {
            console.log('\n✅ Workflow completed successfully!');
            console.log(`⏱️ Time: ${result.executionTime}ms`);
        } else {
            console.log('\n❌ Workflow failed');
            console.log('Check the steps above for details');
        }

        if (verified) {
            console.log('🎉 Workflow completion verified!');
        }
        
        return result;
        
    } catch (error) {
        console.error('💥 MEXC specific trader failed:', error.message);
        return { success: false, error: error.message };
    }
}

if (require.main === module) {
    runMEXCSpecificTrader()
        .then(result => {
            console.log('\n🏁 MEXC specific workflow completed');
            process.exit(result.success ? 0 : 1);
        })
        .catch(error => {
            console.error('💥 Session crashed:', error);
            process.exit(1);
        });
}

module.exports = MEXCSpecificTrader;
