#!/usr/bin/env python3
"""
WEBASSEMBLY SIGNATURE ANALYZER
Hunt for WebAssembly modules that might contain the signature algorithm
"""

import json
import time
import base64
from playwright.sync_api import sync_playwright
from dotenv import dotenv_values

class WasmSignatureAnalyzer:
    """Analyze WebAssembly modules for signature generation"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        
        print("🔬 WEBASSEMBLY SIGNATURE ANALYZER")
        print("="*40)
        print("🎯 HUNTING FOR WASM SIGNATURE MODULES")
    
    def setup_wasm_hooks(self):
        """Setup hooks to intercept WebAssembly operations"""
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                return False
            
            self.context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                mexc_page = self.context.new_page()
                mexc_page.goto('https://futures.mexc.com/exchange/BTC_USDT', wait_until='domcontentloaded')
            
            self.page = mexc_page
            
            # Inject WASM and native crypto hooks
            self.page.evaluate("""
                window.wasmInterceptions = [];
                window.cryptoInterceptions = [];
                window.memoryAnalysis = [];
                
                console.log('🔬 Installing WASM and native crypto hooks...');
                
                // Hook WebAssembly instantiation
                if (window.WebAssembly) {
                    const originalInstantiate = WebAssembly.instantiate;
                    const originalInstantiateStreaming = WebAssembly.instantiateStreaming;
                    const originalCompile = WebAssembly.compile;
                    const originalCompileStreaming = WebAssembly.compileStreaming;
                    
                    WebAssembly.instantiate = function(...args) {
                        console.log('🔥 WebAssembly.instantiate called!', args);
                        
                        window.wasmInterceptions.push({
                            type: 'instantiate',
                            args: args.map(arg => typeof arg === 'object' ? '[object]' : arg),
                            timestamp: Date.now()
                        });
                        
                        const result = originalInstantiate.apply(this, args);
                        
                        // Hook the instance methods
                        if (result && result.then) {
                            result.then(instance => {
                                console.log('🔥 WASM instance created:', instance);
                                window.wasmInterceptions.push({
                                    type: 'instance_created',
                                    exports: Object.keys(instance.instance ? instance.instance.exports : {}),
                                    timestamp: Date.now()
                                });
                                
                                // Hook exported functions
                                if (instance.instance && instance.instance.exports) {
                                    for (const [name, func] of Object.entries(instance.instance.exports)) {
                                        if (typeof func === 'function') {
                                            const originalFunc = func;
                                            instance.instance.exports[name] = function(...args) {
                                                console.log(`🔥 WASM function ${name} called:`, args);
                                                
                                                window.wasmInterceptions.push({
                                                    type: 'function_call',
                                                    function: name,
                                                    args: args,
                                                    timestamp: Date.now()
                                                });
                                                
                                                const result = originalFunc.apply(this, args);
                                                
                                                console.log(`🔥 WASM function ${name} result:`, result);
                                                
                                                window.wasmInterceptions.push({
                                                    type: 'function_result',
                                                    function: name,
                                                    result: result,
                                                    timestamp: Date.now()
                                                });
                                                
                                                return result;
                                            };
                                        }
                                    }
                                }
                            });
                        }
                        
                        return result;
                    };
                    
                    WebAssembly.instantiateStreaming = function(...args) {
                        console.log('🔥 WebAssembly.instantiateStreaming called!', args);
                        
                        window.wasmInterceptions.push({
                            type: 'instantiate_streaming',
                            args: args.map(arg => typeof arg === 'object' ? '[object]' : arg),
                            timestamp: Date.now()
                        });
                        
                        return originalInstantiateStreaming.apply(this, args);
                    };
                }
                
                // Hook native crypto operations
                if (window.crypto && window.crypto.subtle) {
                    const originalDigest = window.crypto.subtle.digest;
                    const originalSign = window.crypto.subtle.sign;
                    const originalImportKey = window.crypto.subtle.importKey;
                    
                    window.crypto.subtle.digest = async function(algorithm, data) {
                        console.log('🔥 crypto.subtle.digest called:', algorithm, data);
                        
                        window.cryptoInterceptions.push({
                            type: 'digest',
                            algorithm: algorithm,
                            dataLength: data.byteLength || data.length,
                            timestamp: Date.now()
                        });
                        
                        const result = await originalDigest.apply(this, arguments);
                        
                        const resultHex = Array.from(new Uint8Array(result))
                            .map(b => b.toString(16).padStart(2, '0')).join('');
                        
                        console.log('🔥 crypto.subtle.digest result:', resultHex);
                        
                        window.cryptoInterceptions.push({
                            type: 'digest_result',
                            algorithm: algorithm,
                            result: resultHex,
                            timestamp: Date.now()
                        });
                        
                        return result;
                    };
                    
                    if (originalSign) {
                        window.crypto.subtle.sign = async function(algorithm, key, data) {
                            console.log('🔥 crypto.subtle.sign called:', algorithm, key, data);
                            
                            window.cryptoInterceptions.push({
                                type: 'sign',
                                algorithm: algorithm,
                                dataLength: data.byteLength || data.length,
                                timestamp: Date.now()
                            });
                            
                            const result = await originalSign.apply(this, arguments);
                            
                            const resultHex = Array.from(new Uint8Array(result))
                                .map(b => b.toString(16).padStart(2, '0')).join('');
                            
                            console.log('🔥 crypto.subtle.sign result:', resultHex);
                            
                            window.cryptoInterceptions.push({
                                type: 'sign_result',
                                algorithm: algorithm,
                                result: resultHex,
                                timestamp: Date.now()
                            });
                            
                            return result;
                        };
                    }
                }
                
                // Hook ArrayBuffer and Uint8Array operations (often used with crypto)
                const originalUint8Array = window.Uint8Array;
                window.Uint8Array = function(...args) {
                    const result = new originalUint8Array(...args);
                    
                    // Hook methods that might be used for crypto
                    const originalSet = result.set;
                    result.set = function(array, offset) {
                        if (array && array.length > 16) { // Potential crypto data
                            window.memoryAnalysis.push({
                                type: 'uint8array_set',
                                length: array.length,
                                offset: offset,
                                data: Array.from(array.slice(0, 32)), // First 32 bytes
                                timestamp: Date.now()
                            });
                        }
                        return originalSet.apply(this, arguments);
                    };
                    
                    return result;
                };
                
                console.log('✅ WASM and native crypto hooks installed!');
            """)
            
            print("✅ WASM hooks setup completed")
            return True
            
        except Exception as e:
            print(f"❌ Setup failed: {e}")
            return False
    
    def scan_for_wasm_modules(self):
        """Scan the page for WebAssembly modules"""
        
        print("\n🔍 SCANNING FOR WEBASSEMBLY MODULES")
        print("="*45)
        
        # Look for WASM files and modules
        wasm_scan = self.page.evaluate("""
            () => {
                const results = {
                    wasmFiles: [],
                    wasmModules: [],
                    wasmInstances: []
                };
                
                // Scan for .wasm files in network requests
                if (window.performance && window.performance.getEntriesByType) {
                    const resources = window.performance.getEntriesByType('resource');
                    for (const resource of resources) {
                        if (resource.name.includes('.wasm') || 
                            resource.name.includes('wasm') ||
                            resource.initiatorType === 'fetch' && resource.name.includes('binary')) {
                            results.wasmFiles.push({
                                url: resource.name,
                                size: resource.transferSize,
                                type: resource.initiatorType
                            });
                        }
                    }
                }
                
                // Look for WebAssembly objects in global scope
                function searchForWasm(obj, path = '', depth = 0) {
                    if (depth > 3) return;
                    
                    try {
                        for (const key in obj) {
                            const value = obj[key];
                            
                            if (value && typeof value === 'object') {
                                if (value.constructor && value.constructor.name === 'WebAssembly.Module') {
                                    results.wasmModules.push({
                                        path: path + '.' + key,
                                        type: 'Module'
                                    });
                                } else if (value.constructor && value.constructor.name === 'WebAssembly.Instance') {
                                    results.wasmInstances.push({
                                        path: path + '.' + key,
                                        exports: Object.keys(value.exports || {}),
                                        type: 'Instance'
                                    });
                                } else if (depth < 2) {
                                    searchForWasm(value, path + '.' + key, depth + 1);
                                }
                            }
                        }
                    } catch (e) {
                        // Ignore access errors
                    }
                }
                
                searchForWasm(window, 'window');
                
                return results;
            }
        """)
        
        print(f"✅ WASM scan completed!")
        print(f"   WASM files: {len(wasm_scan['wasmFiles'])}")
        print(f"   WASM modules: {len(wasm_scan['wasmModules'])}")
        print(f"   WASM instances: {len(wasm_scan['wasmInstances'])}")
        
        if wasm_scan['wasmFiles']:
            print(f"\n📁 WASM FILES FOUND:")
            for wasm_file in wasm_scan['wasmFiles']:
                print(f"   🔗 {wasm_file['url']} ({wasm_file['size']} bytes)")
        
        if wasm_scan['wasmModules']:
            print(f"\n📦 WASM MODULES FOUND:")
            for module in wasm_scan['wasmModules']:
                print(f"   📍 {module['path']}")
        
        if wasm_scan['wasmInstances']:
            print(f"\n⚙️ WASM INSTANCES FOUND:")
            for instance in wasm_scan['wasmInstances']:
                print(f"   📍 {instance['path']}")
                print(f"      Exports: {instance['exports']}")
        
        return wasm_scan
    
    def monitor_wasm_activity(self):
        """Monitor WebAssembly and crypto activity during order placement"""
        
        print("\n🔍 MONITORING WASM/CRYPTO ACTIVITY")
        print("="*45)
        print()
        print("🎯 PLACE AN ORDER NOW!")
        print("   - We'll monitor all WASM and native crypto operations")
        print("   - Use very low price to avoid fills")
        print()
        
        timeout = 300  # 5 minutes
        start_time = time.time()
        last_wasm_count = 0
        last_crypto_count = 0
        last_memory_count = 0
        
        while time.time() - start_time < timeout:
            try:
                # Get interceptions
                wasm_interceptions = self.page.evaluate("() => window.wasmInterceptions || []")
                crypto_interceptions = self.page.evaluate("() => window.cryptoInterceptions || []")
                memory_analysis = self.page.evaluate("() => window.memoryAnalysis || []")
                
                # Check for new WASM activity
                if len(wasm_interceptions) > last_wasm_count:
                    new_wasm = wasm_interceptions[last_wasm_count:]
                    for interception in new_wasm:
                        print(f"\n🔥 WASM {interception['type'].upper()}:")
                        if interception['type'] == 'function_call':
                            print(f"   Function: {interception['function']}")
                            print(f"   Args: {interception['args']}")
                        elif interception['type'] == 'function_result':
                            print(f"   Function: {interception['function']}")
                            print(f"   Result: {interception['result']}")
                        elif interception['type'] == 'instance_created':
                            print(f"   Exports: {interception['exports']}")
                    
                    last_wasm_count = len(wasm_interceptions)
                
                # Check for new crypto activity
                if len(crypto_interceptions) > last_crypto_count:
                    new_crypto = crypto_interceptions[last_crypto_count:]
                    for interception in new_crypto:
                        print(f"\n🔥 CRYPTO {interception['type'].upper()}:")
                        if interception['type'] in ['digest', 'sign']:
                            print(f"   Algorithm: {interception['algorithm']}")
                            print(f"   Data length: {interception['dataLength']}")
                        elif interception['type'] in ['digest_result', 'sign_result']:
                            print(f"   Algorithm: {interception['algorithm']}")
                            print(f"   Result: {interception['result']}")
                            
                            # Check if this matches any known signatures
                            self.check_signature_match(interception['result'])
                    
                    last_crypto_count = len(crypto_interceptions)
                
                # Check for memory activity
                if len(memory_analysis) > last_memory_count:
                    new_memory = memory_analysis[last_memory_count:]
                    for analysis in new_memory:
                        print(f"\n🧠 MEMORY {analysis['type'].upper()}:")
                        print(f"   Length: {analysis['length']}")
                        print(f"   Data: {analysis['data'][:16]}...")
                    
                    last_memory_count = len(memory_analysis)
                
                # Show progress
                elapsed = int(time.time() - start_time)
                if elapsed % 30 == 0 and elapsed > 0:
                    total_activity = len(wasm_interceptions) + len(crypto_interceptions) + len(memory_analysis)
                    print(f"⏱️  Monitoring... ({elapsed}s, {total_activity} activities)")
                
                time.sleep(1)
                
            except Exception as e:
                print(f"⚠️  Error: {e}")
                time.sleep(1)
        
        print(f"\n⏰ Monitoring complete")
        return True
    
    def check_signature_match(self, crypto_result):
        """Check if crypto result matches known signatures"""
        
        # Known signatures from our captures
        known_signatures = [
            "e5d090fa331cef9aa0921b014f53210e",
            "e048fb8b1b6e42caf416298ce272548f", 
            "047836d7d32b9c04a4671e8ad93e5baf",
            "1ed499f829cd58b0473709cbb4b44619",
            "99aa050ac9852cf2bae033964204ec23",
            "d2b32c5665cd430a4e2fd23f2f9e5147",
            "8310de0797a2b7cbb814320b41fdb316"
        ]
        
        if crypto_result in known_signatures:
            print(f"🎉🎉🎉 SIGNATURE MATCH FOUND! 🎉🎉🎉")
            print(f"   Crypto result: {crypto_result}")
            print(f"   Known signature: YES")
            return True
        
        # Check if it's 32 chars hex (potential signature)
        if len(crypto_result) == 32 and all(c in '0123456789abcdef' for c in crypto_result.lower()):
            print(f"🔍 Potential signature detected: {crypto_result}")
            return True
        
        return False
    
    def run_wasm_analysis(self):
        """Run the complete WebAssembly analysis"""
        
        print("="*60)
        print("🔬 WEBASSEMBLY SIGNATURE ANALYSIS")
        print("="*60)
        
        # Setup hooks
        if not self.setup_wasm_hooks():
            return False
        
        try:
            # Scan for WASM modules
            wasm_scan = self.scan_for_wasm_modules()
            
            # Monitor activity
            self.monitor_wasm_activity()
            
            print("\n🎉 WASM ANALYSIS COMPLETE!")
            return True
            
        finally:
            # Cleanup
            if hasattr(self, 'browser'):
                self.browser.close()
            if hasattr(self, 'playwright'):
                self.playwright.stop()

def main():
    """Main function"""
    
    analyzer = WasmSignatureAnalyzer()
    analyzer.run_wasm_analysis()

if __name__ == '__main__':
    main()
