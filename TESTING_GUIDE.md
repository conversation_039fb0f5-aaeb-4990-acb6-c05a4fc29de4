# 🧪 MEXC Trading System - Testing Guide

This guide provides comprehensive testing procedures for both the TradingView Webhook Listener and MEXC Futures Trader services.

## 📋 Prerequisites

### 1. Browser Setup (Required for MEXC Trader)
```bash
# Start Chrome with remote debugging for MEXC trading
# Port 9223 for single browser approach
chrome --remote-debugging-port=9223 --user-data-dir=./browser_data_close --disable-web-security --disable-features=VizDisplayCompositor
```

### 2. MEXC Login
- Navigate to https://www.mexc.com/futures/TRU_USDT
- Login to your MEXC account
- Ensure you have some USDT balance for testing

### 3. Dependencies
```bash
# Install dependencies for both services
cd mexc-futures-trader && npm install
cd ../tradingview-webhook-listener && npm install
```

## 🚀 Quick Start Testing

### Option 1: Automated Service Startup + Testing
```bash
# Start both services automatically
node start-both-services.js

# In another terminal, run comprehensive tests
node test-complete-system-fixed.js
```

### Option 2: Manual Service Startup
```bash
# Terminal 1: Start MEXC Trader Service
cd mexc-futures-trader
node src/server.js

# Terminal 2: Start Webhook Listener Service  
cd tradingview-webhook-listener
node src/server.js

# Terminal 3: Run tests
node test-complete-system-fixed.js
```

## 🧪 Individual Test Scripts

### 1. Webhook Format Testing
```bash
# Test new TradingView webhook format handling
node test-webhook-formats.js
```

Tests:
- ✅ Buy signal → Open Long mapping
- ✅ Sell signal → Open Short mapping  
- ✅ Close signal ignored
- ✅ Both "leverage" and "leverege" spellings
- ✅ Invalid format rejection

### 2. MEXC Trader Service Testing
```bash
# Test MEXC trader service functionality
node test-mexc-trader.js
```

Tests:
- ✅ Service health check
- ✅ Service info retrieval
- ✅ Balance retrieval (requires browser connection)
- ✅ Trade validation
- ✅ Error handling

### 3. Complete System Testing
```bash
# Comprehensive end-to-end testing
node test-complete-system-fixed.js
```

Tests:
- ✅ Both service health checks
- ✅ Webhook format processing
- ✅ MEXC trader functionality
- ✅ End-to-end integration
- ✅ Error handling scenarios

## 📡 Manual Webhook Testing

### New TradingView Format Examples

#### Buy Signal (Maps to Open Long)
```bash
curl -X POST http://localhost:80/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "TRUUSDT",
    "trade": "buy", 
    "last_price": "0.*********",
    "leverege": "2"
  }'
```

#### Sell Signal (Maps to Open Short)
```bash
curl -X POST http://localhost:80/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "TRUUSDT",
    "trade": "sell",
    "last_price": "0.*********", 
    "leverege": "2"
  }'
```

#### Close Signal (Should be Ignored)
```bash
curl -X POST http://localhost:80/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "TRUUSDT",
    "trade": "close",
    "last_price": "0.*********",
    "leverege": "2"
  }'
```

## 🔍 Service Endpoints

### TradingView Webhook Listener (Port 80)
- `GET /health` - Service health check
- `POST /webhook` - Main webhook endpoint
- `GET /api/status` - System status
- `GET /api/config` - Configuration
- `GET /api/balance` - Account balance
- `GET /` - Web dashboard

### MEXC Futures Trader (Port 3000)
- `GET /health` - Service health check
- `GET /info` - Service information
- `GET /balance` - Balance retrieval
- `POST /trade` - Execute trade

## ⚠️ Common Issues & Solutions

### 1. Port 80 Permission Issues (Linux/Mac)
```bash
# Run with sudo or use different port
sudo node src/server.js
# OR change PORT in tradingview-webhook-listener/src/server.js
```

### 2. Browser Connection Failed
```bash
# Ensure Chrome is running with correct flags
chrome --remote-debugging-port=9223 --user-data-dir=./browser_data_close --disable-web-security
```

### 3. MEXC Login Required
- Check browser and login to MEXC manually
- Telegram alert should be sent if login required

### 4. Balance Retrieval Issues
- Ensure you're logged into MEXC
- Check browser is on TRU_USDT futures page
- Balance selector might need updating if MEXC UI changes

## 📊 Expected Test Results

### Successful Test Run Should Show:
- ✅ All service health checks pass
- ✅ Buy/Sell signals properly mapped to Open Long/Short
- ✅ Close signals ignored with appropriate message
- ✅ Both leverage spellings accepted
- ✅ Invalid formats properly rejected
- ✅ Balance retrieval works (if browser connected)
- ✅ Error handling works correctly

### Telegram Integration
- Chat ID: `243673531`
- Bot Token: `**********:AAHCHHOXMt58dBigH1jPHS3FwsJC4CnyFJg`
- Alerts sent for login requirements and system issues

## 🎯 Production Readiness Checklist

- [ ] Both services start without errors
- [ ] All webhook format tests pass
- [ ] MEXC trader service responds correctly
- [ ] Balance retrieval works
- [ ] Telegram notifications work
- [ ] Error handling is robust
- [ ] Close signals are properly ignored
- [ ] Buy/Sell signals map correctly

## 📝 Notes

1. **Port Configuration**: Webhook listener runs on port 80 as requested
2. **Format Support**: Both "leverage" and "leverege" spellings supported
3. **Close Signal Handling**: All close signals from TradingView are ignored
4. **Single Browser**: MEXC trader uses single browser on port 9223
5. **Telegram Updates**: Chat ID updated to 243673531

Run the tests and fix any issues before deploying to production!
