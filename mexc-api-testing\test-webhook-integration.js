const axios = require('axios');

async function testWebhookIntegration() {
    console.log('🧪 TESTING WEBHOOK INTEGRATION');
    console.log('===============================');
    console.log('Testing the updated webhook system with browser automation');
    console.log('');

    // Test payload matching TradingView format
    const testPayload = {
        last_price: "0.03411",
        symbol: "TRUUSDT", 
        trade: "buy",
        leverage: "2"
    };

    console.log('📡 Test payload:', JSON.stringify(testPayload, null, 2));
    console.log('');

    try {
        const startTime = Date.now();
        
        console.log('🚀 Sending webhook request to localhost...');
        
        const response = await axios.post('http://localhost:80/webhook', testPayload, {
            timeout: 15000, // 15 second timeout
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Webhook-Integration-Test/1.0.0'
            }
        });

        const totalTime = Date.now() - startTime;

        console.log('📊 WEBHOOK RESPONSE:');
        console.log('====================');
        console.log(`Status: ${response.status}`);
        console.log(`Total time: ${totalTime}ms`);
        console.log('Response data:', JSON.stringify(response.data, null, 2));

        if (response.data.success) {
            console.log('\n🎉 WEBHOOK INTEGRATION SUCCESS!');
            console.log(`✅ Trade executed successfully`);
            console.log(`⚡ Execution time: ${response.data.executionTime || totalTime}ms`);
            
            if (totalTime < 2000) {
                console.log('🏆 TARGET ACHIEVED: Under 2 seconds!');
            } else {
                console.log('⚠️ Target missed: Over 2 seconds');
            }
        } else {
            console.log('\n❌ WEBHOOK INTEGRATION FAILED');
            console.log(`Error: ${response.data.error || response.data.message}`);
        }

    } catch (error) {
        console.log('\n💥 WEBHOOK REQUEST FAILED');
        console.log('==========================');
        
        if (error.response) {
            console.log(`Status: ${error.response.status}`);
            console.log(`Response: ${JSON.stringify(error.response.data, null, 2)}`);
        } else if (error.request) {
            console.log('No response received');
            console.log(`Error: ${error.message}`);
        } else {
            console.log(`Error: ${error.message}`);
        }
    }
}

async function testMultipleSignals() {
    console.log('\n🔄 TESTING MULTIPLE SIGNALS');
    console.log('============================');
    
    const signals = [
        { trade: "buy", description: "Open Long" },
        { trade: "sell", description: "Open Short" }
    ];

    for (let i = 0; i < signals.length; i++) {
        const signal = signals[i];
        console.log(`\n📡 Test ${i + 1}: ${signal.description}`);
        
        const payload = {
            last_price: "0.03411",
            symbol: "TRUUSDT",
            trade: signal.trade,
            leverage: "2"
        };

        try {
            const startTime = Date.now();
            
            const response = await axios.post('http://localhost:80/webhook', payload, {
                timeout: 15000,
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'Multi-Signal-Test/1.0.0'
                }
            });

            const totalTime = Date.now() - startTime;

            console.log(`✅ ${signal.description}: ${response.data.success ? 'SUCCESS' : 'FAILED'} (${totalTime}ms)`);
            
            if (response.data.success && totalTime < 2000) {
                console.log('🏆 Target achieved!');
            }

        } catch (error) {
            console.log(`❌ ${signal.description}: ERROR - ${error.message}`);
        }

        // Wait 2 seconds between signals
        if (i < signals.length - 1) {
            console.log('⏳ Waiting 2 seconds...');
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }
}

async function main() {
    try {
        console.log('🎯 MEXC WEBHOOK INTEGRATION TEST');
        console.log('=================================');
        console.log('Testing browser automation integration with existing webhook system');
        console.log('');

        // Test 1: Single webhook
        await testWebhookIntegration();

        // Wait a bit
        console.log('\n⏳ Waiting 3 seconds before multiple signal test...');
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Test 2: Multiple signals
        await testMultipleSignals();

        console.log('\n🏁 INTEGRATION TEST COMPLETED');
        console.log('==============================');
        console.log('✅ If you see SUCCESS messages above, the integration is working!');
        console.log('⚡ Browser automation has replaced the failing MEXC Trader Service');
        console.log('🎯 Your webhook system should now execute trades in under 2 seconds');

    } catch (error) {
        console.error('💥 Integration test failed:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = { testWebhookIntegration, testMultipleSignals };
