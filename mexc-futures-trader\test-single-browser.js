const axios = require('axios');

class SingleBrowserTester {
    constructor() {
        this.baseUrl = 'http://localhost:3001';
        this.testResults = [];
    }

    async log(message) {
        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] ${message}`);
    }

    async testConnection() {
        try {
            await this.log('🔗 Testing browser connection...');
            const response = await axios.get(`${this.baseUrl}/test-connection`);
            
            if (response.data.success) {
                await this.log('✅ Browser connection successful');
                return true;
            } else {
                await this.log('❌ Browser connection failed');
                return false;
            }
        } catch (error) {
            await this.log(`❌ Connection test failed: ${error.message}`);
            return false;
        }
    }

    async testTrade(orderType, quantity = '0.0001') {
        try {
            await this.log(`🎯 Testing ${orderType} trade...`);
            const startTime = Date.now();
            
            const response = await axios.post(`${this.baseUrl}/execute`, {
                orderType,
                quantity
            });
            
            const totalTime = Date.now() - startTime;
            
            if (response.data.success) {
                await this.log(`✅ ${orderType} successful!`);
                await this.log(`   Execution Time: ${response.data.executionTime}ms`);
                await this.log(`   Total Time: ${totalTime}ms`);
                await this.log(`   Target Achieved: ${response.data.targetAchieved ? '✅ YES' : '❌ NO'}`);
                await this.log(`   Verified: ${response.data.verified ? '✅ YES' : '❌ NO'}`);
                
                this.testResults.push({
                    orderType,
                    success: true,
                    executionTime: response.data.executionTime,
                    totalTime,
                    targetAchieved: response.data.targetAchieved
                });
            } else {
                await this.log(`❌ ${orderType} failed: ${response.data.error}`);
                this.testResults.push({
                    orderType,
                    success: false,
                    error: response.data.error,
                    totalTime
                });
            }
            
            return response.data;
        } catch (error) {
            await this.log(`❌ ${orderType} request failed: ${error.message}`);
            this.testResults.push({
                orderType,
                success: false,
                error: error.message
            });
            return null;
        }
    }

    async runCompleteTest() {
        await this.log('🚀 Starting Single Browser Trader Test');
        await this.log('======================================');

        // Test 1: Service Health
        await this.log('\n1️⃣ Testing Service Health...');
        try {
            const healthResponse = await axios.get(`${this.baseUrl}/health`);
            await this.log(`✅ Service is running: ${healthResponse.data.status}`);
            await this.log(`   Service: ${healthResponse.data.service}`);
            await this.log(`   Port: ${healthResponse.data.port}`);
        } catch (error) {
            await this.log('❌ Service not running. Please start the single browser trader server.');
            return;
        }

        // Test 2: Browser Connection
        await this.log('\n2️⃣ Testing Browser Connection...');
        const connected = await this.testConnection();
        if (!connected) {
            await this.log('❌ Browser not connected. Please ensure browser is running on port 9223.');
            return;
        }

        // Test 3: Test All Order Types
        await this.log('\n3️⃣ Testing All Order Types...');
        
        const orderTypes = [
            'Open Long',
            'Open Short', 
            'Close Long',
            'Close Short'
        ];

        for (const orderType of orderTypes) {
            await this.testTrade(orderType);
            await this.log(''); // Empty line for readability
            
            // Wait between tests to avoid overwhelming the system
            await new Promise(resolve => setTimeout(resolve, 2000));
        }

        // Test Results Summary
        await this.log('\n📊 TEST RESULTS SUMMARY');
        await this.log('========================');
        
        const successful = this.testResults.filter(r => r.success).length;
        const total = this.testResults.length;
        const under2Seconds = this.testResults.filter(r => r.success && r.targetAchieved).length;
        
        await this.log(`✅ Successful: ${successful}/${total}`);
        await this.log(`❌ Failed: ${total - successful}/${total}`);
        await this.log(`⚡ Under 2 seconds: ${under2Seconds}/${successful}`);
        
        if (successful > 0) {
            const avgExecutionTime = this.testResults
                .filter(r => r.success && r.executionTime)
                .reduce((sum, r) => sum + r.executionTime, 0) / successful;
            await this.log(`📊 Average Execution Time: ${avgExecutionTime.toFixed(0)}ms`);
        }

        // Performance Analysis
        await this.log('\n📈 PERFORMANCE ANALYSIS');
        await this.log('========================');
        
        for (const result of this.testResults) {
            if (result.success) {
                const status = result.targetAchieved ? '🟢 FAST' : '🟡 SLOW';
                await this.log(`${status} ${result.orderType}: ${result.executionTime}ms`);
            } else {
                await this.log(`🔴 FAIL ${result.orderType}: ${result.error}`);
            }
        }

        if (successful === total && under2Seconds === successful) {
            await this.log('\n🎉 PERFECT! All tests passed and under 2 seconds!');
        } else if (successful === total) {
            await this.log('\n✅ All tests passed but some were over 2 seconds.');
        } else {
            await this.log('\n⚠️ Some tests failed. Check logs above for details.');
        }

        await this.log('\n🏁 Single Browser Trader Test Completed');
    }
}

// Run the test
async function main() {
    const tester = new SingleBrowserTester();
    await tester.runCompleteTest();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = SingleBrowserTester;
