#!/usr/bin/env node

/**
 * Start Webhook Listener Only
 * Starts only the TradingView Webhook Listener service with browser automation
 */

const { spawn } = require('child_process');
const path = require('path');
const axios = require('axios');

class WebhookServiceManager {
    constructor() {
        this.webhookService = null;
        this.isShuttingDown = false;
    }

    async startService() {
        console.log('🚀 Starting TradingView Webhook Listener with Browser Automation...\n');

        // Start TradingView Webhook Listener Service (port 80)
        console.log('📡 Starting TradingView Webhook Listener Service...');
        this.webhookService = spawn('node', ['src/server.js'], {
            cwd: path.join(__dirname, 'tradingview-webhook-listener'),
            stdio: ['pipe', 'pipe', 'pipe'],
            env: { ...process.env, PORT: '80' }
        });

        this.webhookService.stdout.on('data', (data) => {
            console.log(`[WEBHOOK] ${data.toString().trim()}`);
        });

        this.webhookService.stderr.on('data', (data) => {
            console.error(`[WEBHOOK ERROR] ${data.toString().trim()}`);
        });

        this.webhookService.on('close', (code) => {
            if (!this.isShuttingDown) {
                console.log(`❌ Webhook Listener service exited with code ${code}`);
            }
        });

        // Wait for service to fully start
        await this.sleep(5000);

        // Check service health
        await this.checkServiceHealth();

        // Setup graceful shutdown
        this.setupGracefulShutdown();

        console.log('\n🎉 Webhook Listener is running with Browser Automation!');
        console.log('📡 TradingView Webhook Listener: http://localhost:80');
        console.log('🌐 Browser Automation: Direct connection to MEXC via ports 9222/9223');
        console.log('\n💡 Press Ctrl+C to stop the service');
        console.log('🧪 Make sure browsers are running on ports 9222/9223');
        console.log('🔧 Use "start-browsers.js" to start browsers if needed');
    }

    async checkServiceHealth() {
        console.log('\n🔍 Checking service health...');

        // Check Webhook Listener
        try {
            const webhookHealth = await axios.get('http://localhost:80/health', { timeout: 5000 });
            console.log(`✅ Webhook Listener: ${webhookHealth.data.status}`);
            console.log(`🔧 Trading Executor: ${webhookHealth.data.components.tradingExecutor ? 'Ready (Browser Automation)' : 'Not Ready'}`);
        } catch (error) {
            console.log(`❌ Webhook Listener: ${error.message}`);
        }
    }

    setupGracefulShutdown() {
        const shutdown = async (signal) => {
            if (this.isShuttingDown) return;
            this.isShuttingDown = true;

            console.log(`\n🛑 Received ${signal}, shutting down service gracefully...`);

            // Kill service
            if (this.webhookService) {
                console.log('🔄 Stopping Webhook Listener service...');
                this.webhookService.kill('SIGTERM');
            }

            // Wait a bit for graceful shutdown
            await this.sleep(2000);

            // Force kill if still running
            if (this.webhookService && !this.webhookService.killed) {
                this.webhookService.kill('SIGKILL');
            }

            console.log('✅ Service stopped');
            process.exit(0);
        };

        process.on('SIGINT', () => shutdown('SIGINT'));
        process.on('SIGTERM', () => shutdown('SIGTERM'));
        process.on('SIGQUIT', () => shutdown('SIGQUIT'));
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Start service if called directly
if (require.main === module) {
    const manager = new WebhookServiceManager();
    manager.startService().catch(error => {
        console.error('❌ Failed to start service:', error);
        process.exit(1);
    });
}

module.exports = WebhookServiceManager;
