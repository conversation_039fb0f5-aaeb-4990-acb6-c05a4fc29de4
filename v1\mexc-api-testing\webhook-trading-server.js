const express = require('express');
const FastMEXCFuturesBot = require('./fast-futures-bot');
require('dotenv').config();

class WebhookTradingServer {
    constructor() {
        this.app = express();
        this.bot = null;
        this.isInitialized = false;
        this.port = process.env.PORT || 3000;
        
        this.setupMiddleware();
        this.setupRoutes();
    }

    setupMiddleware() {
        this.app.use(express.json());
        this.app.use(express.urlencoded({ extended: true }));
        
        // CORS for TradingView webhooks
        this.app.use((req, res, next) => {
            res.header('Access-Control-Allow-Origin', '*');
            res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
            res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            next();
        });

        // Request logging
        this.app.use((req, res, next) => {
            console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
            next();
        });
    }

    setupRoutes() {
        // Health check
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'ok',
                initialized: this.isInitialized,
                timestamp: new Date().toISOString()
            });
        });

        // Initialize bot
        this.app.post('/init', async (req, res) => {
            try {
                if (!this.bot) {
                    this.bot = new FastMEXCFuturesBot();
                    await this.bot.initialize();
                    await this.bot.login();
                    this.isInitialized = true;
                }
                
                res.json({ success: true, message: 'Bot initialized' });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });

        // TradingView webhook endpoint
        this.app.post('/webhook', async (req, res) => {
            const startTime = Date.now();
            
            try {
                if (!this.isInitialized) {
                    return res.status(400).json({ 
                        success: false, 
                        error: 'Bot not initialized. Call /init first' 
                    });
                }

                const signal = this.parseWebhookSignal(req.body);
                console.log('📡 Webhook received:', signal);

                // Validate signal
                if (!this.validateSignal(signal)) {
                    return res.status(400).json({ 
                        success: false, 
                        error: 'Invalid signal format' 
                    });
                }

                // Execute trade
                const result = await this.bot.placeFuturesOrder({
                    symbol: signal.symbol,
                    side: signal.action.toLowerCase(),
                    type: signal.type || 'market',
                    quantity: signal.quantity,
                    price: signal.price,
                    leverage: signal.leverage || '1'
                });

                const totalTime = Date.now() - startTime;
                
                const response = {
                    success: result.success,
                    executionTime: result.executionTime,
                    totalTime: totalTime,
                    signal: signal,
                    timestamp: new Date().toISOString()
                };

                if (result.error) {
                    response.error = result.error;
                }

                console.log(`⚡ Trade executed in ${totalTime}ms`);
                res.json(response);

            } catch (error) {
                const totalTime = Date.now() - startTime;
                console.error('❌ Webhook error:', error.message);
                
                res.status(500).json({
                    success: false,
                    error: error.message,
                    totalTime: totalTime,
                    timestamp: new Date().toISOString()
                });
            }
        });

        // Manual trade endpoint
        this.app.post('/trade', async (req, res) => {
            const startTime = Date.now();
            
            try {
                if (!this.isInitialized) {
                    return res.status(400).json({ 
                        success: false, 
                        error: 'Bot not initialized. Call /init first' 
                    });
                }

                const orderConfig = req.body;
                console.log('🎯 Manual trade:', orderConfig);

                const result = await this.bot.placeFuturesOrder(orderConfig);
                const totalTime = Date.now() - startTime;

                res.json({
                    success: result.success,
                    executionTime: result.executionTime,
                    totalTime: totalTime,
                    order: orderConfig,
                    error: result.error,
                    timestamp: new Date().toISOString()
                });

            } catch (error) {
                const totalTime = Date.now() - startTime;
                res.status(500).json({
                    success: false,
                    error: error.message,
                    totalTime: totalTime,
                    timestamp: new Date().toISOString()
                });
            }
        });

        // Get account info
        this.app.get('/account', async (req, res) => {
            try {
                if (!this.isInitialized) {
                    return res.status(400).json({ 
                        success: false, 
                        error: 'Bot not initialized. Call /init first' 
                    });
                }

                const accountInfo = await this.bot.getAccountInfo();
                res.json({
                    success: true,
                    data: accountInfo,
                    timestamp: new Date().toISOString()
                });

            } catch (error) {
                res.status(500).json({
                    success: false,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        });

        // Shutdown endpoint
        this.app.post('/shutdown', async (req, res) => {
            try {
                if (this.bot) {
                    await this.bot.close();
                }
                res.json({ success: true, message: 'Bot shutdown' });
                process.exit(0);
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
    }

    parseWebhookSignal(body) {
        // Handle different webhook formats
        if (typeof body === 'string') {
            try {
                body = JSON.parse(body);
            } catch {
                // Try to parse as simple text format
                return this.parseTextSignal(body);
            }
        }

        // TradingView format
        if (body.strategy && body.ticker) {
            return {
                symbol: body.ticker.replace('MEXC:', '').replace('USDT', '_USDT'),
                action: body.strategy.order_action || body.action,
                type: body.strategy.order_type || 'market',
                quantity: body.strategy.order_size || body.quantity,
                price: body.strategy.order_price || body.price,
                leverage: body.leverage
            };
        }

        // Simple format
        return {
            symbol: body.symbol || body.pair,
            action: body.action || body.side,
            type: body.type || 'market',
            quantity: body.quantity || body.size || body.amount,
            price: body.price,
            leverage: body.leverage
        };
    }

    parseTextSignal(text) {
        // Parse simple text signals like "BUY BTCUSDT 0.001"
        const parts = text.trim().split(/\s+/);
        if (parts.length >= 3) {
            return {
                action: parts[0].toLowerCase(),
                symbol: parts[1].replace('USDT', '_USDT'),
                quantity: parts[2],
                type: 'market'
            };
        }
        return null;
    }

    validateSignal(signal) {
        return signal && 
               signal.symbol && 
               signal.action && 
               ['buy', 'sell', 'long', 'short'].includes(signal.action.toLowerCase()) &&
               signal.quantity;
    }

    async start() {
        this.app.listen(this.port, () => {
            console.log('🚀 MEXC Fast Trading Webhook Server Started');
            console.log('==========================================');
            console.log(`📡 Server running on port ${this.port}`);
            console.log(`🔗 Webhook URL: http://localhost:${this.port}/webhook`);
            console.log(`💊 Health check: http://localhost:${this.port}/health`);
            console.log('');
            console.log('📋 Available endpoints:');
            console.log('  POST /init        - Initialize trading bot');
            console.log('  POST /webhook     - TradingView webhook');
            console.log('  POST /trade       - Manual trade');
            console.log('  GET  /account     - Account info');
            console.log('  GET  /health      - Health check');
            console.log('  POST /shutdown    - Shutdown server');
            console.log('');
            console.log('⚠️  Remember to call /init before trading!');
        });
    }
}

// Example webhook payloads for testing
const examplePayloads = {
    tradingView: {
        strategy: {
            order_action: 'buy',
            order_type: 'market',
            order_size: '40'
        },
        ticker: 'MEXC:TRUUSDT',
        time: '2025-01-13T10:30:00Z'
    },
    simple: {
        symbol: 'TRU_USDT',
        action: 'buy',
        type: 'market',
        quantity: '40',
        leverage: '1'
    },
    text: 'BUY TRUUSDT 40'
};

// Start server if run directly
if (require.main === module) {
    const server = new WebhookTradingServer();
    server.start();

    // Graceful shutdown
    process.on('SIGINT', async () => {
        console.log('\n🛑 Shutting down server...');
        if (server.bot) {
            await server.bot.close();
        }
        process.exit(0);
    });
}

module.exports = { WebhookTradingServer, examplePayloads };
