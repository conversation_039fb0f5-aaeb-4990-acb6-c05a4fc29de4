#!/usr/bin/env node

/**
 * Test Fixed MEXC Trading System
 * Tests the webhook listener with browser automation
 */

const axios = require('axios');

class SystemTester {
    constructor() {
        this.webhookUrl = 'http://localhost:80/webhook';
        this.healthUrl = 'http://localhost:80/health';
    }

    async runTests() {
        console.log('🧪 Testing Fixed MEXC Trading System');
        console.log('=====================================\n');

        try {
            // Test 1: Health Check
            await this.testHealthCheck();

            // Test 2: Send Test Signal
            await this.testWebhookSignal();

            console.log('\n✅ All tests completed successfully!');
            console.log('🎉 System is working with browser automation');

        } catch (error) {
            console.error('\n❌ Test failed:', error.message);
            process.exit(1);
        }
    }

    async testHealthCheck() {
        console.log('🔍 Test 1: Health Check');
        
        try {
            const response = await axios.get(this.healthUrl, { timeout: 5000 });
            
            console.log(`✅ Service Status: ${response.data.status}`);
            console.log(`🔧 Trading Executor: ${response.data.components.tradingExecutor ? 'Ready' : 'Not Ready'}`);
            console.log(`⚙️ Config: ${response.data.components.config ? 'Configured' : 'Not Configured'}`);
            
            if (response.data.status !== 'healthy') {
                throw new Error('Service is not healthy');
            }
            
        } catch (error) {
            throw new Error(`Health check failed: ${error.message}`);
        }
    }

    async testWebhookSignal() {
        console.log('\n📡 Test 2: Webhook Signal');
        
        const testSignal = {
            symbol: "TRUUSDT",
            trade: "buy",
            last_price: "0.03295",
            leverage: "2"
        };

        console.log('📤 Sending test signal:', JSON.stringify(testSignal, null, 2));
        
        try {
            const response = await axios.post(this.webhookUrl, testSignal, {
                timeout: 30000, // 30 seconds for trade execution
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            console.log('📥 Response received:');
            console.log(`✅ Success: ${response.data.success}`);
            
            if (response.data.tradeRecord) {
                console.log(`⚡ Execution Time: ${response.data.tradeRecord.executionTime}ms`);
                console.log(`📊 Order Type: ${response.data.tradeRecord.processedSignal.orderType}`);
                console.log(`💰 Position Size: ${response.data.tradeRecord.positionSize}`);
                
                if (response.data.tradeRecord.executionTime < 2000) {
                    console.log('🏆 TARGET ACHIEVED: Sub-2 second execution!');
                } else {
                    console.log('⏱️ Execution time above 2 seconds');
                }
            }

            if (response.data.message) {
                console.log(`📝 Message: ${response.data.message}`);
            }

            if (!response.data.success && response.data.error) {
                console.log(`❌ Error: ${response.data.error}`);
            }

        } catch (error) {
            if (error.response) {
                console.log(`❌ HTTP Error ${error.response.status}: ${error.response.data?.error || error.response.statusText}`);
            } else {
                throw new Error(`Webhook test failed: ${error.message}`);
            }
        }
    }
}

// Run tests if called directly
if (require.main === module) {
    const tester = new SystemTester();
    tester.runTests().catch(error => {
        console.error('❌ Test suite failed:', error);
        process.exit(1);
    });
}

module.exports = SystemTester;
