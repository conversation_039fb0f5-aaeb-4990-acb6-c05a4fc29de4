#!/usr/bin/env node

/**
 * Start Both Services Script
 * Starts both TradingView Webhook Listener and MEXC Futures Trader services
 */

const { spawn } = require('child_process');
const path = require('path');
const axios = require('axios');

class ServiceManager {
    constructor() {
        this.services = {
            webhookListener: null,
            mexcTrader: null
        };
        this.isShuttingDown = false;
    }

    async startServices() {
        console.log('🚀 Starting both services...\n');

        // Start MEXC Futures Trader Service (port 3000)
        console.log('📊 Starting MEXC Futures Trader Service...');
        this.services.mexcTrader = spawn('node', ['src/server.js'], {
            cwd: path.join(__dirname, 'mexc-futures-trader'),
            stdio: ['pipe', 'pipe', 'pipe'],
            env: { ...process.env, PORT: '3000' }
        });

        this.services.mexcTrader.stdout.on('data', (data) => {
            console.log(`[MEXC] ${data.toString().trim()}`);
        });

        this.services.mexcTrader.stderr.on('data', (data) => {
            console.error(`[MEXC ERROR] ${data.toString().trim()}`);
        });

        this.services.mexcTrader.on('close', (code) => {
            if (!this.isShuttingDown) {
                console.log(`❌ MEXC Trader service exited with code ${code}`);
            }
        });

        // Wait a bit for MEXC service to start
        await this.sleep(3000);

        // Start TradingView Webhook Listener Service (port 80)
        console.log('📡 Starting TradingView Webhook Listener Service...');
        this.services.webhookListener = spawn('node', ['src/server.js'], {
            cwd: path.join(__dirname, 'tradingview-webhook-listener'),
            stdio: ['pipe', 'pipe', 'pipe'],
            env: { ...process.env, PORT: '80' }
        });

        this.services.webhookListener.stdout.on('data', (data) => {
            console.log(`[WEBHOOK] ${data.toString().trim()}`);
        });

        this.services.webhookListener.stderr.on('data', (data) => {
            console.error(`[WEBHOOK ERROR] ${data.toString().trim()}`);
        });

        this.services.webhookListener.on('close', (code) => {
            if (!this.isShuttingDown) {
                console.log(`❌ Webhook Listener service exited with code ${code}`);
            }
        });

        // Wait for services to fully start
        await this.sleep(5000);

        // Check service health
        await this.checkServiceHealth();

        // Setup graceful shutdown
        this.setupGracefulShutdown();

        console.log('\n🎉 Both services are running!');
        console.log('📊 MEXC Futures Trader: http://localhost:3000');
        console.log('📡 TradingView Webhook Listener: http://localhost:80');
        console.log('\n💡 Press Ctrl+C to stop both services');
        console.log('🧪 Run "node test-complete-system-fixed.js" in another terminal to test');
    }

    async checkServiceHealth() {
        console.log('\n🔍 Checking service health...');

        // Check MEXC Trader
        try {
            const mexcHealth = await axios.get('http://localhost:3000/health', { timeout: 5000 });
            console.log(`✅ MEXC Trader: ${mexcHealth.data.status}`);
        } catch (error) {
            console.log(`❌ MEXC Trader: ${error.message}`);
        }

        // Check Webhook Listener
        try {
            const webhookHealth = await axios.get('http://localhost:80/health', { timeout: 5000 });
            console.log(`✅ Webhook Listener: ${webhookHealth.data.status}`);
        } catch (error) {
            console.log(`❌ Webhook Listener: ${error.message}`);
        }
    }

    setupGracefulShutdown() {
        const shutdown = async (signal) => {
            if (this.isShuttingDown) return;
            this.isShuttingDown = true;

            console.log(`\n🛑 Received ${signal}, shutting down services gracefully...`);

            // Kill services
            if (this.services.mexcTrader) {
                console.log('🔄 Stopping MEXC Trader service...');
                this.services.mexcTrader.kill('SIGTERM');
            }

            if (this.services.webhookListener) {
                console.log('🔄 Stopping Webhook Listener service...');
                this.services.webhookListener.kill('SIGTERM');
            }

            // Wait a bit for graceful shutdown
            await this.sleep(2000);

            // Force kill if still running
            if (this.services.mexcTrader && !this.services.mexcTrader.killed) {
                this.services.mexcTrader.kill('SIGKILL');
            }

            if (this.services.webhookListener && !this.services.webhookListener.killed) {
                this.services.webhookListener.kill('SIGKILL');
            }

            console.log('✅ All services stopped');
            process.exit(0);
        };

        process.on('SIGINT', () => shutdown('SIGINT'));
        process.on('SIGTERM', () => shutdown('SIGTERM'));
        process.on('SIGQUIT', () => shutdown('SIGQUIT'));
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Start services if called directly
if (require.main === module) {
    const manager = new ServiceManager();
    manager.startServices().catch(error => {
        console.error('❌ Failed to start services:', error);
        process.exit(1);
    });
}

module.exports = ServiceManager;
