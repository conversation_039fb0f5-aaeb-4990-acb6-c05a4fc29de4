#!/usr/bin/env python3
"""
PATIENT SIGNATURE INTERCEPTOR
Wait for actual order confirmation and analyze the signatures we've seen
"""

import json
import time
import hashlib
import hmac
from playwright.sync_api import sync_playwright
from dotenv import dotenv_values

class PatientSignatureInterceptor:
    """Patient interceptor that waits for actual order confirmation"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        
        print("⏳ PATIENT SIGNATURE INTERCEPTOR")
        print("="*40)
        print("🎯 WAITING FOR ACTUAL ORDER CONFIRMATION")
        
        # Analyze the signatures we captured earlier
        self.analyze_previous_signatures()
    
    def analyze_previous_signatures(self):
        """Analyze the signatures we captured in previous runs"""
        
        print("\n🔍 ANALYZING PREVIOUSLY CAPTURED SIGNATURES")
        print("="*50)
        
        # Signatures we captured from string operations
        captured_sigs = [
            "489625061511e0322bfa4ad1f6efa950",
            "21cf564a252b46ef8a845f65372695b7"
        ]
        
        # Known real signatures from our captures
        known_sigs = [
            "e5d090fa331cef9aa0921b014f53210e",
            "e048fb8b1b6e42caf416298ce272548f", 
            "047836d7d32b9c04a4671e8ad93e5baf",
            "1ed499f829cd58b0473709cbb4b44619",
            "99aa050ac9852cf2bae033964204ec23",
            "d2b32c5665cd430a4e2fd23f2f9e5147",
            "8310de0797a2b7cbb814320b41fdb316"
        ]
        
        print(f"📊 Captured signatures: {len(captured_sigs)}")
        print(f"📊 Known real signatures: {len(known_sigs)}")
        
        for i, sig in enumerate(captured_sigs):
            print(f"\n🔍 Analyzing captured signature #{i+1}: {sig}")
            
            if sig in known_sigs:
                print(f"🎉 MATCH! This is a known real signature!")
            else:
                print(f"🔍 New signature - analyzing patterns...")
                
                # Try to reverse engineer this signature
                self.reverse_engineer_signature(sig)
    
    def reverse_engineer_signature(self, signature):
        """Try to reverse engineer how a signature was generated"""
        
        print(f"   🧪 Reverse engineering: {signature}")
        
        # Test with current timestamp variations
        current_time = int(time.time() * 1000)
        test_nonces = [
            str(current_time),
            str(current_time - 1000),
            str(current_time - 2000),
            str(current_time - 5000),
            str(current_time - 10000),
        ]
        
        # Test common order parameters
        test_symbols = ["BTC_USDT", "TRU_USDT", "ETH_USDT"]
        test_sides = ["1", "2"]
        test_prices = ["1000", "0.02", "50000"]
        test_volumes = ["1", "10", "0.1"]
        
        for nonce in test_nonces:
            for symbol in test_symbols:
                for side in test_sides:
                    for price in test_prices:
                        for volume in test_volumes:
                            # Test various combinations
                            test_patterns = [
                                f"{self.auth}{nonce}",
                                f"{nonce}{self.auth}",
                                f"{self.auth}{nonce}{symbol}",
                                f"{nonce}{symbol}{self.auth}",
                                f"{self.auth}{nonce}{symbol}{side}{price}{volume}",
                                f"{nonce}{symbol}{side}{price}{volume}{self.auth}",
                            ]
                            
                            for pattern in test_patterns:
                                # Test MD5
                                test_sig = hashlib.md5(pattern.encode()).hexdigest()
                                if test_sig == signature:
                                    print(f"   🎉 CRACKED! MD5({pattern}) = {signature}")
                                    return True
                                
                                # Test SHA256 (first 32 chars)
                                test_sig = hashlib.sha256(pattern.encode()).hexdigest()[:32]
                                if test_sig == signature:
                                    print(f"   🎉 CRACKED! SHA256({pattern})[:32] = {signature}")
                                    return True
                                
                                # Test HMAC-MD5
                                try:
                                    test_sig = hmac.new(self.auth.encode(), pattern.encode(), hashlib.md5).hexdigest()
                                    if test_sig == signature:
                                        print(f"   🎉 CRACKED! HMAC-MD5(auth, {pattern}) = {signature}")
                                        return True
                                except:
                                    pass
        
        print(f"   ❌ Could not reverse engineer this signature")
        return False
    
    def setup_patient_hooks(self):
        """Setup hooks that wait for actual order confirmation"""
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                return False
            
            self.context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                mexc_page = self.context.new_page()
                mexc_page.goto('https://futures.mexc.com/exchange/BTC_USDT', wait_until='domcontentloaded')
            
            self.page = mexc_page
            
            # Inject patient hooks
            self.page.evaluate("""
                window.orderConfirmations = [];
                window.signatureCaptures = [];
                
                console.log('⏳ Installing PATIENT signature hooks...');
                
                // Hook XMLHttpRequest for order confirmations
                const originalSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;
                XMLHttpRequest.prototype.setRequestHeader = function(name, value) {
                    if (name.toLowerCase() === 'x-mxc-sign') {
                        console.log('🎉🎉🎉 ORDER SIGNATURE CAPTURED! 🎉🎉🎉');
                        console.log('Signature:', value);
                        
                        window.signatureCaptures.push({
                            type: 'order_signature',
                            signature: value,
                            timestamp: Date.now(),
                            stack: new Error().stack
                        });
                        
                        alert(`ORDER SIGNATURE CAPTURED!\\n\\nSignature: ${value}\\n\\nThis is the real one!`);
                    }
                    
                    return originalSetRequestHeader.apply(this, arguments);
                };
                
                // Hook fetch for order requests
                const originalFetch = window.fetch;
                window.fetch = function(...args) {
                    const [url, options] = args;
                    
                    if (url.includes('order/create') || url.includes('order/submit') || url.includes('order/place')) {
                        console.log('🚀 ORDER REQUEST DETECTED!');
                        console.log('URL:', url);
                        console.log('Options:', options);
                        
                        window.orderConfirmations.push({
                            type: 'order_request',
                            url: url,
                            headers: options.headers,
                            body: options.body,
                            timestamp: Date.now()
                        });
                        
                        // Check for signature in headers
                        if (options.headers) {
                            for (const [name, value] of Object.entries(options.headers)) {
                                if (name.toLowerCase().includes('sign')) {
                                    console.log(`🔥 SIGNATURE IN FETCH: ${name} = ${value}`);
                                    
                                    window.signatureCaptures.push({
                                        type: 'fetch_signature',
                                        name: name,
                                        value: value,
                                        url: url,
                                        timestamp: Date.now()
                                    });
                                    
                                    alert(`FETCH SIGNATURE CAPTURED!\\n\\nSignature: ${value}\\n\\nURL: ${url}`);
                                }
                            }
                        }
                    }
                    
                    return originalFetch.apply(this, args);
                };
                
                // Hook any crypto operations that might happen during order confirmation
                if (window.CryptoJS) {
                    ['MD5', 'SHA1', 'SHA256', 'HmacMD5', 'HmacSHA1', 'HmacSHA256'].forEach(method => {
                        if (window.CryptoJS[method]) {
                            const original = window.CryptoJS[method];
                            window.CryptoJS[method] = function(...args) {
                                const result = original.apply(this, args);
                                
                                console.log(`🔥 CryptoJS.${method} during order:`, args, '=>', result.toString());
                                
                                window.signatureCaptures.push({
                                    type: `order_crypto_${method.toLowerCase()}`,
                                    args: args.map(arg => arg.toString().substring(0, 100)),
                                    result: result.toString(),
                                    timestamp: Date.now()
                                });
                                
                                return result;
                            };
                        }
                    });
                }
                
                console.log('✅ PATIENT hooks ready - waiting for order confirmation!');
            """)
            
            print("✅ Patient hooks setup completed")
            return True
            
        except Exception as e:
            print(f"❌ Setup failed: {e}")
            return False
    
    def wait_for_order_confirmation(self):
        """Wait patiently for actual order confirmation"""
        
        print("\n⏳ WAITING FOR ORDER CONFIRMATION")
        print("="*45)
        print()
        print("🎯 INSTRUCTIONS:")
        print("1. Set your order parameters (symbol, price, quantity)")
        print("2. Click the CONFIRM/SUBMIT button to place the order")
        print("3. The system will capture the signature when you confirm")
        print("4. Use very low price to avoid fills")
        print()
        print("⏳ Waiting patiently for you to confirm the order...")
        print()
        
        timeout = 600  # 10 minutes - more time for manual order placement
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # Check for order confirmations
                confirmations = self.page.evaluate("() => window.orderConfirmations || []")
                signatures = self.page.evaluate("() => window.signatureCaptures || []")
                
                if signatures:
                    print(f"\n🎉 SIGNATURE CAPTURED!")
                    
                    for i, sig_capture in enumerate(signatures):
                        print(f"\n📋 SIGNATURE CAPTURE #{i+1}:")
                        print(f"   Type: {sig_capture['type']}")
                        
                        if 'signature' in sig_capture:
                            signature = sig_capture['signature']
                        elif 'value' in sig_capture:
                            signature = sig_capture['value']
                        else:
                            signature = sig_capture.get('result', 'Unknown')
                        
                        print(f"   Signature: {signature}")
                        
                        if len(signature) == 32:
                            print(f"🎯 ANALYZING REAL ORDER SIGNATURE: {signature}")
                            
                            # This is the real signature! Try to crack it
                            if self.crack_real_signature(signature, confirmations):
                                return True
                
                if confirmations:
                    print(f"\n📊 Order confirmations: {len(confirmations)}")
                
                # Show progress less frequently
                elapsed = int(time.time() - start_time)
                if elapsed % 60 == 0 and elapsed > 0:
                    print(f"⏱️  Still waiting... ({elapsed//60} minutes elapsed)")
                
                time.sleep(2)
                
            except Exception as e:
                print(f"⚠️  Error: {e}")
                time.sleep(2)
        
        print(f"\n⏰ Timeout reached - no order confirmation detected")
        return False
    
    def crack_real_signature(self, signature, confirmations):
        """Try to crack the real signature from order confirmation"""
        
        print(f"\n🔥 CRACKING REAL ORDER SIGNATURE")
        print("="*40)
        print(f"🎯 Target: {signature}")
        
        if confirmations:
            confirmation = confirmations[-1]  # Latest confirmation
            
            print(f"📋 Order details:")
            print(f"   URL: {confirmation['url']}")
            
            if confirmation.get('body'):
                try:
                    body_data = json.loads(confirmation['body'])
                    print(f"   Symbol: {body_data.get('symbol', 'Unknown')}")
                    print(f"   Side: {body_data.get('side', 'Unknown')}")
                    print(f"   Price: {body_data.get('price', 'Unknown')}")
                    print(f"   Volume: {body_data.get('vol', 'Unknown')}")
                    
                    # Try to crack with this specific order data
                    if self.crack_with_order_data(signature, body_data, confirmation):
                        return True
                except:
                    pass
        
        # Try general cracking
        return self.reverse_engineer_signature(signature)
    
    def crack_with_order_data(self, signature, order_data, confirmation):
        """Try to crack signature with specific order data"""
        
        print(f"\n🧪 CRACKING WITH SPECIFIC ORDER DATA")
        
        # Get headers for nonce
        headers = confirmation.get('headers', {})
        nonce = headers.get('x-mxc-nonce', str(int(time.time() * 1000)))
        
        print(f"   Nonce: {nonce}")
        
        # Create JSON variations
        json_variations = [
            json.dumps(order_data, separators=(',', ':')),
            json.dumps(order_data, separators=(',', ':'), sort_keys=True),
            json.dumps(order_data),
        ]
        
        for json_str in json_variations:
            # Test various patterns
            test_patterns = [
                f"{self.auth}{nonce}{json_str}",
                f"{nonce}{json_str}{self.auth}",
                f"{self.auth}{nonce}",
                f"{nonce}{self.auth}",
                f"{self.auth}{nonce}{order_data.get('symbol', '')}{order_data.get('side', '')}{order_data.get('price', '')}{order_data.get('vol', '')}",
            ]
            
            for pattern in test_patterns:
                # Test MD5
                test_sig = hashlib.md5(pattern.encode()).hexdigest()
                if test_sig == signature:
                    print(f"🎉🎉🎉 SIGNATURE CRACKED! 🎉🎉🎉")
                    print(f"Algorithm: MD5")
                    print(f"Input: {pattern}")
                    print(f"Output: {signature}")
                    return True
                
                # Test SHA256 (first 32)
                test_sig = hashlib.sha256(pattern.encode()).hexdigest()[:32]
                if test_sig == signature:
                    print(f"🎉🎉🎉 SIGNATURE CRACKED! 🎉🎉🎉")
                    print(f"Algorithm: SHA256[:32]")
                    print(f"Input: {pattern}")
                    print(f"Output: {signature}")
                    return True
                
                # Test HMAC-MD5
                try:
                    test_sig = hmac.new(self.auth.encode(), pattern.encode(), hashlib.md5).hexdigest()
                    if test_sig == signature:
                        print(f"🎉🎉🎉 SIGNATURE CRACKED! 🎉🎉🎉")
                        print(f"Algorithm: HMAC-MD5")
                        print(f"Key: {self.auth}")
                        print(f"Message: {pattern}")
                        print(f"Output: {signature}")
                        return True
                except:
                    pass
        
        return False
    
    def run_patient_interception(self):
        """Run the patient interception"""
        
        print("="*60)
        print("⏳ PATIENT SIGNATURE INTERCEPTION")
        print("="*60)
        
        # Setup hooks
        if not self.setup_patient_hooks():
            return False
        
        try:
            # Wait for order confirmation
            if self.wait_for_order_confirmation():
                print("\n🎉 SIGNATURE SUCCESSFULLY CRACKED!")
                return True
            else:
                print("\n⏳ No order confirmation detected")
                return False
            
        finally:
            # Cleanup
            if hasattr(self, 'browser'):
                self.browser.close()
            if hasattr(self, 'playwright'):
                self.playwright.stop()

def main():
    """Main function"""
    
    interceptor = PatientSignatureInterceptor()
    interceptor.run_patient_interception()

if __name__ == '__main__':
    main()
