#!/usr/bin/env python3
"""
SIGNATURE PATTERN ANALYZER
Analyze the 92+ captured signatures to find the algorithm
"""

import json
import hashlib
import hmac
import time
from collections import Counter

class SignaturePatternAnalyzer:
    """Analyze patterns in the captured signatures"""
    
    def __init__(self):
        print("🔬 SIGNATURE PATTERN ANALYZER")
        print("="*40)
        print("🎯 ANALYZING 92+ CAPTURED SIGNATURES")
        
        # All the signatures we captured
        self.captured_signatures = [
            "3f12838193c056346ba5f20e49a64fe9",
            "c8e122d82086eec9d6cfcf9d035cae6a", 
            "b600f608c21018449300100df05d884d",
            "f103c9aa3504be1e41ad2450b5203bc8",
            "d0a8ae0af29f80c59a368bdf854a3c40",
            "a262344e944c309dac9be1b398d255b3",
            "7b231a1f0f45ac6e4e9ddc94f7998608",
            "44c0755b5804c21404bf2bfd2dfb5118",
            "8b3cae90280eaed5429e7b1a86027606",
            "0576abb1a9ded381ab1032836f183658",
            "b2e6d930f6d2fb33daa01709a9e38632",
            "438ffba0166ea7b042b0ea18ad0a696b",
            "50c6b451984ebdb4bee8165c3c079f3f",
            "37270808772c0af78771f281199d7785",
            "0f5f6263ccc4c085f4c5e2e29ac3b5ae",
            "f42b35fb85c6b0f30b77741712a2d344",
            "25717027457b56a8c3c2ce0e25873334",
            "6be58e3f9671ccc7beaac319e6a680a8",
            "3f1ce0ef297fafe77dabf609d6fdf1a9",
            "410bb6165cc3481d147700c8488a4bac",
            "a8f0dfe144a35a76fbb36d03e1c056f5",
            "eeed0fe55846e5f8f7bd24f2c08a3903",
            "9c272a46c5a7309dbe50a87483e3f82e",
            "3c0c833d57896a5fdfcce88ebc82d454",
            "b6e8f0bbfcdd863349e668894048fe91",
            "4a61ad3c6ce674fe6cb91bce0e66e244",
            "ea7d6670d12e27fdf6f8e9eff51888ec",
            "347705f352d829780cd3c10c6e22cde5",
            "0e75c6bbffc219cafab32780ec3ff1bd",
            "294a9c1c0213f252cf6bf37278f85b63",
            "284b7242be3ff55ac2170d33c41252c6",
            "3cfe5753ec426289d87761e23fd2d158",
            "26ae559d79174849a139602be8646625",
            "463c508e18f6ec7dba4b73d86cb85d68",
            "775b84b9ffd2a198c66fc1effb6dbee4",
            "57a4862a216949baafaa5b2a8f02bc85",
            "06da70f2480c3bfe423763a817968a98",
            "8bd9a267698ff66ba0deab16befdc42d",
            "011d3f9a03d14f03b8a74ce5b450fbdc",
            "3b446414193f8128352cde698ba7b1a6",
            "6cb5edc5d050c34eadee6bf5a1140d1d",
            "d415887fad21c3fa2979dd600ec15952",
            "ebbf8d5300f366c9b68a6fed51f6391e",
            "2ff3d17c199857254aa92ac272e49534",
            "fc166b74905b1e2cf40b141b85ccd3bc",
            "ab517babc55a78c922bf32dd1649f1f8",
            "73d575abbca8ae33d157a82f7699cf58"
        ]
        
        # Order parameters (same for all)
        self.order_params = {
            "symbol": "TRU_USDT",
            "side": 1,
            "price": "0.02",
            "vol": 1,
            "nonce": "1754929178532"  # Same nonce for all!
        }
        
        print(f"📊 Total signatures: {len(self.captured_signatures)}")
        print(f"📊 Unique signatures: {len(set(self.captured_signatures))}")
    
    def analyze_signature_characteristics(self):
        """Analyze basic characteristics of the signatures"""
        
        print(f"\n🔍 SIGNATURE CHARACTERISTICS ANALYSIS")
        print("="*45)
        
        # Check lengths
        lengths = [len(sig) for sig in self.captured_signatures]
        print(f"📏 Signature lengths: {set(lengths)}")
        
        # Check if all are hex
        all_hex = all(all(c in '0123456789abcdef' for c in sig.lower()) for sig in self.captured_signatures)
        print(f"🔤 All hexadecimal: {all_hex}")
        
        # Character frequency analysis
        all_chars = ''.join(self.captured_signatures)
        char_freq = Counter(all_chars)
        print(f"📊 Character frequency: {dict(char_freq.most_common(5))}")
        
        # Check for patterns in first/last characters
        first_chars = [sig[0] for sig in self.captured_signatures]
        last_chars = [sig[-1] for sig in self.captured_signatures]
        
        print(f"🔤 First char frequency: {dict(Counter(first_chars).most_common(3))}")
        print(f"🔤 Last char frequency: {dict(Counter(last_chars).most_common(3))}")
    
    def analyze_time_patterns(self):
        """Analyze if signatures are time-based"""
        
        print(f"\n⏰ TIME-BASED PATTERN ANALYSIS")
        print("="*40)
        
        # Since all signatures have the same nonce but are different,
        # the algorithm must include a time component or random element
        
        nonce = self.order_params["nonce"]
        timestamp = int(nonce) // 1000  # Convert to seconds
        
        print(f"📅 Base nonce: {nonce}")
        print(f"📅 Timestamp: {timestamp}")
        print(f"📅 Human time: {time.ctime(timestamp)}")
        
        # Test if signatures include microsecond timestamps
        base_time = int(nonce)
        
        for i, sig in enumerate(self.captured_signatures[:5]):  # Test first 5
            print(f"\n🔍 Testing signature #{i+1}: {sig}")
            
            # Try different time variations
            time_variations = [
                str(base_time + i),  # Add index
                str(base_time + i * 1000),  # Add milliseconds
                str(base_time + i * 100),   # Add 100ms
                str(int(time.time() * 1000000) + i),  # Microseconds
            ]
            
            for time_var in time_variations:
                # Test with time variation
                test_content = f"{self.order_params['symbol']}{self.order_params['side']}{self.order_params['price']}{self.order_params['vol']}{time_var}"
                test_sig = hashlib.md5(test_content.encode()).hexdigest()
                
                if test_sig == sig:
                    print(f"🎉 TIME-BASED MATCH FOUND!")
                    print(f"   Time variation: {time_var}")
                    print(f"   Content: {test_content}")
                    return True
    
    def analyze_incremental_patterns(self):
        """Analyze if signatures follow incremental patterns"""
        
        print(f"\n🔢 INCREMENTAL PATTERN ANALYSIS")
        print("="*40)
        
        # Test if signatures are based on incremental counters
        for i, sig in enumerate(self.captured_signatures[:10]):  # Test first 10
            print(f"\n🔍 Testing signature #{i+1}: {sig}")
            
            # Try different incremental patterns
            incremental_tests = [
                str(i),  # Simple index
                str(i + 1),  # Index + 1
                str(i * 1000),  # Index * 1000
                str(int(self.order_params["nonce"]) + i),  # Nonce + index
                str(int(self.order_params["nonce"]) + i * 1000),  # Nonce + index * 1000
            ]
            
            for inc_val in incremental_tests:
                # Test different content combinations with incremental value
                test_combinations = [
                    f"{self.order_params['symbol']}{self.order_params['side']}{self.order_params['price']}{self.order_params['vol']}{inc_val}",
                    f"{inc_val}{self.order_params['symbol']}{self.order_params['side']}{self.order_params['price']}{self.order_params['vol']}",
                    f"{self.order_params['nonce']}{inc_val}",
                    f"{inc_val}{self.order_params['nonce']}",
                ]
                
                for test_content in test_combinations:
                    test_sig = hashlib.md5(test_content.encode()).hexdigest()
                    
                    if test_sig == sig:
                        print(f"🎉 INCREMENTAL MATCH FOUND!")
                        print(f"   Incremental value: {inc_val}")
                        print(f"   Content: {test_content}")
                        return True
    
    def analyze_random_component_patterns(self):
        """Analyze if signatures include random components"""
        
        print(f"\n🎲 RANDOM COMPONENT ANALYSIS")
        print("="*35)
        
        # Since we have different signatures for identical parameters,
        # there must be a random or time-sensitive component
        
        print("🔍 Key observations:")
        print(f"   - Same order parameters for all signatures")
        print(f"   - Same nonce: {self.order_params['nonce']}")
        print(f"   - {len(set(self.captured_signatures))} unique signatures")
        print(f"   - This proves there's a random/time component!")
        
        # The random component could be:
        print(f"\n🎯 Possible random components:")
        print(f"   1. Browser-generated random values")
        print(f"   2. Microsecond timestamps")
        print(f"   3. Request sequence numbers")
        print(f"   4. Session-specific random seeds")
        print(f"   5. Hardware-based entropy")
        
        # Try to find the pattern by analyzing signature differences
        print(f"\n🔍 Analyzing signature differences:")
        
        for i in range(min(5, len(self.captured_signatures) - 1)):
            sig1 = self.captured_signatures[i]
            sig2 = self.captured_signatures[i + 1]
            
            # Convert to integers and find difference
            try:
                int1 = int(sig1, 16)
                int2 = int(sig2, 16)
                diff = abs(int1 - int2)
                
                print(f"   Sig {i+1} vs {i+2}: diff = {diff:x}")
                
                # Check if difference has patterns
                if diff < 1000:
                    print(f"      Small difference - possible incremental!")
                elif diff > 2**120:
                    print(f"      Large difference - likely random")
                
            except:
                continue
    
    def test_advanced_algorithms(self):
        """Test advanced signature algorithms"""
        
        print(f"\n🧪 ADVANCED ALGORITHM TESTING")
        print("="*40)
        
        # Test with different auth token parts
        auth = "WEBd98cd3a6eecf378454e859e5b4b680aeae16c4bd38492179d5a4f4afa81f43b6"
        nonce = self.order_params["nonce"]
        
        # Test first signature with various advanced methods
        target_sig = self.captured_signatures[0]
        print(f"🎯 Testing against: {target_sig}")
        
        # Advanced test combinations
        advanced_tests = [
            # With current timestamp
            f"{auth}{nonce}{int(time.time())}",
            f"{auth}{nonce}{int(time.time() * 1000)}",
            f"{auth}{nonce}{int(time.time() * 1000000)}",
            
            # With browser fingerprint components
            f"{auth}{nonce}Mozilla/5.0",
            f"{auth}{nonce}b03MOmeXoiZid75ogtwP",  # mtoken
            f"{auth}{nonce}85723e9fb269ff0e1e19525050842a3c",  # mhash
            
            # With order-specific data
            f"{auth}{nonce}TRU_USDT10.021",
            f"{nonce}TRU_USDT10.021{auth}",
            
            # Complex combinations
            f"{auth[:32]}{nonce}{auth[32:]}",
            f"{nonce}{auth[:16]}{auth[16:32]}{auth[32:48]}{auth[48:]}",
        ]
        
        print(f"🧪 Testing {len(advanced_tests)} advanced combinations...")
        
        for i, test_content in enumerate(advanced_tests):
            # Test multiple hash algorithms
            algorithms = [
                ('MD5', hashlib.md5),
                ('SHA1', lambda x: hashlib.sha1(x).hexdigest()[:32]),
                ('SHA256', lambda x: hashlib.sha256(x).hexdigest()[:32]),
            ]
            
            for algo_name, algo_func in algorithms:
                try:
                    if callable(algo_func):
                        if algo_name == 'MD5':
                            test_sig = algo_func(test_content.encode()).hexdigest()
                        else:
                            test_sig = algo_func(test_content.encode())
                    
                    if test_sig == target_sig:
                        print(f"🎉 ADVANCED MATCH FOUND!")
                        print(f"   Algorithm: {algo_name}")
                        print(f"   Content: {test_content}")
                        return True
                        
                except:
                    continue
        
        print(f"❌ No advanced matches found")
        return False
    
    def run_complete_analysis(self):
        """Run the complete signature analysis"""
        
        print("="*60)
        print("🔬 COMPREHENSIVE SIGNATURE PATTERN ANALYSIS")
        print("="*60)
        
        # Basic characteristics
        self.analyze_signature_characteristics()
        
        # Time patterns
        self.analyze_time_patterns()
        
        # Incremental patterns
        self.analyze_incremental_patterns()
        
        # Random component analysis
        self.analyze_random_component_patterns()
        
        # Advanced algorithms
        self.test_advanced_algorithms()
        
        print(f"\n🎯 ANALYSIS CONCLUSIONS:")
        print("="*30)
        print("1. All signatures are 32-char hex (MD5 length)")
        print("2. Same order parameters produce different signatures")
        print("3. Algorithm includes random/time-sensitive component")
        print("4. Standard MD5/SHA combinations don't match")
        print("5. Likely uses browser-specific entropy or hardware RNG")
        
        print(f"\n🚀 NEXT STEPS:")
        print("- Hook into browser's random number generation")
        print("- Capture WebAssembly or native crypto calls")
        print("- Analyze browser fingerprinting components")
        print("- Look for hardware-based signature generation")

def main():
    """Main function"""
    
    analyzer = SignaturePatternAnalyzer()
    analyzer.run_complete_analysis()

if __name__ == '__main__':
    main()
