#!/usr/bin/env node

/**
 * Test MEXC Futures Trader Service
 * Tests the MEXC trader service functionality
 */

const axios = require('axios');

class MexcTraderTester {
    constructor() {
        this.mexcUrl = 'http://localhost:3000';
        this.testResults = [];
    }

    async runTests() {
        console.log('🎯 Testing MEXC Futures Trader Service...\n');

        // Test 1: Service health
        await this.testServiceHealth();

        // Test 2: Service info
        await this.testServiceInfo();

        // Test 3: Balance retrieval
        await this.testBalanceRetrieval();

        // Test 4: Trade execution validation (without actual execution)
        await this.testTradeValidation();

        // Test 5: Error handling
        await this.testErrorHandling();

        // Print results
        this.printResults();
    }

    async testServiceHealth() {
        console.log('🔍 Testing service health...');
        
        try {
            const response = await axios.get(`${this.mexcUrl}/health`, { timeout: 5000 });
            
            const healthy = response.data.status === 'healthy';
            
            this.addResult('Service Health Check', healthy, {
                status: response.data.status,
                service: response.data.service,
                version: response.data.version
            });

        } catch (error) {
            this.addResult('Service Health Check', false, {
                error: error.message,
                code: error.code
            });
        }
    }

    async testServiceInfo() {
        console.log('📋 Testing service info...');
        
        try {
            const response = await axios.get(`${this.mexcUrl}/info`, { timeout: 5000 });
            
            const hasRequiredInfo = response.data.service && 
                                   response.data.version && 
                                   response.data.supportedOrders;
            
            this.addResult('Service Info Retrieval', hasRequiredInfo, {
                service: response.data.service,
                version: response.data.version,
                supported_orders: response.data.supportedOrders?.join(', '),
                features: response.data.features?.length || 0
            });

        } catch (error) {
            this.addResult('Service Info Retrieval', false, {
                error: error.message
            });
        }
    }

    async testBalanceRetrieval() {
        console.log('💰 Testing balance retrieval...');
        
        try {
            const response = await axios.get(`${this.mexcUrl}/balance`, { timeout: 15000 });
            
            // Balance retrieval might fail if browser not connected, but service should respond
            const serviceResponded = response.status === 200;
            
            this.addResult('Balance Service Response', serviceResponded, {
                success: response.data.success,
                balance: response.data.balance,
                currency: response.data.currency,
                error: response.data.error,
                selector: response.data.selector
            });

        } catch (error) {
            this.addResult('Balance Service Response', false, {
                error: error.response?.data?.error || error.message,
                status: error.response?.status
            });
        }
    }

    async testTradeValidation() {
        console.log('⚡ Testing trade validation...');
        
        // Test valid trade request format
        const validTrade = {
            orderType: "Open Long",
            quantity: "0.3600"
        };

        try {
            // This will likely fail due to browser connection, but we're testing the validation
            const response = await axios.post(`${this.mexcUrl}/trade`, validTrade, { timeout: 10000 });
            
            this.addResult('Valid Trade Format', true, {
                success: response.data.success,
                order_type: response.data.orderType,
                quantity: response.data.quantity,
                error: response.data.error
            });

        } catch (error) {
            // Check if it's a validation error or connection error
            const isValidationError = error.response?.status === 400;
            const isConnectionError = error.response?.data?.error?.includes('browser') || 
                                     error.response?.data?.error?.includes('connect');
            
            if (isConnectionError) {
                this.addResult('Valid Trade Format', true, {
                    note: 'Format validation passed, browser connection expected to fail in test',
                    error: error.response?.data?.error
                });
            } else {
                this.addResult('Valid Trade Format', !isValidationError, {
                    error: error.response?.data?.error || error.message,
                    status: error.response?.status
                });
            }
        }
    }

    async testErrorHandling() {
        console.log('❌ Testing error handling...');
        
        // Test invalid order type
        const invalidOrderType = {
            orderType: "Invalid Order",
            quantity: "0.3600"
        };

        try {
            const response = await axios.post(`${this.mexcUrl}/trade`, invalidOrderType, { timeout: 5000 });
            
            // Should be rejected
            const properlyRejected = !response.data.success;
            
            this.addResult('Invalid Order Type Rejected', properlyRejected, {
                error: response.data.error,
                valid_order_types: response.data.validOrderTypes?.join(', ')
            });

        } catch (error) {
            // Should be a 400 error
            const is400Error = error.response?.status === 400;
            
            this.addResult('Invalid Order Type Rejected', is400Error, {
                status: error.response?.status,
                error: error.response?.data?.error
            });
        }

        // Test missing quantity
        const missingQuantity = {
            orderType: "Open Long"
            // Missing quantity
        };

        try {
            const response = await axios.post(`${this.mexcUrl}/trade`, missingQuantity, { timeout: 5000 });
            
            const properlyRejected = !response.data.success;
            
            this.addResult('Missing Quantity Rejected', properlyRejected, {
                error: response.data.error
            });

        } catch (error) {
            const is400Error = error.response?.status === 400;
            
            this.addResult('Missing Quantity Rejected', is400Error, {
                status: error.response?.status,
                error: error.response?.data?.error
            });
        }
    }

    addResult(testName, passed, details) {
        this.testResults.push({
            name: testName,
            passed,
            details,
            timestamp: new Date().toISOString()
        });
    }

    printResults() {
        console.log('\n📊 MEXC TRADER TEST RESULTS');
        console.log('=' .repeat(60));

        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;

        console.log(`\n🎯 Results: ${passed}/${total} tests passed (${Math.round(passed/total*100)}%)\n`);

        this.testResults.forEach((result, index) => {
            const status = result.passed ? '✅' : '❌';
            console.log(`${status} ${index + 1}. ${result.name}`);
            
            if (result.details) {
                Object.entries(result.details).forEach(([key, value]) => {
                    if (value !== undefined && value !== null) {
                        console.log(`   ${key}: ${value}`);
                    }
                });
            }
            console.log('');
        });

        if (passed === total) {
            console.log('🎉 All MEXC trader tests passed!');
        } else {
            console.log('⚠️ Some MEXC trader tests failed.');
        }
    }
}

// Run tests if called directly
if (require.main === module) {
    const tester = new MexcTraderTester();
    tester.runTests().catch(error => {
        console.error('❌ Test execution failed:', error);
        process.exit(1);
    });
}

module.exports = MexcTraderTester;
