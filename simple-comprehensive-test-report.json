{"summary": {"totalTests": 17, "passed": 16, "failed": 1, "successRate": "94%"}, "testResults": [{"testName": "System Status Check", "success": true, "details": {"botActive": true, "configured": true, "mexcConnected": true, "balance": {"asset": "USDT", "free": 0, "locked": 0, "total": 0, "updateTime": null, "timestamp": "2025-08-16T08:54:09.167Z", "source": "api"}}, "timestamp": "2025-08-16T08:54:09.214Z"}, {"testName": "Buy Signal with Current Price", "success": true, "details": {"expectedOrderType": "Open Long", "actualOrderType": "Open Long", "orderTypeMatch": true, "success": false, "message": "Trade execution failed", "skipped": false, "executionTime": "9ms", "price": 0.03225, "leverage": 2}, "timestamp": "2025-08-16T08:54:09.224Z"}, {"testName": "<PERSON><PERSON> with Current Price", "success": true, "details": {"expectedOrderType": "Open Short", "actualOrderType": "Open Short", "orderTypeMatch": true, "success": false, "message": "Trade execution failed", "skipped": false, "executionTime": "303ms", "price": 0.03225, "leverage": 2}, "timestamp": "2025-08-16T08:54:11.031Z"}, {"testName": "Close Signal with Current Price", "success": false, "details": {"expectedOrderType": "Close Long", "orderTypeMatch": false, "success": true, "message": "Close signals are ignored. Close mechanism handled by TP/SL.", "executionTime": "9ms"}, "timestamp": "2025-08-16T08:54:12.551Z"}, {"testName": "Buy Signal with Slightly Different Price", "success": true, "details": {"expectedOrderType": "Open Long", "actualOrderType": "Open Long", "orderTypeMatch": true, "success": false, "message": "Trade execution failed", "skipped": false, "executionTime": "10ms", "price": 0.032572500000000004, "leverage": 2}, "timestamp": "2025-08-16T08:54:14.108Z"}, {"testName": "TRUUSDT Signal Processing", "success": true, "details": {"symbol": "TRUUSDT", "processed": true, "success": false, "message": "Trade execution failed", "skipped": false}, "timestamp": "2025-08-16T08:54:15.621Z"}, {"testName": "BTCUSDT Signal Processing", "success": true, "details": {"symbol": "BTCUSDT", "processed": false, "success": false}, "timestamp": "2025-08-16T08:54:16.727Z"}, {"testName": "ETHUSDT Signal Processing", "success": true, "details": {"symbol": "ETHUSDT", "processed": false, "success": false}, "timestamp": "2025-08-16T08:54:17.877Z"}, {"testName": "1x Leverage", "success": true, "details": {"requestedLeverage": "1", "processedLeverage": 1, "success": false, "message": "Trade execution failed"}, "timestamp": "2025-08-16T08:54:18.893Z"}, {"testName": "2x Leverage", "success": true, "details": {"requestedLeverage": "2", "processedLeverage": 2, "success": false, "message": "Trade execution failed"}, "timestamp": "2025-08-16T08:54:19.905Z"}, {"testName": "5x Leverage", "success": true, "details": {"requestedLeverage": "5", "processedLeverage": 5, "success": false, "message": "Trade execution failed"}, "timestamp": "2025-08-16T08:54:20.916Z"}, {"testName": "10x Leverage", "success": true, "details": {"requestedLeverage": "10", "processedLeverage": 10, "success": false, "message": "Trade execution failed"}, "timestamp": "2025-08-16T08:54:21.924Z"}, {"testName": "Invalid Symbol", "success": true, "details": {"success": false}, "timestamp": "2025-08-16T08:54:22.933Z"}, {"testName": "Missing Symbol", "success": true, "details": {"success": false}, "timestamp": "2025-08-16T08:54:23.939Z"}, {"testName": "Invalid Trade Type", "success": true, "details": {"success": false}, "timestamp": "2025-08-16T08:54:24.949Z"}, {"testName": "Missing Price", "success": true, "details": {"success": false}, "timestamp": "2025-08-16T08:54:25.958Z"}, {"testName": "Execution Performance", "success": true, "details": {"averageTime": "149ms", "successfulExecutions": "5/5", "sub2SecondExecutions": "5/5", "allExecutions": [{"attempt": 1, "totalTime": 9, "success": true, "message": "Trade execution failed", "skipped": false}, {"attempt": 2, "totalTime": 351, "success": true, "message": "Trade execution failed", "skipped": false}, {"attempt": 3, "totalTime": 13, "success": true, "message": "Trade execution failed", "skipped": false}, {"attempt": 4, "totalTime": 6, "success": true, "message": "Trade execution failed", "skipped": false}, {"attempt": 5, "totalTime": 365, "success": true, "message": "Trade execution failed", "skipped": false}]}, "timestamp": "2025-08-16T08:54:37.794Z"}], "timestamp": "2025-08-16T08:54:37.800Z", "environment": {"webhookListenerUrl": "http://localhost:4000/webhook", "currentTRUPrice": 0.03225, "testDuration": "2025-08-16T08:54:37.800Z"}}