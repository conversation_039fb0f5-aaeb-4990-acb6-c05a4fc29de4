# PowerShell deployment script for the trading system fixes
# Run this script to safely deploy the fixes and restart services

Write-Host "🚀 Deploying Trading System Fixes..." -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# Function to check if a process is running
function Test-ProcessRunning {
    param([string]$ProcessName)
    return (Get-Process -Name $ProcessName -ErrorAction SilentlyContinue) -ne $null
}

# Function to stop a service gracefully
function Stop-Service {
    param([string]$ServiceName, [string]$ProcessPattern)
    
    Write-Host "🛑 Stopping $ServiceName..." -ForegroundColor Yellow
    
    # Try to find and stop the process
    $processes = Get-WmiObject Win32_Process | Where-Object { $_.CommandLine -like "*$ProcessPattern*" }
    
    if ($processes) {
        foreach ($process in $processes) {
            Write-Host "   Stopping process ID: $($process.ProcessId)" -ForegroundColor Gray
            Stop-Process -Id $process.ProcessId -Force -ErrorAction SilentlyContinue
        }
        Start-Sleep -Seconds 2
        Write-Host "   ✅ $ServiceName stopped" -ForegroundColor Green
    } else {
        Write-Host "   ℹ️  $ServiceName was not running" -ForegroundColor Gray
    }
}

# Function to start a service
function Start-Service {
    param([string]$ServiceName, [string]$Directory, [string]$Command)
    
    Write-Host "🚀 Starting $ServiceName..." -ForegroundColor Yellow
    
    if (Test-Path $Directory) {
        Set-Location $Directory
        
        # Start the service in a new window
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "& { $Command }" -WindowStyle Minimized
        
        Start-Sleep -Seconds 3
        Write-Host "   ✅ $ServiceName started" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Directory not found: $Directory" -ForegroundColor Red
        return $false
    }
    return $true
}

# Function to test service health
function Test-ServiceHealth {
    param([string]$ServiceName, [string]$HealthUrl)
    
    Write-Host "🔍 Testing $ServiceName health..." -ForegroundColor Yellow
    
    for ($i = 1; $i -le 10; $i++) {
        try {
            $response = Invoke-RestMethod -Uri $HealthUrl -TimeoutSec 5 -ErrorAction Stop
            if ($response.status -eq "healthy" -or $response.status -eq "degraded") {
                Write-Host "   ✅ $ServiceName is healthy" -ForegroundColor Green
                return $true
            }
        } catch {
            Write-Host "   ⏳ Waiting for $ServiceName... (attempt $i/10)" -ForegroundColor Gray
            Start-Sleep -Seconds 2
        }
    }
    
    Write-Host "   ❌ $ServiceName health check failed" -ForegroundColor Red
    return $false
}

# Main deployment process
try {
    # Step 1: Stop existing services
    Write-Host "`n📋 Step 1: Stopping existing services..." -ForegroundColor Cyan
    Stop-Service "MEXC Futures Trader" "mexc-futures-trader"
    Stop-Service "TradingView Webhook Listener" "tradingview-webhook-listener"
    
    # Step 2: Start MEXC Futures Trader
    Write-Host "`n📋 Step 2: Starting MEXC Futures Trader..." -ForegroundColor Cyan
    $mexcStarted = Start-Service "MEXC Futures Trader" "mexc-futures-trader" "npm start"
    
    if (-not $mexcStarted) {
        throw "Failed to start MEXC Futures Trader"
    }
    
    # Step 3: Test MEXC Trader health
    Write-Host "`n📋 Step 3: Testing MEXC Trader health..." -ForegroundColor Cyan
    $mexcHealthy = Test-ServiceHealth "MEXC Futures Trader" "http://localhost:3001/health"
    
    if (-not $mexcHealthy) {
        Write-Host "⚠️  MEXC Trader may not be fully ready, but continuing..." -ForegroundColor Yellow
    }
    
    # Step 4: Start TradingView Webhook Listener
    Write-Host "`n📋 Step 4: Starting TradingView Webhook Listener..." -ForegroundColor Cyan
    $webhookStarted = Start-Service "TradingView Webhook Listener" "tradingview-webhook-listener" "npm start"
    
    if (-not $webhookStarted) {
        throw "Failed to start TradingView Webhook Listener"
    }
    
    # Step 5: Test Webhook Listener health
    Write-Host "`n📋 Step 5: Testing Webhook Listener health..." -ForegroundColor Cyan
    $webhookHealthy = Test-ServiceHealth "TradingView Webhook Listener" "http://localhost:80/health"
    
    if (-not $webhookHealthy) {
        Write-Host "⚠️  Webhook Listener may not be fully ready, but continuing..." -ForegroundColor Yellow
    }
    
    # Step 6: Run system tests
    Write-Host "`n📋 Step 6: Running system tests..." -ForegroundColor Cyan
    if (Test-Path "test-fixes.js") {
        try {
            node test-fixes.js
            Write-Host "   ✅ System tests passed" -ForegroundColor Green
        } catch {
            Write-Host "   ⚠️  System tests had issues, but services are running" -ForegroundColor Yellow
        }
    } else {
        Write-Host "   ℹ️  Test script not found, skipping tests" -ForegroundColor Gray
    }
    
    # Success message
    Write-Host "`n🎉 DEPLOYMENT SUCCESSFUL!" -ForegroundColor Green
    Write-Host "=====================================" -ForegroundColor Green
    Write-Host "Services Status:" -ForegroundColor White
    Write-Host "  • MEXC Futures Trader: http://localhost:3001/health" -ForegroundColor Gray
    Write-Host "  • Webhook Listener: http://localhost:80/health" -ForegroundColor Gray
    Write-Host "  • Dashboard: http://localhost:80" -ForegroundColor Gray
    Write-Host "`nMonitoring Commands:" -ForegroundColor White
    Write-Host "  • Circuit Breaker: curl http://localhost:3001/health" -ForegroundColor Gray
    Write-Host "  • Emergency Stop: curl http://localhost:80/api/status" -ForegroundColor Gray
    Write-Host "`n✅ System is ready for trading!" -ForegroundColor Green
    
} catch {
    Write-Host "`n❌ DEPLOYMENT FAILED!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "`nPlease check the logs and try manual deployment." -ForegroundColor Yellow
    exit 1
}

# Keep the window open
Write-Host "`nPress any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
