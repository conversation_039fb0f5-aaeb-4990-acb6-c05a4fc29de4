#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Quantity Field Test
ONLY focuses on finding and filling the quantity field with detailed verification.

PURPOSE: Definitively prove whether we can successfully populate the quantity field.
NO OTHER FUNCTIONALITY - just quantity field testing with detailed logging.
"""

import os
import sys
import time
import logging
from datetime import datetime
from playwright.sync_api import sync_playwright

class SimpleQuantityTest:
    """Simple test focused only on quantity field interaction"""
    
    def __init__(self, test_value="2.5"):
        self.test_value = test_value
        
        # Setup detailed logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(f'quantity_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        self.playwright = None
        self.browser = None
        self.page = None
        
        self.logger.info(f"🧪 SIMPLE QUANTITY TEST INITIALIZED - Test Value: {test_value}")
    
    def connect_to_mexc(self):
        """Connect to MEXC browser tab"""
        self.logger.info("🔌 Connecting to MEXC browser tab...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                self.logger.error("❌ No browser contexts found")
                return False
            
            context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in context.pages:
                url = page.url or ''
                self.logger.info(f"   Found page: {url}")
                if 'mexc.com' in url and 'testnet' not in url:
                    mexc_page = page
                    break
            
            if not mexc_page:
                self.logger.error("❌ MEXC page not found - please open https://www.mexc.com/futures/TRU_USDT")
                return False
            
            self.page = mexc_page
            self.logger.info(f"✅ Connected to MEXC: {self.page.url}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Connection failed: {e}")
            return False
    
    def find_quantity_field(self):
        """Find the quantity field with detailed analysis"""
        self.logger.info("🔍 SEARCHING FOR QUANTITY FIELD...")
        
        search_script = f"""
        () => {{
            console.log('🔍 Starting detailed quantity field search...');
            
            const results = {{
                search_methods: [],
                all_inputs: [],
                quantity_candidates: [],
                final_target: null
            }};
            
            // STEP 1: Catalog ALL input fields
            console.log('Step 1: Cataloging all input fields...');
            const allInputs = document.querySelectorAll('input');
            
            allInputs.forEach((input, index) => {{
                const rect = input.getBoundingClientRect();
                const isVisible = rect.width > 0 && rect.height > 0;
                const style = window.getComputedStyle(input);
                const isDisplayed = style.display !== 'none' && style.visibility !== 'hidden';
                
                const inputInfo = {{
                    index: index,
                    type: input.type || 'text',
                    placeholder: input.placeholder || '',
                    name: input.name || '',
                    value: input.value || '',
                    className: input.className || '',
                    disabled: input.disabled,
                    readonly: input.readOnly,
                    visible: isVisible,
                    displayed: isDisplayed,
                    position: {{
                        x: Math.round(rect.x),
                        y: Math.round(rect.y),
                        width: Math.round(rect.width),
                        height: Math.round(rect.height)
                    }}
                }};
                
                results.all_inputs.push(inputInfo);
                
                console.log(`Input ${{index}}: type="${{inputInfo.type}}", placeholder="${{inputInfo.placeholder}}", value="${{inputInfo.value}}", class="${{inputInfo.className}}", position=(${{inputInfo.position.x}}, ${{inputInfo.position.y}}), visible=${{inputInfo.visible}}, displayed=${{inputInfo.displayed}}`);
            }});
            
            // STEP 2: Look for "Quantity" text labels
            console.log('Step 2: Looking for Quantity labels...');
            const allElements = document.querySelectorAll('*');
            const quantityLabels = [];
            
            for (const element of allElements) {{
                const text = element.textContent || '';
                if (text.includes('Quantity') && (text.includes('USDT') || text.includes('USD'))) {{
                    const rect = element.getBoundingClientRect();
                    quantityLabels.push({{
                        element: element,
                        text: text.trim(),
                        tagName: element.tagName,
                        className: element.className || '',
                        position: {{
                            x: Math.round(rect.x),
                            y: Math.round(rect.y)
                        }}
                    }});
                    console.log(`Found Quantity label: "${{text.trim()}}" (${{element.tagName}}) at (${{rect.x}}, ${{rect.y}})`);
                }}
            }}
            
            results.search_methods.push({{
                method: 'quantity_labels',
                found: quantityLabels.length,
                labels: quantityLabels.map(l => ({{ text: l.text, position: l.position, tagName: l.tagName }}))
            }});
            
            // STEP 3: For each quantity label, find nearby inputs
            console.log('Step 3: Finding inputs near Quantity labels...');
            
            for (const labelInfo of quantityLabels) {{
                console.log(`Searching near label: "${{labelInfo.text}}"`);
                
                // Search in parent containers
                let parent = labelInfo.element.parentElement;
                let depth = 0;
                
                while (parent && depth < 5) {{
                    const inputs = parent.querySelectorAll('input');
                    
                    inputs.forEach(input => {{
                        const rect = input.getBoundingClientRect();
                        if (rect.width > 0 && rect.height > 0 && !input.disabled) {{
                            
                            // Calculate distance from label
                            const distance = Math.sqrt(
                                Math.pow(rect.x - labelInfo.position.x, 2) + 
                                Math.pow(rect.y - labelInfo.position.y, 2)
                            );
                            
                            const candidate = {{
                                input: input,
                                type: input.type || 'text',
                                placeholder: input.placeholder || '',
                                name: input.name || '',
                                value: input.value || '',
                                className: input.className || '',
                                position: {{
                                    x: Math.round(rect.x),
                                    y: Math.round(rect.y)
                                }},
                                distance_from_label: Math.round(distance),
                                associated_label: labelInfo.text,
                                label_position: labelInfo.position
                            }};
                            
                            results.quantity_candidates.push(candidate);
                            
                            console.log(`  Candidate input: placeholder="${{candidate.placeholder}}", class="${{candidate.className}}", position=(${{candidate.position.x}}, ${{candidate.position.y}}), distance=${{candidate.distance_from_label}}px`);
                        }}
                    }});
                    
                    parent = parent.parentElement;
                    depth++;
                }}
            }}
            
            // STEP 4: Select best candidate (closest to quantity label, not a price field)
            console.log('Step 4: Selecting best candidate...');
            
            let bestCandidate = null;
            let bestScore = Infinity;
            
            for (const candidate of results.quantity_candidates) {{
                // Skip if it looks like a price field
                const placeholder = candidate.placeholder.toLowerCase();
                const className = candidate.className.toLowerCase();
                
                if (placeholder.includes('price') || className.includes('price')) {{
                    console.log(`  Skipping price field: ${{candidate.placeholder}}`);
                    continue;
                }}
                
                // Score based on distance from label (closer is better)
                const score = candidate.distance_from_label;
                
                if (score < bestScore) {{
                    bestScore = score;
                    bestCandidate = candidate;
                }}
            }}
            
            if (bestCandidate) {{
                results.final_target = bestCandidate;
                console.log(`✅ Selected target: placeholder="${{bestCandidate.placeholder}}", position=(${{bestCandidate.position.x}}, ${{bestCandidate.position.y}}), distance=${{bestCandidate.distance_from_label}}px`);
            }} else {{
                console.log('❌ No suitable quantity field candidate found');
            }}
            
            return {{
                success: true,
                results: results,
                summary: {{
                    total_inputs: results.all_inputs.length,
                    visible_inputs: results.all_inputs.filter(i => i.visible && i.displayed).length,
                    quantity_labels: quantityLabels.length,
                    quantity_candidates: results.quantity_candidates.length,
                    target_found: bestCandidate !== null
                }}
            }};
        }}
        """
        
        try:
            result = self.page.evaluate(search_script)
            
            if result.get('success'):
                summary = result.get('summary', {})
                results_data = result.get('results', {})
                
                self.logger.info("🔍 QUANTITY FIELD SEARCH RESULTS:")
                self.logger.info(f"   Total inputs found: {summary.get('total_inputs', 0)}")
                self.logger.info(f"   Visible inputs: {summary.get('visible_inputs', 0)}")
                self.logger.info(f"   Quantity labels found: {summary.get('quantity_labels', 0)}")
                self.logger.info(f"   Quantity candidates: {summary.get('quantity_candidates', 0)}")
                self.logger.info(f"   Target found: {summary.get('target_found', False)}")
                
                # Log all inputs for debugging
                self.logger.info("📝 ALL INPUT FIELDS:")
                for input_info in results_data.get('all_inputs', []):
                    if input_info.get('visible') and input_info.get('displayed'):
                        self.logger.info(f"   Input {input_info.get('index')}: {input_info.get('placeholder')} at {input_info.get('position')} - class: {input_info.get('className')}")
                
                # Log quantity labels
                search_methods = results_data.get('search_methods', [])
                for method in search_methods:
                    if method.get('method') == 'quantity_labels':
                        self.logger.info("📋 QUANTITY LABELS FOUND:")
                        for label in method.get('labels', []):
                            self.logger.info(f"   '{label.get('text')}' at {label.get('position')}")
                
                # Log candidates
                self.logger.info("🎯 QUANTITY FIELD CANDIDATES:")
                for candidate in results_data.get('quantity_candidates', []):
                    self.logger.info(f"   Placeholder: '{candidate.get('placeholder')}' at {candidate.get('position')} (distance: {candidate.get('distance_from_label')}px)")
                
                # Log final target
                final_target = results_data.get('final_target')
                if final_target:
                    self.logger.info("🎯 SELECTED TARGET:")
                    self.logger.info(f"   Placeholder: '{final_target.get('placeholder')}'")
                    self.logger.info(f"   Class: '{final_target.get('className')}'")
                    self.logger.info(f"   Position: {final_target.get('position')}")
                    self.logger.info(f"   Associated label: '{final_target.get('associated_label')}'")
                    self.logger.info(f"   Distance from label: {final_target.get('distance_from_label')}px")
                    
                    return final_target
                else:
                    self.logger.error("❌ NO TARGET SELECTED")
                    return None
            else:
                self.logger.error("❌ Search script failed")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Search error: {e}")
            return None
    
    def fill_and_verify_field(self, target_field):
        """Fill the target field and verify the value is actually there"""
        self.logger.info("📝 FILLING AND VERIFYING QUANTITY FIELD...")
        
        fill_script = f"""
        () => {{
            console.log('📝 Starting field fill and verification...');
            
            const testValue = '{self.test_value}';
            const targetPosition = {target_field.get('position')};
            
            const results = {{
                field_found: false,
                fill_attempted: false,
                fill_methods: [],
                verification_checks: [],
                final_success: false
            }};
            
            // Find the target input by position
            const allInputs = document.querySelectorAll('input');
            let targetInput = null;
            
            for (const input of allInputs) {{
                const rect = input.getBoundingClientRect();
                const positionMatch = Math.abs(rect.x - targetPosition.x) < 10 && Math.abs(rect.y - targetPosition.y) < 10;
                
                if (positionMatch) {{
                    targetInput = input;
                    results.field_found = true;
                    console.log(`✅ Found target input at (${{rect.x}}, ${{rect.y}})`);
                    break;
                }}
            }}
            
            if (!targetInput) {{
                console.log('❌ Target input not found by position');
                return {{ success: false, error: 'Target input not found', results: results }};
            }}
            
            // Record initial state
            const initialValue = targetInput.value;
            console.log(`Initial value: "${{initialValue}}"`);
            
            // METHOD 1: Basic fill
            console.log('Method 1: Basic fill...');
            try {{
                targetInput.focus();
                targetInput.value = '';
                targetInput.value = testValue;
                targetInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                targetInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
                
                const afterBasic = targetInput.value;
                const basicSuccess = afterBasic === testValue;
                
                results.fill_methods.push({{
                    method: 'basic_fill',
                    attempted: true,
                    result_value: afterBasic,
                    success: basicSuccess
                }});
                
                console.log(`Basic fill result: "${{afterBasic}}" (success: ${{basicSuccess}})`);
                
            }} catch (error) {{
                results.fill_methods.push({{
                    method: 'basic_fill',
                    attempted: true,
                    error: error.message,
                    success: false
                }});
                console.log(`Basic fill error: ${{error.message}}`);
            }}
            
            // METHOD 2: Character-by-character typing (if basic failed)
            if (targetInput.value !== testValue) {{
                console.log('Method 2: Character-by-character typing...');
                try {{
                    targetInput.focus();
                    targetInput.value = '';
                    
                    for (let i = 0; i < testValue.length; i++) {{
                        const char = testValue[i];
                        targetInput.value += char;
                        targetInput.dispatchEvent(new KeyboardEvent('keydown', {{ key: char, bubbles: true }}));
                        targetInput.dispatchEvent(new KeyboardEvent('keyup', {{ key: char, bubbles: true }}));
                        targetInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                    }}
                    
                    targetInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    
                    const afterTyping = targetInput.value;
                    const typingSuccess = afterTyping === testValue;
                    
                    results.fill_methods.push({{
                        method: 'character_typing',
                        attempted: true,
                        result_value: afterTyping,
                        success: typingSuccess
                    }});
                    
                    console.log(`Character typing result: "${{afterTyping}}" (success: ${{typingSuccess}})`);
                    
                }} catch (error) {{
                    results.fill_methods.push({{
                        method: 'character_typing',
                        attempted: true,
                        error: error.message,
                        success: false
                    }});
                    console.log(`Character typing error: ${{error.message}}`);
                }}
            }}
            
            // METHOD 3: React/Vue override (if still failed)
            if (targetInput.value !== testValue) {{
                console.log('Method 3: React/Vue override...');
                try {{
                    const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set;
                    nativeInputValueSetter.call(targetInput, testValue);
                    
                    targetInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                    targetInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    
                    const afterOverride = targetInput.value;
                    const overrideSuccess = afterOverride === testValue;
                    
                    results.fill_methods.push({{
                        method: 'react_vue_override',
                        attempted: true,
                        result_value: afterOverride,
                        success: overrideSuccess
                    }});
                    
                    console.log(`React/Vue override result: "${{afterOverride}}" (success: ${{overrideSuccess}})`);
                    
                }} catch (error) {{
                    results.fill_methods.push({{
                        method: 'react_vue_override',
                        attempted: true,
                        error: error.message,
                        success: false
                    }});
                    console.log(`React/Vue override error: ${{error.message}}`);
                }}
            }}
            
            // VERIFICATION: Multiple checks over time
            console.log('Starting verification checks...');
            
            const performVerification = (checkNumber, delay) => {{
                setTimeout(() => {{
                    const currentValue = targetInput.value;
                    const currentAttribute = targetInput.getAttribute('value');
                    const isVisible = targetInput.getBoundingClientRect().width > 0;
                    const isFocused = document.activeElement === targetInput;
                    
                    const verification = {{
                        check_number: checkNumber,
                        delay_ms: delay,
                        dom_value: currentValue,
                        attribute_value: currentAttribute,
                        visible: isVisible,
                        focused: isFocused,
                        success: currentValue === testValue,
                        timestamp: Date.now()
                    }};
                    
                    results.verification_checks.push(verification);
                    console.log(`Verification ${{checkNumber}} (after ${{delay}}ms): value="${{currentValue}}", attribute="${{currentAttribute}}", success=${{verification.success}}`);
                    
                    if (checkNumber === 3) {{
                        // Final verification
                        results.final_success = currentValue === testValue;
                        console.log(`FINAL RESULT: ${{results.final_success ? 'SUCCESS' : 'FAILED'}} - Final value: "${{currentValue}}"`);
                    }}
                }}, delay);
            }};
            
            // Perform verification checks at different intervals
            performVerification(1, 500);   // 0.5 seconds
            performVerification(2, 1500);  // 1.5 seconds
            performVerification(3, 3000);  // 3 seconds
            
            results.fill_attempted = true;
            
            return {{
                success: true,
                results: results,
                immediate_value: targetInput.value,
                immediate_success: targetInput.value === testValue
            }};
        }}
        """
        
        try:
            result = self.page.evaluate(fill_script)
            
            if result.get('success'):
                results_data = result.get('results', {})
                immediate_value = result.get('immediate_value', '')
                immediate_success = result.get('immediate_success', False)
                
                self.logger.info("📝 FIELD FILL RESULTS:")
                self.logger.info(f"   Field found: {results_data.get('field_found', False)}")
                self.logger.info(f"   Fill attempted: {results_data.get('fill_attempted', False)}")
                self.logger.info(f"   Immediate value: '{immediate_value}'")
                self.logger.info(f"   Immediate success: {immediate_success}")
                
                # Log fill methods
                self.logger.info("🔧 FILL METHODS TRIED:")
                for method in results_data.get('fill_methods', []):
                    method_name = method.get('method', 'unknown')
                    attempted = method.get('attempted', False)
                    success = method.get('success', False)
                    result_value = method.get('result_value', '')
                    error = method.get('error', '')
                    
                    status = "✅" if success else "❌"
                    self.logger.info(f"   {status} {method_name}: '{result_value}'" + (f" (Error: {error})" if error else ""))
                
                # Wait for verification checks to complete
                self.logger.info("⏳ Waiting for verification checks...")
                time.sleep(4)  # Wait for all verification checks
                
                # Get final verification results
                final_verification = self.get_final_verification(target_field)
                return final_verification
            else:
                self.logger.error("❌ Fill script failed")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Fill and verify error: {e}")
            return False
    
    def get_final_verification(self, target_field):
        """Get final verification results"""
        self.logger.info("🔍 FINAL VERIFICATION...")
        
        verification_script = f"""
        () => {{
            const testValue = '{self.test_value}';
            const targetPosition = {target_field.get('position')};
            
            // Find the target input again
            const allInputs = document.querySelectorAll('input');
            let targetInput = null;
            
            for (const input of allInputs) {{
                const rect = input.getBoundingClientRect();
                const positionMatch = Math.abs(rect.x - targetPosition.x) < 10 && Math.abs(rect.y - targetPosition.y) < 10;
                
                if (positionMatch) {{
                    targetInput = input;
                    break;
                }}
            }}
            
            if (!targetInput) {{
                return {{ success: false, error: 'Target input not found for verification' }};
            }}
            
            const finalValue = targetInput.value;
            const finalAttribute = targetInput.getAttribute('value');
            const isVisible = targetInput.getBoundingClientRect().width > 0;
            const computedStyle = window.getComputedStyle(targetInput);
            
            return {{
                success: true,
                final_value: finalValue,
                final_attribute: finalAttribute,
                visible: isVisible,
                display: computedStyle.display,
                visibility: computedStyle.visibility,
                verification_success: finalValue === testValue,
                position: {{
                    x: Math.round(targetInput.getBoundingClientRect().x),
                    y: Math.round(targetInput.getBoundingClientRect().y)
                }}
            }};
        }}
        """
        
        try:
            result = self.page.evaluate(verification_script)
            
            if result.get('success'):
                final_value = result.get('final_value', '')
                verification_success = result.get('verification_success', False)
                
                self.logger.info("🔍 FINAL VERIFICATION RESULTS:")
                self.logger.info(f"   Final DOM value: '{final_value}'")
                self.logger.info(f"   Final attribute value: '{result.get('final_attribute', '')}'")
                self.logger.info(f"   Field visible: {result.get('visible', False)}")
                self.logger.info(f"   Display style: {result.get('display', '')}")
                self.logger.info(f"   Visibility style: {result.get('visibility', '')}")
                self.logger.info(f"   Position: {result.get('position', {})}")
                self.logger.info(f"   VERIFICATION SUCCESS: {verification_success}")
                
                if verification_success:
                    self.logger.info("🎉 SUCCESS: Quantity field contains the expected value!")
                else:
                    self.logger.error(f"❌ FAILED: Expected '{self.test_value}', got '{final_value}'")
                
                return verification_success
            else:
                self.logger.error("❌ Final verification script failed")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Final verification error: {e}")
            return False
    
    def run_simple_test(self):
        """Run the simple quantity field test"""
        self.logger.info("🧪 STARTING SIMPLE QUANTITY FIELD TEST")
        self.logger.info("="*60)
        
        try:
            # Step 1: Connect
            if not self.connect_to_mexc():
                return False
            
            # Step 2: Find quantity field
            target_field = self.find_quantity_field()
            if not target_field:
                return False
            
            # Step 3: Fill and verify
            success = self.fill_and_verify_field(target_field)
            
            self.logger.info("="*60)
            if success:
                self.logger.info("🎉 SIMPLE QUANTITY TEST: SUCCESS")
                self.logger.info(f"   The quantity field now contains: '{self.test_value}'")
                self.logger.info("   Please manually verify this in the browser!")
            else:
                self.logger.error("❌ SIMPLE QUANTITY TEST: FAILED")
                self.logger.error("   The quantity field does not contain the expected value")
            
            return success
            
        except Exception as e:
            self.logger.error(f"❌ Test error: {e}")
            return False
    
    def cleanup(self):
        """Cleanup resources"""
        try:
            if self.playwright:
                self.playwright.stop()
        except:
            pass

def main():
    print("""
🧪 SIMPLE QUANTITY FIELD TEST
=============================
PURPOSE: Definitively prove whether we can fill the quantity field

WHAT THIS TEST DOES:
1. Connect to MEXC browser tab
2. Search for "Quantity (USDT)" field with detailed analysis
3. Attempt to fill it with test value "2.5"
4. Verify the value is actually in the field
5. Provide detailed logging for manual verification

REQUIREMENTS:
- MEXC futures page must be open: https://www.mexc.com/futures/TRU_USDT
- Browser must be running with --remote-debugging-port=9222

Starting test...
    """)
    
    test = SimpleQuantityTest("2.5")
    
    try:
        success = test.run_simple_test()
        
        print("\n" + "="*60)
        if success:
            print("🎉 TEST RESULT: SUCCESS")
            print("   The quantity field should now contain '2.5'")
            print("   Please check the MEXC browser tab to manually verify!")
        else:
            print("❌ TEST RESULT: FAILED")
            print("   The quantity field was not successfully filled")
            print("   Check the log file for detailed analysis")
        print("="*60)
        
    except KeyboardInterrupt:
        print("\n👋 Test interrupted")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
    finally:
        test.cleanup()

if __name__ == "__main__":
    main()
