const { chromium } = require('playwright');

class SingleBrowserTrader {
    constructor(port = 9222) {
        this.browser = null;
        this.page = null;
        this.port = port;
    }

    async connectToBrowser() {
        console.log(`🔗 Connecting to browser on port ${this.port}...`);
        
        try {
            this.browser = await chromium.connectOverCDP(`http://localhost:${this.port}`);
            const contexts = this.browser.contexts();
            
            if (contexts.length > 0) {
                const pages = contexts[0].pages();
                this.page = pages.length > 0 ? pages[0] : await contexts[0].newPage();
            } else {
                const context = await this.browser.newContext();
                this.page = await context.newPage();
            }
            
            console.log(`✅ Connected to browser on port ${this.port}`);
            return true;
        } catch (error) {
            console.error(`❌ Connection failed to port ${this.port}:`, error.message);
            return false;
        }
    }

    async retryFindElement(selectors, maxRetries = 5, timeout = 300) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            for (const selector of selectors) {
                try {
                    const element = this.page.locator(selector).first();
                    const isVisible = await element.isVisible({ timeout });
                    
                    if (isVisible) {
                        console.log(`✅ Found element: ${selector} (attempt ${attempt})`);
                        return element;
                    }
                } catch (error) {
                    continue;
                }
            }
            
            if (attempt < maxRetries) {
                console.log(`⚠️ Retry ${attempt}/${maxRetries} - Element not found, retrying...`);
                await new Promise(resolve => setTimeout(resolve, 200));
            }
        }
        
        throw new Error(`Element not found after ${maxRetries} attempts`);
    }

    async executeOpenTrade() {
        const startTime = Date.now();
        console.log('📈 EXECUTING OPEN TRADE...');
        
        try {
            // Ensure we're on the right page
            const url = this.page.url();
            if (!url.includes('mexc.com/futures/TRU_USDT')) {
                console.log('🌐 Navigating to TRU_USDT...');
                await this.page.goto('https://www.mexc.com/futures/TRU_USDT');
                await this.page.waitForTimeout(2000);
            }

            // Step 1: Make sure we're in Open mode
            console.log('🔄 Step 1: Ensuring Open mode...');
            try {
                const openModeSelectors = [
                    'button:has-text("Open")',
                    'text=Open',
                    '.open-tab',
                    '[data-tab="open"]'
                ];
                
                const openModeButton = await this.retryFindElement(openModeSelectors, 3);
                await openModeButton.click();
                console.log('✅ Switched to Open mode');
                await this.page.waitForTimeout(500);
            } catch (error) {
                console.log('⚠️ Open mode button not found, assuming already in open mode');
            }

            // Step 2: Fill Quantity with retry
            console.log('🔢 Step 2: Filling quantity...');
            const quantitySelectors = [
                'text=Quantity(USDT) >> xpath=following::input[1]',
                'text=Quantity >> xpath=following::input[1]',
                'input[placeholder*="quantity"]',
                'input[placeholder*="amount"]',
                'input[type="number"]'
            ];

            const quantityField = await this.retryFindElement(quantitySelectors);
            await quantityField.click();
            await quantityField.fill('0.3600');
            console.log('✅ Quantity filled: 0.3600');

            // Step 3: Click Open Long with retry
            console.log('📈 Step 3: Clicking Open Long...');
            const openLongSelectors = [
                'button:has-text("Open Long")',
                'text=Open Long',
                '.open-long',
                'button[class*="long"]'
            ];

            const openLongButton = await this.retryFindElement(openLongSelectors);
            await openLongButton.click();
            console.log('✅ Open Long clicked');

            // Step 4: Handle popup with retry
            console.log('✅ Step 4: Handling popup...');
            const confirmClicked = await this.handleConfirmPopup('OPEN');

            const executionTime = Date.now() - startTime;

            // Verify success
            const verified = await this.verifyTrade('OPEN');

            console.log(`⚡ OPEN trade completed in ${executionTime}ms`);
            console.log(`🎉 OPEN verified: ${verified ? '✅ YES' : '❌ NO'}`);

            return {
                success: verified,
                executionTime,
                verified,
                type: 'OPEN'
            };

        } catch (error) {
            const executionTime = Date.now() - startTime;
            console.error(`❌ OPEN trade failed after ${executionTime}ms:`, error.message);
            return { success: false, executionTime, error: error.message, type: 'OPEN' };
        }
    }

    async executeCloseTrade() {
        const startTime = Date.now();
        console.log('📉 EXECUTING CLOSE TRADE...');
        
        try {
            // Ensure we're on the right page
            const url = this.page.url();
            if (!url.includes('mexc.com/futures/TRU_USDT')) {
                console.log('🌐 Navigating to TRU_USDT...');
                await this.page.goto('https://www.mexc.com/futures/TRU_USDT');
                await this.page.waitForTimeout(2000);
            }

            // Step 1: Switch to Close mode
            console.log('🔄 Step 1: Switching to Close mode...');
            const closeModeSelectors = [
                'button:has-text("Close")',
                'text=Close',
                '.close-tab',
                '[data-tab="close"]'
            ];

            const closeModeButton = await this.retryFindElement(closeModeSelectors);
            await closeModeButton.click();
            console.log('✅ Switched to Close mode');
            await this.page.waitForTimeout(500);

            // Step 2: Fill Quantity with retry
            console.log('🔢 Step 2: Filling quantity...');
            const quantitySelectors = [
                'text=Quantity(USDT) >> xpath=following::input[1]',
                'text=Quantity >> xpath=following::input[1]',
                'input[placeholder*="quantity"]',
                'input[placeholder*="amount"]',
                'input[type="number"]'
            ];

            const quantityField = await this.retryFindElement(quantitySelectors);
            await quantityField.click();
            await quantityField.fill('0.3600');
            console.log('✅ Quantity filled: 0.3600');

            // Step 3: Click Close Long (red button) with retry
            console.log('📉 Step 3: Clicking Close Long...');
            const closeLongSelectors = [
                'button:has-text("Close Long")',
                'text=Close Long',
                '.close-long',
                'button[class*="close"][class*="long"]',
                // Alternative: might be "Sell" in close mode
                'button:has-text("Sell")',
                '.sell-btn'
            ];

            const closeLongButton = await this.retryFindElement(closeLongSelectors);
            await closeLongButton.click();
            console.log('✅ Close Long clicked');

            // Step 4: Handle popup with retry
            console.log('✅ Step 4: Handling popup...');
            const confirmClicked = await this.handleConfirmPopup('CLOSE');

            const executionTime = Date.now() - startTime;

            // Verify success
            const verified = await this.verifyTrade('CLOSE');

            console.log(`⚡ CLOSE trade completed in ${executionTime}ms`);
            console.log(`🎉 CLOSE verified: ${verified ? '✅ YES' : '❌ NO'}`);

            return {
                success: verified,
                executionTime,
                verified,
                type: 'CLOSE'
            };

        } catch (error) {
            const executionTime = Date.now() - startTime;
            console.error(`❌ CLOSE trade failed after ${executionTime}ms:`, error.message);
            return { success: false, executionTime, error: error.message, type: 'CLOSE' };
        }
    }

    async handleConfirmPopup(tradeType) {
        const confirmSelectors = [
            'button:has-text("Confirm")',
            'text=Confirm',
            '.confirm-btn',
            'button[class*="confirm"]'
        ];

        // Aggressive popup handling with retry
        for (let attempt = 1; attempt <= 20; attempt++) {
            for (const selector of confirmSelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    const isVisible = await element.isVisible({ timeout: 100 });
                    
                    if (isVisible) {
                        await element.click();
                        console.log(`✅ ${tradeType}: Confirm clicked (attempt ${attempt})`);
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }
            await new Promise(resolve => setTimeout(resolve, 50));
        }

        console.log(`⚠️ ${tradeType}: Confirm button not found after 20 attempts`);
        return false;
    }

    async verifyTrade(tradeType) {
        try {
            const successSelectors = [
                'text=Purchased successfully',
                'text=Success',
                'text=success',
                'text=completed',
                '.success',
                '.toast-success'
            ];

            for (const selector of successSelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    const isVisible = await element.isVisible({ timeout: 500 });
                    if (isVisible) {
                        const text = await element.textContent();
                        console.log(`✅ ${tradeType} success: ${text}`);
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }
            return false;
        } catch (error) {
            return false;
        }
    }
}

async function executeCommand(command, port) {
    const trader = new SingleBrowserTrader(port);
    
    try {
        console.log('🎯 MEXC SINGLE BROWSER TRADER');
        console.log('==============================');
        console.log(`🌐 Port: ${port}`);
        console.log('⚡ Target: <2 seconds per trade');
        console.log('🔄 Retry logic: 5 attempts per element');
        console.log('');

        const connected = await trader.connectToBrowser();
        if (!connected) {
            throw new Error(`Failed to connect to browser on port ${port}`);
        }

        let result;
        
        switch (command) {
            case 'open':
                result = await trader.executeOpenTrade();
                break;
                
            case 'close':
                result = await trader.executeCloseTrade();
                break;
                
            default:
                console.log('📋 USAGE:');
                console.log('=========');
                console.log('node single-browser-trader.js open [port]   - Execute OPEN trade');
                console.log('node single-browser-trader.js close [port]  - Execute CLOSE trade');
                console.log('');
                console.log('Examples:');
                console.log('node single-browser-trader.js open 9222     - Open trade on port 9222');
                console.log('node single-browser-trader.js close 9223    - Close trade on port 9223');
                console.log('');
                console.log('🚀 Prerequisites:');
                console.log('Start browser with:');
                console.log('chrome.exe --remote-debugging-port=9222 --user-data-dir="./browser_data"');
                return;
        }

        // Final results
        if (result.success) {
            if (result.executionTime && result.executionTime < 2000) {
                console.log('\n🏆 TARGET ACHIEVED!');
                console.log(`⚡ Executed in ${result.executionTime}ms (<2 seconds)`);
            } else {
                console.log('\n✅ Trade completed successfully!');
            }
        } else {
            console.log('\n❌ Trade failed');
            if (result.error) console.log(`Error: ${result.error}`);
        }

        // Save results
        require('fs').writeFileSync(`single-browser-results-${port}.json`, JSON.stringify(result, null, 2));
        
        process.exit(result.success ? 0 : 1);
        
    } catch (error) {
        console.error('💥 Single browser trader failed:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    const command = process.argv[2];
    const port = parseInt(process.argv[3]) || 9222;
    executeCommand(command, port);
}

module.exports = SingleBrowserTrader;
