#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Blur Prevention System
DISCOVERED ISSUE: MEXC clears quantity field on blur event (when field loses focus)
SOLUTION: Block blur events and maintain focus during trade execution
"""

import os
import sys
import time
import logging
from datetime import datetime
from playwright.sync_api import sync_playwright

class BlurPreventionSystem:
    """System that prevents blur events from clearing the quantity field"""
    
    def __init__(self, symbol="TRU_USDT", side="BUY", quantity=2.5):
        self.symbol = symbol
        self.side = side
        self.quantity = quantity
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(f'blur_prevention_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        self.playwright = None
        self.browser = None
        self.page = None
        
        self.logger.info(f"BLUR PREVENTION SYSTEM: {side} {quantity} {symbol}")
    
    def connect_to_mexc(self):
        """Connect to MEXC browser tab"""
        self.logger.info("Connecting to MEXC...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                self.logger.error("No browser contexts found")
                return False
            
            context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in context.pages:
                url = page.url or ''
                if 'mexc.com' in url and 'testnet' not in url:
                    mexc_page = page
                    break
            
            if not mexc_page:
                self.logger.error("MEXC page not found")
                return False
            
            self.page = mexc_page
            self.logger.info(f"Connected to MEXC: {self.page.url}")
            return True
            
        except Exception as e:
            self.logger.error(f"Connection failed: {e}")
            return False
    
    def setup_blur_prevention_system(self):
        """Setup system to prevent blur events from clearing quantity field"""
        self.logger.info("Setting up blur prevention system...")
        
        blur_prevention_script = f"""
        () => {{
            console.log('Setting up blur prevention system...');
            
            const TARGET_VALUE = '{self.quantity}';
            const SIDE = '{self.side}';
            
            // Global blur prevention state
            window.mexcBlurPrevention = {{
                quantityField: null,
                tradeButton: null,
                originalBlurHandlers: [],
                blurEventsBlocked: 0,
                valueRestorations: 0,
                preventionActive: false,
                executingTrade: false
            }};
            
            const prevention = window.mexcBlurPrevention;
            
            // Step 1: Find elements
            function findElements() {{
                // Find quantity field
                const inputs = document.querySelectorAll('input.ant-input');
                for (const input of inputs) {{
                    const rect = input.getBoundingClientRect();
                    if (Math.abs(rect.x - 668) < 10 && Math.abs(rect.y - 603) < 50) {{
                        prevention.quantityField = input;
                        console.log(`Quantity field found at (${{rect.x}}, ${{rect.y}})`);
                        break;
                    }}
                }}
                
                // Find trade button
                const buttonClass = SIDE === 'BUY' ? 'component_longBtn__eazYU' : 'component_shortBtn__x5P3I';
                prevention.tradeButton = document.querySelector(`button.${{buttonClass}}`);
                
                if (prevention.tradeButton) {{
                    const rect = prevention.tradeButton.getBoundingClientRect();
                    console.log(`Trade button found: "${{prevention.tradeButton.textContent}}" at (${{rect.x}}, ${{rect.y}})`);
                }}
                
                return prevention.quantityField && prevention.tradeButton;
            }}
            
            // Step 2: Block blur events on quantity field
            function blockBlurEvents() {{
                if (!prevention.quantityField) return false;
                
                console.log('Blocking blur events on quantity field...');
                
                // Method 1: Override blur event handlers
                const originalBlur = prevention.quantityField.blur;
                prevention.quantityField.blur = function() {{
                    if (prevention.preventionActive) {{
                        console.log('Blur event blocked (method 1)');
                        prevention.blurEventsBlocked++;
                        return;
                    }}
                    return originalBlur.apply(this, arguments);
                }};
                
                // Method 2: Capture and block blur events
                prevention.quantityField.addEventListener('blur', function(event) {{
                    if (prevention.preventionActive) {{
                        console.log('Blur event blocked (method 2)');
                        event.preventDefault();
                        event.stopPropagation();
                        event.stopImmediatePropagation();
                        prevention.blurEventsBlocked++;
                        
                        // Immediately restore focus and value
                        setTimeout(() => {{
                            if (prevention.quantityField.value !== TARGET_VALUE) {{
                                prevention.quantityField.focus();
                                prevention.quantityField.value = TARGET_VALUE;
                                prevention.quantityField.dispatchEvent(new Event('input', {{ bubbles: true }}));
                                prevention.valueRestorations++;
                                console.log(`Value restored after blocked blur: "${{prevention.quantityField.value}}"`);
                            }}
                        }}, 1);
                        
                        return false;
                    }}
                }}, true); // Use capture phase
                
                // Method 3: Block all blur-related events
                const blurEvents = ['blur', 'focusout'];
                blurEvents.forEach(eventType => {{
                    prevention.quantityField.addEventListener(eventType, function(event) {{
                        if (prevention.preventionActive) {{
                            console.log(`${{eventType}} event blocked (method 3)`);
                            event.preventDefault();
                            event.stopPropagation();
                            event.stopImmediatePropagation();
                            prevention.blurEventsBlocked++;
                            return false;
                        }}
                    }}, true);
                }});
                
                console.log('Blur event blocking active');
                return true;
            }}
            
            // Step 3: Advanced value protection
            function setupValueProtection() {{
                if (!prevention.quantityField) return false;
                
                console.log('Setting up advanced value protection...');
                
                // Monitor for value changes and restore immediately
                const observer = new MutationObserver(function(mutations) {{
                    if (!prevention.preventionActive) return;
                    
                    mutations.forEach(function(mutation) {{
                        if (mutation.type === 'attributes' && mutation.attributeName === 'value') {{
                            const currentValue = prevention.quantityField.value;
                            if (currentValue !== TARGET_VALUE) {{
                                console.log(`Value change detected: "${{currentValue}}" -> "${{TARGET_VALUE}}"`);
                                prevention.quantityField.value = TARGET_VALUE;
                                prevention.valueRestorations++;
                            }}
                        }}
                    }});
                }});
                
                observer.observe(prevention.quantityField, {{
                    attributes: true,
                    attributeFilter: ['value']
                }});
                
                // Property descriptor override
                const originalDescriptor = Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value');
                Object.defineProperty(prevention.quantityField, 'value', {{
                    get: function() {{
                        return originalDescriptor.get.call(this);
                    }},
                    set: function(newValue) {{
                        if (prevention.preventionActive && newValue !== TARGET_VALUE && newValue !== '') {{
                            console.log(`Value setter blocked: "${{newValue}}" -> "${{TARGET_VALUE}}"`);
                            prevention.valueRestorations++;
                            return originalDescriptor.set.call(this, TARGET_VALUE);
                        }}
                        return originalDescriptor.set.call(this, newValue);
                    }}
                }});
                
                console.log('Advanced value protection active');
                return true;
            }}
            
            // Step 4: Focus-maintaining click execution
            function executeFocusMaintainingClick() {{
                if (!prevention.quantityField || !prevention.tradeButton) {{
                    return {{ success: false, error: 'Required elements not found' }};
                }}
                
                if (prevention.executingTrade) {{
                    return {{ success: false, error: 'Trade already executing' }};
                }}
                
                prevention.executingTrade = true;
                prevention.preventionActive = true;
                
                console.log('Executing focus-maintaining click...');
                
                try {{
                    // Step 1: Ensure quantity field has focus and value
                    prevention.quantityField.focus();
                    prevention.quantityField.value = TARGET_VALUE;
                    prevention.quantityField.dispatchEvent(new Event('input', {{ bubbles: true }}));
                    prevention.quantityField.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    
                    const preClickValue = prevention.quantityField.value;
                    console.log(`Pre-click value: "${{preClickValue}}"`);
                    
                    // Step 2: Click button WITHOUT losing focus from quantity field
                    // Use mouse events instead of focus/click to avoid blur
                    const buttonRect = prevention.tradeButton.getBoundingClientRect();
                    const clickEvent = new MouseEvent('click', {{
                        bubbles: true,
                        cancelable: true,
                        clientX: buttonRect.x + buttonRect.width / 2,
                        clientY: buttonRect.y + buttonRect.height / 2
                    }});
                    
                    prevention.tradeButton.dispatchEvent(clickEvent);
                    
                    const duringClickValue = prevention.quantityField.value;
                    console.log(`During-click value: "${{duringClickValue}}"`);
                    
                    // Step 3: Maintain focus on quantity field
                    setTimeout(() => {{
                        prevention.quantityField.focus();
                        if (prevention.quantityField.value !== TARGET_VALUE) {{
                            prevention.quantityField.value = TARGET_VALUE;
                            prevention.quantityField.dispatchEvent(new Event('input', {{ bubbles: true }}));
                            prevention.valueRestorations++;
                        }}
                    }}, 1);
                    
                    // Step 4: Check results after brief delay
                    setTimeout(() => {{
                        const postClickValue = prevention.quantityField.value;
                        console.log(`Post-click value: "${{postClickValue}}"`);
                        
                        // Check for error messages
                        const errorElements = document.querySelectorAll('.ant-message-error, .ant-notification-notice-error, .error, [class*="error"]');
                        const errors = [];
                        for (const errorEl of errorElements) {{
                            const errorText = errorEl.textContent || '';
                            if (errorText.toLowerCase().includes('quantity') || 
                                errorText.toLowerCase().includes('amount') || 
                                errorText.toLowerCase().includes('enter')) {{
                                errors.push(errorText.trim());
                            }}
                        }}
                        
                        console.log(`Errors found: ${{errors.length}}`);
                        errors.forEach(error => console.log(`Error: ${{error}}`));
                        
                        // Check for success messages
                        const successElements = document.querySelectorAll('.ant-message-success, .ant-notification-notice-success, .success, [class*="success"]');
                        const successes = [];
                        for (const successEl of successElements) {{
                            const successText = successEl.textContent || '';
                            if (successText.trim()) {{
                                successes.push(successText.trim());
                            }}
                        }}
                        
                        console.log(`Success messages: ${{successes.length}}`);
                        successes.forEach(success => console.log(`Success: ${{success}}`));
                        
                    }}, 1000);
                    
                    return {{
                        success: true,
                        pre_click_value: preClickValue,
                        during_click_value: duringClickValue,
                        button_text: prevention.tradeButton.textContent
                    }};
                    
                }} catch (error) {{
                    return {{ success: false, error: error.message }};
                }} finally {{
                    // Keep prevention active for a few more seconds
                    setTimeout(() => {{
                        prevention.executingTrade = false;
                        prevention.preventionActive = false;
                        console.log('Blur prevention deactivated');
                    }}, 5000);
                }}
            }}
            
            // Initialize system
            if (!findElements()) {{
                return {{ success: false, error: 'Required elements not found' }};
            }}
            
            if (!blockBlurEvents()) {{
                return {{ success: false, error: 'Failed to block blur events' }};
            }}
            
            if (!setupValueProtection()) {{
                return {{ success: false, error: 'Failed to setup value protection' }};
            }}
            
            // Expose click function
            window.executeFocusMaintainingClick = executeFocusMaintainingClick;
            
            console.log('Blur prevention system ready');
            
            return {{
                success: true,
                quantity_field_position: {{
                    x: Math.round(prevention.quantityField.getBoundingClientRect().x),
                    y: Math.round(prevention.quantityField.getBoundingClientRect().y)
                }},
                trade_button_text: prevention.tradeButton.textContent,
                blur_blocking_active: true,
                value_protection_active: true
            }};
        }}
        """
        
        try:
            result = self.page.evaluate(blur_prevention_script)
            
            if result.get('success'):
                field_position = result.get('quantity_field_position', {})
                button_text = result.get('trade_button_text', '')
                blur_blocking = result.get('blur_blocking_active', False)
                value_protection = result.get('value_protection_active', False)
                
                self.logger.info("BLUR PREVENTION SYSTEM SETUP SUCCESS:")
                self.logger.info(f"   Quantity field position: {field_position}")
                self.logger.info(f"   Trade button text: '{button_text}'")
                self.logger.info(f"   Blur blocking active: {blur_blocking}")
                self.logger.info(f"   Value protection active: {value_protection}")
                
                return True
            else:
                error = result.get('error', 'Unknown error')
                self.logger.error(f"Blur prevention setup failed: {error}")
                return False
                
        except Exception as e:
            self.logger.error(f"Blur prevention setup error: {e}")
            return False
    
    def execute_protected_trade(self):
        """Execute trade with blur prevention active"""
        self.logger.info("Executing protected trade...")
        
        execute_script = """
        () => {
            if (typeof window.executeFocusMaintainingClick === 'function') {
                return window.executeFocusMaintainingClick();
            } else {
                return { success: false, error: 'Focus-maintaining click function not available' };
            }
        }
        """
        
        try:
            result = self.page.evaluate(execute_script)
            
            if result.get('success'):
                pre_click_value = result.get('pre_click_value', '')
                during_click_value = result.get('during_click_value', '')
                button_text = result.get('button_text', '')
                
                self.logger.info("PROTECTED TRADE EXECUTION SUCCESS:")
                self.logger.info(f"   Pre-click value: '{pre_click_value}'")
                self.logger.info(f"   During-click value: '{during_click_value}'")
                self.logger.info(f"   Button clicked: '{button_text}'")
                
                # Wait for response
                time.sleep(3)
                
                # Check final status
                return self.check_final_status()
            else:
                error = result.get('error', 'Unknown error')
                self.logger.error(f"Protected trade execution failed: {error}")
                return False
                
        except Exception as e:
            self.logger.error(f"Protected trade execution error: {e}")
            return False
    
    def check_final_status(self):
        """Check final status after protected trade"""
        status_script = """
        () => {
            const prevention = window.mexcBlurPrevention;
            
            if (!prevention) {
                return { success: false, error: 'Prevention system not found' };
            }
            
            const currentValue = prevention.quantityField ? prevention.quantityField.value : 'N/A';
            
            // Check for errors
            const errorElements = document.querySelectorAll('.ant-message-error, .ant-notification-notice-error, .error, [class*="error"]');
            const errors = [];
            let hasQuantityError = false;
            
            for (const errorEl of errorElements) {
                const errorText = errorEl.textContent || '';
                if (errorText.trim()) {
                    errors.push(errorText.trim());
                    
                    if (errorText.toLowerCase().includes('quantity') || 
                        errorText.toLowerCase().includes('amount') || 
                        errorText.toLowerCase().includes('enter')) {
                        hasQuantityError = true;
                    }
                }
            }
            
            // Check for successes
            const successElements = document.querySelectorAll('.ant-message-success, .ant-notification-notice-success, .success, [class*="success"]');
            const successes = [];
            
            for (const successEl of successElements) {
                const successText = successEl.textContent || '';
                if (successText.trim()) {
                    successes.push(successText.trim());
                }
            }
            
            return {
                success: true,
                current_quantity_value: currentValue,
                blur_events_blocked: prevention.blurEventsBlocked,
                value_restorations: prevention.valueRestorations,
                errors: errors,
                successes: successes,
                has_quantity_error: hasQuantityError
            };
        }
        """
        
        try:
            result = self.page.evaluate(status_script)
            
            if result.get('success'):
                current_value = result.get('current_quantity_value', '')
                blur_blocked = result.get('blur_events_blocked', 0)
                restorations = result.get('value_restorations', 0)
                errors = result.get('errors', [])
                successes = result.get('successes', [])
                has_quantity_error = result.get('has_quantity_error', False)
                
                self.logger.info("FINAL STATUS CHECK:")
                self.logger.info(f"   Current quantity value: '{current_value}'")
                self.logger.info(f"   Blur events blocked: {blur_blocked}")
                self.logger.info(f"   Value restorations: {restorations}")
                self.logger.info(f"   Errors: {errors}")
                self.logger.info(f"   Successes: {successes}")
                self.logger.info(f"   Has quantity error: {has_quantity_error}")
                
                return not has_quantity_error
            else:
                error = result.get('error', 'Unknown error')
                self.logger.error(f"Status check failed: {error}")
                return False
                
        except Exception as e:
            self.logger.error(f"Status check error: {e}")
            return False
    
    def execute_complete_protected_trade(self):
        """Execute complete protected trade"""
        self.logger.info("EXECUTING COMPLETE PROTECTED TRADE")
        self.logger.info("="*60)
        
        try:
            # Step 1: Connect
            if not self.connect_to_mexc():
                return False
            
            # Step 2: Setup blur prevention system
            if not self.setup_blur_prevention_system():
                return False
            
            # Step 3: Wait for system to stabilize
            self.logger.info("Allowing system to stabilize...")
            time.sleep(2)
            
            # Step 4: Execute protected trade
            success = self.execute_protected_trade()
            
            self.logger.info("="*60)
            if success:
                self.logger.info("PROTECTED TRADE EXECUTION: SUCCESS")
                self.logger.info("Blur prevention system prevented field clearing!")
            else:
                self.logger.error("PROTECTED TRADE EXECUTION: FAILED")
                self.logger.error("Blur prevention was not sufficient")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Complete protected trade error: {e}")
            return False
    
    def cleanup(self):
        """Cleanup resources"""
        try:
            if self.playwright:
                self.playwright.stop()
        except:
            pass

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Blur Prevention System")
    parser.add_argument("--symbol", default="TRU_USDT", help="Trading symbol")
    parser.add_argument("--side", choices=["BUY", "SELL"], default="BUY", help="Order side")
    parser.add_argument("--quantity", type=float, default=2.5, help="Order quantity")
    
    args = parser.parse_args()
    
    print(f"""
BLUR PREVENTION SYSTEM
======================
DISCOVERED ISSUE: MEXC clears quantity field on blur event (focus loss)
SOLUTION: Block blur events and maintain focus during trade execution

PREVENTION METHODS:
1. Override blur() method on quantity field
2. Capture and block blur events (preventDefault)
3. Block focusout events
4. Advanced value protection with MutationObserver
5. Property descriptor override
6. Focus-maintaining click (MouseEvent instead of focus/click)

TARGET: {args.side} {args.quantity} {args.symbol}
    """)
    
    system = BlurPreventionSystem(args.symbol, args.side, args.quantity)
    
    try:
        success = system.execute_complete_protected_trade()
        
        print("\n" + "="*60)
        if success:
            print("SUCCESS: Protected trade execution completed")
            print("Blur prevention system worked - check MEXC for trade confirmation")
        else:
            print("FAILED: Blur prevention was not sufficient")
            print("Check logs for detailed analysis")
        print("="*60)
        
    except KeyboardInterrupt:
        print("\nExecution interrupted")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
    finally:
        system.cleanup()

if __name__ == "__main__":
    main()
