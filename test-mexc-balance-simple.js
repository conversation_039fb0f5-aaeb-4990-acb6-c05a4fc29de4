const crypto = require('crypto');
const axios = require('axios');

class MexcBalanceTester {
    constructor(apiKey, secretKey) {
        this.apiKey = apiKey;
        this.secretKey = secretKey;
        this.baseURL = 'https://api.mexc.com';
    }

    generateSignature(queryString, secretKey) {
        return crypto
            .createHmac('sha256', secretKey)
            .update(queryString)
            .digest('hex');
    }

    async testSpotBalance() {
        console.log('\n💰 Testing Spot Balance...');
        
        try {
            const timestamp = Date.now();
            const queryString = `timestamp=${timestamp}`;
            const signature = this.generateSignature(queryString, this.secretKey);
            
            const url = `${this.baseURL}/api/v3/account?${queryString}&signature=${signature}`;
            
            const response = await axios.get(url, {
                headers: {
                    'X-MEXC-APIKEY': this.apiKey,
                    'Content-Type': 'application/json'
                }
            });

            console.log(`   ✅ Account Status: ${response.data.canTrade ? 'Active' : 'Inactive'}`);
            console.log(`   📊 Total Balances: ${response.data.balances?.length || 0}`);
            
            // Find USDT balance
            const usdtBalance = response.data.balances?.find(b => b.asset === 'USDT');
            if (usdtBalance) {
                const free = parseFloat(usdtBalance.free);
                const locked = parseFloat(usdtBalance.locked);
                const total = free + locked;
                
                console.log(`   💰 USDT Balance Details:`);
                console.log(`      Free: ${free} USDT`);
                console.log(`      Locked: ${locked} USDT`);
                console.log(`      Total: ${total} USDT`);
                
                if (total > 0) {
                    console.log(`   ✅ Balance found: ${total} USDT`);
                } else {
                    console.log(`   ⚠️ Zero balance detected`);
                }
            } else {
                console.log(`   ❌ USDT balance not found in response`);
            }
            
            // Show all non-zero balances
            const nonZeroBalances = response.data.balances?.filter(b => 
                parseFloat(b.free) > 0 || parseFloat(b.locked) > 0
            );
            
            if (nonZeroBalances && nonZeroBalances.length > 0) {
                console.log(`   📋 All Non-Zero Balances:`);
                nonZeroBalances.forEach(balance => {
                    const free = parseFloat(balance.free);
                    const locked = parseFloat(balance.locked);
                    const total = free + locked;
                    console.log(`      ${balance.asset}: ${total} (Free: ${free}, Locked: ${locked})`);
                });
            } else {
                console.log(`   ⚠️ No non-zero balances found`);
            }
            
            return response.data;
            
        } catch (error) {
            console.log(`   ❌ Spot balance test failed:`);
            console.log(`      Status: ${error.response?.status || 'Network Error'}`);
            console.log(`      Message: ${error.response?.data?.msg || error.message}`);
            console.log(`      Code: ${error.response?.data?.code || 'N/A'}`);
            throw error;
        }
    }

    async testFuturesBalance() {
        console.log('\n🔮 Testing Futures Balance...');
        
        const futuresEndpoints = [
            '/fapi/v1/account',
            '/fapi/v2/account',
            '/dapi/v1/account'
        ];

        for (const endpoint of futuresEndpoints) {
            try {
                console.log(`   🔍 Trying: ${endpoint}`);
                
                const timestamp = Date.now();
                const queryString = `timestamp=${timestamp}`;
                const signature = this.generateSignature(queryString, this.secretKey);
                
                const url = `${this.baseURL}${endpoint}?${queryString}&signature=${signature}`;
                
                const response = await axios.get(url, {
                    headers: {
                        'X-MEXC-APIKEY': this.apiKey,
                        'Content-Type': 'application/json'
                    }
                });

                console.log(`   ✅ ${endpoint} - Success!`);
                
                if (response.data.totalWalletBalance !== undefined) {
                    console.log(`   💰 Total Wallet Balance: ${response.data.totalWalletBalance} USDT`);
                    console.log(`   💰 Available Balance: ${response.data.availableBalance} USDT`);
                }
                
                if (response.data.assets) {
                    const usdtAsset = response.data.assets.find(a => a.asset === 'USDT');
                    if (usdtAsset) {
                        console.log(`   💰 USDT Asset:`);
                        console.log(`      Wallet Balance: ${usdtAsset.walletBalance}`);
                        console.log(`      Available Balance: ${usdtAsset.availableBalance}`);
                    }
                }
                
                return response.data;
                
            } catch (error) {
                console.log(`   ❌ ${endpoint} failed: ${error.response?.status || error.message}`);
                if (error.response?.data) {
                    console.log(`      Error: ${JSON.stringify(error.response.data)}`);
                }
            }
        }
        
        console.log(`   ⚠️ All futures endpoints failed`);
        return null;
    }

    async runBalanceTest() {
        console.log('🧪 MEXC Balance Test');
        console.log('====================');
        console.log(`API Key: ${this.apiKey}`);
        console.log(`Secret: ${this.secretKey.substring(0, 8)}...`);
        console.log(`Expected Balance: 2.2951 USDT`);

        try {
            // Test 1: Spot Balance
            const spotResult = await this.testSpotBalance();

            // Test 2: Futures Balance
            const futuresResult = await this.testFuturesBalance();

            console.log('\n📊 SUMMARY');
            console.log('===========');
            
            // Check if we found the expected balance
            let foundExpectedBalance = false;
            
            if (spotResult?.balances) {
                const usdtSpot = spotResult.balances.find(b => b.asset === 'USDT');
                if (usdtSpot) {
                    const spotTotal = parseFloat(usdtSpot.free) + parseFloat(usdtSpot.locked);
                    console.log(`Spot USDT: ${spotTotal}`);
                    if (Math.abs(spotTotal - 2.2951) < 0.001) {
                        foundExpectedBalance = true;
                        console.log('✅ Found expected balance in SPOT account!');
                    }
                }
            }
            
            if (futuresResult?.totalWalletBalance) {
                const futuresTotal = parseFloat(futuresResult.totalWalletBalance);
                console.log(`Futures USDT: ${futuresTotal}`);
                if (Math.abs(futuresTotal - 2.2951) < 0.001) {
                    foundExpectedBalance = true;
                    console.log('✅ Found expected balance in FUTURES account!');
                }
            }
            
            if (!foundExpectedBalance) {
                console.log('❌ Expected balance of 2.2951 USDT not found');
                console.log('💡 Possible reasons:');
                console.log('   - Balance is in a different account type');
                console.log('   - API keys have limited permissions');
                console.log('   - Balance has changed since last check');
                console.log('   - Using wrong API endpoint');
            }
            
        } catch (error) {
            console.log('\n❌ Balance test failed:', error.message);
        }
    }
}

// Test with provided credentials
async function main() {
    const apiKey = 'mx0vgl6bPv2Frz96j0';
    const secretKey = 'ac416e95c9b34ec0ba6d3210c18a1e76';
    
    const tester = new MexcBalanceTester(apiKey, secretKey);
    await tester.runBalanceTest();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = MexcBalanceTester;
