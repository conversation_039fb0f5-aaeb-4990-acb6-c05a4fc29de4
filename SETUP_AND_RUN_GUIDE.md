# 🚀 MEXC Automated Trading System - Complete Setup & Run Guide

## 📋 System Overview

This system consists of three main components:
1. **Browser Manager** - Manages Chrome browsers for MEXC automation
2. **MEXC Futures Trader** - Handles trade execution via browser automation
3. **TradingView Webhook Listener** - Receives signals and manages SL/TP

## 🔧 Prerequisites

- Windows Server/Desktop
- Node.js installed
- Google Chrome browser
- MEXC account with futures trading enabled
- TradingView account (for sending signals)

## 📁 Directory Structure

```
C:\Users\<USER>\Documents\mexc\
├── start-browsers.js              # Browser manager script
├── mexc-futures-trader/           # Trader service
│   ├── src/server.js
│   └── package.json
├── tradingview-webhook-listener/  # Webhook listener
│   ├── src/server.js
│   └── package.json
└── test-complete-system.js        # System testing script
```

## 🚀 Step-by-Step Setup & Run Instructions

### Step 1: Start Browsers

```bash
# Navigate to main directory
cd C:\Users\<USER>\Documents\mexc

# Start both browsers (ports 9222 & 9223)
node start-browsers.js start
```

**Expected Output:**
```
🌐 MEXC FUTURES TRADING - BROWSER MANAGER
==========================================
Chrome Path: C:\Program Files\Google\Chrome\Application\chrome.exe

✅ Created directory: C:\Users\<USER>\Documents\mexc\browser_data_open
✅ Created directory: C:\Users\<USER>\Documents\mexc\browser_data_close

🚀 Starting Open Orders Browser on port 9222...
✅ Open Orders Browser started successfully
   Port: 9222
   Data Dir: C:\Users\<USER>\Documents\mexc\browser_data_open
   PID: 12345

🚀 Starting Close Orders Browser on port 9223...
✅ Close Orders Browser started successfully
   Port: 9223
   Data Dir: C:\Users\<USER>\Documents\mexc\browser_data_close
   PID: 12346

🎯 SETUP INSTRUCTIONS:
======================
1. Open both browser windows that just launched
2. Navigate to: https://www.mexc.com/futures/TRU_USDT
3. Login to your MEXC account in BOTH browsers
4. Ensure you are on the futures trading page
5. Keep both browsers open and logged in
```

### Step 2: Manual Browser Setup

1. **Two Chrome windows should have opened automatically**
2. **In BOTH browser windows:**
   - Navigate to: `https://www.mexc.com/futures/TRU_USDT`
   - Login to your MEXC account
   - Ensure you're on the futures trading page
   - Verify you can see "Open Long" and "Open Short" buttons
   - **KEEP BOTH BROWSERS OPEN AND LOGGED IN**

### Step 3: Start MEXC Futures Trader Service

```bash
# Open new PowerShell window and run:
cd C:\Users\<USER>\Documents\mexc\mexc-futures-trader
node src/server.js
```

**Expected Output:**
```
🚀 MEXC Futures Trader API Server
================================
Server running on port 3000
📊 Health endpoint: http://localhost:3000/health
🎯 Execute endpoint: http://localhost:3000/execute
⚡ Ready to execute trades!
```

### Step 4: Start TradingView Webhook Listener

```bash
# Open another new PowerShell window and run:
cd C:\Users\<USER>\Documents\mexc\tradingview-webhook-listener
node src/server.js
```

**Expected Output:**
```
🚀 TradingView Webhook Listener started on port 4000
📊 Dashboard: http://localhost:4000
🔗 Webhook URL: http://localhost:4000/webhook
📋 API Info: http://localhost:4000/api/info
⚡ Ready to receive TradingView signals!

🔍 Testing MEXC API connection...
✅ MEXC API connected successfully. Balance: 0 USDT
```

## 🎯 System Verification

### Check All Services Are Running

```bash
# Test webhook listener
curl http://localhost:4000/health

# Test trader service  
curl http://localhost:3000/health

# Check system status
curl http://localhost:4000/api/status
```

**Expected Status Response:**
```json
{
  "botActive": true,
  "mexcConnected": true,
  "balance": {
    "total": "0",
    "available": "0"
  },
  "totalSignalsReceived": 0,
  "totalTradesExecuted": 0
}
```

## 📡 Testing the System

### Test with Webhook Signal

```bash
# Run the complete system test
node test-complete-system.js
```

### Manual Test Signal

```json
POST http://localhost:4000/webhook
Content-Type: application/json

{
  "symbol": "TRUUSDT",
  "trade": "open",
  "last_price": "0.03295",
  "leverage": "2"
}
```

## 🎛️ Dashboard Access

Open your browser and navigate to:
- **Main Dashboard**: `http://localhost:4000`
- **Configuration**: `http://localhost:4000` (click Configuration tab)
- **Positions**: `http://localhost:4000` (click Positions tab)

## 🔧 Configuration Options

### SL/TP Configuration

1. **SL Type Options:**
   - **Normal**: Fixed stop loss
   - **Trailing SL**: Trailing stop loss
   - **Move to TPs**: Move SL to TP levels as they're hit

2. **Source Options:**
   - **Close**: Use close price
   - **Open**: Use open price  
   - **High**: Use high price
   - **Low**: Use low price
   - **HLCO/4**: Use average of high, low, close, open

3. **Add (%)**: Percentage to add to source price

### Supported Order Types

- `open` / `open_long` - Open Long Position
- `open_short` - Open Short Position  
- `close` / `close_long` - Close Long Position
- `close_short` - Close Short Position

## 🚨 Troubleshooting

### Common Issues

1. **"Execution timeout" errors:**
   - Check browsers are logged in to MEXC
   - Verify browsers are on futures trading page
   - Ensure both browsers (ports 9222 & 9223) are running

2. **"MEXC Disconnected" status:**
   - Check MEXC API credentials in configuration
   - Verify internet connection
   - Restart webhook listener service

3. **"Login Required" alerts:**
   - MEXC session expired
   - Login manually in both browsers
   - System will send Telegram notification

### Browser Management Commands

```bash
# Check browser status
node start-browsers.js status

# Stop all browsers
node start-browsers.js stop

# Restart browsers
node start-browsers.js restart
```

## 📱 Telegram Notifications

The system sends alerts to Telegram when:
- MEXC login is required
- Critical errors occur
- Emergency bot stop needed

**Telegram Credentials:**
- Bot Token: `**********************************************`
- Chat ID: `142183523`

## ⚡ Performance Targets

- **Trade Execution**: Under 2 seconds
- **Signal Processing**: Under 1 second  
- **SL/TP Monitoring**: 1-second intervals
- **Position Management**: Real-time

## 🎯 System Features

### Enhanced SL/TP Management
- ✅ Normal, Trailing, and Move-to-TPs stop loss types
- ✅ Multiple take profit levels (TP1, TP2, TP3)
- ✅ ATR-based calculations
- ✅ Real-time position monitoring
- ✅ Automatic position management

### Trade Execution
- ✅ Open Long/Short positions
- ✅ Close Long/Short positions  
- ✅ Sub-2 second execution times
- ✅ Error handling and retry logic
- ✅ Login detection and alerts

### Monitoring & Alerts
- ✅ Real-time dashboard
- ✅ Position tracking
- ✅ Performance metrics
- ✅ Telegram notifications
- ✅ Comprehensive logging

## 🏁 Quick Start Checklist

- [ ] Start browsers: `node start-browsers.js start`
- [ ] Login to MEXC in both browser windows
- [ ] Start trader service: `node src/server.js` (in mexc-futures-trader folder)
- [ ] Start webhook listener: `node src/server.js` (in tradingview-webhook-listener folder)
- [ ] Verify system status: `http://localhost:4000/api/status`
- [ ] Test with signal: `node test-complete-system.js`
- [ ] Monitor dashboard: `http://localhost:4000`

**🎉 Your automated trading system is now ready for production!**
