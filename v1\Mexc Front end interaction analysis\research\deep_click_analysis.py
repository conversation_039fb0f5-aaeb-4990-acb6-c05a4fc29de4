#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Deep Click Analysis System
PROBLEM: Despite value persistence, trades still fail
SOLUTION: Deep analysis of what happens during button click to find the real issue
"""

import os
import sys
import time
import logging
from datetime import datetime
from playwright.sync_api import sync_playwright

class DeepClickAnalysis:
    """Deep analysis system to understand exactly what happens during button clicks"""
    
    def __init__(self, symbol="TRU_USDT", side="BUY", quantity=2.5):
        self.symbol = symbol
        self.side = side
        self.quantity = quantity
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(f'deep_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        self.playwright = None
        self.browser = None
        self.page = None
        
        self.logger.info(f"DEEP CLICK ANALYSIS: {side} {quantity} {symbol}")
    
    def connect_to_mexc(self):
        """Connect to MEXC browser tab"""
        self.logger.info("Connecting to MEXC...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                self.logger.error("No browser contexts found")
                return False
            
            context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in context.pages:
                url = page.url or ''
                if 'mexc.com' in url and 'testnet' not in url:
                    mexc_page = page
                    break
            
            if not mexc_page:
                self.logger.error("MEXC page not found")
                return False
            
            self.page = mexc_page
            self.logger.info(f"Connected to MEXC: {self.page.url}")
            return True
            
        except Exception as e:
            self.logger.error(f"Connection failed: {e}")
            return False
    
    def setup_deep_analysis_system(self):
        """Setup comprehensive analysis system"""
        self.logger.info("Setting up deep analysis system...")
        
        analysis_script = f"""
        () => {{
            console.log('Setting up deep click analysis system...');
            
            const TARGET_VALUE = '{self.quantity}';
            const SIDE = '{self.side}';
            
            // Global analysis state
            window.mexcDeepAnalysis = {{
                quantityField: null,
                tradeButton: null,
                priceField: null,
                allFields: [],
                eventLog: [],
                validationLog: [],
                clickAnalysis: null,
                monitoring: false,
                monitorInterval: null
            }};
            
            const analysis = window.mexcDeepAnalysis;
            
            // Step 1: Comprehensive field discovery
            function discoverAllFields() {{
                console.log('Discovering all form fields...');
                
                const allInputs = document.querySelectorAll('input');
                analysis.allFields = [];
                
                allInputs.forEach((input, index) => {{
                    const rect = input.getBoundingClientRect();
                    const isVisible = rect.width > 0 && rect.height > 0;
                    
                    if (isVisible) {{
                        const fieldInfo = {{
                            index: index,
                            element: input,
                            type: input.type || 'text',
                            placeholder: input.placeholder || '',
                            name: input.name || '',
                            id: input.id || '',
                            className: input.className || '',
                            value: input.value || '',
                            position: {{
                                x: Math.round(rect.x),
                                y: Math.round(rect.y),
                                width: Math.round(rect.width),
                                height: Math.round(rect.height)
                            }},
                            disabled: input.disabled,
                            readonly: input.readOnly
                        }};
                        
                        analysis.allFields.push(fieldInfo);
                        
                        // Identify specific fields
                        if (Math.abs(rect.x - 668) < 10 && Math.abs(rect.y - 603) < 50) {{
                            analysis.quantityField = input;
                            console.log(`Quantity field identified: ${{fieldInfo.placeholder}} at (${{rect.x}}, ${{rect.y}})`);
                        }}
                        
                        if (Math.abs(rect.x - 668) < 10 && Math.abs(rect.y - 523) < 50) {{
                            analysis.priceField = input;
                            console.log(`Price field identified: ${{fieldInfo.placeholder}} at (${{rect.x}}, ${{rect.y}})`);
                        }}
                    }}
                }});
                
                console.log(`Total visible fields discovered: ${{analysis.allFields.length}}`);
                return analysis.allFields.length;
            }}
            
            // Step 2: Find trade button with detailed analysis
            function findTradeButtonDetailed() {{
                const buttonClass = SIDE === 'BUY' ? 'component_longBtn__eazYU' : 'component_shortBtn__x5P3I';
                const button = document.querySelector(`button.${{buttonClass}}`);
                
                if (button) {{
                    const rect = button.getBoundingClientRect();
                    console.log(`Trade button found: "${{button.textContent}}" at (${{rect.x}}, ${{rect.y}})`);
                    console.log(`Button classes: ${{button.className}}`);
                    console.log(`Button disabled: ${{button.disabled}}`);
                    console.log(`Button visible: ${{rect.width > 0 && rect.height > 0}}`);
                    
                    analysis.tradeButton = button;
                    return true;
                }}
                
                console.log('Trade button not found');
                return false;
            }}
            
            // Step 3: Event monitoring system
            function setupEventMonitoring() {{
                console.log('Setting up comprehensive event monitoring...');
                
                const eventsToMonitor = [
                    'click', 'mousedown', 'mouseup', 'focus', 'blur',
                    'input', 'change', 'keydown', 'keyup', 'keypress',
                    'submit', 'reset', 'invalid'
                ];
                
                // Monitor all form-related events
                eventsToMonitor.forEach(eventType => {{
                    document.addEventListener(eventType, (event) => {{
                        const timestamp = Date.now();
                        const target = event.target;
                        
                        if (target.tagName === 'INPUT' || target.tagName === 'BUTTON' || target.tagName === 'FORM') {{
                            const eventInfo = {{
                                timestamp: timestamp,
                                type: eventType,
                                target: {{
                                    tagName: target.tagName,
                                    type: target.type || '',
                                    className: target.className || '',
                                    value: target.value || '',
                                    textContent: target.textContent || ''
                                }},
                                position: target.getBoundingClientRect ? {{
                                    x: Math.round(target.getBoundingClientRect().x),
                                    y: Math.round(target.getBoundingClientRect().y)
                                }} : null
                            }};
                            
                            analysis.eventLog.push(eventInfo);
                            
                            // Keep only last 50 events to prevent memory issues
                            if (analysis.eventLog.length > 50) {{
                                analysis.eventLog = analysis.eventLog.slice(-50);
                            }}
                            
                            console.log(`Event: ${{eventType}} on ${{target.tagName}} at (${{eventInfo.position?.x}}, ${{eventInfo.position?.y}})`);
                        }}
                    }}, true); // Use capture phase
                }});
                
                console.log('Event monitoring active');
            }}
            
            // Step 4: Form validation analysis
            function analyzeFormValidation() {{
                console.log('Analyzing form validation state...');
                
                const forms = document.querySelectorAll('form');
                const validationInfo = {{
                    forms: [],
                    requiredFields: [],
                    invalidFields: [],
                    validationMessages: []
                }};
                
                forms.forEach((form, index) => {{
                    const formInfo = {{
                        index: index,
                        action: form.action || '',
                        method: form.method || '',
                        valid: form.checkValidity ? form.checkValidity() : 'unknown'
                    }};
                    
                    validationInfo.forms.push(formInfo);
                    console.log(`Form ${{index}}: valid=${{formInfo.valid}}, action=${{formInfo.action}}`);
                }});
                
                // Check for required fields
                const requiredInputs = document.querySelectorAll('input[required]');
                requiredInputs.forEach(input => {{
                    const rect = input.getBoundingClientRect();
                    if (rect.width > 0 && rect.height > 0) {{
                        const fieldInfo = {{
                            position: {{ x: Math.round(rect.x), y: Math.round(rect.y) }},
                            value: input.value || '',
                            valid: input.checkValidity ? input.checkValidity() : 'unknown',
                            validationMessage: input.validationMessage || ''
                        }};
                        
                        validationInfo.requiredFields.push(fieldInfo);
                        console.log(`Required field at (${{fieldInfo.position.x}}, ${{fieldInfo.position.y}}): value="${{fieldInfo.value}}", valid=${{fieldInfo.valid}}`);
                    }}
                }});
                
                // Check for validation messages
                const validationSelectors = [
                    '.ant-form-item-explain-error',
                    '.ant-form-item-explain',
                    '.error-message',
                    '.validation-error',
                    '[class*="error"]'
                ];
                
                validationSelectors.forEach(selector => {{
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(element => {{
                        const text = element.textContent || '';
                        if (text.trim()) {{
                            validationInfo.validationMessages.push({{
                                selector: selector,
                                text: text.trim(),
                                visible: element.getBoundingClientRect().width > 0
                            }});
                        }}
                    }});
                }});
                
                analysis.validationLog.push({{
                    timestamp: Date.now(),
                    validation: validationInfo
                }});
                
                return validationInfo;
            }}
            
            // Step 5: Deep click analysis
            function performDeepClickAnalysis() {{
                console.log('Performing deep click analysis...');
                
                if (!analysis.quantityField || !analysis.tradeButton) {{
                    return {{ success: false, error: 'Required elements not found' }};
                }}
                
                const clickAnalysis = {{
                    preClick: {{}},
                    duringClick: {{}},
                    postClick: {{}},
                    timeline: []
                }};
                
                // Pre-click state
                clickAnalysis.preClick = {{
                    timestamp: Date.now(),
                    quantityValue: analysis.quantityField.value,
                    priceValue: analysis.priceField ? analysis.priceField.value : 'N/A',
                    buttonDisabled: analysis.tradeButton.disabled,
                    formValidation: analyzeFormValidation(),
                    allFieldValues: analysis.allFields.map(field => ({{
                        position: field.position,
                        value: field.element.value || '',
                        placeholder: field.placeholder
                    }}))
                }};
                
                console.log(`Pre-click: quantity="${{clickAnalysis.preClick.quantityValue}}", price="${{clickAnalysis.preClick.priceValue}}"`);
                
                // Ensure quantity field has value
                if (clickAnalysis.preClick.quantityValue !== TARGET_VALUE) {{
                    console.log('Emergency quantity restoration before click...');
                    analysis.quantityField.focus();
                    analysis.quantityField.value = TARGET_VALUE;
                    analysis.quantityField.dispatchEvent(new Event('input', {{ bubbles: true }}));
                    analysis.quantityField.dispatchEvent(new Event('change', {{ bubbles: true }}));
                }}
                
                // Record click attempt
                clickAnalysis.timeline.push({{ timestamp: Date.now(), event: 'click_initiated' }});
                
                try {{
                    // Execute click
                    analysis.tradeButton.focus();
                    analysis.tradeButton.click();
                    
                    clickAnalysis.timeline.push({{ timestamp: Date.now(), event: 'click_executed' }});
                    
                    // Immediate post-click state (within same event loop)
                    clickAnalysis.duringClick = {{
                        timestamp: Date.now(),
                        quantityValue: analysis.quantityField.value,
                        priceValue: analysis.priceField ? analysis.priceField.value : 'N/A'
                    }};
                    
                    console.log(`During-click: quantity="${{clickAnalysis.duringClick.quantityValue}}", price="${{clickAnalysis.duringClick.priceValue}}"`);
                    
                    // Schedule post-click analysis
                    setTimeout(() => {{
                        clickAnalysis.postClick = {{
                            timestamp: Date.now(),
                            quantityValue: analysis.quantityField.value,
                            priceValue: analysis.priceField ? analysis.priceField.value : 'N/A',
                            formValidation: analyzeFormValidation(),
                            newModals: document.querySelectorAll('.ant-modal, .modal, [role="dialog"]').length,
                            newNotifications: document.querySelectorAll('.ant-notification, .ant-message').length,
                            errorMessages: [],
                            successMessages: []
                        }};
                        
                        // Check for error messages
                        const errorSelectors = [
                            '.ant-message-error',
                            '.ant-notification-notice-error',
                            '.error',
                            '[class*="error"]'
                        ];
                        
                        errorSelectors.forEach(selector => {{
                            const elements = document.querySelectorAll(selector);
                            elements.forEach(element => {{
                                const text = element.textContent || '';
                                if (text.trim()) {{
                                    clickAnalysis.postClick.errorMessages.push({{
                                        selector: selector,
                                        text: text.trim(),
                                        visible: element.getBoundingClientRect().width > 0
                                    }});
                                }}
                            }});
                        }});
                        
                        // Check for success messages
                        const successSelectors = [
                            '.ant-message-success',
                            '.ant-notification-notice-success',
                            '.success',
                            '[class*="success"]'
                        ];
                        
                        successSelectors.forEach(selector => {{
                            const elements = document.querySelectorAll(selector);
                            elements.forEach(element => {{
                                const text = element.textContent || '';
                                if (text.trim()) {{
                                    clickAnalysis.postClick.successMessages.push({{
                                        selector: selector,
                                        text: text.trim(),
                                        visible: element.getBoundingClientRect().width > 0
                                    }});
                                }}
                            }});
                        }});
                        
                        clickAnalysis.timeline.push({{ timestamp: Date.now(), event: 'post_click_analysis_complete' }});
                        
                        console.log(`Post-click: quantity="${{clickAnalysis.postClick.quantityValue}}", errors=${{clickAnalysis.postClick.errorMessages.length}}, successes=${{clickAnalysis.postClick.successMessages.length}}`);
                        
                        // Store analysis results
                        analysis.clickAnalysis = clickAnalysis;
                        
                    }}, 1000);
                    
                    return {{ success: true, message: 'Click analysis initiated' }};
                    
                }} catch (error) {{
                    clickAnalysis.timeline.push({{ timestamp: Date.now(), event: 'click_error', error: error.message }});
                    return {{ success: false, error: error.message }};
                }}
            }}
            
            // Initialize system
            const fieldsFound = discoverAllFields();
            const buttonFound = findTradeButtonDetailed();
            
            if (fieldsFound === 0) {{
                return {{ success: false, error: 'No visible fields found' }};
            }}
            
            if (!buttonFound) {{
                return {{ success: false, error: 'Trade button not found' }};
            }}
            
            setupEventMonitoring();
            
            // Expose analysis function
            window.performDeepClickAnalysis = performDeepClickAnalysis;
            
            console.log('Deep analysis system ready');
            
            return {{
                success: true,
                fields_discovered: fieldsFound,
                quantity_field_found: analysis.quantityField !== null,
                price_field_found: analysis.priceField !== null,
                trade_button_found: buttonFound,
                initial_quantity_value: analysis.quantityField ? analysis.quantityField.value : 'N/A'
            }};
        }}
        """
        
        try:
            result = self.page.evaluate(analysis_script)
            
            if result.get('success'):
                fields_discovered = result.get('fields_discovered', 0)
                quantity_field_found = result.get('quantity_field_found', False)
                price_field_found = result.get('price_field_found', False)
                trade_button_found = result.get('trade_button_found', False)
                initial_value = result.get('initial_quantity_value', '')
                
                self.logger.info("DEEP ANALYSIS SYSTEM SETUP SUCCESS:")
                self.logger.info(f"   Fields discovered: {fields_discovered}")
                self.logger.info(f"   Quantity field found: {quantity_field_found}")
                self.logger.info(f"   Price field found: {price_field_found}")
                self.logger.info(f"   Trade button found: {trade_button_found}")
                self.logger.info(f"   Initial quantity value: '{initial_value}'")
                
                return True
            else:
                error = result.get('error', 'Unknown error')
                self.logger.error(f"Deep analysis setup failed: {error}")
                return False
                
        except Exception as e:
            self.logger.error(f"Deep analysis setup error: {e}")
            return False
    
    def execute_deep_click_analysis(self):
        """Execute the deep click analysis"""
        self.logger.info("Executing deep click analysis...")
        
        # First, set the quantity value
        set_value_script = f"""
        () => {{
            if (window.mexcDeepAnalysis && window.mexcDeepAnalysis.quantityField) {{
                const field = window.mexcDeepAnalysis.quantityField;
                field.focus();
                field.value = '{self.quantity}';
                field.dispatchEvent(new Event('input', {{ bubbles: true }}));
                field.dispatchEvent(new Event('change', {{ bubbles: true }}));
                
                return {{
                    success: true,
                    value_set: field.value
                }};
            }}
            
            return {{ success: false, error: 'Quantity field not available' }};
        }}
        """
        
        try:
            set_result = self.page.evaluate(set_value_script)
            if set_result.get('success'):
                self.logger.info(f"Quantity value set: '{set_result.get('value_set', '')}'")
            else:
                self.logger.error(f"Failed to set quantity: {set_result.get('error', '')}")
                return False
        except Exception as e:
            self.logger.error(f"Set value error: {e}")
            return False
        
        # Wait a moment for value to stabilize
        time.sleep(1)
        
        # Execute deep click analysis
        click_script = """
        () => {
            if (typeof window.performDeepClickAnalysis === 'function') {
                return window.performDeepClickAnalysis();
            } else {
                return { success: false, error: 'Deep click analysis function not available' };
            }
        }
        """
        
        try:
            result = self.page.evaluate(click_script)
            
            if result.get('success'):
                self.logger.info("Deep click analysis initiated successfully")
                
                # Wait for analysis to complete
                time.sleep(3)
                
                # Get analysis results
                return self.get_analysis_results()
            else:
                error = result.get('error', 'Unknown error')
                self.logger.error(f"Deep click analysis failed: {error}")
                return False
                
        except Exception as e:
            self.logger.error(f"Deep click analysis error: {e}")
            return False
    
    def get_analysis_results(self):
        """Get the detailed analysis results"""
        self.logger.info("Retrieving deep analysis results...")
        
        results_script = """
        () => {
            if (!window.mexcDeepAnalysis || !window.mexcDeepAnalysis.clickAnalysis) {
                return { success: false, error: 'Analysis results not available' };
            }
            
            const analysis = window.mexcDeepAnalysis;
            
            return {
                success: true,
                clickAnalysis: analysis.clickAnalysis,
                eventLog: analysis.eventLog.slice(-20), // Last 20 events
                allFields: analysis.allFields.map(field => ({
                    position: field.position,
                    placeholder: field.placeholder,
                    value: field.element.value || '',
                    className: field.className
                })),
                validationLog: analysis.validationLog.slice(-3) // Last 3 validation checks
            };
        }
        """
        
        try:
            result = self.page.evaluate(results_script)
            
            if result.get('success'):
                click_analysis = result.get('clickAnalysis', {})
                event_log = result.get('eventLog', [])
                all_fields = result.get('allFields', [])
                validation_log = result.get('validationLog', [])
                
                self.logger.info("DEEP CLICK ANALYSIS RESULTS:")
                self.logger.info("="*50)
                
                # Pre-click analysis
                pre_click = click_analysis.get('preClick', {})
                self.logger.info("PRE-CLICK STATE:")
                self.logger.info(f"   Quantity value: '{pre_click.get('quantityValue', '')}'")
                self.logger.info(f"   Price value: '{pre_click.get('priceValue', '')}'")
                self.logger.info(f"   Button disabled: {pre_click.get('buttonDisabled', 'unknown')}")
                
                # During-click analysis
                during_click = click_analysis.get('duringClick', {})
                self.logger.info("DURING-CLICK STATE:")
                self.logger.info(f"   Quantity value: '{during_click.get('quantityValue', '')}'")
                self.logger.info(f"   Price value: '{during_click.get('priceValue', '')}'")
                
                # Post-click analysis
                post_click = click_analysis.get('postClick', {})
                self.logger.info("POST-CLICK STATE:")
                self.logger.info(f"   Quantity value: '{post_click.get('quantityValue', '')}'")
                self.logger.info(f"   Price value: '{post_click.get('priceValue', '')}'")
                self.logger.info(f"   New modals: {post_click.get('newModals', 0)}")
                self.logger.info(f"   New notifications: {post_click.get('newNotifications', 0)}")
                
                # Error messages
                error_messages = post_click.get('errorMessages', [])
                self.logger.info(f"ERROR MESSAGES ({len(error_messages)}):")
                for error in error_messages:
                    self.logger.info(f"   {error.get('selector', '')}: '{error.get('text', '')}'")
                
                # Success messages
                success_messages = post_click.get('successMessages', [])
                self.logger.info(f"SUCCESS MESSAGES ({len(success_messages)}):")
                for success in success_messages:
                    self.logger.info(f"   {success.get('selector', '')}: '{success.get('text', '')}'")
                
                # Timeline
                timeline = click_analysis.get('timeline', [])
                self.logger.info("CLICK TIMELINE:")
                for event in timeline:
                    timestamp = event.get('timestamp', 0)
                    event_name = event.get('event', '')
                    self.logger.info(f"   {timestamp}: {event_name}")
                
                # Recent events
                self.logger.info(f"RECENT EVENTS ({len(event_log)}):")
                for event in event_log[-10:]:  # Last 10 events
                    event_type = event.get('type', '')
                    target = event.get('target', {})
                    position = event.get('position', {})
                    self.logger.info(f"   {event_type} on {target.get('tagName', '')} at ({position.get('x', '')}, {position.get('y', '')})")
                
                # All field values
                self.logger.info("ALL FIELD VALUES:")
                for field in all_fields:
                    pos = field.get('position', {})
                    self.logger.info(f"   ({pos.get('x', '')}, {pos.get('y', '')}): '{field.get('value', '')}' - {field.get('placeholder', '')}")
                
                self.logger.info("="*50)
                
                # Determine if there were quantity-related errors
                has_quantity_error = any(
                    'quantity' in error.get('text', '').lower() or 
                    'amount' in error.get('text', '').lower() or
                    'enter' in error.get('text', '').lower()
                    for error in error_messages
                )
                
                return not has_quantity_error
            else:
                error = result.get('error', 'Unknown error')
                self.logger.error(f"Failed to get analysis results: {error}")
                return False
                
        except Exception as e:
            self.logger.error(f"Get analysis results error: {e}")
            return False
    
    def execute_complete_deep_analysis(self):
        """Execute complete deep analysis"""
        self.logger.info("EXECUTING COMPLETE DEEP ANALYSIS")
        self.logger.info("="*60)
        
        try:
            # Step 1: Connect
            if not self.connect_to_mexc():
                return False
            
            # Step 2: Setup deep analysis system
            if not self.setup_deep_analysis_system():
                return False
            
            # Step 3: Execute deep click analysis
            success = self.execute_deep_click_analysis()
            
            self.logger.info("="*60)
            if success:
                self.logger.info("DEEP ANALYSIS: NO QUANTITY ERRORS DETECTED")
                self.logger.info("This suggests the issue may be elsewhere")
            else:
                self.logger.error("DEEP ANALYSIS: QUANTITY ERRORS STILL PRESENT")
                self.logger.error("The core issue persists despite our efforts")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Complete deep analysis error: {e}")
            return False
    
    def cleanup(self):
        """Cleanup resources"""
        try:
            if self.playwright:
                self.playwright.stop()
        except:
            pass

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Deep Click Analysis")
    parser.add_argument("--symbol", default="TRU_USDT", help="Trading symbol")
    parser.add_argument("--side", choices=["BUY", "SELL"], default="BUY", help="Order side")
    parser.add_argument("--quantity", type=float, default=2.5, help="Order quantity")
    
    args = parser.parse_args()
    
    print(f"""
DEEP CLICK ANALYSIS SYSTEM
==========================
PURPOSE: Understand exactly what happens during button clicks

ANALYSIS FEATURES:
1. Comprehensive field discovery and monitoring
2. Real-time event logging (click, input, change, etc.)
3. Form validation state analysis
4. Pre/during/post-click state comparison
5. Error and success message detection
6. Complete timeline of events
7. All field value tracking

TARGET: {args.side} {args.quantity} {args.symbol}
    """)
    
    analyzer = DeepClickAnalysis(args.symbol, args.side, args.quantity)
    
    try:
        success = analyzer.execute_complete_deep_analysis()
        
        print("\n" + "="*60)
        if success:
            print("ANALYSIS COMPLETE: Check logs for detailed findings")
            print("No quantity errors detected - issue may be elsewhere")
        else:
            print("ANALYSIS COMPLETE: Quantity errors still present")
            print("Check logs for detailed error analysis")
        print("="*60)
        
    except KeyboardInterrupt:
        print("\nAnalysis interrupted")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
    finally:
        analyzer.cleanup()

if __name__ == "__main__":
    main()
