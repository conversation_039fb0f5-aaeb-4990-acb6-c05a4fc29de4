#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MEXC Simple Browser Automation
A simplified version for testing and debugging MEXC trading automation.
"""

import os
import sys
import json
import time
import logging
import argparse
from datetime import datetime
from typing import Dict, Any, Optional
from playwright.sync_api import sync_playwright

class MEXCSimpleAutomation:
    """Simplified MEXC browser automation for testing"""
    
    def __init__(self, symbol="TRU_USDT", side="BUY", quantity=10.0, order_type="MARKET", price=None):
        self.symbol = symbol
        self.side = side
        self.quantity = quantity
        self.order_type = order_type
        self.price = price
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # Browser components
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        
        self.logger.info(f"Initialized automation for {symbol} {side} {quantity} {order_type}")
    
    def connect_to_browser(self) -> bool:
        """Connect to existing browser session"""
        self.logger.info("Connecting to browser on port 9222...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                self.logger.error("No browser contexts found")
                return False
            
            self.context = self.browser.contexts[0]
            self.logger.info(f"Connected to browser with {len(self.context.pages)} pages")
            
            # Find existing MEXC page or create new one
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                mexc_page = self.context.new_page()
                mexc_page.goto(f'https://futures.mexc.com/exchange/{self.symbol}', wait_until='domcontentloaded')
                self.logger.info(f"Created new page and navigated to {self.symbol}")
            else:
                self.logger.info(f"Found existing MEXC page: {mexc_page.url}")
            
            self.page = mexc_page
            return True
            
        except Exception as e:
            self.logger.error(f"Browser connection failed: {e}")
            return False
    
    def take_screenshot(self, name: str) -> str:
        """Take a screenshot for debugging"""
        if not self.page:
            return ""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"screenshot_{name}_{timestamp}.png"
        
        try:
            self.page.screenshot(path=filename, full_page=True)
            self.logger.info(f"Screenshot saved: {filename}")
            return filename
        except Exception as e:
            self.logger.error(f"Failed to take screenshot: {e}")
            return ""
    
    def check_authentication(self) -> Dict[str, Any]:
        """Check if user is authenticated"""
        self.logger.info("Checking authentication status...")
        
        auth_status = {
            "is_authenticated": False,
            "needs_login": False,
            "page_title": "",
            "current_url": ""
        }
        
        try:
            auth_status["page_title"] = self.page.title()
            auth_status["current_url"] = self.page.url
            
            # Take screenshot of current state
            self.take_screenshot("auth_check")
            
            # Check for login indicators
            login_selectors = [
                'button:has-text("Log In")',
                'a:has-text("Log In")',
                'input[type="email"]',
                'input[type="password"]'
            ]
            
            for selector in login_selectors:
                try:
                    if self.page.locator(selector).is_visible(timeout=2000):
                        auth_status["needs_login"] = True
                        self.logger.warning(f"Login required - found: {selector}")
                        break
                except:
                    continue
            
            # Check for authenticated state indicators
            auth_selectors = [
                '.user-info',
                '.account-balance',
                'text="Available Balance"',
                'button:has-text("Buy")',
                'button:has-text("Sell")'
            ]
            
            for selector in auth_selectors:
                try:
                    if self.page.locator(selector).is_visible(timeout=2000):
                        auth_status["is_authenticated"] = True
                        self.logger.info(f"Authenticated - found: {selector}")
                        break
                except:
                    continue
            
            return auth_status
            
        except Exception as e:
            self.logger.error(f"Error checking auth status: {e}")
            auth_status["error"] = str(e)
            return auth_status
    
    def navigate_to_trading_pair(self) -> bool:
        """Navigate to the specified trading pair"""
        self.logger.info(f"Navigating to {self.symbol} trading pair...")
        
        try:
            target_url = f"https://futures.mexc.com/exchange/{self.symbol}"
            current_url = self.page.url
            
            if self.symbol.replace('_', '') not in current_url:
                self.logger.info(f"Navigating to {target_url}")
                self.page.goto(target_url, wait_until='domcontentloaded')
                time.sleep(3)  # Allow page to fully load
            else:
                self.logger.info(f"Already on correct trading pair: {current_url}")
            
            # Take screenshot after navigation
            self.take_screenshot("after_navigation")
            
            # Wait for trading interface to load
            trading_selectors = [
                'button:has-text("Buy")',
                'button:has-text("Sell")',
                '.trading-panel',
                '.order-form'
            ]
            
            interface_loaded = False
            for selector in trading_selectors:
                try:
                    if self.page.locator(selector).is_visible(timeout=10000):
                        interface_loaded = True
                        self.logger.info(f"Trading interface loaded - found: {selector}")
                        break
                except:
                    continue
            
            if not interface_loaded:
                self.logger.error("Trading interface not detected")
                self.take_screenshot("interface_not_found")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Navigation failed: {e}")
            self.take_screenshot("navigation_error")
            return False
    
    def execute_trade(self) -> Dict[str, Any]:
        """Execute the configured trade"""
        self.logger.info(f"Executing trade: {self.side} {self.quantity} {self.symbol}")
        
        trade_result = {
            "success": False,
            "error": None,
            "steps_completed": []
        }
        
        try:
            # Take pre-trade screenshot
            self.take_screenshot("pre_trade")
            
            # Step 1: Find and click the side button (Buy/Sell)
            self.logger.info(f"Step 1: Clicking {self.side} button")
            
            side_selectors = [
                f'button:has-text("{self.side.title()}")',
                f'.{self.side.lower()}-btn',
                f'[data-testid="{self.side.lower()}-button"]'
            ]
            
            side_clicked = False
            for selector in side_selectors:
                try:
                    button = self.page.locator(selector).first
                    if button.is_visible(timeout=3000):
                        button.click()
                        side_clicked = True
                        trade_result["steps_completed"].append(f"Clicked {self.side} button")
                        time.sleep(1)
                        break
                except Exception as e:
                    self.logger.warning(f"Failed to click {selector}: {e}")
                    continue
            
            if not side_clicked:
                trade_result["error"] = f"Could not find {self.side} button"
                self.take_screenshot("side_button_not_found")
                return trade_result
            
            # Step 2: Enter quantity
            self.logger.info(f"Step 2: Entering quantity: {self.quantity}")
            
            quantity_selectors = [
                'input[placeholder*="quantity"]',
                'input[placeholder*="amount"]',
                'input[placeholder*="size"]',
                '.quantity-input'
            ]
            
            quantity_entered = False
            for selector in quantity_selectors:
                try:
                    input_field = self.page.locator(selector).first
                    if input_field.is_visible(timeout=3000):
                        input_field.clear()
                        input_field.fill(str(self.quantity))
                        quantity_entered = True
                        trade_result["steps_completed"].append(f"Entered quantity: {self.quantity}")
                        break
                except Exception as e:
                    self.logger.warning(f"Failed to enter quantity in {selector}: {e}")
                    continue
            
            if not quantity_entered:
                trade_result["error"] = "Could not enter quantity"
                self.take_screenshot("quantity_entry_failed")
                return trade_result
            
            # Step 3: Submit order
            self.logger.info("Step 3: Submitting order")
            
            # Take screenshot before submitting
            self.take_screenshot("before_submit")
            
            submit_selectors = [
                f'button:has-text("{self.side.title()}")',
                'button:has-text("Submit")',
                'button:has-text("Place Order")',
                '.submit-btn'
            ]
            
            order_submitted = False
            for selector in submit_selectors:
                try:
                    button = self.page.locator(selector).first
                    if button.is_visible(timeout=3000) and button.is_enabled():
                        # This is where we would actually submit - for safety, let's just log
                        self.logger.info(f"WOULD SUBMIT ORDER with button: {selector}")
                        trade_result["steps_completed"].append("Order ready to submit (not executed for safety)")
                        order_submitted = True
                        break
                except Exception as e:
                    self.logger.warning(f"Failed to find submit button {selector}: {e}")
                    continue
            
            if not order_submitted:
                trade_result["error"] = "Could not find submit button"
                self.take_screenshot("submit_button_not_found")
                return trade_result
            
            # For safety, we're not actually submitting the order
            trade_result["success"] = True
            trade_result["note"] = "Order prepared but not submitted for safety"
            
            # Take final screenshot
            self.take_screenshot("trade_completed")
            
            return trade_result
            
        except Exception as e:
            trade_result["error"] = str(e)
            self.logger.error(f"Trade execution failed: {e}")
            self.take_screenshot("trade_exception")
            return trade_result
    
    def run_automation(self) -> Dict[str, Any]:
        """Run the complete automation workflow"""
        self.logger.info("Starting MEXC browser automation workflow")
        
        workflow_result = {
            "success": False,
            "steps": {},
            "total_duration": 0
        }
        
        start_time = time.time()
        
        try:
            # Step 1: Connect to browser
            self.logger.info("Step 1: Connecting to browser")
            if not self.connect_to_browser():
                workflow_result["steps"]["browser_connection"] = "FAILED"
                return workflow_result
            workflow_result["steps"]["browser_connection"] = "SUCCESS"
            
            # Step 2: Check authentication
            self.logger.info("Step 2: Checking authentication")
            auth_status = self.check_authentication()
            workflow_result["steps"]["authentication"] = auth_status
            
            if not auth_status["is_authenticated"] and auth_status["needs_login"]:
                self.logger.warning("Manual login required")
                input("Please log in manually in the browser, then press Enter to continue...")
                
                # Re-check authentication
                auth_status = self.check_authentication()
                if not auth_status["is_authenticated"]:
                    workflow_result["steps"]["manual_login"] = "FAILED"
                    return workflow_result
                workflow_result["steps"]["manual_login"] = "SUCCESS"
            
            # Step 3: Navigate to trading pair
            self.logger.info("Step 3: Navigating to trading pair")
            if not self.navigate_to_trading_pair():
                workflow_result["steps"]["navigation"] = "FAILED"
                return workflow_result
            workflow_result["steps"]["navigation"] = "SUCCESS"
            
            # Step 4: Execute trade
            self.logger.info("Step 4: Executing trade")
            trade_result = self.execute_trade()
            workflow_result["steps"]["trade_execution"] = trade_result
            
            if trade_result["success"]:
                workflow_result["success"] = True
                self.logger.info("Automation workflow completed successfully")
            else:
                self.logger.error(f"Trade execution failed: {trade_result.get('error', 'Unknown error')}")
            
        except Exception as e:
            self.logger.error(f"Workflow exception: {e}")
            workflow_result["steps"]["exception"] = str(e)
        
        finally:
            workflow_result["total_duration"] = time.time() - start_time
            self.logger.info(f"Total workflow duration: {workflow_result['total_duration']:.2f}s")
        
        return workflow_result
    
    def cleanup(self):
        """Clean up resources"""
        try:
            if self.playwright:
                self.playwright.stop()
        except Exception as e:
            self.logger.error(f"Cleanup error: {e}")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="MEXC Simple Browser Automation")
    parser.add_argument("--symbol", default="TRU_USDT", help="Trading symbol")
    parser.add_argument("--side", choices=["BUY", "SELL"], default="BUY", help="Order side")
    parser.add_argument("--quantity", type=float, default=10.0, help="Order quantity")
    parser.add_argument("--type", choices=["MARKET", "LIMIT"], default="MARKET", help="Order type")
    parser.add_argument("--price", type=float, help="Order price for limit orders")
    
    args = parser.parse_args()
    
    print(f"""
MEXC Simple Browser Automation
==============================
Symbol: {args.symbol}
Side: {args.side}
Quantity: {args.quantity}
Type: {args.type}
Price: {args.price or 'Market Price'}

Starting automation...
    """)
    
    automation = MEXCSimpleAutomation(
        symbol=args.symbol,
        side=args.side,
        quantity=args.quantity,
        order_type=args.type,
        price=args.price
    )
    
    try:
        result = automation.run_automation()
        
        print(f"""
Automation Results:
==================
Success: {result['success']}
Duration: {result['total_duration']:.2f}s

Steps:
{json.dumps(result['steps'], indent=2)}
        """)
        
        if result['success']:
            print("✅ Automation completed successfully!")
        else:
            print("❌ Automation failed. Check logs and screenshots for details.")
    
    except KeyboardInterrupt:
        print("\n👋 Interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
    finally:
        automation.cleanup()

if __name__ == "__main__":
    main()
