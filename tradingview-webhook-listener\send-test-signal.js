const axios = require('axios');

async function sendTestSignal() {
    console.log('🚀 Sending TradingView webhook signal...\n');

    try {
        // Get current market price first
        const marketData = await axios.get('http://localhost:4000/api/market-data');
        const currentPrice = marketData.data.currentPrice.price;
        const atr = marketData.data.atr;
        
        console.log(`📊 Current TRUUSDT Price: $${currentPrice}`);
        console.log(`📈 Current ATR: ${atr}`);
        console.log('');

        // Send webhook signal
        const signal = {
            symbol: "TRUUSDT",
            trade: "open",
            last_price: currentPrice.toString(),
            leverage: "2"
        };

        console.log('📡 Sending webhook signal:', JSON.stringify(signal, null, 2));
        console.log('');

        const response = await axios.post('http://localhost:4000/webhook', signal);
        
        console.log('✅ Webhook Response:', response.data.success ? 'SUCCESS' : 'FAILED');
        console.log('📋 Response Details:', JSON.stringify(response.data, null, 2));
        
        if (response.data.success) {
            console.log('\n🎯 Expected SL/TP Levels:');
            console.log(`   💰 Entry Price: $${currentPrice}`);
            console.log(`   🛑 Stop Loss: $${(currentPrice - (atr * 1.5)).toFixed(8)} (1.5 × ATR below)`);
            console.log(`   🎯 TP1: $${(currentPrice + (atr * 2)).toFixed(8)} (2 × ATR above, 50%)`);
            console.log(`   🎯 TP2: $${(currentPrice + (atr * 4)).toFixed(8)} (4 × ATR above, 30%)`);
            console.log(`   📈 ATR: ${atr}`);
            
            console.log('\n⏱️  Execution Time:', response.data.executionTime || 'N/A', 'ms');
            
            // Check positions after trade
            setTimeout(async () => {
                try {
                    const positions = await axios.get('http://localhost:4000/api/positions');
                    console.log('\n📍 Active Positions:', positions.data.statistics.activePositions);
                    
                    if (positions.data.positions.length > 0) {
                        const position = positions.data.positions[0];
                        console.log('\n🔍 Position Details:');
                        console.log(`   📍 Position ID: ${position.id}`);
                        console.log(`   💰 Entry Price: $${position.entryPrice}`);
                        console.log(`   🛑 Stop Loss: $${position.stopLoss}`);
                        console.log(`   📊 Quantity: ${position.quantity}`);
                        console.log(`   📈 ATR: ${position.atr}`);
                        console.log(`   🎯 Take Profits: ${position.takeProfits.length}`);
                        
                        position.takeProfits.forEach((tp, index) => {
                            console.log(`   🎯 TP${tp.level}: $${tp.price} (${tp.percent}% of position)`);
                        });
                        
                        if (position.isTrailing) {
                            console.log(`   📈 Trailing: Active (Stop: $${position.trailingStopLoss})`);
                        } else {
                            console.log(`   📈 Trailing: Inactive`);
                        }
                    }
                } catch (error) {
                    console.log('❌ Error checking positions:', error.message);
                }
            }, 2000);
        }

    } catch (error) {
        console.error('❌ Error sending webhook:', error.response?.data || error.message);
    }
}

sendTestSignal();
