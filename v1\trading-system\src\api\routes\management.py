"""
Management API routes for system administration and monitoring
"""

from datetime import datetime
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from src.config import settings
from src.core.session_manager import SessionManager
from src.core.trading_engine import TradingEngine
from src.models.database import get_recent_trades, Trade
from src.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()

# Global instances (will be injected)
session_manager: Optional[SessionManager] = None
trading_engine: Optional[TradingEngine] = None


class SystemStatus(BaseModel):
    """System status response model"""
    status: str
    timestamp: str
    version: str
    environment: str
    uptime: str
    components: Dict[str, bool]
    metrics: Dict[str, Any]


class SessionInfo(BaseModel):
    """Session information model"""
    session_id: str
    status: str
    health_score: float
    authenticated: bool
    created_at: str
    last_used_at: str
    expires_at: Optional[str]
    total_trades: int
    successful_trades: int
    success_rate: float


class TradeInfo(BaseModel):
    """Trade information model"""
    id: int
    symbol: str
    action: str
    side: str
    quantity: float
    price: Optional[float]
    status: str
    created_at: str
    execution_time: Optional[str]
    order_id: Optional[str]
    pnl: Optional[float]


class RefreshSessionRequest(BaseModel):
    """Session refresh request model"""
    session_id: str


async def get_session_manager() -> SessionManager:
    """Get session manager instance"""
    global session_manager
    if not session_manager:
        raise HTTPException(status_code=503, detail="Session manager not available")
    return session_manager


async def get_trading_engine() -> TradingEngine:
    """Get trading engine instance"""
    global trading_engine
    if not trading_engine:
        raise HTTPException(status_code=503, detail="Trading engine not available")
    return trading_engine


@router.get("/status", response_model=SystemStatus)
async def get_system_status(
    sm: SessionManager = Depends(get_session_manager),
    te: TradingEngine = Depends(get_trading_engine)
):
    """
    Get comprehensive system status
    
    Returns:
        System status information
    """
    try:
        # Get component health
        components = {
            "session_manager": sm.is_healthy(),
            "trading_engine": te.is_healthy(),
            "database": True,  # TODO: Add database health check
        }
        
        # Get session stats
        session_stats = sm.get_session_stats()
        
        # Get trading stats
        trading_stats = te.get_performance_stats()
        
        # Calculate uptime (placeholder)
        uptime = "N/A"  # TODO: Calculate actual uptime
        
        return SystemStatus(
            status="healthy" if all(components.values()) else "degraded",
            timestamp=datetime.utcnow().isoformat(),
            version=settings.VERSION,
            environment=settings.ENVIRONMENT,
            uptime=uptime,
            components=components,
            metrics={
                "sessions": session_stats,
                "trading": trading_stats
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to get system status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get system status")


@router.get("/sessions", response_model=List[SessionInfo])
async def get_sessions(sm: SessionManager = Depends(get_session_manager)):
    """
    Get information about all browser sessions
    
    Returns:
        List of session information
    """
    try:
        sessions_info = []
        
        for session in sm.sessions:
            success_rate = session.successful_trades / max(1, session.total_trades)
            
            session_info = SessionInfo(
                session_id=session.session_id,
                status="active" if session.is_healthy() else "unhealthy",
                health_score=session.health_score,
                authenticated=session.is_authenticated,
                created_at=session.created_at.isoformat(),
                last_used_at=session.last_used_at.isoformat(),
                expires_at=session.expires_at.isoformat() if session.expires_at else None,
                total_trades=session.total_trades,
                successful_trades=session.successful_trades,
                success_rate=success_rate
            )
            
            sessions_info.append(session_info)
        
        return sessions_info
        
    except Exception as e:
        logger.error(f"Failed to get sessions: {e}")
        raise HTTPException(status_code=500, detail="Failed to get sessions")


@router.post("/sessions/refresh")
async def refresh_session(
    request: RefreshSessionRequest,
    sm: SessionManager = Depends(get_session_manager)
):
    """
    Refresh a specific session
    
    Args:
        request: Session refresh request
        
    Returns:
        Refresh result
    """
    try:
        success = await sm.refresh_session(request.session_id)
        
        if success:
            return {
                "success": True,
                "message": f"Session {request.session_id[:8]} refreshed successfully",
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            return {
                "success": False,
                "message": f"Failed to refresh session {request.session_id[:8]}",
                "timestamp": datetime.utcnow().isoformat()
            }
            
    except Exception as e:
        logger.error(f"Failed to refresh session: {e}")
        raise HTTPException(status_code=500, detail="Failed to refresh session")


@router.get("/trades", response_model=List[TradeInfo])
async def get_trades(limit: int = 50):
    """
    Get recent trades
    
    Args:
        limit: Maximum number of trades to return
        
    Returns:
        List of recent trades
    """
    try:
        trades = await get_recent_trades(limit)
        trades_info = []
        
        for trade in trades:
            trade_info = TradeInfo(
                id=trade.id,
                symbol=trade.symbol,
                action=trade.action,
                side=trade.side,
                quantity=trade.quantity,
                price=trade.price,
                status=trade.status,
                created_at=trade.created_at.isoformat() if trade.created_at else None,
                execution_time=trade.execution_time.isoformat() if trade.execution_time else None,
                order_id=trade.order_id,
                pnl=trade.pnl
            )
            trades_info.append(trade_info)
        
        return trades_info
        
    except Exception as e:
        logger.error(f"Failed to get trades: {e}")
        raise HTTPException(status_code=500, detail="Failed to get trades")


@router.get("/metrics")
async def get_metrics(
    sm: SessionManager = Depends(get_session_manager),
    te: TradingEngine = Depends(get_trading_engine)
):
    """
    Get system metrics for monitoring
    
    Returns:
        System metrics
    """
    try:
        session_stats = sm.get_session_stats()
        trading_stats = te.get_performance_stats()
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "sessions": {
                "total": session_stats["total_sessions"],
                "healthy": session_stats["healthy_sessions"],
                "authenticated": session_stats["authenticated_sessions"],
                "average_health_score": session_stats["average_health_score"]
            },
            "trading": {
                "total_trades": trading_stats["total_trades"],
                "successful_trades": trading_stats["successful_trades"],
                "failed_trades": trading_stats["failed_trades"],
                "success_rate": trading_stats["success_rate"],
                "active_trades": trading_stats["active_trades"],
                "queued_trades": trading_stats["queued_trades"]
            },
            "system": {
                "version": settings.VERSION,
                "environment": settings.ENVIRONMENT,
                "max_concurrent_trades": settings.MAX_CONCURRENT_TRADES,
                "session_pool_size": settings.SESSION_POOL_SIZE
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get metrics")


@router.post("/emergency-stop")
async def emergency_stop(te: TradingEngine = Depends(get_trading_engine)):
    """
    Emergency stop all trading operations
    
    Returns:
        Emergency stop result
    """
    try:
        # Clear trade queue
        te.trade_queue.clear()
        
        # TODO: Cancel all active orders if possible
        
        logger.warning("Emergency stop activated")
        
        return {
            "success": True,
            "message": "Emergency stop activated - all trading operations halted",
            "timestamp": datetime.utcnow().isoformat(),
            "active_trades_count": len(te.active_trades),
            "cleared_queue_count": 0  # Since we cleared it
        }
        
    except Exception as e:
        logger.error(f"Emergency stop failed: {e}")
        raise HTTPException(status_code=500, detail="Emergency stop failed")


@router.get("/config")
async def get_config():
    """
    Get system configuration (non-sensitive values only)
    
    Returns:
        System configuration
    """
    try:
        return {
            "trading": {
                "max_concurrent_trades": settings.MAX_CONCURRENT_TRADES,
                "default_leverage": settings.DEFAULT_LEVERAGE,
                "risk_management_enabled": settings.RISK_MANAGEMENT_ENABLED,
                "max_position_size": settings.MAX_POSITION_SIZE,
                "supported_symbols": settings.SUPPORTED_SYMBOLS
            },
            "sessions": {
                "pool_size": settings.SESSION_POOL_SIZE,
                "health_check_interval": settings.SESSION_HEALTH_CHECK_INTERVAL,
                "expiry_warning_hours": settings.SESSION_EXPIRY_WARNING_HOURS,
                "session_timeout": settings.MEXC_SESSION_TIMEOUT
            },
            "browser": {
                "headless_mode": settings.HEADLESS_MODE,
                "timeout": settings.BROWSER_TIMEOUT,
                "network_interception": settings.ENABLE_NETWORK_INTERCEPTION
            },
            "system": {
                "version": settings.VERSION,
                "environment": settings.ENVIRONMENT,
                "log_level": settings.LOG_LEVEL
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get config: {e}")
        raise HTTPException(status_code=500, detail="Failed to get config")


def set_managers(sm: SessionManager, te: TradingEngine):
    """Set the global manager instances"""
    global session_manager, trading_engine
    session_manager = sm
    trading_engine = te
