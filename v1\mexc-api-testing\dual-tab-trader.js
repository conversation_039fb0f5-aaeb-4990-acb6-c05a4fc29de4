const { chromium } = require('playwright');

class DualTabMEXCTrader {
    constructor() {
        this.browser = null;
        this.openTab = null;
        this.closeTab = null;
        this.startTime = null;
    }

    async connectToRemoteBrowser() {
        console.log('🔗 Connecting to remote browser...');
        
        try {
            this.browser = await chromium.connectOverCDP('http://localhost:9222');
            const contexts = this.browser.contexts();
            
            if (contexts.length > 0) {
                const context = contexts[0];
                const pages = context.pages();
                
                if (pages.length >= 2) {
                    this.openTab = pages[0];
                    this.closeTab = pages[1];
                    console.log('✅ Connected - Found 2 tabs');
                } else if (pages.length === 1) {
                    this.openTab = pages[0];
                    this.closeTab = await context.newPage();
                    console.log('✅ Connected - Created second tab');
                } else {
                    this.openTab = await context.newPage();
                    this.closeTab = await context.newPage();
                    console.log('✅ Connected - Created both tabs');
                }
            } else {
                const context = await this.browser.newContext();
                this.openTab = await context.newPage();
                this.closeTab = await context.newPage();
                console.log('✅ Connected - Created new context and tabs');
            }
            
            return true;
        } catch (error) {
            console.error('❌ Connection failed:', error.message);
            return false;
        }
    }

    async setupTabs() {
        console.log('🔧 Setting up tabs...');
        
        try {
            // Setup Open Tab
            const openUrl = this.openTab.url();
            if (!openUrl.includes('mexc.com/futures/TRU_USDT')) {
                console.log('🌐 Setting up OPEN tab...');
                await this.openTab.goto('https://www.mexc.com/futures/TRU_USDT', {
                    waitUntil: 'domcontentloaded',
                    timeout: 5000
                });
                await this.openTab.waitForTimeout(1000);
            }

            // Setup Close Tab
            const closeUrl = this.closeTab.url();
            if (!closeUrl.includes('mexc.com/futures/TRU_USDT')) {
                console.log('🌐 Setting up CLOSE tab...');
                await this.closeTab.goto('https://www.mexc.com/futures/TRU_USDT', {
                    waitUntil: 'domcontentloaded',
                    timeout: 5000
                });
                await this.closeTab.waitForTimeout(1000);
            }

            console.log('✅ Both tabs ready');
            return true;
        } catch (error) {
            console.error('❌ Tab setup failed:', error.message);
            return false;
        }
    }

    async executeOpenTrade() {
        this.startTime = Date.now();
        console.log('📈 EXECUTING OPEN TRADE (Tab 1)...');
        
        try {
            const parallelOperations = await Promise.allSettled([
                // Fill quantity field
                (async () => {
                    const quantitySelectors = [
                        'text=Quantity(USDT) >> xpath=following::input[1]',
                        'text=Quantity >> xpath=following::input[1]',
                        'input[placeholder*="quantity"]'
                    ];

                    for (const selector of quantitySelectors) {
                        try {
                            const element = this.openTab.locator(selector).first();
                            const isVisible = await element.isVisible({ timeout: 300 });
                            
                            if (isVisible) {
                                await element.click({ timeout: 200 });
                                await element.fill('0.3600');
                                console.log('⚡ OPEN: Quantity filled: 0.3600');
                                return true;
                            }
                        } catch (error) {
                            continue;
                        }
                    }
                    throw new Error('Quantity field not found');
                })(),

                // Prepare Open Long button
                (async () => {
                    await new Promise(resolve => setTimeout(resolve, 200));
                    
                    const openLongSelectors = [
                        'button:has-text("Open Long")',
                        'text=Open Long',
                        '.open-long'
                    ];

                    for (const selector of openLongSelectors) {
                        try {
                            const element = this.openTab.locator(selector).first();
                            const isVisible = await element.isVisible({ timeout: 300 });
                            
                            if (isVisible) {
                                return { element, selector };
                            }
                        } catch (error) {
                            continue;
                        }
                    }
                    throw new Error('Open Long button not found');
                })()
            ]);

            const quantitySuccess = parallelOperations[0].status === 'fulfilled';
            const openLongReady = parallelOperations[1].status === 'fulfilled' ? parallelOperations[1].value : null;

            if (!quantitySuccess || !openLongReady) {
                throw new Error('Open trade preparation failed');
            }

            // Click Open Long
            await openLongReady.element.click({ timeout: 300 });
            console.log('⚡ OPEN: Open Long clicked');

            // Handle popup
            const confirmClicked = await this.handleConfirmPopup(this.openTab, 'OPEN');

            const executionTime = Date.now() - this.startTime;

            return {
                success: quantitySuccess && confirmClicked,
                executionTime,
                quantityFilled: quantitySuccess,
                confirmClicked
            };

        } catch (error) {
            const executionTime = Date.now() - this.startTime;
            console.error(`❌ OPEN trade failed after ${executionTime}ms:`, error.message);
            return { success: false, executionTime, error: error.message };
        }
    }

    async executeCloseTrade() {
        this.startTime = Date.now();
        console.log('📉 EXECUTING CLOSE TRADE (Tab 2)...');
        
        try {
            const parallelOperations = await Promise.allSettled([
                // Fill quantity field
                (async () => {
                    const quantitySelectors = [
                        'text=Quantity(USDT) >> xpath=following::input[1]',
                        'text=Quantity >> xpath=following::input[1]',
                        'input[placeholder*="quantity"]'
                    ];

                    for (const selector of quantitySelectors) {
                        try {
                            const element = this.closeTab.locator(selector).first();
                            const isVisible = await element.isVisible({ timeout: 300 });
                            
                            if (isVisible) {
                                await element.click({ timeout: 200 });
                                await element.fill('0.3600');
                                console.log('⚡ CLOSE: Quantity filled: 0.3600');
                                return true;
                            }
                        } catch (error) {
                            continue;
                        }
                    }
                    throw new Error('Quantity field not found');
                })(),

                // Prepare Open Short button (for closing long position)
                (async () => {
                    await new Promise(resolve => setTimeout(resolve, 200));
                    
                    const openShortSelectors = [
                        'button:has-text("Open Short")',
                        'text=Open Short',
                        '.open-short',
                        '.short-btn'
                    ];

                    for (const selector of openShortSelectors) {
                        try {
                            const element = this.closeTab.locator(selector).first();
                            const isVisible = await element.isVisible({ timeout: 300 });
                            
                            if (isVisible) {
                                return { element, selector };
                            }
                        } catch (error) {
                            continue;
                        }
                    }
                    throw new Error('Open Short button not found');
                })()
            ]);

            const quantitySuccess = parallelOperations[0].status === 'fulfilled';
            const openShortReady = parallelOperations[1].status === 'fulfilled' ? parallelOperations[1].value : null;

            if (!quantitySuccess || !openShortReady) {
                throw new Error('Close trade preparation failed');
            }

            // Click Open Short
            await openShortReady.element.click({ timeout: 300 });
            console.log('⚡ CLOSE: Open Short clicked');

            // Handle popup
            const confirmClicked = await this.handleConfirmPopup(this.closeTab, 'CLOSE');

            const executionTime = Date.now() - this.startTime;

            return {
                success: quantitySuccess && confirmClicked,
                executionTime,
                quantityFilled: quantitySuccess,
                confirmClicked
            };

        } catch (error) {
            const executionTime = Date.now() - this.startTime;
            console.error(`❌ CLOSE trade failed after ${executionTime}ms:`, error.message);
            return { success: false, executionTime, error: error.message };
        }
    }

    async handleConfirmPopup(page, tradeType) {
        console.log(`⚡ ${tradeType}: Handling popup...`);
        
        const confirmPromise = (async () => {
            const confirmSelectors = [
                'button:has-text("Confirm")',
                'text=Confirm',
                '.confirm-btn'
            ];

            for (let attempt = 0; attempt < 10; attempt++) {
                for (const selector of confirmSelectors) {
                    try {
                        const element = page.locator(selector).first();
                        const isVisible = await element.isVisible({ timeout: 100 });
                        
                        if (isVisible) {
                            await element.click({ timeout: 200 });
                            console.log(`⚡ ${tradeType}: Confirm clicked`);
                            return true;
                        }
                    } catch (error) {
                        continue;
                    }
                }
                await new Promise(resolve => setTimeout(resolve, 50));
            }
            return false;
        })();

        return await Promise.race([
            confirmPromise,
            new Promise(resolve => setTimeout(() => resolve(false), 2000))
        ]);
    }

    async verifyTrade(page, tradeType) {
        try {
            const successIndicators = [
                'text=Purchased successfully',
                'text=Success',
                'text=completed',
                '.success'
            ];

            for (const indicator of successIndicators) {
                try {
                    const element = page.locator(indicator).first();
                    const isVisible = await element.isVisible({ timeout: 500 });
                    if (isVisible) {
                        const text = await element.textContent();
                        console.log(`✅ ${tradeType} verified: ${text}`);
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }
            return false;
        } catch (error) {
            return false;
        }
    }
}

async function runDualTabTrader() {
    const trader = new DualTabMEXCTrader();
    
    try {
        console.log('🎯 DUAL TAB MEXC TRADER');
        console.log('========================');
        console.log('📈 Tab 1: Open Long trades');
        console.log('📉 Tab 2: Open Short trades (to close longs)');
        console.log('⚡ Target: <2 seconds per trade');
        console.log('');

        const connected = await trader.connectToRemoteBrowser();
        if (!connected) {
            throw new Error('Could not connect to remote browser');
        }

        const tabsReady = await trader.setupTabs();
        if (!tabsReady) {
            throw new Error('Could not setup tabs');
        }

        // Interactive menu
        console.log('🎮 TRADING MENU:');
        console.log('================');
        console.log('Choose an action:');
        console.log('1. Execute OPEN trade (Tab 1)');
        console.log('2. Execute CLOSE trade (Tab 2)');
        console.log('3. Execute BOTH trades (Open then Close)');
        console.log('');

        // For now, let's demonstrate with option 1 (Open trade)
        console.log('🚀 Executing OPEN trade...');
        
        const openResult = await trader.executeOpenTrade();
        
        // Verify open trade
        const openVerified = await trader.verifyTrade(trader.openTab, 'OPEN');
        
        if (openResult.success && openResult.executionTime < 2000) {
            console.log('\n🏆 OPEN TRADE SUCCESS!');
            console.log(`⚡ Executed in ${openResult.executionTime}ms (<2 seconds)`);
        } else if (openResult.success) {
            console.log('\n✅ OPEN trade completed successfully!');
            console.log(`⏱️ Time: ${openResult.executionTime}ms`);
        } else {
            console.log('\n❌ OPEN trade failed');
        }

        if (openVerified) {
            console.log('🎉 OPEN TRADE VERIFIED!');
        }

        // Save results
        const results = {
            openTrade: openResult,
            openVerified,
            timestamp: new Date().toISOString()
        };

        require('fs').writeFileSync('dual-tab-results.json', JSON.stringify(results, null, 2));
        
        return results;
        
    } catch (error) {
        console.error('💥 Dual tab trader failed:', error.message);
        return { success: false, error: error.message };
    }
}

// Export functions for individual use
async function executeOpenTrade() {
    const trader = new DualTabMEXCTrader();
    await trader.connectToRemoteBrowser();
    await trader.setupTabs();
    return await trader.executeOpenTrade();
}

async function executeCloseTrade() {
    const trader = new DualTabMEXCTrader();
    await trader.connectToRemoteBrowser();
    await trader.setupTabs();
    return await trader.executeCloseTrade();
}

if (require.main === module) {
    runDualTabTrader()
        .then(result => {
            console.log('\n🏁 Dual tab trading session completed');
            process.exit(result.openTrade?.success ? 0 : 1);
        })
        .catch(error => {
            console.error('💥 Session crashed:', error);
            process.exit(1);
        });
}

module.exports = { DualTabMEXCTrader, executeOpenTrade, executeCloseTrade };
