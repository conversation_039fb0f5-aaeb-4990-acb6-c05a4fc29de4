const axios = require('axios');

async function testBalance() {
    console.log('🧪 Simple Balance Test');
    console.log('======================');

    try {
        console.log('Testing webhook listener health...');
        const health = await axios.get('http://localhost:4000/health');
        console.log('✅ Webhook listener is healthy');

        console.log('\nTesting balance endpoint...');
        const balance = await axios.get('http://localhost:4000/api/balance');
        
        console.log('Balance Response:');
        console.log('  Success:', balance.data.success);
        console.log('  Balance:', balance.data.balance.total);
        console.log('  Source:', balance.data.source);
        console.log('  Raw:', balance.data.balance.raw);

        if (balance.data.source === 'frontend' && balance.data.balance.total > 0) {
            console.log('✅ SUCCESS: Frontend balance is working!');
        } else if (balance.data.source === 'api' && balance.data.balance.total === 0) {
            console.log('⚠️ Using API balance (0), frontend fallback may not be triggered');
        }

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Data:', error.response.data);
        }
    }
}

testBalance().catch(console.error);
