#!/usr/bin/env python3
"""
ULTIMATE SIGNATURE CRACKER
Hook into EVERYTHING - all possible crypto APIs, memory operations, and network stack
"""

import json
import time
import hashlib
import hmac
import base64
from playwright.sync_api import sync_playwright
from dotenv import dotenv_values

class UltimateSignatureCracker:
    """Hook into every possible signature generation method"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        
        print("💀 ULTIMATE SIGNATURE CRACKER")
        print("="*35)
        print("🔥 HOOKING INTO EVERYTHING!")
    
    def setup_ultimate_hooks(self):
        """Setup hooks for EVERY possible crypto operation"""
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                return False
            
            self.context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                mexc_page = self.context.new_page()
                mexc_page.goto('https://futures.mexc.com/exchange/BTC_USDT', wait_until='domcontentloaded')
            
            self.page = mexc_page
            
            # Inject ULTIMATE hooks
            self.page.evaluate("""
                window.ultimateInterceptions = [];
                window.allStringOperations = [];
                window.allFunctionCalls = [];
                
                console.log('💀 INSTALLING ULTIMATE HOOKS...');
                
                // Hook ALL string operations that could be crypto-related
                const originalStringMethods = {};
                ['charAt', 'charCodeAt', 'concat', 'indexOf', 'lastIndexOf', 'slice', 'substring', 'substr', 'toLowerCase', 'toUpperCase', 'replace', 'split', 'trim'].forEach(method => {
                    originalStringMethods[method] = String.prototype[method];
                    String.prototype[method] = function(...args) {
                        const result = originalStringMethods[method].apply(this, args);
                        
                        // Check if this string looks like crypto data
                        if (this.length >= 32 && /^[a-f0-9]+$/i.test(this)) {
                            window.allStringOperations.push({
                                type: 'string_' + method,
                                input: this.toString(),
                                args: args,
                                result: result,
                                timestamp: Date.now(),
                                stack: new Error().stack.split('\\n').slice(1, 5)
                            });
                            
                            console.log('🔥 CRYPTO STRING OP:', method, this.substring(0, 32), '=>', typeof result === 'string' ? result.substring(0, 32) : result);
                        }
                        
                        return result;
                    };
                });
                
                // Hook ALL function calls that might be crypto-related
                const originalFunction = window.Function;
                window.Function = function(...args) {
                    const func = originalFunction.apply(this, args);
                    const funcStr = func.toString();
                    
                    // Check if function contains crypto keywords
                    if (funcStr.includes('md5') || funcStr.includes('sha') || funcStr.includes('hash') || 
                        funcStr.includes('sign') || funcStr.includes('crypto') || funcStr.includes('hmac')) {
                        
                        window.allFunctionCalls.push({
                            type: 'function_creation',
                            source: funcStr.substring(0, 500),
                            timestamp: Date.now()
                        });
                        
                        console.log('🔥 CRYPTO FUNCTION CREATED:', funcStr.substring(0, 100));
                    }
                    
                    return func;
                };
                
                // Hook eval and similar dynamic code execution
                const originalEval = window.eval;
                window.eval = function(code) {
                    if (typeof code === 'string' && (code.includes('md5') || code.includes('sha') || code.includes('sign') || code.includes('hash'))) {
                        console.log('🔥 CRYPTO EVAL:', code.substring(0, 200));
                        
                        window.ultimateInterceptions.push({
                            type: 'eval_crypto',
                            code: code.substring(0, 500),
                            timestamp: Date.now()
                        });
                    }
                    
                    return originalEval.apply(this, arguments);
                };
                
                // Hook ALL possible crypto libraries and methods
                const cryptoLibraries = ['CryptoJS', 'forge', 'sjcl', 'crypto', 'md5', 'sha1', 'sha256', 'btoa', 'atob'];
                
                cryptoLibraries.forEach(libName => {
                    if (window[libName]) {
                        console.log('🔥 FOUND CRYPTO LIBRARY:', libName);
                        
                        // Hook all methods of the library
                        function hookObject(obj, path) {
                            try {
                                for (const key in obj) {
                                    if (typeof obj[key] === 'function') {
                                        const originalFunc = obj[key];
                                        obj[key] = function(...args) {
                                            console.log(`🔥 ${path}.${key} CALLED:`, args);
                                            
                                            window.ultimateInterceptions.push({
                                                type: 'crypto_lib_call',
                                                library: libName,
                                                method: `${path}.${key}`,
                                                args: args.map(arg => typeof arg === 'string' ? arg.substring(0, 100) : typeof arg),
                                                timestamp: Date.now()
                                            });
                                            
                                            const result = originalFunc.apply(this, args);
                                            
                                            console.log(`🔥 ${path}.${key} RESULT:`, result);
                                            
                                            window.ultimateInterceptions.push({
                                                type: 'crypto_lib_result',
                                                library: libName,
                                                method: `${path}.${key}`,
                                                result: typeof result === 'string' ? result.substring(0, 100) : typeof result,
                                                timestamp: Date.now()
                                            });
                                            
                                            return result;
                                        };
                                    } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                                        hookObject(obj[key], `${path}.${key}`);
                                    }
                                }
                            } catch (e) {
                                // Ignore errors
                            }
                        }
                        
                        hookObject(window[libName], libName);
                    }
                });
                
                // Hook native crypto
                if (window.crypto) {
                    if (window.crypto.subtle) {
                        const methods = ['digest', 'sign', 'verify', 'encrypt', 'decrypt', 'importKey', 'exportKey', 'generateKey', 'deriveKey', 'deriveBits'];
                        
                        methods.forEach(method => {
                            if (window.crypto.subtle[method]) {
                                const originalMethod = window.crypto.subtle[method];
                                window.crypto.subtle[method] = async function(...args) {
                                    console.log(`🔥 crypto.subtle.${method} CALLED:`, args);
                                    
                                    window.ultimateInterceptions.push({
                                        type: 'native_crypto',
                                        method: method,
                                        args: args.map(arg => typeof arg),
                                        timestamp: Date.now()
                                    });
                                    
                                    const result = await originalMethod.apply(this, args);
                                    
                                    if (result instanceof ArrayBuffer) {
                                        const hex = Array.from(new Uint8Array(result)).map(b => b.toString(16).padStart(2, '0')).join('');
                                        console.log(`🔥 crypto.subtle.${method} RESULT:`, hex);
                                        
                                        window.ultimateInterceptions.push({
                                            type: 'native_crypto_result',
                                            method: method,
                                            result: hex,
                                            timestamp: Date.now()
                                        });
                                    }
                                    
                                    return result;
                                };
                            }
                        });
                    }
                    
                    // Hook crypto.getRandomValues
                    if (window.crypto.getRandomValues) {
                        const originalGetRandomValues = window.crypto.getRandomValues;
                        window.crypto.getRandomValues = function(array) {
                            const result = originalGetRandomValues.apply(this, arguments);
                            
                            console.log('🔥 crypto.getRandomValues called:', array.length);
                            
                            window.ultimateInterceptions.push({
                                type: 'random_values',
                                length: array.length,
                                result: Array.from(array.slice(0, 16)),
                                timestamp: Date.now()
                            });
                            
                            return result;
                        };
                    }
                }
                
                // Hook TextEncoder/TextDecoder (often used with crypto)
                if (window.TextEncoder) {
                    const originalTextEncoder = window.TextEncoder;
                    window.TextEncoder = function() {
                        const encoder = new originalTextEncoder();
                        const originalEncode = encoder.encode;
                        
                        encoder.encode = function(input) {
                            const result = originalEncode.apply(this, arguments);
                            
                            if (input && input.length > 20) {
                                console.log('🔥 TextEncoder.encode:', input.substring(0, 50));
                                
                                window.ultimateInterceptions.push({
                                    type: 'text_encode',
                                    input: input.substring(0, 200),
                                    length: result.length,
                                    timestamp: Date.now()
                                });
                            }
                            
                            return result;
                        };
                        
                        return encoder;
                    };
                }
                
                // Hook ArrayBuffer operations
                const originalArrayBuffer = window.ArrayBuffer;
                window.ArrayBuffer = function(length) {
                    const buffer = new originalArrayBuffer(length);
                    
                    if (length >= 16) { // Potential crypto buffer
                        console.log('🔥 ArrayBuffer created:', length);
                        
                        window.ultimateInterceptions.push({
                            type: 'array_buffer',
                            length: length,
                            timestamp: Date.now()
                        });
                    }
                    
                    return buffer;
                };
                
                // Hook XMLHttpRequest and fetch for signature headers
                const originalSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;
                XMLHttpRequest.prototype.setRequestHeader = function(name, value) {
                    if (name.toLowerCase().includes('sign') || name.toLowerCase().includes('nonce')) {
                        console.log(`🔥🔥🔥 SIGNATURE HEADER: ${name} = ${value} 🔥🔥🔥`);
                        
                        window.ultimateInterceptions.push({
                            type: 'signature_header',
                            name: name,
                            value: value,
                            timestamp: Date.now(),
                            stack: new Error().stack
                        });
                        
                        // Try to trace where this value came from
                        if (name.toLowerCase() === 'x-mxc-sign') {
                            alert(`SIGNATURE INTERCEPTED: ${value}\\n\\nStack trace logged to console!`);
                            console.log('🔥 SIGNATURE STACK TRACE:', new Error().stack);
                        }
                    }
                    
                    return originalSetRequestHeader.apply(this, arguments);
                };
                
                const originalFetch = window.fetch;
                window.fetch = function(...args) {
                    const [url, options] = args;
                    
                    if (options && options.headers) {
                        for (const [name, value] of Object.entries(options.headers)) {
                            if (name.toLowerCase().includes('sign')) {
                                console.log(`🔥🔥🔥 FETCH SIGNATURE: ${name} = ${value} 🔥🔥🔥`);
                                
                                window.ultimateInterceptions.push({
                                    type: 'fetch_signature',
                                    name: name,
                                    value: value,
                                    url: url,
                                    timestamp: Date.now(),
                                    stack: new Error().stack
                                });
                            }
                        }
                    }
                    
                    return originalFetch.apply(this, args);
                };
                
                console.log('💀 ULTIMATE HOOKS INSTALLED!');
                console.log('🔥 Monitoring ALL crypto operations...');
            """)
            
            print("✅ Ultimate hooks setup completed")
            return True
            
        except Exception as e:
            print(f"❌ Setup failed: {e}")
            return False
    
    def monitor_ultimate_activity(self):
        """Monitor ALL crypto activity"""
        
        print("\n🔍 MONITORING ALL CRYPTO ACTIVITY")
        print("="*45)
        print()
        print("🎯 PLACE AN ORDER NOW!")
        print("   - ALL crypto operations will be intercepted")
        print("   - String operations, function calls, native crypto")
        print("   - Memory operations, network requests")
        print("   - Use very low price to avoid fills")
        print()
        
        timeout = 300  # 5 minutes
        start_time = time.time()
        last_ultimate_count = 0
        last_string_count = 0
        last_function_count = 0
        
        while time.time() - start_time < timeout:
            try:
                # Get all interceptions
                ultimate_interceptions = self.page.evaluate("() => window.ultimateInterceptions || []")
                string_operations = self.page.evaluate("() => window.allStringOperations || []")
                function_calls = self.page.evaluate("() => window.allFunctionCalls || []")
                
                # Check for new ultimate interceptions
                if len(ultimate_interceptions) > last_ultimate_count:
                    new_ultimate = ultimate_interceptions[last_ultimate_count:]
                    for interception in new_ultimate:
                        print(f"\n🔥 {interception['type'].upper()}:")
                        
                        if interception['type'] == 'signature_header':
                            print(f"   🎯 SIGNATURE: {interception['name']} = {interception['value']}")
                            
                            # This is our target! Analyze the stack trace
                            self.analyze_signature_stack_trace(interception)
                        
                        elif interception['type'] == 'crypto_lib_call':
                            print(f"   Library: {interception['library']}")
                            print(f"   Method: {interception['method']}")
                            print(f"   Args: {interception['args']}")
                        
                        elif interception['type'] == 'crypto_lib_result':
                            print(f"   Library: {interception['library']}")
                            print(f"   Method: {interception['method']}")
                            print(f"   Result: {interception['result']}")
                            
                            # Check if this is a signature
                            self.check_potential_signature(interception['result'])
                        
                        elif interception['type'] == 'native_crypto_result':
                            print(f"   Method: {interception['method']}")
                            print(f"   Result: {interception['result']}")
                            
                            # Check if this is a signature
                            self.check_potential_signature(interception['result'])
                    
                    last_ultimate_count = len(ultimate_interceptions)
                
                # Check for new string operations
                if len(string_operations) > last_string_count:
                    new_strings = string_operations[last_string_count:]
                    for operation in new_strings:
                        if len(operation['input']) == 32:  # Potential signature
                            print(f"\n🔤 STRING OP: {operation['type']}")
                            print(f"   Input: {operation['input']}")
                            print(f"   Result: {operation['result']}")
                            
                            self.check_potential_signature(operation['input'])
                    
                    last_string_count = len(string_operations)
                
                # Check for new function calls
                if len(function_calls) > last_function_count:
                    new_functions = function_calls[last_function_count:]
                    for func_call in new_functions:
                        print(f"\n🔧 CRYPTO FUNCTION: {func_call['source'][:100]}...")
                    
                    last_function_count = len(function_calls)
                
                # Show progress
                elapsed = int(time.time() - start_time)
                if elapsed % 20 == 0 and elapsed > 0:
                    total_activity = len(ultimate_interceptions) + len(string_operations) + len(function_calls)
                    print(f"⏱️  Monitoring... ({elapsed}s, {total_activity} activities)")
                
                time.sleep(1)
                
            except Exception as e:
                print(f"⚠️  Error: {e}")
                time.sleep(1)
        
        print(f"\n⏰ Ultimate monitoring complete")
        return True
    
    def analyze_signature_stack_trace(self, signature_interception):
        """Analyze the stack trace to find signature generation"""
        
        print(f"\n🔍 ANALYZING SIGNATURE STACK TRACE")
        print("-" * 50)
        
        signature = signature_interception['value']
        stack = signature_interception.get('stack', '')
        
        print(f"Signature: {signature}")
        print(f"Stack trace: {stack[:500]}...")
        
        # Try to extract function names from stack trace
        stack_lines = stack.split('\n')
        for line in stack_lines[:10]:  # First 10 lines
            if 'at ' in line:
                print(f"   📍 {line.strip()}")
    
    def check_potential_signature(self, value):
        """Check if a value could be our signature"""
        
        if not isinstance(value, str):
            return False
        
        # Known signatures
        known_signatures = [
            "e5d090fa331cef9aa0921b014f53210e",
            "e048fb8b1b6e42caf416298ce272548f", 
            "047836d7d32b9c04a4671e8ad93e5baf",
            "1ed499f829cd58b0473709cbb4b44619",
            "99aa050ac9852cf2bae033964204ec23",
            "d2b32c5665cd430a4e2fd23f2f9e5147",
            "8310de0797a2b7cbb814320b41fdb316"
        ]
        
        if value in known_signatures:
            print(f"🎉🎉🎉 KNOWN SIGNATURE FOUND: {value} 🎉🎉🎉")
            return True
        
        # Check if it looks like a signature (32 char hex)
        if len(value) == 32 and all(c in '0123456789abcdef' for c in value.lower()):
            print(f"🔍 Potential signature: {value}")
            return True
        
        return False
    
    def run_ultimate_crack(self):
        """Run the ultimate signature cracking"""
        
        print("="*60)
        print("💀 ULTIMATE SIGNATURE CRACKING")
        print("="*60)
        
        # Setup ultimate hooks
        if not self.setup_ultimate_hooks():
            return False
        
        try:
            # Monitor all activity
            self.monitor_ultimate_activity()
            
            print("\n🎉 ULTIMATE ANALYSIS COMPLETE!")
            return True
            
        finally:
            # Cleanup
            if hasattr(self, 'browser'):
                self.browser.close()
            if hasattr(self, 'playwright'):
                self.playwright.stop()

def main():
    """Main function"""
    
    cracker = UltimateSignatureCracker()
    cracker.run_ultimate_crack()

if __name__ == '__main__':
    main()
