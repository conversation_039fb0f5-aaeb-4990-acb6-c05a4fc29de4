#!/usr/bin/env python3
"""
Browser Session Control Utility
Provides manual control and inspection of browser sessions
"""

import asyncio
import json
import aiohttp
from typing import List, Dict, Any
import webbrowser


class BrowserController:
    """Controller for managing browser sessions via Chrome DevTools Protocol"""
    
    def __init__(self, debug_port: int = 9222):
        self.debug_port = debug_port
        self.base_url = f"http://localhost:{debug_port}"
    
    async def list_sessions(self) -> List[Dict[str, Any]]:
        """List all active browser sessions/tabs"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/json") as response:
                    if response.status == 200:
                        tabs = await response.json()
                        return tabs
                    else:
                        print(f"Failed to get sessions: {response.status}")
                        return []
        except Exception as e:
            print(f"Error listing sessions: {e}")
            return []
    
    async def get_session_info(self, session_id: str) -> Dict[str, Any]:
        """Get detailed information about a specific session"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/json/{session_id}") as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        print(f"Failed to get session info: {response.status}")
                        return {}
        except Exception as e:
            print(f"Error getting session info: {e}")
            return {}
    
    async def take_screenshot(self, session_id: str, filename: str = None) -> str:
        """Take screenshot of a browser session"""
        try:
            # Connect to WebSocket for the session
            ws_url = f"ws://localhost:{self.debug_port}/devtools/page/{session_id}"
            
            async with aiohttp.ClientSession() as session:
                async with session.ws_connect(ws_url) as ws:
                    # Send screenshot command
                    command = {
                        "id": 1,
                        "method": "Page.captureScreenshot",
                        "params": {"format": "png", "quality": 90}
                    }
                    await ws.send_str(json.dumps(command))
                    
                    # Get response
                    async for msg in ws:
                        if msg.type == aiohttp.WSMsgType.TEXT:
                            response = json.loads(msg.data)
                            if "result" in response and "data" in response["result"]:
                                import base64
                                screenshot_data = base64.b64decode(response["result"]["data"])
                                
                                if not filename:
                                    filename = f"screenshot_{session_id[:8]}.png"
                                
                                with open(filename, "wb") as f:
                                    f.write(screenshot_data)
                                
                                print(f"Screenshot saved: {filename}")
                                return filename
                            break
        except Exception as e:
            print(f"Error taking screenshot: {e}")
            return ""
    
    def open_debug_interface(self):
        """Open the Chrome DevTools interface in default browser"""
        webbrowser.open(self.base_url)
        print(f"Debug interface opened: {self.base_url}")
    
    async def execute_javascript(self, session_id: str, script: str) -> Any:
        """Execute JavaScript in a browser session"""
        try:
            ws_url = f"ws://localhost:{self.debug_port}/devtools/page/{session_id}"
            
            async with aiohttp.ClientSession() as session:
                async with session.ws_connect(ws_url) as ws:
                    command = {
                        "id": 1,
                        "method": "Runtime.evaluate",
                        "params": {"expression": script, "returnByValue": True}
                    }
                    await ws.send_str(json.dumps(command))
                    
                    async for msg in ws:
                        if msg.type == aiohttp.WSMsgType.TEXT:
                            response = json.loads(msg.data)
                            if "result" in response:
                                return response["result"].get("result", {}).get("value")
                            break
        except Exception as e:
            print(f"Error executing JavaScript: {e}")
            return None


async def main():
    """Interactive browser control interface"""
    controller = BrowserController()
    
    print("🌐 Browser Session Controller")
    print("=" * 40)
    
    while True:
        print("\nAvailable commands:")
        print("1. List sessions")
        print("2. Open debug interface")
        print("3. Take screenshot")
        print("4. Execute JavaScript")
        print("5. Session details")
        print("6. Exit")
        
        choice = input("\nEnter choice (1-6): ").strip()
        
        if choice == "1":
            sessions = await controller.list_sessions()
            if sessions:
                print(f"\n📋 Found {len(sessions)} active sessions:")
                for i, session in enumerate(sessions):
                    print(f"  {i+1}. {session.get('title', 'Untitled')[:50]}")
                    print(f"     ID: {session.get('id', 'N/A')[:16]}...")
                    print(f"     URL: {session.get('url', 'N/A')[:60]}")
                    print()
            else:
                print("❌ No active sessions found")
        
        elif choice == "2":
            controller.open_debug_interface()
        
        elif choice == "3":
            sessions = await controller.list_sessions()
            if sessions:
                print("\nSelect session for screenshot:")
                for i, session in enumerate(sessions):
                    print(f"  {i+1}. {session.get('title', 'Untitled')[:30]}")
                
                try:
                    idx = int(input("Enter session number: ")) - 1
                    if 0 <= idx < len(sessions):
                        session_id = sessions[idx]['id']
                        filename = await controller.take_screenshot(session_id)
                        if filename:
                            print(f"✅ Screenshot saved: {filename}")
                    else:
                        print("❌ Invalid session number")
                except ValueError:
                    print("❌ Invalid input")
            else:
                print("❌ No sessions available")
        
        elif choice == "4":
            sessions = await controller.list_sessions()
            if sessions:
                print("\nSelect session for JavaScript execution:")
                for i, session in enumerate(sessions):
                    print(f"  {i+1}. {session.get('title', 'Untitled')[:30]}")
                
                try:
                    idx = int(input("Enter session number: ")) - 1
                    if 0 <= idx < len(sessions):
                        session_id = sessions[idx]['id']
                        script = input("Enter JavaScript code: ")
                        result = await controller.execute_javascript(session_id, script)
                        print(f"Result: {result}")
                    else:
                        print("❌ Invalid session number")
                except ValueError:
                    print("❌ Invalid input")
            else:
                print("❌ No sessions available")
        
        elif choice == "5":
            sessions = await controller.list_sessions()
            if sessions:
                print("\nSelect session for details:")
                for i, session in enumerate(sessions):
                    print(f"  {i+1}. {session.get('title', 'Untitled')[:30]}")
                
                try:
                    idx = int(input("Enter session number: ")) - 1
                    if 0 <= idx < len(sessions):
                        session = sessions[idx]
                        print(f"\n📄 Session Details:")
                        print(f"  Title: {session.get('title', 'N/A')}")
                        print(f"  URL: {session.get('url', 'N/A')}")
                        print(f"  ID: {session.get('id', 'N/A')}")
                        print(f"  Type: {session.get('type', 'N/A')}")
                        print(f"  WebSocket URL: {session.get('webSocketDebuggerUrl', 'N/A')}")
                    else:
                        print("❌ Invalid session number")
                except ValueError:
                    print("❌ Invalid input")
            else:
                print("❌ No sessions available")
        
        elif choice == "6":
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid choice")


if __name__ == "__main__":
    asyncio.run(main())
