#!/usr/bin/env node

/**
 * Test script to verify the fixes for infinite loop and browser connection issues
 * This script tests the system's resilience and error handling improvements
 */

const axios = require('axios');

const MEXC_TRADER_URL = 'http://localhost:3001';
const WEBHOOK_LISTENER_URL = 'http://localhost:80';

class SystemTester {
    constructor() {
        this.testResults = [];
        this.startTime = Date.now();
    }

    async runTests() {
        console.log('🧪 Starting system resilience tests...\n');

        try {
            // Test 1: Health checks
            await this.testHealthChecks();
            
            // Test 2: Circuit breaker functionality
            await this.testCircuitBreaker();
            
            // Test 3: Emergency stop mechanism
            await this.testEmergencyStop();
            
            // Test 4: Service availability checks
            await this.testServiceAvailability();
            
            // Test 5: Error rate limiting
            await this.testErrorRateLimiting();

            this.printResults();
            
        } catch (error) {
            console.error('❌ Test suite failed:', error.message);
            process.exit(1);
        }
    }

    async testHealthChecks() {
        console.log('🔍 Testing health check endpoints...');
        
        try {
            // Test MEXC Trader health
            const mexcHealth = await axios.get(`${MEXC_TRADER_URL}/health`);
            this.addResult('MEXC Trader Health Check', mexcHealth.status === 200, {
                status: mexcHealth.data.status,
                circuitBreaker: mexcHealth.data.circuitBreaker
            });

            // Test Webhook Listener health
            const webhookHealth = await axios.get(`${WEBHOOK_LISTENER_URL}/health`);
            this.addResult('Webhook Listener Health Check', webhookHealth.status === 200, {
                status: webhookHealth.data.status
            });

        } catch (error) {
            this.addResult('Health Checks', false, { error: error.message });
        }
    }

    async testCircuitBreaker() {
        console.log('⚡ Testing circuit breaker functionality...');
        
        try {
            // Get initial circuit breaker state
            const initialHealth = await axios.get(`${MEXC_TRADER_URL}/health`);
            const initialState = initialHealth.data.circuitBreaker?.state || 'CLOSED';
            
            this.addResult('Circuit Breaker Initial State', initialState === 'CLOSED', {
                state: initialState
            });

            // Test that circuit breaker info is included in responses
            this.addResult('Circuit Breaker Info Available', 
                initialHealth.data.circuitBreaker !== undefined, {
                circuitBreaker: initialHealth.data.circuitBreaker
            });

        } catch (error) {
            this.addResult('Circuit Breaker Test', false, { error: error.message });
        }
    }

    async testEmergencyStop() {
        console.log('🚨 Testing emergency stop mechanism...');
        
        try {
            // Get trading executor status
            const status = await axios.get(`${WEBHOOK_LISTENER_URL}/api/status`);
            const executorStatus = status.data.tradingExecutorStatus;
            
            this.addResult('Emergency Stop Status Available', 
                executorStatus !== undefined, {
                executorStatus
            });

            this.addResult('Emergency Stop Initially Inactive', 
                !executorStatus?.emergencyStopActive, {
                emergencyStopActive: executorStatus?.emergencyStopActive
            });

        } catch (error) {
            this.addResult('Emergency Stop Test', false, { error: error.message });
        }
    }

    async testServiceAvailability() {
        console.log('🔗 Testing service availability checks...');
        
        try {
            // Test webhook listener status endpoint
            const status = await axios.get(`${WEBHOOK_LISTENER_URL}/api/status`);
            
            this.addResult('Service Status Endpoint', status.status === 200, {
                configured: status.data.configured,
                mexcConnected: status.data.mexcConnected
            });

            // Test that trading executor status is included
            this.addResult('Trading Executor Status Included', 
                status.data.tradingExecutorStatus !== undefined, {
                tradingExecutorStatus: status.data.tradingExecutorStatus
            });

        } catch (error) {
            this.addResult('Service Availability Test', false, { error: error.message });
        }
    }

    async testErrorRateLimiting() {
        console.log('📊 Testing error rate limiting...');
        
        try {
            // Test that invalid requests don't spam logs
            const invalidRequests = [];
            for (let i = 0; i < 5; i++) {
                try {
                    await axios.post(`${MEXC_TRADER_URL}/trade`, {
                        orderType: 'Invalid',
                        quantity: 'invalid'
                    });
                } catch (error) {
                    invalidRequests.push(error.response?.status || 'network_error');
                }
            }

            this.addResult('Error Rate Limiting', 
                invalidRequests.length === 5, {
                invalidRequestsHandled: invalidRequests.length,
                statusCodes: invalidRequests
            });

        } catch (error) {
            this.addResult('Error Rate Limiting Test', false, { error: error.message });
        }
    }

    addResult(testName, passed, details = {}) {
        this.testResults.push({
            test: testName,
            passed,
            details,
            timestamp: new Date().toISOString()
        });
        
        const status = passed ? '✅' : '❌';
        console.log(`  ${status} ${testName}`);
        if (!passed || Object.keys(details).length > 0) {
            console.log(`     Details:`, JSON.stringify(details, null, 2));
        }
    }

    printResults() {
        const totalTime = Date.now() - this.startTime;
        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        
        console.log('\n' + '='.repeat(60));
        console.log('🧪 TEST RESULTS SUMMARY');
        console.log('='.repeat(60));
        console.log(`Total Tests: ${total}`);
        console.log(`Passed: ${passed}`);
        console.log(`Failed: ${total - passed}`);
        console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
        console.log(`Total Time: ${totalTime}ms`);
        console.log('='.repeat(60));

        if (passed === total) {
            console.log('🎉 All tests passed! System fixes are working correctly.');
        } else {
            console.log('⚠️  Some tests failed. Please review the issues above.');
            process.exit(1);
        }
    }
}

// Run tests if this script is executed directly
if (require.main === module) {
    const tester = new SystemTester();
    tester.runTests().catch(error => {
        console.error('❌ Test execution failed:', error);
        process.exit(1);
    });
}

module.exports = SystemTester;
