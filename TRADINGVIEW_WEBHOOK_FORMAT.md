# TradingView Webhook Format for MEXC Futures Trading

## 📡 Webhook URL
```
http://localhost:4000/webhook
```

## 🎯 Supported Order Types

### 1. Open Long Position
```json
{
    "symbol": "TRUUSDT",
    "trade": "open_long",
    "last_price": "0.03295",
    "leverage": "2"
}
```

### 2. Open Short Position
```json
{
    "symbol": "TRUUSDT",
    "trade": "open_short", 
    "last_price": "0.03295",
    "leverage": "2"
}
```

### 3. Close Long Position
```json
{
    "symbol": "TRUUSDT",
    "trade": "close_long",
    "last_price": "0.03295",
    "leverage": "2"
}
```

### 4. Close Short Position
```json
{
    "symbol": "TRUUSDT",
    "trade": "close_short",
    "last_price": "0.03295", 
    "leverage": "2"
}
```

## 📊 Balance Access

### Get Current Balance
```bash
# Get cached balance (fast)
curl http://localhost:4000/api/balance

# Force refresh balance from frontend
curl http://localhost:4000/api/balance?refresh=true
```

### Balance Response Format
```json
{
    "success": true,
    "balance": {
        "asset": "USDT",
        "free": 2.2951,
        "locked": 0,
        "total": 2.2951,
        "updateTime": "2025-08-15T10:45:00.000Z",
        "timestamp": "2025-08-15T10:45:00.000Z",
        "source": "frontend",
        "raw": "‎2.2951 USDT",
        "cached": false
    },
    "source": "frontend",
    "timestamp": "2025-08-15T10:45:00.000Z"
}
```

## 🔧 TradingView Alert Setup

### Pine Script Example
```pinescript
//@version=5
strategy("MEXC Futures Alert", overlay=true)

// Your trading logic here
longCondition = ta.crossover(ta.sma(close, 14), ta.sma(close, 28))
shortCondition = ta.crossunder(ta.sma(close, 14), ta.sma(close, 28))

if (longCondition)
    strategy.entry("Long", strategy.long)
    alert('{"symbol":"TRUUSDT","trade":"open_long","last_price":"' + str.tostring(close) + '","leverage":"2"}', alert.freq_once_per_bar)

if (shortCondition)
    strategy.entry("Short", strategy.short)
    alert('{"symbol":"TRUUSDT","trade":"open_short","last_price":"' + str.tostring(close) + '","leverage":"2"}', alert.freq_once_per_bar)

// Close conditions
if (strategy.position_size > 0 and ta.crossunder(ta.sma(close, 14), ta.sma(close, 28)))
    strategy.close("Long")
    alert('{"symbol":"TRUUSDT","trade":"close_long","last_price":"' + str.tostring(close) + '","leverage":"2"}', alert.freq_once_per_bar)

if (strategy.position_size < 0 and ta.crossover(ta.sma(close, 14), ta.sma(close, 28)))
    strategy.close("Short")
    alert('{"symbol":"TRUUSDT","trade":"close_short","last_price":"' + str.tostring(close) + '","leverage":"2"}', alert.freq_once_per_bar)
```

### Alert Configuration
1. **Condition**: Your strategy condition
2. **Options**: 
   - ✅ Once Per Bar Close
   - ✅ Only Order Fills (if using strategy)
3. **Message**: Use the JSON format above
4. **Webhook URL**: `http://localhost:4000/webhook`

## 📈 System Status Check

### Health Check
```bash
curl http://localhost:4000/health
```

### Full Status (includes balance)
```bash
curl http://localhost:4000/api/status
```

### Status Response
```json
{
    "botActive": true,
    "configured": true,
    "mexcConnected": true,
    "lastSignalTime": "2025-08-15T10:45:00.000Z",
    "totalSignalsReceived": 5,
    "totalTradesExecuted": 4,
    "timestamp": "2025-08-15T10:45:00.000Z",
    "balance": {
        "asset": "USDT",
        "free": 2.2951,
        "locked": 0,
        "total": 2.2951,
        "updateTime": "2025-08-15T10:45:00.000Z",
        "timestamp": "2025-08-15T10:45:00.000Z",
        "source": "frontend",
        "raw": "‎2.2951 USDT",
        "cached": true
    }
}
```

## ⚡ Performance Features

### Balance Caching
- **Cached**: Balance is cached for 30 seconds for fast access
- **Refresh**: Use `?refresh=true` to force fresh balance fetch
- **Source**: Shows whether balance came from 'api' or 'frontend'

### Execution Speed
- **Target**: Sub-2 second execution time
- **Single Browser**: All order types use single browser on port 9223
- **Tab Switching**: Automatic Open/Close tab selection

## 🔍 Troubleshooting

### Check Balance Source
```bash
# Check if balance is from frontend
curl http://localhost:4000/api/balance | grep "source"
```

### Force Balance Refresh
```bash
# Force fresh balance from MEXC frontend
curl "http://localhost:4000/api/balance?refresh=true"
```

### Test Webhook
```bash
# Test webhook with sample data
curl -X POST http://localhost:4000/webhook \
  -H "Content-Type: application/json" \
  -d '{"symbol":"TRUUSDT","trade":"open_long","last_price":"0.03295","leverage":"2"}'
```

## 📋 Required Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `symbol` | string | ✅ | Trading pair (e.g., "TRUUSDT") |
| `trade` | string | ✅ | Action: "open_long", "open_short", "close_long", "close_short" |
| `last_price` | string | ✅ | Current price for reference |
| `leverage` | string | ✅ | Leverage setting (e.g., "2") |

## 🎯 Balance Integration

The system now automatically fetches balance from the MEXC frontend page using the CSS selector:
```css
span.AssetsItem_num__9eLwJ
```

This provides real-time balance information even when the MEXC API shows zero balance, ensuring accurate position sizing and money management.

## 🚀 Quick Start

1. **Start Services**:
   ```bash
   # Start MEXC browser on port 9223
   # Start MEXC Futures Trader (port 3000)
   # Start TradingView Webhook Listener (port 4000)
   ```

2. **Verify Balance**:
   ```bash
   curl http://localhost:4000/api/balance
   ```

3. **Configure TradingView Alert**:
   - Webhook URL: `http://localhost:4000/webhook`
   - Message: Use JSON format above

4. **Test Trade**:
   ```bash
   curl -X POST http://localhost:4000/webhook \
     -H "Content-Type: application/json" \
     -d '{"symbol":"TRUUSDT","trade":"open_long","last_price":"0.03295","leverage":"2"}'
   ```

Your MEXC futures trading system is now ready with frontend balance integration! 🎉
