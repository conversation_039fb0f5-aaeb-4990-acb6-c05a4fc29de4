require('dotenv').config();

async function testGothamSDK() {
    console.log('\n=== Testing @theothergothamdev/mexc-sdk ===');
    
    try {
        // Try to import the package
        let MexcSDK;
        try {
            MexcSDK = require('@theothergothamdev/mexc-sdk');
        } catch (importError) {
            console.log('⚠ Import failed, trying alternative import methods...');
            try {
                const pkg = require('@theothergothamdev/mexc-sdk');
                MexcSDK = pkg.default || pkg.MexcSDK || pkg;
            } catch (altError) {
                throw new Error(`Failed to import @theothergothamdev/mexc-sdk: ${importError.message}`);
            }
        }

        console.log('✓ @theothergothamdev/mexc-sdk imported successfully');

        // Initialize the SDK
        let client;
        try {
            client = new MexcSDK({
                apiKey: process.env.MEXC_API_KEY,
                apiSecret: process.env.MEXC_API_SECRET,
                baseURL: 'https://api.mexc.com',
            });
        } catch (initError) {
            // Try alternative initialization
            client = MexcSDK({
                apiKey: process.env.MEXC_API_KEY,
                apiSecret: process.env.MEXC_API_SECRET,
            });
        }

        console.log('✓ SDK client initialized');

        // Test connection
        console.log('\n1. Testing connection...');
        
        // Try to get server time
        try {
            const serverTime = await client.getServerTime();
            console.log('✓ Server time retrieved:', serverTime);
        } catch (error) {
            console.log('⚠ Server time failed:', error.message);
        }

        // Try to get account info
        try {
            const accountInfo = await client.getAccountInfo();
            console.log('✓ Account info retrieved');
        } catch (error) {
            console.log('⚠ Account info failed:', error.message);
        }

        // Test market data
        console.log('\n2. Testing market data...');
        try {
            const symbol = process.env.TEST_SYMBOL || 'BTCUSDT';
            const ticker = await client.getTicker(symbol);
            console.log('✓ Ticker data retrieved for', symbol);
        } catch (error) {
            console.log('⚠ Market data failed:', error.message);
        }

        // Test futures functionality if available
        console.log('\n3. Testing futures functionality...');
        try {
            const positions = await client.getFuturesPositions();
            console.log('✓ Futures positions retrieved:', positions.length, 'positions');
        } catch (error) {
            console.log('⚠ Futures positions failed:', error.message);
        }

        // Test order placement (dry run)
        console.log('\n4. Testing order creation (dry run)...');
        try {
            const orderParams = {
                symbol: process.env.TEST_SYMBOL || 'BTCUSDT',
                side: process.env.TEST_SIDE || 'buy',
                type: 'LIMIT',
                quantity: process.env.TEST_QUANTITY || '0.001',
                price: '30000',
                timeInForce: 'GTC'
            };

            console.log('Order parameters:', orderParams);
            
            // Note: Uncomment to actually place order
            // const order = await client.createOrder(orderParams);
            console.log('✓ Order parameters validated (order not placed)');
            
        } catch (error) {
            console.log('⚠ Order creation test failed:', error.message);
        }

        console.log('\n✅ @theothergothamdev/mexc-sdk test completed');
        return { success: true, library: '@theothergothamdev/mexc-sdk', features: ['spot', 'futures_possible'] };

    } catch (error) {
        console.error('❌ @theothergothamdev/mexc-sdk test failed:', error.message);
        return { success: false, library: '@theothergothamdev/mexc-sdk', error: error.message };
    }
}

if (require.main === module) {
    testGothamSDK().then(result => {
        console.log('\nResult:', result);
        process.exit(result.success ? 0 : 1);
    });
}

module.exports = testGothamSDK;
