# MEXC Configuration
MEXC_BASE_URL=https://futures.mexc.com
MEXC_SESSION_TIMEOUT=432000  # 5 days in seconds
MEXC_LOGIN_URL=https://www.mexc.com/login

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_CHAT_ID=142183523
TELEGRAM_ENABLED=true

# Trading Configuration
MAX_CONCURRENT_TRADES=5
DEFAULT_LEVERAGE=1
RISK_MANAGEMENT_ENABLED=true
MAX_POSITION_SIZE=1000  # USDT
EMERGENCY_STOP_ENABLED=true

# Session Management
SESSION_POOL_SIZE=3
SESSION_HEALTH_CHECK_INTERVAL=1800  # 30 minutes in seconds
SESSION_EXPIRY_WARNING_HOURS=24
SESSION_ROTATION_ENABLED=true
AUTO_SESSION_REFRESH=true

# Browser Configuration
BROWSER_POOL_SIZE=3
HEADLESS_MODE=false  # Set to false for testing to see browser windows
BROWSER_TIMEOUT=30000  # 30 seconds
ENABLE_NETWORK_INTERCEPTION=true
BROWSER_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36

# Performance Settings
ENABLE_SELECTOR_CACHING=true
ENABLE_CONNECTION_POOLING=true
MAX_RETRY_ATTEMPTS=3
RETRY_DELAY_SECONDS=1
PARALLEL_PROCESSING_ENABLED=true

# Security Settings
ENCRYPTION_KEY=gk3Bwk4P9nT69JFrJcdcPksrAg61xwk5anCFsCdgJjg=
SESSION_ENCRYPTION_ENABLED=true
SECURE_COOKIES=true

# Database Configuration
DATABASE_URL=sqlite:///./data/trading_system.db
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Logging Configuration
LOG_LEVEL=DEBUG  # DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FILE=logs/trading_system.log
LOG_MAX_SIZE=10485760  # 10MB
LOG_BACKUP_COUNT=5
STRUCTURED_LOGGING=true

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1
API_TIMEOUT=30
CORS_ENABLED=true
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000"]

# Webhook Configuration
WEBHOOK_SECRET=mexc_webhook_secret_2024_secure_key_for_testing
WEBHOOK_TIMEOUT=10
WEBHOOK_MAX_RETRIES=3

# Monitoring & Health Checks
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=300  # 5 minutes
METRICS_ENABLED=true
PROMETHEUS_PORT=9090

# Development Settings
DEBUG_MODE=true  # Enable for testing
RELOAD_ON_CHANGE=true  # Enable for development
ENABLE_PROFILING=false

# Proxy Configuration (Optional)
PROXY_ENABLED=false
PROXY_URL=http://proxy-server:port
PROXY_USERNAME=
PROXY_PASSWORD=

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60  # seconds

# Backup & Recovery
AUTO_BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=30
BACKUP_LOCATION=./backups

# Trading Pairs Configuration
SUPPORTED_SYMBOLS=["TRU_USDT", "ETH_USDT", "BNB_USDT", "ADA_USDT", "SOL_USDT"]
DEFAULT_SYMBOL=TRU_USDT

# Risk Management
STOP_LOSS_ENABLED=true
TAKE_PROFIT_ENABLED=true
TRAILING_STOP_ENABLED=false
MAX_DRAWDOWN_PERCENT=10
DAILY_LOSS_LIMIT=500  # USDT

# Notification Settings
EMAIL_NOTIFICATIONS_ENABLED=false
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=
EMAIL_PASSWORD=
EMAIL_RECIPIENTS=["<EMAIL>"]

# Advanced Settings
CUSTOM_HEADERS_ENABLED=true
USER_DATA_DIR=./browser_data
SCREENSHOT_ON_ERROR=true
SAVE_NETWORK_LOGS=true
ENABLE_STEALTH_MODE=true

# Environment
ENVIRONMENT=development  # development, staging, production
VERSION=1.0.0
