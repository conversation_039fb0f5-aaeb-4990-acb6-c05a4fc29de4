const FastMEXCFuturesBot = require('./fast-futures-bot');
require('dotenv').config();

async function finalTest() {
    console.log('🎯 FINAL TEST: MEXC Fast Futures Trading Bot');
    console.log('============================================');
    console.log('⚠️  WARNING: This will attempt to place a REAL order!');
    console.log('⚠️  Make sure you understand the risks!');
    console.log('');
    console.log('Configuration:');
    console.log(`  Email: ${process.env.MEXC_EMAIL}`);
    console.log(`  Symbol: TRU_USDT`);
    console.log(`  Side: buy`);
    console.log(`  Type: market`);
    console.log(`  Quantity: 40`);
    console.log(`  Leverage: 1`);
    console.log('');
    console.log('🎯 Target: Sub-2 second execution');
    console.log('');

    // Countdown
    for (let i = 5; i > 0; i--) {
        console.log(`Starting in ${i} seconds... (Press Ctrl+C to cancel)`);
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    const bot = new FastMEXCFuturesBot();
    const overallStart = Date.now();

    try {
        // Initialize
        console.log('\n🚀 Phase 1: Initialization');
        const initStart = Date.now();
        await bot.initialize();
        const initTime = Date.now() - initStart;
        console.log(`✅ Browser initialized in ${initTime}ms`);

        // Login
        console.log('\n🔐 Phase 2: Authentication');
        const loginStart = Date.now();
        const loginSuccess = await bot.login();
        const loginTime = Date.now() - loginStart;
        
        if (!loginSuccess) {
            throw new Error('Login failed');
        }
        console.log(`✅ Login completed in ${loginTime}ms`);

        // Get account info
        console.log('\n💰 Phase 3: Account Verification');
        const accountStart = Date.now();
        const accountInfo = await bot.getAccountInfo();
        const accountTime = Date.now() - accountStart;
        console.log(`✅ Account info retrieved in ${accountTime}ms`);
        console.log('Account data:', accountInfo);

        // Place order
        console.log('\n🎯 Phase 4: Order Execution');
        console.log('⚠️  PLACING REAL ORDER NOW!');
        
        const orderStart = Date.now();
        const result = await bot.placeFuturesOrder({
            symbol: 'TRU_USDT',
            side: 'buy',
            type: 'market',
            quantity: '40',
            leverage: '1'
        });
        const orderTime = Date.now() - orderStart;

        // Results
        const totalTime = Date.now() - overallStart;
        
        console.log('\n📊 EXECUTION RESULTS');
        console.log('===================');
        console.log(`Order Success: ${result.success ? '✅ YES' : '❌ NO'}`);
        console.log(`Order Time: ${orderTime}ms`);
        console.log(`Total Time: ${totalTime}ms`);
        console.log(`Speed Target: ${totalTime < 2000 ? '🎉 ACHIEVED' : '⚠️ MISSED'} (target: <2000ms)`);
        
        if (result.error) {
            console.log(`Error: ${result.error}`);
        }

        // Performance breakdown
        console.log('\n⚡ PERFORMANCE BREAKDOWN');
        console.log('=======================');
        console.log(`Initialization: ${initTime}ms`);
        console.log(`Authentication: ${loginTime}ms`);
        console.log(`Account Check: ${accountTime}ms`);
        console.log(`Order Execution: ${orderTime}ms`);
        console.log(`Total Time: ${totalTime}ms`);

        // Speed rating
        let rating;
        if (totalTime < 2000) {
            rating = '🏆 EXCELLENT - Sub-2 second target achieved!';
        } else if (totalTime < 3000) {
            rating = '✅ GOOD - Close to target';
        } else if (totalTime < 5000) {
            rating = '⚠️ ACCEPTABLE - Needs optimization';
        } else {
            rating = '❌ SLOW - Significant optimization needed';
        }

        console.log(`\nSpeed Rating: ${rating}`);

        // Save detailed results
        const detailedResults = {
            timestamp: new Date().toISOString(),
            success: result.success,
            performance: {
                initialization: initTime,
                authentication: loginTime,
                accountCheck: accountTime,
                orderExecution: orderTime,
                totalTime: totalTime,
                targetAchieved: totalTime < 2000
            },
            order: {
                symbol: 'TRU_USDT',
                side: 'buy',
                type: 'market',
                quantity: '40',
                leverage: '1'
            },
            result: result,
            rating: rating
        };

        require('fs').writeFileSync(
            'final-test-results.json',
            JSON.stringify(detailedResults, null, 2)
        );

        console.log('\n📄 Detailed results saved to: final-test-results.json');

        if (result.success) {
            console.log('\n🎉 SUCCESS! Order placed successfully!');
            console.log('✅ MEXC Fast Futures Trading Bot is working!');
            console.log('⚡ Ready for production use with proper risk management');
        } else {
            console.log('\n❌ Order placement failed');
            console.log('🔧 Check the error details and try again');
        }

        return detailedResults;

    } catch (error) {
        const totalTime = Date.now() - overallStart;
        console.error('\n💥 Test failed:', error.message);
        
        const errorResults = {
            timestamp: new Date().toISOString(),
            success: false,
            error: error.message,
            totalTime: totalTime
        };

        require('fs').writeFileSync(
            'final-test-results.json',
            JSON.stringify(errorResults, null, 2)
        );

        return errorResults;
    } finally {
        await bot.close();
        console.log('\n✅ Browser closed');
    }
}

// Run the final test
if (require.main === module) {
    finalTest()
        .then(results => {
            console.log('\n🏁 Final test completed');
            process.exit(results.success ? 0 : 1);
        })
        .catch(error => {
            console.error('💥 Test crashed:', error);
            process.exit(1);
        });
}

module.exports = finalTest;
