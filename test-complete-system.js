const axios = require('axios');

class SystemTester {
    constructor() {
        this.baseUrl = 'http://localhost:4000';
        this.traderUrl = 'http://localhost:3000';
        this.testResults = [];
    }

    async log(message) {
        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] ${message}`);
    }

    async testService(url, name) {
        try {
            const response = await axios.get(`${url}/health`);
            await this.log(`✅ ${name} is running: ${response.data.status}`);
            return true;
        } catch (error) {
            await this.log(`❌ ${name} is not running: ${error.message}`);
            return false;
        }
    }

    async getSystemStatus() {
        try {
            const response = await axios.get(`${this.baseUrl}/api/status`);
            await this.log(`📊 System Status:`);
            await this.log(`   Bot Active: ${response.data.botActive}`);
            await this.log(`   MEXC Connected: ${response.data.mexcConnected}`);
            await this.log(`   Balance: ${response.data.balance?.total || 'N/A'} USDT`);
            await this.log(`   Signals Received: ${response.data.totalSignalsReceived}`);
            await this.log(`   Trades Executed: ${response.data.totalTradesExecuted}`);
            return response.data;
        } catch (error) {
            await this.log(`❌ Failed to get system status: ${error.message}`);
            return null;
        }
    }

    async getMarketData() {
        try {
            const response = await axios.get(`${this.baseUrl}/api/market-data`);
            await this.log(`📈 Market Data:`);
            await this.log(`   Current Price: $${response.data.currentPrice.price}`);
            await this.log(`   ATR: ${response.data.atr}`);
            return response.data;
        } catch (error) {
            await this.log(`❌ Failed to get market data: ${error.message}`);
            return null;
        }
    }

    async sendWebhookSignal(signal) {
        try {
            await this.log(`📡 Sending signal: ${JSON.stringify(signal)}`);
            const startTime = Date.now();
            
            const response = await axios.post(`${this.baseUrl}/webhook`, signal);
            const executionTime = Date.now() - startTime;
            
            if (response.data.success) {
                await this.log(`✅ Signal processed successfully in ${executionTime}ms`);
                await this.log(`   Trade ID: ${response.data.tradeId}`);
                await this.log(`   Execution Time: ${response.data.executionTime}ms`);
                await this.log(`   Position Size: ${response.data.positionSize}`);
                
                this.testResults.push({
                    signal,
                    success: true,
                    executionTime: response.data.executionTime,
                    tradeId: response.data.tradeId
                });
            } else {
                await this.log(`❌ Signal failed: ${response.data.error}`);
                this.testResults.push({
                    signal,
                    success: false,
                    error: response.data.error
                });
            }
            
            return response.data;
        } catch (error) {
            await this.log(`❌ Webhook failed: ${error.response?.data?.error || error.message}`);
            this.testResults.push({
                signal,
                success: false,
                error: error.message
            });
            return null;
        }
    }

    async getPositions() {
        try {
            const response = await axios.get(`${this.baseUrl}/api/positions`);
            await this.log(`📍 Active Positions: ${response.data.statistics.activePositions}`);
            
            if (response.data.positions.length > 0) {
                for (const position of response.data.positions) {
                    await this.log(`   Position ${position.id}:`);
                    await this.log(`     Entry: $${position.entryPrice}`);
                    await this.log(`     Stop Loss: $${position.stopLoss}`);
                    await this.log(`     SL Type: ${position.slType}`);
                    await this.log(`     Take Profits: ${position.takeProfits.length}`);
                    await this.log(`     Status: ${position.status}`);
                }
            }
            
            return response.data;
        } catch (error) {
            await this.log(`❌ Failed to get positions: ${error.message}`);
            return null;
        }
    }

    async testTraderService(orderType, quantity = '0.0001') {
        try {
            await this.log(`🎯 Testing trader service: ${orderType}`);
            
            const response = await axios.post(`${this.traderUrl}/execute`, {
                orderType,
                quantity
            });
            
            if (response.data.success) {
                await this.log(`✅ Trader test successful: ${orderType}`);
                await this.log(`   Execution Time: ${response.data.executionTime}ms`);
                await this.log(`   Target Achieved: ${response.data.targetAchieved}`);
            } else {
                await this.log(`❌ Trader test failed: ${response.data.error}`);
            }
            
            return response.data;
        } catch (error) {
            await this.log(`❌ Trader service error: ${error.message}`);
            return null;
        }
    }

    async runCompleteTest() {
        await this.log('🚀 Starting Complete System Test');
        await this.log('=====================================');

        // Test 1: Service Health
        await this.log('\n1️⃣ Testing Service Health...');
        const webhookListenerOk = await this.testService(this.baseUrl, 'Webhook Listener');
        const traderServiceOk = await this.testService(this.traderUrl, 'Trader Service');

        if (!webhookListenerOk || !traderServiceOk) {
            await this.log('❌ Services not running. Aborting test.');
            return;
        }

        // Test 2: System Status
        await this.log('\n2️⃣ Checking System Status...');
        const status = await this.getSystemStatus();
        
        if (!status?.mexcConnected) {
            await this.log('❌ MEXC not connected. Aborting test.');
            return;
        }

        // Test 3: Market Data
        await this.log('\n3️⃣ Getting Market Data...');
        const marketData = await this.getMarketData();
        
        if (!marketData) {
            await this.log('❌ Market data not available. Aborting test.');
            return;
        }

        // Test 4: Test All Order Types
        await this.log('\n4️⃣ Testing All Order Types...');
        
        const testSignals = [
            {
                symbol: "TRUUSDT",
                trade: "open_long",
                last_price: marketData.currentPrice.price.toString(),
                leverage: "2"
            },
            {
                symbol: "TRUUSDT", 
                trade: "open_short",
                last_price: marketData.currentPrice.price.toString(),
                leverage: "2"
            },
            {
                symbol: "TRUUSDT",
                trade: "close_long", 
                last_price: marketData.currentPrice.price.toString(),
                leverage: "2"
            },
            {
                symbol: "TRUUSDT",
                trade: "close_short",
                last_price: marketData.currentPrice.price.toString(), 
                leverage: "2"
            }
        ];

        for (const signal of testSignals) {
            await this.sendWebhookSignal(signal);
            await new Promise(resolve => setTimeout(resolve, 3000)); // Wait 3 seconds between tests
            await this.getPositions();
            await this.log(''); // Empty line for readability
        }

        // Test 5: Final Status
        await this.log('\n5️⃣ Final System Status...');
        await this.getSystemStatus();
        await this.getPositions();

        // Test Results Summary
        await this.log('\n📊 TEST RESULTS SUMMARY');
        await this.log('========================');
        const successful = this.testResults.filter(r => r.success).length;
        const total = this.testResults.length;
        
        await this.log(`✅ Successful: ${successful}/${total}`);
        await this.log(`❌ Failed: ${total - successful}/${total}`);
        
        if (successful === total) {
            await this.log('🎉 ALL TESTS PASSED! System is fully operational.');
        } else {
            await this.log('⚠️ Some tests failed. Check logs above for details.');
        }

        await this.log('\n🏁 Complete System Test Finished');
    }
}

// Run the test
async function main() {
    const tester = new SystemTester();
    await tester.runCompleteTest();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = SystemTester;
