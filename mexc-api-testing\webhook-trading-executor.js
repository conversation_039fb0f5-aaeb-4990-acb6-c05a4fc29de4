const { spawn } = require('child_process');
const path = require('path');

class WebhookTradingExecutor {
    constructor(logger = null, telegramBot = null) {
        this.logger = logger || console;
        this.telegramBot = telegramBot;
        this.isExecutingTrade = false;
        this.consecutiveFailures = 0;
        this.emergencyStopActive = false;
        this.emergencyStopTime = null;
        this.emergencyStopResetTime = 30 * 60 * 1000; // 30 minutes
        this.ready = true;
    }

    async executeTrade(tradeRequest) {
        const startTime = Date.now();
        
        try {
            this.logger.info('Executing trade with browser automation', { tradeRequest });
            
            // Map the trade request to our order types
            const orderType = this.mapOrderType(tradeRequest);
            const port = this.selectPort(orderType);
            
            this.logger.info('Mapped trade request', { 
                originalOrderType: tradeRequest.orderType,
                mappedOrderType: orderType,
                port: port
            });

            // Execute using our working trader
            const result = await this.executeWithWorkingTrader(orderType, port, tradeRequest.quantity);
            
            const executionTime = Date.now() - startTime;
            
            if (result.success) {
                this.consecutiveFailures = 0; // Reset failure counter
                
                return {
                    success: true,
                    executionTime: result.executionTime,
                    totalTime: executionTime,
                    orderType: orderType,
                    quantity: tradeRequest.quantity,
                    port: port,
                    targetAchieved: result.executionTime < 2000,
                    verified: result.verified,
                    timestamp: new Date().toISOString()
                };
            } else {
                this.consecutiveFailures++;
                
                // Check for emergency stop condition
                if (this.consecutiveFailures >= 5) {
                    this.emergencyStopActive = true;
                    this.emergencyStopTime = Date.now();
                    
                    if (this.telegramBot && this.telegramBot.isReady()) {
                        await this.telegramBot.sendEmergencyStopAlert(this.consecutiveFailures);
                    }
                }
                
                return {
                    success: false,
                    error: result.error || 'Trade execution failed',
                    executionTime: result.executionTime || executionTime,
                    orderType: orderType,
                    quantity: tradeRequest.quantity,
                    port: port,
                    consecutiveFailures: this.consecutiveFailures,
                    timestamp: new Date().toISOString()
                };
            }
            
        } catch (error) {
            const executionTime = Date.now() - startTime;
            this.consecutiveFailures++;
            
            this.logger.error('Trade execution error', { 
                error: error.message,
                tradeRequest,
                executionTime
            });
            
            return {
                success: false,
                error: error.message,
                executionTime: executionTime,
                consecutiveFailures: this.consecutiveFailures,
                timestamp: new Date().toISOString()
            };
        }
    }

    mapOrderType(tradeRequest) {
        // Handle different input formats
        const orderType = tradeRequest.orderType || tradeRequest.action || tradeRequest.trade;
        
        if (!orderType) {
            throw new Error('No order type specified in trade request');
        }

        const normalizedOrderType = orderType.toLowerCase();
        
        // Map various formats to our standard order types
        switch (normalizedOrderType) {
            case 'buy':
            case 'long':
            case 'open long':
            case 'openlong':
                return 'Open Long';
                
            case 'sell':
            case 'short':
            case 'open short':
            case 'openshort':
                return 'Open Short';
                
            case 'close long':
            case 'closelong':
            case 'close_long':
                return 'Close Long';
                
            case 'close short':
            case 'closeshort':
            case 'close_short':
                return 'Close Short';
                
            default:
                throw new Error(`Unknown order type: ${orderType}`);
        }
    }

    selectPort(orderType) {
        // Port selection based on order type
        if (orderType.includes('Close')) {
            return 9223; // Close trades browser
        } else {
            return 9222; // Open trades browser
        }
    }

    async executeWithWorkingTrader(orderType, port, quantity = '0.3600') {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            // Use our working trader
            const traderPath = path.join(__dirname, 'working-trader.js');
            const args = [traderPath, orderType, port.toString()];
            
            this.logger.info('Spawning working trader', { 
                command: `node ${args.join(' ')}`,
                orderType,
                port,
                quantity
            });

            const child = spawn('node', args, {
                cwd: __dirname,
                stdio: ['pipe', 'pipe', 'pipe']
            });

            let stdout = '';
            let stderr = '';

            child.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            child.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            child.on('close', (code) => {
                const executionTime = Date.now() - startTime;
                
                const result = {
                    success: code === 0,
                    executionTime: executionTime,
                    exitCode: code,
                    stdout: stdout,
                    stderr: stderr,
                    verified: code === 0 // Assume success if exit code is 0
                };

                if (code === 0) {
                    this.logger.info('Working trader completed successfully', {
                        orderType,
                        port,
                        executionTime,
                        targetAchieved: executionTime < 2000
                    });
                } else {
                    this.logger.error('Working trader failed', {
                        orderType,
                        port,
                        executionTime,
                        exitCode: code,
                        stderr: stderr.substring(0, 500) // Limit error output
                    });
                    result.error = `Process exited with code ${code}`;
                }

                resolve(result);
            });

            child.on('error', (error) => {
                const executionTime = Date.now() - startTime;
                
                this.logger.error('Working trader process error', {
                    error: error.message,
                    orderType,
                    port,
                    executionTime
                });
                
                resolve({
                    success: false,
                    executionTime: executionTime,
                    error: error.message,
                    verified: false
                });
            });

            // Timeout after 10 seconds (generous for browser automation)
            setTimeout(() => {
                if (!child.killed) {
                    child.kill();
                    const executionTime = Date.now() - startTime;
                    
                    this.logger.warn('Working trader timeout', {
                        orderType,
                        port,
                        executionTime
                    });
                    
                    resolve({
                        success: false,
                        executionTime: executionTime,
                        error: 'Execution timeout (10s)',
                        verified: false
                    });
                }
            }, 10000);
        });
    }

    // Compatibility methods for existing webhook system
    async executeTradeWithRetry(tradeRequest, maxAttempts = 3) {
        let lastError = null;
        
        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                this.logger.info(`Trade attempt ${attempt}/${maxAttempts}`, { tradeRequest });
                
                const result = await this.executeTrade(tradeRequest);
                
                if (result.success) {
                    return result;
                } else {
                    lastError = result.error;
                    if (attempt < maxAttempts) {
                        this.logger.warn(`Attempt ${attempt} failed, retrying...`, { error: result.error });
                        await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay
                    }
                }
            } catch (error) {
                lastError = error.message;
                if (attempt < maxAttempts) {
                    this.logger.warn(`Attempt ${attempt} failed with exception, retrying...`, { error: error.message });
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }
        }
        
        // All attempts failed
        return {
            success: false,
            error: lastError || 'All retry attempts failed',
            retryLimitExceeded: true,
            maxAttempts: maxAttempts,
            timestamp: new Date().toISOString()
        };
    }

    async checkServiceHealth() {
        // Always return healthy since we're using direct browser automation
        this.ready = true;
        return { healthy: true, message: 'Browser automation ready' };
    }

    validateTradeRequest(tradeRequest) {
        if (!tradeRequest) {
            throw new Error('Trade request is required');
        }
        
        const orderType = tradeRequest.orderType || tradeRequest.action || tradeRequest.trade;
        if (!orderType) {
            throw new Error('Order type is required');
        }
        
        // Validate that we can map the order type
        try {
            this.mapOrderType(tradeRequest);
        } catch (error) {
            throw new Error(`Invalid order type: ${error.message}`);
        }
        
        return true;
    }

    getStats() {
        return {
            ready: this.ready,
            isExecutingTrade: this.isExecutingTrade,
            consecutiveFailures: this.consecutiveFailures,
            emergencyStopActive: this.emergencyStopActive,
            emergencyStopTime: this.emergencyStopTime
        };
    }
}

module.exports = WebhookTradingExecutor;
