#!/bin/bash

echo "🚀 Setting up MEXC Fast Futures Trading Bot"
echo "==========================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if Python is installed
if ! command -v python &> /dev/null && ! command -v python3 &> /dev/null; then
    echo "❌ Python is not installed. Please install Python first."
    exit 1
fi

echo "✅ Node.js and Python detected"

# Install Node.js dependencies
echo ""
echo "📦 Installing Node.js dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install Node.js dependencies"
    exit 1
fi

# Install Playwright browser
echo ""
echo "🎭 Installing Playwright browser..."
npx playwright install chromium

if [ $? -ne 0 ]; then
    echo "❌ Failed to install Playwright browser"
    exit 1
fi

# Install Python dependencies
echo ""
echo "🐍 Installing Python dependencies..."
pip install -r requirements-python.txt

if [ $? -ne 0 ]; then
    echo "❌ Failed to install Python dependencies"
    exit 1
fi

# Install Python Playwright
echo ""
echo "🎭 Installing Python Playwright..."
playwright install chromium

if [ $? -ne 0 ]; then
    echo "❌ Failed to install Python Playwright"
    exit 1
fi

# Check .env file
echo ""
echo "🔧 Checking configuration..."
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Creating template..."
    cat > .env << EOF
# MEXC Account Credentials
MEXC_EMAIL=<EMAIL>
MEXC_PASSWORD=your_password

# Server Configuration
PORT=3000
EOF
    echo "📝 Please edit .env file with your MEXC credentials"
else
    echo "✅ .env file found"
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Edit .env file with your MEXC credentials"
echo "2. Test the bot: npm run fast-bot"
echo "3. Start webhook server: node webhook-trading-server.js"
echo ""
echo "⚡ Target performance: Sub-2 second order execution"
echo "🚨 Warning: This bot trades with real money!"
echo ""
echo "📚 Read FAST_BOT_USAGE.md for detailed instructions"
