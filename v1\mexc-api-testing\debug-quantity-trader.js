const { chromium } = require('playwright');

class DebugQuantityTrader {
    constructor(port = 9222) {
        this.browser = null;
        this.page = null;
        this.port = port;
    }

    async connectToBrowser() {
        console.log(`🔗 Connecting to browser on port ${this.port}...`);
        
        try {
            this.browser = await chromium.connectOverCDP(`http://localhost:${this.port}`);
            const contexts = this.browser.contexts();
            
            if (contexts.length > 0) {
                const pages = contexts[0].pages();
                this.page = pages.length > 0 ? pages[0] : await contexts[0].newPage();
            } else {
                const context = await this.browser.newContext();
                this.page = await context.newPage();
            }
            
            console.log(`✅ Connected to browser on port ${this.port}`);
            return true;
        } catch (error) {
            console.error(`❌ Connection failed to port ${this.port}:`, error.message);
            return false;
        }
    }

    async debugQuantityFields() {
        console.log('🔍 DEBUGGING QUANTITY FIELDS...');
        console.log('================================');
        
        try {
            // Get all input elements
            const inputs = await this.page.locator('input').all();
            console.log(`Found ${inputs.length} input elements total`);
            
            for (let i = 0; i < inputs.length; i++) {
                try {
                    const input = inputs[i];
                    const isVisible = await input.isVisible();
                    
                    if (isVisible) {
                        const type = await input.getAttribute('type');
                        const placeholder = await input.getAttribute('placeholder');
                        const className = await input.getAttribute('class');
                        const value = await input.inputValue();
                        
                        console.log(`Input ${i + 1}:`);
                        console.log(`  Type: ${type || 'none'}`);
                        console.log(`  Placeholder: ${placeholder || 'none'}`);
                        console.log(`  Class: ${className || 'none'}`);
                        console.log(`  Value: "${value}"`);
                        console.log(`  Visible: ${isVisible}`);
                        console.log('');
                    }
                } catch (error) {
                    console.log(`Input ${i + 1}: Error - ${error.message}`);
                }
            }

            // Look for text containing "Quantity"
            console.log('🔍 Looking for "Quantity" text elements...');
            const quantityTexts = await this.page.locator('text=Quantity').all();
            console.log(`Found ${quantityTexts.length} "Quantity" text elements`);
            
            for (let i = 0; i < quantityTexts.length; i++) {
                try {
                    const element = quantityTexts[i];
                    const text = await element.textContent();
                    const isVisible = await element.isVisible();
                    console.log(`Quantity text ${i + 1}: "${text}" (visible: ${isVisible})`);
                } catch (error) {
                    console.log(`Quantity text ${i + 1}: Error - ${error.message}`);
                }
            }

            // Try specific selectors
            console.log('🔍 Testing specific selectors...');
            const testSelectors = [
                'input[type="number"]',
                'input[placeholder*="quantity"]',
                'input[placeholder*="amount"]',
                'input[placeholder*="size"]',
                'text=Quantity(USDT) >> xpath=following::input[1]',
                'text=Quantity >> xpath=following::input[1]',
                'text=Quantity(USDT) >> xpath=following::input[@type="number"][1]',
                'text=Quantity >> xpath=following::input[@type="number"][1]'
            ];

            for (const selector of testSelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    const isVisible = await element.isVisible({ timeout: 500 });
                    
                    if (isVisible) {
                        const type = await element.getAttribute('type');
                        const placeholder = await element.getAttribute('placeholder');
                        console.log(`✅ FOUND: ${selector}`);
                        console.log(`   Type: ${type}, Placeholder: ${placeholder}`);
                    } else {
                        console.log(`❌ NOT VISIBLE: ${selector}`);
                    }
                } catch (error) {
                    console.log(`❌ ERROR: ${selector} - ${error.message}`);
                }
            }

        } catch (error) {
            console.error('Debug failed:', error.message);
        }
    }

    async testQuantityFill() {
        console.log('🧪 TESTING QUANTITY FILL...');
        console.log('============================');
        
        const strategies = [
            {
                name: 'Strategy 1: Direct number input',
                selector: 'input[type="number"]'
            },
            {
                name: 'Strategy 2: Placeholder quantity',
                selector: 'input[placeholder*="quantity"]'
            },
            {
                name: 'Strategy 3: Placeholder amount',
                selector: 'input[placeholder*="amount"]'
            },
            {
                name: 'Strategy 4: XPath following Quantity(USDT)',
                selector: 'text=Quantity(USDT) >> xpath=following::input[1]'
            },
            {
                name: 'Strategy 5: XPath following Quantity',
                selector: 'text=Quantity >> xpath=following::input[1]'
            },
            {
                name: 'Strategy 6: XPath number input after Quantity(USDT)',
                selector: 'text=Quantity(USDT) >> xpath=following::input[@type="number"][1]'
            },
            {
                name: 'Strategy 7: XPath number input after Quantity',
                selector: 'text=Quantity >> xpath=following::input[@type="number"][1]'
            }
        ];

        for (const strategy of strategies) {
            try {
                console.log(`\n🧪 Testing: ${strategy.name}`);
                const element = this.page.locator(strategy.selector).first();
                const isVisible = await element.isVisible({ timeout: 1000 });
                
                if (isVisible) {
                    const type = await element.getAttribute('type');
                    const placeholder = await element.getAttribute('placeholder');
                    console.log(`   ✅ Element found - Type: ${type}, Placeholder: ${placeholder}`);
                    
                    // Try to fill it
                    try {
                        await element.click({ timeout: 1000 });
                        await element.fill('0.3600');
                        const value = await element.inputValue();
                        console.log(`   ✅ Successfully filled with: "${value}"`);
                        
                        // Clear it for next test
                        await element.fill('');
                        return strategy;
                    } catch (fillError) {
                        console.log(`   ❌ Fill failed: ${fillError.message}`);
                    }
                } else {
                    console.log(`   ❌ Element not visible`);
                }
            } catch (error) {
                console.log(`   ❌ Strategy failed: ${error.message}`);
            }
        }

        return null;
    }

    async executeDebugOrder(orderType) {
        console.log(`🎯 DEBUG EXECUTION: ${orderType.toUpperCase()}`);
        console.log('=====================================');
        
        try {
            // Ensure correct page
            const url = this.page.url();
            if (!url.includes('mexc.com/futures/TRU_USDT')) {
                console.log('🌐 Navigating to TRU_USDT...');
                await this.page.goto('https://www.mexc.com/futures/TRU_USDT');
                await this.page.waitForTimeout(3000);
            }

            // Set mode
            if (orderType.includes('Close')) {
                console.log('🔄 Setting Close mode...');
                try {
                    const closeBtn = this.page.locator('button:has-text("Close")').first();
                    if (await closeBtn.isVisible({ timeout: 1000 })) {
                        await closeBtn.click();
                        await this.page.waitForTimeout(1000);
                        console.log('✅ Switched to Close mode');
                    }
                } catch (error) {
                    console.log('⚠️ Close mode switch failed, continuing...');
                }
            } else {
                console.log('🔄 Setting Open mode...');
                try {
                    const openBtn = this.page.locator('button:has-text("Open")').first();
                    if (await openBtn.isVisible({ timeout: 1000 })) {
                        await openBtn.click();
                        await this.page.waitForTimeout(1000);
                        console.log('✅ Switched to Open mode');
                    }
                } catch (error) {
                    console.log('⚠️ Open mode switch failed, continuing...');
                }
            }

            // Debug quantity fields
            await this.debugQuantityFields();

            // Test quantity fill
            const workingStrategy = await this.testQuantityFill();
            
            if (workingStrategy) {
                console.log(`\n🎉 WORKING STRATEGY FOUND: ${workingStrategy.name}`);
                console.log(`   Selector: ${workingStrategy.selector}`);
                return {
                    success: true,
                    workingStrategy: workingStrategy,
                    orderType: orderType
                };
            } else {
                console.log('\n❌ NO WORKING STRATEGY FOUND');
                return {
                    success: false,
                    error: 'No quantity field strategy worked',
                    orderType: orderType
                };
            }

        } catch (error) {
            console.error(`❌ Debug execution failed: ${error.message}`);
            return {
                success: false,
                error: error.message,
                orderType: orderType
            };
        }
    }
}

async function executeDebug(orderType, port) {
    const trader = new DebugQuantityTrader(port);
    
    try {
        console.log('🔍 MEXC QUANTITY FIELD DEBUG');
        console.log('=============================');
        console.log(`📊 Order: ${orderType}`);
        console.log(`🌐 Port: ${port}`);
        console.log('🎯 Goal: Find working quantity field selector');
        console.log('');

        const connected = await trader.connectToBrowser();
        if (!connected) {
            throw new Error(`Failed to connect to port ${port}`);
        }

        const result = await trader.executeDebugOrder(orderType);

        if (result.success) {
            console.log('\n🎉 SUCCESS! Found working quantity field strategy');
            console.log(`Strategy: ${result.workingStrategy.name}`);
            console.log(`Selector: ${result.workingStrategy.selector}`);
        } else {
            console.log('\n❌ FAILED to find working quantity field strategy');
            if (result.error) console.log(`Error: ${result.error}`);
        }

        // Save debug results
        require('fs').writeFileSync(`debug-quantity-${orderType.replace(' ', '')}-${port}.json`, JSON.stringify(result, null, 2));
        
        process.exit(result.success ? 0 : 1);
        
    } catch (error) {
        console.error('💥 Debug failed:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    const orderType = process.argv[2] || 'Open Long';
    const port = parseInt(process.argv[3]) || 9222;
    
    executeDebug(orderType, port);
}

module.exports = DebugQuantityTrader;
