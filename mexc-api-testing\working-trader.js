const { chromium } = require('playwright');

class WorkingTrader {
    constructor(port = 9222) {
        this.browser = null;
        this.page = null;
        this.port = port;
    }

    async connectToBrowser() {
        console.log(`🔗 Connecting to browser on port ${this.port}...`);
        
        try {
            this.browser = await chromium.connectOverCDP(`http://localhost:${this.port}`);
            const contexts = this.browser.contexts();
            
            if (contexts.length > 0) {
                const pages = contexts[0].pages();
                this.page = pages.length > 0 ? pages[0] : await contexts[0].newPage();
            } else {
                const context = await this.browser.newContext();
                this.page = await context.newPage();
            }
            
            console.log(`✅ Connected to browser on port ${this.port}`);
            return true;
        } catch (error) {
            console.error(`❌ Connection failed to port ${this.port}:`, error.message);
            return false;
        }
    }

    async executeOrder(orderType) {
        const startTime = Date.now();
        console.log(`🎯 EXECUTING ${orderType.toUpperCase()} ORDER...`);
        
        try {
            // Ensure correct page
            const url = this.page.url();
            if (!url.includes('mexc.com/futures/TRU_USDT')) {
                console.log('🌐 Navigating to TRU_USDT...');
                await this.page.goto('https://www.mexc.com/futures/TRU_USDT');
                await this.page.waitForTimeout(2000);
            }

            // Set mode
            if (orderType.includes('Close')) {
                await this.setCloseMode();
            } else {
                await this.setOpenMode();
            }

            // Fill quantity - using the WORKING selector from debug
            await this.fillQuantity();

            // Click order button
            await this.clickOrderButton(orderType);

            // Handle popup for Open orders only
            if (orderType.includes('Open')) {
                await this.handlePopup(orderType);
            }

            const executionTime = Date.now() - startTime;
            const verified = await this.verifySuccess(orderType);

            console.log(`⚡ ${orderType} completed in ${executionTime}ms`);
            console.log(`🎉 Verified: ${verified ? '✅ YES' : '❌ NO'}`);

            return {
                success: verified,
                executionTime,
                verified,
                orderType,
                targetAchieved: executionTime < 2000
            };

        } catch (error) {
            const executionTime = Date.now() - startTime;
            console.error(`❌ ${orderType} failed: ${error.message}`);
            
            return { 
                success: false, 
                executionTime, 
                error: error.message, 
                orderType 
            };
        }
    }

    async setOpenMode() {
        console.log('🔄 Setting Open mode...');
        try {
            const openBtn = this.page.locator('button:has-text("Open")').first();
            if (await openBtn.isVisible({ timeout: 1000 })) {
                await openBtn.click();
                await this.page.waitForTimeout(300);
            }
        } catch (error) {
            console.log('⚠️ Already in Open mode');
        }
    }

    async setCloseMode() {
        console.log('🔄 Setting Close mode...');
        try {
            const closeBtn = this.page.locator('button:has-text("Close")').first();
            if (await closeBtn.isVisible({ timeout: 1000 })) {
                await closeBtn.click();
                await this.page.waitForTimeout(300);
            }
        } catch (error) {
            console.log('⚠️ Already in Close mode');
        }
    }

    async fillQuantity() {
        console.log('🔢 Filling quantity...');
        
        // Use the WORKING selector from our debug session
        const workingSelector = 'text=Quantity(USDT) >> xpath=following::input[1]';
        
        try {
            const input = this.page.locator(workingSelector).first();
            if (await input.isVisible({ timeout: 1000 })) {
                await input.click();
                await input.fill('0.3600');
                console.log('✅ Quantity filled using working selector');
                return;
            }
        } catch (error) {
            console.log('⚠️ Working selector failed, trying alternatives...');
        }

        // Fallback strategies
        const fallbackSelectors = [
            'text=Quantity >> xpath=following::input[1]',
            'input[type="text"]'
        ];

        for (const selector of fallbackSelectors) {
            try {
                const input = this.page.locator(selector).first();
                if (await input.isVisible({ timeout: 500 })) {
                    const type = await input.getAttribute('type');
                    if (type !== 'checkbox') {
                        await input.click();
                        await input.fill('0.3600');
                        console.log(`✅ Quantity filled using fallback: ${selector}`);
                        return;
                    }
                }
            } catch (error) {
                continue;
            }
        }

        throw new Error('Could not fill quantity field');
    }

    async clickOrderButton(orderType) {
        console.log(`📊 Clicking ${orderType} button...`);
        
        const buttonMap = {
            'Open Long': ['button:has-text("Open Long")', 'text=Open Long'],
            'Open Short': ['button:has-text("Open Short")', 'text=Open Short'],
            'Close Long': ['button:has-text("Close Long")', 'text=Close Long'],
            'Close Short': ['button:has-text("Close Short")', 'text=Close Short']
        };

        const selectors = buttonMap[orderType];
        if (!selectors) {
            throw new Error(`Unknown order type: ${orderType}`);
        }

        for (const selector of selectors) {
            try {
                const button = this.page.locator(selector).first();
                if (await button.isVisible({ timeout: 1000 })) {
                    await button.click();
                    console.log(`✅ ${orderType} button clicked`);
                    return;
                }
            } catch (error) {
                continue;
            }
        }

        throw new Error(`${orderType} button not found`);
    }

    async handlePopup(orderType) {
        console.log('✅ Handling popup...');
        
        for (let attempt = 1; attempt <= 15; attempt++) {
            try {
                const confirmBtn = this.page.locator('button:has-text("Confirm")').first();
                if (await confirmBtn.isVisible({ timeout: 100 })) {
                    await confirmBtn.click();
                    console.log(`✅ Confirm clicked (attempt ${attempt})`);
                    return true;
                }
            } catch (error) {
                // Continue trying
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        console.log('⚠️ No popup found');
        return false;
    }

    async verifySuccess(orderType) {
        try {
            const successSelectors = [
                'text=Purchased successfully',
                'text=Success',
                'text=success',
                '.success'
            ];

            for (const selector of successSelectors) {
                try {
                    if (await this.page.locator(selector).first().isVisible({ timeout: 500 })) {
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }
            return false;
        } catch (error) {
            return false;
        }
    }
}

async function executeCommand(orderType, port) {
    const trader = new WorkingTrader(port);
    
    try {
        console.log('🎯 MEXC WORKING TRADER');
        console.log('=======================');
        console.log(`📊 Order: ${orderType}`);
        console.log(`🌐 Port: ${port}`);
        console.log('⚡ Target: <2 seconds');
        console.log('');

        const connected = await trader.connectToBrowser();
        if (!connected) {
            throw new Error(`Failed to connect to port ${port}`);
        }

        const result = await trader.executeOrder(orderType);

        if (result.success) {
            if (result.targetAchieved) {
                console.log('\n🏆 TARGET ACHIEVED!');
                console.log(`⚡ ${result.executionTime}ms (<2 seconds)`);
            } else {
                console.log('\n✅ SUCCESS!');
                console.log(`⏱️ ${result.executionTime}ms`);
            }
        } else {
            console.log('\n❌ FAILED');
            if (result.error) console.log(`Error: ${result.error}`);
        }

        require('fs').writeFileSync(`working-${orderType.replace(' ', '')}-${port}.json`, JSON.stringify(result, null, 2));
        process.exit(result.success ? 0 : 1);
        
    } catch (error) {
        console.error('💥 Failed:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    const orderType = process.argv[2];
    const port = parseInt(process.argv[3]) || (orderType && orderType.includes('Close') ? 9223 : 9222);
    
    const validOrders = ['Open Long', 'Open Short', 'Close Long', 'Close Short'];
    
    if (!orderType || !validOrders.includes(orderType)) {
        console.log('🎯 MEXC WORKING TRADER');
        console.log('=======================');
        console.log('📋 USAGE:');
        console.log('node working-trader.js "Open Long"');
        console.log('node working-trader.js "Open Short"');
        console.log('node working-trader.js "Close Long"');
        console.log('node working-trader.js "Close Short"');
        console.log('');
        console.log('🌐 Auto Port Selection:');
        console.log('- Open orders → 9222');
        console.log('- Close orders → 9223');
        process.exit(1);
    }
    
    executeCommand(orderType, port);
}

module.exports = WorkingTrader;
