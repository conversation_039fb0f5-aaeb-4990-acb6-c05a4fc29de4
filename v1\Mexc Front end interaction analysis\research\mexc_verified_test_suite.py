#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MEXC Verified Test Suite
Real verification for all test interactions using proven techniques.

CRITICAL FIXES:
✅ Fixed CSS selector syntax errors
✅ Implemented real verification for all interactions
✅ Uses proven techniques from quantity field and button click solutions
✅ Visual confirmation that interactions actually occurred
✅ Only reports success when interactions genuinely work

VERIFICATION METHODS:
- Tab selection: Confirm active tab changes with before/after state comparison
- Field population: Confirm values persist using advanced persistence techniques
- Button clicks: Confirm UI responses (modals, notifications, loading states)
- Popup handling: Confirm popups are actually closed and removed from DOM
"""

import os
import sys
import json
import time
import logging
import argparse
from datetime import datetime
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from playwright.sync_api import sync_playwright

@dataclass
class TestResult:
    test_name: str
    success: bool
    duration: float
    details: Dict[str, Any] = field(default_factory=dict)
    errors: List[str] = field(default_factory=list)
    screenshots: List[str] = field(default_factory=list)
    verification_data: Dict[str, Any] = field(default_factory=dict)

@dataclass
class TradeConfig:
    symbol: str = "TRU_USDT"
    side: str = "BUY"
    quantity: float = 10.0

class MEXCVerifiedTestSuite:
    """Test suite with real verification using proven techniques"""
    
    def __init__(self, config: TradeConfig):
        self.config = config
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # Browser components
        self.playwright = None
        self.browser = None
        self.page = None
        
        # Test tracking
        self.screenshot_counter = 0
        self.test_results: List[TestResult] = []
        
        self.logger.info(f"🔍 Verified test suite initialized: {config}")
    
    def take_screenshot(self, name: str, description: str = "") -> str:
        """Take a screenshot for verification"""
        self.screenshot_counter += 1
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"verified_{self.screenshot_counter:03d}_{name}_{timestamp}.png"
        
        try:
            self.page.screenshot(path=filename, full_page=True)
            self.logger.info(f"📸 {filename} - {description}")
            return filename
        except Exception as e:
            self.logger.error(f"Screenshot failed: {e}")
            return ""
    
    def connect_to_browser(self) -> bool:
        """Connect to browser"""
        self.logger.info("🔌 Connecting to browser...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                self.logger.error("No browser contexts found")
                return False
            
            context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                self.logger.error("No MEXC page found")
                return False
            
            self.page = mexc_page
            self.logger.info(f"✅ Connected to MEXC page: {self.page.url}")
            
            # Take initial screenshot
            self.take_screenshot("initial_state", "Initial browser state")
            return True
            
        except Exception as e:
            self.logger.error(f"Browser connection failed: {e}")
            return False
    
    def test_verified_popup_management(self) -> TestResult:
        """Test 1: VERIFIED Popup Management with real closure confirmation"""
        self.logger.info("🔍 TEST 1: VERIFIED Popup Management")
        
        start_time = time.time()
        result = TestResult(
            test_name="verified_popup_management",
            success=False,
            duration=0
        )
        
        # Take before screenshot
        before_screenshot = self.take_screenshot("before_popup_mgmt", "Before popup management")
        result.screenshots.append(before_screenshot)
        
        popup_mgmt_script = """
        () => {
            console.log('🔍 VERIFIED popup management with real closure confirmation...');
            
            const results = {
                initial_popups: [],
                closure_attempts: [],
                final_popups: [],
                actually_closed: 0
            };
            
            // Step 1: Detect all visible popups BEFORE closure attempts
            function detectVisiblePopups() {
                const popupSelectors = [
                    '.ant-modal:not([style*="display: none"])',
                    '.modal:not([style*="display: none"])',
                    '[role="dialog"]:not([style*="display: none"])',
                    '.ant-notification',
                    '.ant-message'
                ];
                
                const visiblePopups = [];
                popupSelectors.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(element => {
                        const rect = element.getBoundingClientRect();
                        const style = window.getComputedStyle(element);
                        
                        if (rect.width > 0 && rect.height > 0 && 
                            style.display !== 'none' && 
                            style.visibility !== 'hidden') {
                            
                            visiblePopups.push({
                                element: element,
                                selector: selector,
                                text: element.textContent?.substring(0, 100) || '',
                                id: element.id || `popup_${visiblePopups.length}`,
                                position: {
                                    x: Math.round(rect.x),
                                    y: Math.round(rect.y),
                                    width: Math.round(rect.width),
                                    height: Math.round(rect.height)
                                }
                            });
                        }
                    });
                });
                
                return visiblePopups;
            }
            
            // Detect initial popups
            results.initial_popups = detectVisiblePopups().map(p => ({
                selector: p.selector,
                text: p.text,
                id: p.id,
                position: p.position
            }));
            
            console.log(`Found ${results.initial_popups.length} initial popups`);
            
            // Step 2: Attempt to close each popup with VERIFICATION
            const initialPopups = detectVisiblePopups();
            
            initialPopups.forEach((popup, index) => {
                const closureAttempt = {
                    popup_index: index,
                    popup_id: popup.id,
                    popup_text: popup.text,
                    close_methods_tried: [],
                    actually_closed: false
                };
                
                // Method 1: Close button/icon
                const closeSelectors = [
                    '.ant-modal-close',
                    '.close',
                    '[aria-label="close"]',
                    '[aria-label="Close"]',
                    '.ant-modal-close-x',
                    '.ant-modal-close-icon'
                ];
                
                closeSelectors.forEach(selector => {
                    const closeButtons = popup.element.querySelectorAll(selector);
                    closeButtons.forEach(btn => {
                        try {
                            btn.click();
                            closureAttempt.close_methods_tried.push({
                                method: 'close_button',
                                selector: selector,
                                success: true
                            });
                        } catch (error) {
                            closureAttempt.close_methods_tried.push({
                                method: 'close_button',
                                selector: selector,
                                success: false,
                                error: error.message
                            });
                        }
                    });
                });
                
                // Method 2: Cancel/Close text buttons
                const textButtons = popup.element.querySelectorAll('button');
                textButtons.forEach(btn => {
                    const text = btn.textContent?.toLowerCase() || '';
                    if (text.includes('cancel') || text.includes('close') || text === '×') {
                        try {
                            btn.click();
                            closureAttempt.close_methods_tried.push({
                                method: 'text_button',
                                text: text,
                                success: true
                            });
                        } catch (error) {
                            closureAttempt.close_methods_tried.push({
                                method: 'text_button',
                                text: text,
                                success: false,
                                error: error.message
                            });
                        }
                    }
                });
                
                // Method 3: ESC key
                try {
                    popup.element.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape', bubbles: true }));
                    closureAttempt.close_methods_tried.push({
                        method: 'escape_key',
                        success: true
                    });
                } catch (error) {
                    closureAttempt.close_methods_tried.push({
                        method: 'escape_key',
                        success: false,
                        error: error.message
                    });
                }
                
                results.closure_attempts.push(closureAttempt);
            });
            
            // Step 3: Wait for closures to take effect
            setTimeout(() => {}, 1000);
            
            // Step 4: VERIFY actual closure by re-detecting popups
            results.final_popups = detectVisiblePopups().map(p => ({
                selector: p.selector,
                text: p.text,
                id: p.id,
                position: p.position
            }));
            
            // Step 5: Calculate actually closed popups
            results.actually_closed = results.initial_popups.length - results.final_popups.length;
            
            // Mark closure attempts as successful if popup actually disappeared
            results.closure_attempts.forEach(attempt => {
                const stillExists = results.final_popups.some(fp => 
                    fp.id === attempt.popup_id || fp.text === attempt.popup_text
                );
                attempt.actually_closed = !stillExists;
            });
            
            console.log(`Actually closed ${results.actually_closed} popups`);
            
            return {
                success: true,
                results: results,
                summary: {
                    initial_popups: results.initial_popups.length,
                    final_popups: results.final_popups.length,
                    actually_closed: results.actually_closed,
                    closure_success_rate: results.initial_popups.length > 0 ? 
                        results.actually_closed / results.initial_popups.length : 1
                }
            };
        }
        """
        
        try:
            popup_result = self.page.evaluate(popup_mgmt_script)
            
            # Wait for popup actions to complete
            time.sleep(2)
            
            # Take after screenshot
            after_screenshot = self.take_screenshot("after_popup_mgmt", "After popup management")
            result.screenshots.append(after_screenshot)
            
            if popup_result.get('success'):
                results_data = popup_result.get('results', {})
                summary = popup_result.get('summary', {})
                
                # REAL SUCCESS CRITERIA: Actually closed popups
                actually_closed = summary.get('actually_closed', 0)
                initial_popups = summary.get('initial_popups', 0)
                
                result.success = actually_closed > 0 or initial_popups == 0  # Success if closed popups OR no popups to close
                result.details = {
                    'results': results_data,
                    'summary': summary
                }
                result.verification_data = {
                    'initial_popups': initial_popups,
                    'final_popups': summary.get('final_popups', 0),
                    'actually_closed': actually_closed,
                    'closure_success_rate': summary.get('closure_success_rate', 0)
                }
                
                self.logger.info(f"🔍 VERIFIED popup management completed:")
                self.logger.info(f"   Initial popups: {initial_popups}")
                self.logger.info(f"   Final popups: {summary.get('final_popups', 0)}")
                self.logger.info(f"   Actually closed: {actually_closed}")
                self.logger.info(f"   Success rate: {summary.get('closure_success_rate', 0)*100:.1f}%")
                
                if result.success:
                    if actually_closed > 0:
                        self.logger.info(f"✅ REAL SUCCESS: {actually_closed} popups actually closed")
                    else:
                        self.logger.info("✅ SUCCESS: No popups needed closing")
                else:
                    self.logger.error("❌ REAL FAILURE: No popups were actually closed")
                
            else:
                error = popup_result.get('error', 'Unknown error')
                result.errors.append(error)
                self.logger.error(f"❌ Popup management test failed: {error}")
                
        except Exception as e:
            result.errors.append(str(e))
            self.logger.error(f"❌ Popup management test exception: {e}")
        
        result.duration = time.time() - start_time
        return result

    def test_verified_field_population(self) -> TestResult:
        """Test 2: VERIFIED Field Population with persistence confirmation"""
        self.logger.info("🔍 TEST 2: VERIFIED Field Population")

        start_time = time.time()
        result = TestResult(
            test_name="verified_field_population",
            success=False,
            duration=0
        )

        # Take before screenshot
        before_screenshot = self.take_screenshot("before_field_pop", "Before field population")
        result.screenshots.append(before_screenshot)

        field_pop_script = f"""
        () => {{
            console.log('🔍 VERIFIED field population with persistence confirmation...');

            const results = {{
                fields_found: [],
                population_attempts: [],
                persistence_verification: []
            }};

            // Step 1: Find all visible input fields
            const allInputs = document.querySelectorAll('input');
            const visibleInputs = [];

            allInputs.forEach((input, index) => {{
                const rect = input.getBoundingClientRect();
                const style = window.getComputedStyle(input);

                if (rect.width > 0 && rect.height > 0 &&
                    style.display !== 'none' &&
                    style.visibility !== 'hidden' &&
                    !input.disabled) {{

                    visibleInputs.push({{
                        element: input,
                        index: index,
                        type: input.type || 'text',
                        placeholder: input.placeholder || '',
                        name: input.name || '',
                        value: input.value || '',
                        position: {{
                            x: Math.round(rect.x),
                            y: Math.round(rect.y)
                        }}
                    }});
                }}
            }});

            results.fields_found = visibleInputs.map(f => ({{
                index: f.index,
                type: f.type,
                placeholder: f.placeholder,
                name: f.name,
                initial_value: f.value,
                position: f.position
            }}));

            console.log(`Found ${{visibleInputs.length}} visible input fields`);

            // Step 2: Test field population with ADVANCED PERSISTENCE (using our proven technique)
            const testValue = '{self.config.quantity}';

            visibleInputs.slice(0, 5).forEach((fieldInfo, testIndex) => {{
                const input = fieldInfo.element;

                const populationAttempt = {{
                    field_index: fieldInfo.index,
                    test_value: testValue,
                    original_value: input.value,
                    population_methods: [],
                    immediate_success: false,
                    persistent_success: false,
                    final_value: ''
                }};

                try {{
                    console.log(`Testing field ${{testIndex}}: ${{fieldInfo.placeholder || fieldInfo.name || 'unnamed'}}`);

                    // ADVANCED PERSISTENCE METHOD (from our successful quantity field solution)

                    // Method 1: Focus and clear
                    input.focus();
                    input.value = '';

                    // Method 2: Set value with comprehensive events
                    input.value = testValue;

                    // Method 3: Trigger all necessary events
                    const events = [
                        new Event('focus', {{ bubbles: true }}),
                        new Event('input', {{ bubbles: true }}),
                        new Event('change', {{ bubbles: true }}),
                        new KeyboardEvent('keyup', {{ bubbles: true, key: 'Enter' }}),
                        new Event('blur', {{ bubbles: true }})
                    ];

                    events.forEach(event => {{
                        input.dispatchEvent(event);
                    }});

                    populationAttempt.population_methods.push({{
                        method: 'advanced_persistence',
                        success: true
                    }});

                    // Check immediate success
                    populationAttempt.immediate_success = input.value === testValue;

                    console.log(`Immediate result: "${{input.value}}" (expected: "${{testValue}}")`);

                }} catch (error) {{
                    populationAttempt.population_methods.push({{
                        method: 'advanced_persistence',
                        success: false,
                        error: error.message
                    }});
                }}

                results.population_attempts.push(populationAttempt);
            }});

            // Step 3: CRITICAL - Wait and verify persistence (like our successful quantity field test)
            console.log('Waiting 3 seconds to verify persistence...');

            return new Promise((resolve) => {{
                setTimeout(() => {{
                    console.log('Verifying field persistence...');

                    // Re-check all fields for persistence
                    results.population_attempts.forEach(attempt => {{
                        const input = allInputs[attempt.field_index];
                        if (input) {{
                            const currentValue = input.value;
                            attempt.final_value = currentValue;
                            attempt.persistent_success = currentValue === testValue;

                            console.log(`Field ${{attempt.field_index}}: "${{currentValue}}" (persistent: ${{attempt.persistent_success}})`);

                            results.persistence_verification.push({{
                                field_index: attempt.field_index,
                                test_value: testValue,
                                final_value: currentValue,
                                persistent: attempt.persistent_success
                            }});
                        }}
                    }});

                    const summary = {{
                        total_fields: results.fields_found.length,
                        tested_fields: results.population_attempts.length,
                        immediate_successes: results.population_attempts.filter(a => a.immediate_success).length,
                        persistent_successes: results.population_attempts.filter(a => a.persistent_success).length,
                        persistence_rate: results.population_attempts.length > 0 ?
                            results.population_attempts.filter(a => a.persistent_success).length / results.population_attempts.length : 0
                    }};

                    resolve({{
                        success: true,
                        results: results,
                        summary: summary
                    }});
                }}, 3000); // Wait 3 seconds like our successful tests
            }});
        }}
        """

        try:
            field_result = self.page.evaluate(field_pop_script)

            # Take after screenshot
            after_screenshot = self.take_screenshot("after_field_pop", "After field population")
            result.screenshots.append(after_screenshot)

            if field_result.get('success'):
                results_data = field_result.get('results', {})
                summary = field_result.get('summary', {})

                # REAL SUCCESS CRITERIA: Fields actually populated AND persistent
                persistent_successes = summary.get('persistent_successes', 0)
                tested_fields = summary.get('tested_fields', 0)

                result.success = persistent_successes > 0  # Only success if values actually persist
                result.details = {
                    'results': results_data,
                    'summary': summary
                }
                result.verification_data = {
                    'total_fields': summary.get('total_fields', 0),
                    'tested_fields': tested_fields,
                    'immediate_successes': summary.get('immediate_successes', 0),
                    'persistent_successes': persistent_successes,
                    'persistence_rate': summary.get('persistence_rate', 0)
                }

                self.logger.info(f"🔍 VERIFIED field population completed:")
                self.logger.info(f"   Total fields found: {summary.get('total_fields', 0)}")
                self.logger.info(f"   Fields tested: {tested_fields}")
                self.logger.info(f"   Immediate successes: {summary.get('immediate_successes', 0)}")
                self.logger.info(f"   Persistent successes: {persistent_successes}")
                self.logger.info(f"   Persistence rate: {summary.get('persistence_rate', 0)*100:.1f}%")

                if result.success:
                    self.logger.info(f"✅ REAL SUCCESS: {persistent_successes} fields actually populated and persistent")
                else:
                    self.logger.error("❌ REAL FAILURE: No fields maintained their values after population")

                # Log individual field results
                for verification in results_data.get('persistence_verification', []):
                    field_idx = verification.get('field_index', 0)
                    final_value = verification.get('final_value', '')
                    persistent = verification.get('persistent', False)
                    status = "✅" if persistent else "❌"
                    self.logger.info(f"   {status} Field {field_idx}: '{final_value}' (persistent: {persistent})")

            else:
                error = field_result.get('error', 'Unknown error')
                result.errors.append(error)
                self.logger.error(f"❌ Field population test failed: {error}")

        except Exception as e:
            result.errors.append(str(e))
            self.logger.error(f"❌ Field population test exception: {e}")

        result.duration = time.time() - start_time
        return result

    def test_verified_tab_navigation(self) -> TestResult:
        """Test 3: VERIFIED Tab Navigation with visual state confirmation"""
        self.logger.info("🔍 TEST 3: VERIFIED Tab Navigation")

        start_time = time.time()
        result = TestResult(
            test_name="verified_tab_navigation",
            success=False,
            duration=0
        )

        # Take before screenshot
        before_screenshot = self.take_screenshot("before_tab_nav", "Before tab navigation")
        result.screenshots.append(before_screenshot)

        tab_nav_script = """
        () => {
            console.log('🔍 VERIFIED tab navigation with visual state confirmation...');

            const results = {
                tabs_found: [],
                initial_active_tab: null,
                navigation_attempts: [],
                final_active_tab: null,
                actual_changes: 0
            };

            // Step 1: Find all tab elements
            const tabSelectors = [
                '.ant-tabs-tab',
                '[role="tab"]',
                '.tab',
                'button[data-testid*="tab"]',
                'div[class*="tab"]'
            ];

            let allTabs = [];
            for (const selector of tabSelectors) {
                const elements = document.querySelectorAll(selector);
                if (elements.length > 0) {
                    allTabs = Array.from(elements);
                    break;
                }
            }

            if (allTabs.length === 0) {
                return { success: false, error: 'No tabs found', results: results };
            }

            // Step 2: Analyze initial state
            allTabs.forEach((tab, index) => {
                const text = tab.textContent?.trim() || '';
                const isActive = tab.classList.contains('ant-tabs-tab-active') ||
                                tab.classList.contains('active') ||
                                tab.getAttribute('aria-selected') === 'true';

                const tabInfo = {
                    index: index,
                    text: text,
                    isActive: isActive,
                    className: tab.className,
                    position: {
                        x: Math.round(tab.getBoundingClientRect().x),
                        y: Math.round(tab.getBoundingClientRect().y)
                    }
                };

                results.tabs_found.push(tabInfo);

                if (isActive) {
                    results.initial_active_tab = tabInfo;
                }
            });

            console.log(`Found ${allTabs.length} tabs, initial active: ${results.initial_active_tab?.text || 'none'}`);

            // Step 3: Test tab navigation with REAL VERIFICATION
            const tabsToTest = allTabs.slice(0, 3); // Test first 3 tabs

            tabsToTest.forEach((tab, testIndex) => {
                const tabInfo = results.tabs_found[testIndex];

                const navigationAttempt = {
                    tab_index: testIndex,
                    tab_text: tabInfo.text,
                    was_initially_active: tabInfo.isActive,
                    click_attempted: false,
                    became_active: false,
                    ui_changed: false
                };

                try {
                    console.log(`Testing tab ${testIndex}: "${tabInfo.text}"`);

                    // Record initial UI state
                    const initialContent = document.body.innerHTML.length;

                    // Click the tab
                    tab.focus();
                    tab.click();
                    navigationAttempt.click_attempted = true;

                    // Wait for UI changes
                    setTimeout(() => {}, 500);

                    // Check if tab became active
                    const isNowActive = tab.classList.contains('ant-tabs-tab-active') ||
                                       tab.classList.contains('active') ||
                                       tab.getAttribute('aria-selected') === 'true';

                    navigationAttempt.became_active = isNowActive;

                    // Check for UI content changes
                    const finalContent = document.body.innerHTML.length;
                    navigationAttempt.ui_changed = Math.abs(finalContent - initialContent) > 100; // Significant change

                    console.log(`Tab ${testIndex} result: active=${isNowActive}, ui_changed=${navigationAttempt.ui_changed}`);

                } catch (error) {
                    navigationAttempt.error = error.message;
                }

                results.navigation_attempts.push(navigationAttempt);
            });

            // Step 4: Determine final active tab
            allTabs.forEach((tab, index) => {
                const isActive = tab.classList.contains('ant-tabs-tab-active') ||
                                tab.classList.contains('active') ||
                                tab.getAttribute('aria-selected') === 'true';

                if (isActive) {
                    results.final_active_tab = {
                        index: index,
                        text: tab.textContent?.trim() || '',
                        isActive: true
                    };
                }
            });

            // Step 5: Calculate actual changes
            const initialActiveIndex = results.initial_active_tab?.index;
            const finalActiveIndex = results.final_active_tab?.index;

            if (initialActiveIndex !== finalActiveIndex) {
                results.actual_changes = 1;
            }

            // Count successful navigations (became active OR caused UI changes)
            const successfulNavigations = results.navigation_attempts.filter(
                attempt => attempt.became_active || attempt.ui_changed
            ).length;

            console.log(`Actual tab changes: ${results.actual_changes}, Successful navigations: ${successfulNavigations}`);

            return {
                success: true,
                results: results,
                summary: {
                    total_tabs: allTabs.length,
                    tabs_tested: results.navigation_attempts.length,
                    successful_navigations: successfulNavigations,
                    actual_changes: results.actual_changes,
                    initial_active: results.initial_active_tab?.text || 'none',
                    final_active: results.final_active_tab?.text || 'none'
                }
            };
        }
        """

        try:
            tab_result = self.page.evaluate(tab_nav_script)

            # Take after screenshot
            after_screenshot = self.take_screenshot("after_tab_nav", "After tab navigation")
            result.screenshots.append(after_screenshot)

            if tab_result.get('success'):
                results_data = tab_result.get('results', {})
                summary = tab_result.get('summary', {})

                # REAL SUCCESS CRITERIA: Actual tab changes OR successful navigations
                actual_changes = summary.get('actual_changes', 0)
                successful_navigations = summary.get('successful_navigations', 0)

                result.success = actual_changes > 0 or successful_navigations > 0
                result.details = {
                    'results': results_data,
                    'summary': summary
                }
                result.verification_data = {
                    'total_tabs': summary.get('total_tabs', 0),
                    'tabs_tested': summary.get('tabs_tested', 0),
                    'successful_navigations': successful_navigations,
                    'actual_changes': actual_changes,
                    'initial_active': summary.get('initial_active', 'none'),
                    'final_active': summary.get('final_active', 'none')
                }

                self.logger.info(f"🔍 VERIFIED tab navigation completed:")
                self.logger.info(f"   Total tabs: {summary.get('total_tabs', 0)}")
                self.logger.info(f"   Tabs tested: {summary.get('tabs_tested', 0)}")
                self.logger.info(f"   Successful navigations: {successful_navigations}")
                self.logger.info(f"   Actual changes: {actual_changes}")
                self.logger.info(f"   Initial active: {summary.get('initial_active', 'none')}")
                self.logger.info(f"   Final active: {summary.get('final_active', 'none')}")

                if result.success:
                    if actual_changes > 0:
                        self.logger.info(f"✅ REAL SUCCESS: Active tab actually changed")
                    else:
                        self.logger.info(f"✅ REAL SUCCESS: {successful_navigations} tabs responded to clicks")
                else:
                    self.logger.error("❌ REAL FAILURE: No tabs actually changed or responded")

            else:
                error = tab_result.get('error', 'Unknown error')
                result.errors.append(error)
                self.logger.error(f"❌ Tab navigation test failed: {error}")

        except Exception as e:
            result.errors.append(str(e))
            self.logger.error(f"❌ Tab navigation test exception: {e}")

        result.duration = time.time() - start_time
        return result

    def test_verified_button_interaction(self) -> TestResult:
        """Test 4: VERIFIED Button Interaction with UI response confirmation"""
        self.logger.info("🔍 TEST 4: VERIFIED Button Interaction")

        start_time = time.time()
        result = TestResult(
            test_name="verified_button_interaction",
            success=False,
            duration=0
        )

        # Take before screenshot
        before_screenshot = self.take_screenshot("before_button_int", "Before button interaction")
        result.screenshots.append(before_screenshot)

        # Use our proven button classes
        if self.config.side == "BUY":
            button_class = "component_longBtn__eazYU"
            expected_text = "Open Long"
        else:
            button_class = "component_shortBtn__x5P3I"
            expected_text = "Open Short"

        button_int_script = f"""
        () => {{
            console.log('🔍 VERIFIED button interaction with UI response confirmation...');

            const results = {{
                button_found: false,
                button_info: null,
                initial_ui_state: null,
                click_attempts: [],
                ui_responses: [],
                final_ui_state: null,
                actual_response: false
            }};

            // Step 1: Find the target button
            const button = document.querySelector('button.{button_class}');

            if (!button) {{
                return {{ success: false, error: 'Target button not found', results: results }};
            }}

            results.button_found = true;
            results.button_info = {{
                text: button.textContent || '',
                className: button.className,
                position: {{
                    x: Math.round(button.getBoundingClientRect().x),
                    y: Math.round(button.getBoundingClientRect().y)
                }},
                enabled: !button.disabled
            }};

            console.log(`Found button: "${{results.button_info.text}}" at (${{results.button_info.position.x}}, ${{results.button_info.position.y}})`);

            // Step 2: Record initial UI state
            results.initial_ui_state = {{
                modals: document.querySelectorAll('.ant-modal:not([style*="display: none"])').length,
                notifications: document.querySelectorAll('.ant-notification, .ant-message').length,
                loading: document.querySelectorAll('.ant-spin, .loading').length,
                body_content_length: document.body.innerHTML.length
            }};

            console.log('Initial UI state:', results.initial_ui_state);

            // Step 3: Execute button click using our PROVEN METHOD
            const clickAttempt = {{
                method: 'proven_simple_click',
                attempted: false,
                success: false,
                steps: []
            }};

            try {{
                console.log('Executing proven button click method...');

                // Step 3a: Focus (proven to work)
                button.focus();
                clickAttempt.steps.push({{ step: 'focus', success: true }});

                // Step 3b: Simple click event (proven to work)
                const clickEvent = new Event('click', {{
                    bubbles: true,
                    cancelable: true
                }});

                const dispatched = button.dispatchEvent(clickEvent);
                clickAttempt.steps.push({{ step: 'click_event', success: dispatched }});

                // Step 3c: Native click (proven to work)
                button.click();
                clickAttempt.steps.push({{ step: 'native_click', success: true }});

                clickAttempt.attempted = true;
                clickAttempt.success = true;

                console.log('Button click methods executed');

            }} catch (error) {{
                clickAttempt.error = error.message;
                clickAttempt.steps.push({{ step: 'error', success: false, error: error.message }});
            }}

            results.click_attempts.push(clickAttempt);

            // Step 4: Wait for UI responses (like our successful tests)
            console.log('Waiting for UI responses...');

            return new Promise((resolve) => {{
                setTimeout(() => {{
                    console.log('Checking for UI responses...');

                    // Step 5: Record final UI state
                    results.final_ui_state = {{
                        modals: document.querySelectorAll('.ant-modal:not([style*="display: none"])').length,
                        notifications: document.querySelectorAll('.ant-notification, .ant-message').length,
                        loading: document.querySelectorAll('.ant-spin, .loading').length,
                        body_content_length: document.body.innerHTML.length
                    }};

                    console.log('Final UI state:', results.final_ui_state);

                    // Step 6: Detect UI responses
                    const modalChange = results.final_ui_state.modals - results.initial_ui_state.modals;
                    const notificationChange = results.final_ui_state.notifications - results.initial_ui_state.notifications;
                    const loadingChange = results.final_ui_state.loading - results.initial_ui_state.loading;
                    const contentChange = Math.abs(results.final_ui_state.body_content_length - results.initial_ui_state.body_content_length);

                    if (modalChange > 0) {{
                        results.ui_responses.push({{ type: 'modal', change: modalChange }});
                    }}
                    if (notificationChange > 0) {{
                        results.ui_responses.push({{ type: 'notification', change: notificationChange }});
                    }}
                    if (loadingChange > 0) {{
                        results.ui_responses.push({{ type: 'loading', change: loadingChange }});
                    }}
                    if (contentChange > 1000) {{ // Significant content change
                        results.ui_responses.push({{ type: 'content', change: contentChange }});
                    }}

                    results.actual_response = results.ui_responses.length > 0;

                    console.log(`UI responses detected: ${{results.ui_responses.length}}`);
                    results.ui_responses.forEach(response => {{
                        console.log(`- ${{response.type}}: ${{response.change}}`);
                    }});

                    resolve({{
                        success: true,
                        results: results,
                        summary: {{
                            button_found: results.button_found,
                            click_attempted: clickAttempt.attempted,
                            click_successful: clickAttempt.success,
                            ui_responses: results.ui_responses.length,
                            actual_response: results.actual_response,
                            response_types: results.ui_responses.map(r => r.type)
                        }}
                    }});
                }}, 3000); // Wait 3 seconds like our successful tests
            }});
        }}
        """

        try:
            button_result = self.page.evaluate(button_int_script)

            # Take after screenshot
            after_screenshot = self.take_screenshot("after_button_int", "After button interaction")
            result.screenshots.append(after_screenshot)

            if button_result.get('success'):
                results_data = button_result.get('results', {})
                summary = button_result.get('summary', {})

                # REAL SUCCESS CRITERIA: Button found AND clicked AND UI responded
                button_found = summary.get('button_found', False)
                click_successful = summary.get('click_successful', False)
                actual_response = summary.get('actual_response', False)

                result.success = button_found and click_successful and actual_response
                result.details = {
                    'results': results_data,
                    'summary': summary
                }
                result.verification_data = {
                    'button_found': button_found,
                    'click_attempted': summary.get('click_attempted', False),
                    'click_successful': click_successful,
                    'ui_responses': summary.get('ui_responses', 0),
                    'actual_response': actual_response,
                    'response_types': summary.get('response_types', [])
                }

                self.logger.info(f"🔍 VERIFIED button interaction completed:")
                self.logger.info(f"   Button found: {button_found}")
                self.logger.info(f"   Click attempted: {summary.get('click_attempted', False)}")
                self.logger.info(f"   Click successful: {click_successful}")
                self.logger.info(f"   UI responses: {summary.get('ui_responses', 0)}")
                self.logger.info(f"   Actual response: {actual_response}")
                self.logger.info(f"   Response types: {summary.get('response_types', [])}")

                if result.success:
                    response_types = summary.get('response_types', [])
                    self.logger.info(f"✅ REAL SUCCESS: Button clicked and triggered UI responses: {response_types}")
                else:
                    if not button_found:
                        self.logger.error("❌ REAL FAILURE: Button not found")
                    elif not click_successful:
                        self.logger.error("❌ REAL FAILURE: Button click failed")
                    else:
                        self.logger.error("❌ REAL FAILURE: Button clicked but no UI response detected")

            else:
                error = button_result.get('error', 'Unknown error')
                result.errors.append(error)
                self.logger.error(f"❌ Button interaction test failed: {error}")

        except Exception as e:
            result.errors.append(str(e))
            self.logger.error(f"❌ Button interaction test exception: {e}")

        result.duration = time.time() - start_time
        return result

    def run_verified_tests(self) -> Dict[str, Any]:
        """Run all verified tests with real confirmation"""
        self.logger.info("🔍 Starting MEXC Verified Test Suite")

        if not self.connect_to_browser():
            return {"error": "Browser connection failed"}

        # Run all verified tests
        tests = [
            self.test_verified_popup_management,
            self.test_verified_field_population,
            self.test_verified_tab_navigation,
            self.test_verified_button_interaction
        ]

        for test_func in tests:
            try:
                test_result = test_func()
                self.test_results.append(test_result)
            except Exception as e:
                self.logger.error(f"Test {test_func.__name__} failed with exception: {e}")
                failed_result = TestResult(
                    test_name=test_func.__name__,
                    success=False,
                    duration=0,
                    errors=[str(e)]
                )
                self.test_results.append(failed_result)

        # Generate verified report
        return self.generate_verified_report()

    def generate_verified_report(self) -> Dict[str, Any]:
        """Generate verified test report with real success metrics"""
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result.success)
        total_duration = sum(result.duration for result in self.test_results)

        # Calculate real success metrics
        real_interactions = 0
        total_interactions = 0

        for result in self.test_results:
            verification = result.verification_data
            if result.test_name == "verified_popup_management":
                total_interactions += verification.get('initial_popups', 0)
                real_interactions += verification.get('actually_closed', 0)
            elif result.test_name == "verified_field_population":
                total_interactions += verification.get('tested_fields', 0)
                real_interactions += verification.get('persistent_successes', 0)
            elif result.test_name == "verified_tab_navigation":
                total_interactions += verification.get('tabs_tested', 0)
                real_interactions += verification.get('successful_navigations', 0)
            elif result.test_name == "verified_button_interaction":
                total_interactions += 1 if verification.get('button_found') else 0
                real_interactions += 1 if verification.get('actual_response') else 0

        report = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "failed_tests": total_tests - successful_tests,
                "test_success_rate": successful_tests / total_tests if total_tests > 0 else 0,
                "total_interactions": total_interactions,
                "real_interactions": real_interactions,
                "real_interaction_rate": real_interactions / total_interactions if total_interactions > 0 else 0,
                "total_duration": total_duration,
                "total_screenshots": self.screenshot_counter
            },
            "test_results": [
                {
                    "test_name": result.test_name,
                    "success": result.success,
                    "duration": result.duration,
                    "errors": result.errors,
                    "screenshots": result.screenshots,
                    "details": result.details,
                    "verification_data": result.verification_data
                }
                for result in self.test_results
            ],
            "real_verification_summary": self.generate_real_verification_summary()
        }

        # Save report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"mexc_verified_test_report_{timestamp}.json"

        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)

            self.logger.info(f"📊 Verified test report saved: {report_file}")
        except Exception as e:
            self.logger.error(f"Failed to save verified test report: {e}")

        return report

    def generate_real_verification_summary(self) -> Dict[str, Any]:
        """Generate summary of what actually worked vs what was claimed"""
        summary = {
            "popup_management": {"claimed": False, "verified": False, "details": ""},
            "field_population": {"claimed": False, "verified": False, "details": ""},
            "tab_navigation": {"claimed": False, "verified": False, "details": ""},
            "button_interaction": {"claimed": False, "verified": False, "details": ""}
        }

        for result in self.test_results:
            verification = result.verification_data

            if result.test_name == "verified_popup_management":
                summary["popup_management"]["claimed"] = result.success
                actually_closed = verification.get('actually_closed', 0)
                summary["popup_management"]["verified"] = actually_closed > 0
                summary["popup_management"]["details"] = f"{actually_closed} popups actually closed"

            elif result.test_name == "verified_field_population":
                summary["field_population"]["claimed"] = result.success
                persistent = verification.get('persistent_successes', 0)
                summary["field_population"]["verified"] = persistent > 0
                summary["field_population"]["details"] = f"{persistent} fields actually persistent"

            elif result.test_name == "verified_tab_navigation":
                summary["tab_navigation"]["claimed"] = result.success
                changes = verification.get('actual_changes', 0)
                navigations = verification.get('successful_navigations', 0)
                summary["tab_navigation"]["verified"] = changes > 0 or navigations > 0
                summary["tab_navigation"]["details"] = f"{changes} tab changes, {navigations} successful navigations"

            elif result.test_name == "verified_button_interaction":
                summary["button_interaction"]["claimed"] = result.success
                response = verification.get('actual_response', False)
                summary["button_interaction"]["verified"] = response
                response_types = verification.get('response_types', [])
                summary["button_interaction"]["details"] = f"UI responses: {response_types}"

        return summary

    def cleanup(self):
        """Clean up resources"""
        try:
            if self.playwright:
                self.playwright.stop()
        except Exception as e:
            self.logger.error(f"Cleanup error: {e}")

def main():
    """Main entry point for verified test suite"""
    parser = argparse.ArgumentParser(description="MEXC Verified Test Suite")

    parser.add_argument("--symbol", default="TRU_USDT", help="Trading symbol")
    parser.add_argument("--side", choices=["BUY", "SELL"], default="BUY", help="Order side")
    parser.add_argument("--quantity", type=float, default=5.0, help="Order quantity")

    args = parser.parse_args()

    config = TradeConfig(
        symbol=args.symbol,
        side=args.side,
        quantity=args.quantity
    )

    print(f"""
🔍 MEXC Verified Test Suite
===========================

REAL VERIFICATION APPROACH:
✅ Fixed CSS selector syntax errors
✅ Popup management: Confirms popups actually closed
✅ Field population: Confirms values persist after entry
✅ Tab navigation: Confirms active tab actually changes
✅ Button interaction: Confirms UI responses to clicks

NO MORE FALSE POSITIVES:
❌ Element detection ≠ Working interaction
✅ Real verification = Confirmed working interaction

Test Configuration:
  Symbol: {config.symbol}
  Side: {config.side}
  Quantity: {config.quantity}
    """)

    print("Starting verified test suite with real confirmation...")

    # Initialize verified test suite
    test_suite = MEXCVerifiedTestSuite(config)

    try:
        report = test_suite.run_verified_tests()

        # Display results
        if "error" in report:
            print(f"❌ Verified test suite failed: {report['error']}")
        else:
            summary = report["summary"]
            verification_summary = report["real_verification_summary"]

            print(f"""
📊 VERIFIED Test Results:
========================
Test Success Rate: {summary['test_success_rate']*100:.1f}% ({summary['successful_tests']}/{summary['total_tests']})
REAL Interaction Rate: {summary['real_interaction_rate']*100:.1f}% ({summary['real_interactions']}/{summary['total_interactions']})
Total Duration: {summary['total_duration']:.2f}s
Screenshots: {summary['total_screenshots']}

🔍 REAL VERIFICATION BREAKDOWN:
            """)

            for test_name, verification in verification_summary.items():
                claimed = "✅" if verification["claimed"] else "❌"
                verified = "✅" if verification["verified"] else "❌"
                details = verification["details"]

                print(f"  {test_name.replace('_', ' ').title()}:")
                print(f"    Claimed Success: {claimed}")
                print(f"    REAL Success: {verified}")
                print(f"    Details: {details}")
                print()

            # Overall assessment
            real_successes = sum(1 for v in verification_summary.values() if v["verified"])
            total_tests = len(verification_summary)

            print(f"🎯 OVERALL REAL SUCCESS RATE: {real_successes}/{total_tests} ({real_successes/total_tests*100:.1f}%)")

            if real_successes == total_tests:
                print("🎉 ALL INTERACTIONS ACTUALLY WORKING!")
            elif real_successes > total_tests / 2:
                print("✅ MAJORITY OF INTERACTIONS WORKING")
            else:
                print("⚠️ MOST INTERACTIONS NOT ACTUALLY WORKING")

    except KeyboardInterrupt:
        print("\n👋 Verified test suite interrupted")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        test_suite.cleanup()

if __name__ == "__main__":
    main()
