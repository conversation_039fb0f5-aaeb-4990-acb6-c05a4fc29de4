const { DualTabMEXCTrader } = require('./dual-tab-trader');

class TradingCommands {
    constructor() {
        this.trader = new DualTabMEXCTrader();
        this.isConnected = false;
    }

    async initialize() {
        if (!this.isConnected) {
            console.log('🔗 Initializing trading system...');
            const connected = await this.trader.connectToRemoteBrowser();
            if (!connected) {
                throw new Error('Could not connect to remote browser');
            }

            const tabsReady = await this.trader.setupTabs();
            if (!tabsReady) {
                throw new Error('Could not setup tabs');
            }

            this.isConnected = true;
            console.log('✅ Trading system ready');
        }
    }

    async openTrade() {
        console.log('📈 EXECUTING OPEN TRADE...');
        console.log('==========================');
        
        await this.initialize();
        
        const result = await this.trader.executeOpenTrade();
        const verified = await this.trader.verifyTrade(this.trader.openTab, 'OPEN');
        
        if (result.success && result.executionTime < 2000) {
            console.log('\n🏆 OPEN TRADE SUCCESS!');
            console.log(`⚡ Executed in ${result.executionTime}ms (<2 seconds)`);
        } else if (result.success) {
            console.log('\n✅ OPEN trade completed!');
            console.log(`⏱️ Time: ${result.executionTime}ms`);
        } else {
            console.log('\n❌ OPEN trade failed');
            if (result.error) console.log(`Error: ${result.error}`);
        }

        if (verified) {
            console.log('🎉 OPEN TRADE VERIFIED!');
        }

        return { ...result, verified };
    }

    async closeTrade() {
        console.log('📉 EXECUTING CLOSE TRADE...');
        console.log('===========================');
        
        await this.initialize();
        
        const result = await this.trader.executeCloseTrade();
        const verified = await this.trader.verifyTrade(this.trader.closeTab, 'CLOSE');
        
        if (result.success && result.executionTime < 2000) {
            console.log('\n🏆 CLOSE TRADE SUCCESS!');
            console.log(`⚡ Executed in ${result.executionTime}ms (<2 seconds)`);
        } else if (result.success) {
            console.log('\n✅ CLOSE trade completed!');
            console.log(`⏱️ Time: ${result.executionTime}ms`);
        } else {
            console.log('\n❌ CLOSE trade failed');
            if (result.error) console.log(`Error: ${result.error}`);
        }

        if (verified) {
            console.log('🎉 CLOSE TRADE VERIFIED!');
        }

        return { ...result, verified };
    }

    async bothTrades() {
        console.log('🔄 EXECUTING BOTH TRADES...');
        console.log('============================');
        console.log('📈 First: OPEN trade');
        console.log('📉 Then: CLOSE trade');
        console.log('');

        const openResult = await this.openTrade();
        
        if (openResult.success) {
            console.log('\n⏳ Waiting 1 second before close trade...');
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const closeResult = await this.closeTrade();
            
            const totalTime = openResult.executionTime + closeResult.executionTime;
            
            console.log('\n🏆 BOTH TRADES SUMMARY:');
            console.log('=======================');
            console.log(`📈 Open: ${openResult.success ? '✅' : '❌'} (${openResult.executionTime}ms)`);
            console.log(`📉 Close: ${closeResult.success ? '✅' : '❌'} (${closeResult.executionTime}ms)`);
            console.log(`⏱️ Total time: ${totalTime}ms`);
            console.log(`🎯 Both under 2s: ${openResult.executionTime < 2000 && closeResult.executionTime < 2000 ? '✅' : '❌'}`);
            
            return { openResult, closeResult, totalTime };
        } else {
            console.log('\n❌ Open trade failed, skipping close trade');
            return { openResult, closeResult: null };
        }
    }
}

async function main() {
    const commands = new TradingCommands();
    
    try {
        console.log('🎯 MEXC DUAL TAB TRADING SYSTEM');
        console.log('================================');
        console.log('🚀 Ultra-fast futures trading');
        console.log('📈 Tab 1: Open Long positions');
        console.log('📉 Tab 2: Open Short positions (close longs)');
        console.log('⚡ Target: <2 seconds per trade');
        console.log('');

        // Get command from command line arguments
        const command = process.argv[2];
        
        if (!command) {
            console.log('📋 AVAILABLE COMMANDS:');
            console.log('======================');
            console.log('node trading-commands.js open   - Execute OPEN trade');
            console.log('node trading-commands.js close  - Execute CLOSE trade');
            console.log('node trading-commands.js both   - Execute BOTH trades');
            console.log('');
            console.log('Example: node trading-commands.js open');
            return;
        }

        switch (command.toLowerCase()) {
            case 'open':
                const openResult = await commands.openTrade();
                process.exit(openResult.success ? 0 : 1);
                break;
                
            case 'close':
                const closeResult = await commands.closeTrade();
                process.exit(closeResult.success ? 0 : 1);
                break;
                
            case 'both':
                const bothResults = await commands.bothTrades();
                const success = bothResults.openResult.success && (bothResults.closeResult?.success ?? false);
                process.exit(success ? 0 : 1);
                break;
                
            default:
                console.log(`❌ Unknown command: ${command}`);
                console.log('Available commands: open, close, both');
                process.exit(1);
        }
        
    } catch (error) {
        console.error('💥 Trading system failed:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = TradingCommands;
