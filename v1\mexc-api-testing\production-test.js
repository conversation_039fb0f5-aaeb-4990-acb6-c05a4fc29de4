const FinalUltimateTrader = require('./final-ultimate-trader.js');

async function runProductionTests() {
    console.log('🚀 PRODUCTION-READY MEXC TRADER TESTS');
    console.log('=====================================');
    console.log('Testing all scenarios with enhanced error handling');
    console.log('');

    const testScenarios = [
        {
            name: 'Open Long Order',
            orderType: 'Open Long',
            port: 9222,
            description: 'Should work - creates new long position'
        },
        {
            name: 'Close Long Order (No Position)',
            orderType: 'Close Long', 
            port: 9223,
            description: 'Should fail gracefully - no position to close'
        },
        {
            name: 'Open Short Order',
            orderType: 'Open Short',
            port: 9222,
            description: 'Should work - creates new short position'
        }
    ];

    const results = [];

    for (const scenario of testScenarios) {
        console.log(`\n🧪 TEST: ${scenario.name}`);
        console.log(`📝 ${scenario.description}`);
        console.log(`🌐 Port: ${scenario.port}`);
        console.log('─'.repeat(50));

        const trader = new FinalUltimateTrader(scenario.port);
        const startTime = Date.now();

        try {
            const connected = await trader.connectToBrowser();
            if (!connected) {
                throw new Error(`Failed to connect to port ${scenario.port}`);
            }

            const result = await trader.executeOrder(scenario.orderType);
            const testTime = Date.now() - startTime;

            console.log(`\n📊 RESULT: ${result.success ? '✅ SUCCESS' : '❌ FAILED'}`);
            console.log(`⏱️  Execution Time: ${result.executionTime}ms`);
            console.log(`🎯 Target (<2s): ${result.targetAchieved ? '✅ YES' : '❌ NO'}`);
            
            if (result.error) {
                console.log(`❌ Error: ${result.error}`);
            }

            results.push({
                scenario: scenario.name,
                orderType: scenario.orderType,
                port: scenario.port,
                success: result.success,
                executionTime: result.executionTime,
                targetAchieved: result.targetAchieved,
                error: result.error || null,
                testTime
            });

        } catch (error) {
            const testTime = Date.now() - startTime;
            console.log(`\n❌ TEST FAILED: ${error.message}`);
            
            results.push({
                scenario: scenario.name,
                orderType: scenario.orderType,
                port: scenario.port,
                success: false,
                executionTime: null,
                targetAchieved: false,
                error: error.message,
                testTime
            });
        }

        // Wait between tests
        await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // Summary
    console.log('\n🏁 PRODUCTION TEST SUMMARY');
    console.log('==========================');
    
    const successful = results.filter(r => r.success).length;
    const total = results.length;
    
    console.log(`📊 Success Rate: ${successful}/${total} (${Math.round(successful/total*100)}%)`);
    console.log('');

    results.forEach(result => {
        const status = result.success ? '✅' : '❌';
        const time = result.executionTime ? `${result.executionTime}ms` : 'N/A';
        const target = result.targetAchieved ? '🎯' : '⏰';
        
        console.log(`${status} ${result.scenario}: ${time} ${target}`);
        if (result.error) {
            console.log(`   └─ ${result.error}`);
        }
    });

    // Save detailed results
    const detailedResults = {
        timestamp: new Date().toISOString(),
        summary: {
            total,
            successful,
            successRate: Math.round(successful/total*100)
        },
        results
    };

    require('fs').writeFileSync('production-test-results.json', JSON.stringify(detailedResults, null, 2));
    console.log('\n💾 Detailed results saved to production-test-results.json');

    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    console.log('===================');
    
    const openTests = results.filter(r => r.orderType.includes('Open'));
    const closeTests = results.filter(r => r.orderType.includes('Close'));
    
    if (openTests.some(r => r.success)) {
        console.log('✅ Open orders are working correctly');
    } else {
        console.log('❌ Open orders need attention - check browser setup and modal handling');
    }
    
    if (closeTests.some(r => r.error && r.error.includes('No open positions'))) {
        console.log('✅ Close order validation is working - correctly detects no positions');
        console.log('💡 To test close orders, first create positions with open orders');
    }

    console.log('\n🔧 NEXT STEPS:');
    console.log('1. If open orders fail, check for persistent popups on port 9222');
    console.log('2. To test close orders, first execute successful open orders');
    console.log('3. Monitor execution times - target is <2 seconds');
    console.log('4. Check quantity field clearing is working properly');
}

if (require.main === module) {
    runProductionTests().catch(console.error);
}

module.exports = runProductionTests;
