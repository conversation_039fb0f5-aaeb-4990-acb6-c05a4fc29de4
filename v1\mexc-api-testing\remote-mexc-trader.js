const { chromium } = require('playwright');

class RemoteMEXCTrader {
    constructor() {
        this.browser = null;
        this.page = null;
        this.startTime = null;
    }

    async connectToRemoteBrowser() {
        console.log('🔗 Connecting to remote Chrome browser on port 9222...');
        
        try {
            // Connect to existing Chrome instance
            this.browser = await chromium.connectOverCDP('http://localhost:9222');
            console.log('✅ Connected to remote browser');
            
            // Get existing pages or create new one
            const contexts = this.browser.contexts();
            let context;
            
            if (contexts.length > 0) {
                context = contexts[0];
                const pages = context.pages();
                if (pages.length > 0) {
                    this.page = pages[0];
                    console.log('✅ Using existing page');
                } else {
                    this.page = await context.newPage();
                    console.log('✅ Created new page in existing context');
                }
            } else {
                context = await this.browser.newContext();
                this.page = await context.newPage();
                console.log('✅ Created new context and page');
            }
            
            return true;
        } catch (error) {
            console.error('❌ Failed to connect to remote browser:', error.message);
            console.log('💡 Make sure Chrome is running with: chrome.exe --remote-debugging-port=9222 --user-data-dir="./browser_data"');
            return false;
        }
    }

    async navigateToMEXC() {
        console.log('🌐 Navigating to MEXC futures...');
        
        try {
            await this.page.goto('https://futures.mexc.com/exchange/TRU_USDT', {
                waitUntil: 'domcontentloaded',
                timeout: 15000
            });
            
            await this.page.waitForTimeout(3000);
            console.log('✅ MEXC futures page loaded');
            return true;
        } catch (error) {
            console.log('⚠️ Navigation failed:', error.message);
            return false;
        }
    }

    async humanTouch(selector) {
        try {
            const element = await this.page.locator(selector).first();

            // Try click first (faster), fallback to touch if needed
            try {
                await element.click({ timeout: 500 });
                console.log(`🖱️ Clicked: ${selector}`);
            } catch (clickError) {
                // Fallback to mouse click with coordinates
                const box = await element.boundingBox();
                if (box) {
                    const x = box.x + box.width / 2;
                    const y = box.y + box.height / 2;
                    await this.page.mouse.click(x, y);
                    console.log(`👆 Mouse clicked: ${selector}`);
                } else {
                    throw new Error(`Element not visible: ${selector}`);
                }
            }

            await this.page.waitForTimeout(100); // Reduced delay for speed
            return true;
        } catch (error) {
            console.log(`❌ Failed to interact with ${selector}: ${error.message}`);
            return false;
        }
    }

    async humanType(selector, text) {
        try {
            await this.page.locator(selector).first().focus();
            await this.page.waitForTimeout(100);
            
            // Clear and type
            await this.page.keyboard.press('Control+a');
            await this.page.keyboard.type(text);
            
            console.log(`⌨️ Typed "${text}" into ${selector}`);
            await this.page.waitForTimeout(50); // Reduced for speed
            return true;
        } catch (error) {
            console.log(`❌ Failed to type in ${selector}: ${error.message}`);
            return false;
        }
    }

    async analyzePageAndFindElements() {
        console.log('🔍 Analyzing page elements...');
        
        // Get page title and URL to confirm we're on the right page
        const title = await this.page.title();
        const url = this.page.url();
        console.log(`📄 Page: ${title}`);
        console.log(`🔗 URL: ${url}`);
        
        // Look for trading interface elements
        const elementChecks = [
            // Buy/Long buttons
            { name: 'Buy Button', selectors: ['button:has-text("Buy")', 'button:has-text("Long")', '.buy', '.long', '[data-testid*="buy"]', '[class*="buy"]'] },
            
            // Market order
            { name: 'Market Order', selectors: ['button:has-text("Market")', '.market', '[data-testid*="market"]', '[class*="market"]'] },
            
            // Amount/Quantity inputs
            { name: 'Amount Input', selectors: ['input[placeholder*="amount"]', 'input[placeholder*="quantity"]', 'input[type="number"]', '.amount-input', '.quantity-input'] },
            
            // Submit buttons
            { name: 'Submit Button', selectors: ['button:has-text("Place")', 'button:has-text("Submit")', 'button:has-text("Confirm")', '.submit', '.place-order'] }
        ];

        const foundElements = {};
        
        for (const check of elementChecks) {
            console.log(`🔍 Looking for ${check.name}...`);
            
            for (const selector of check.selectors) {
                try {
                    const elements = await this.page.locator(selector).all();
                    if (elements.length > 0) {
                        const visible = await this.page.locator(selector).first().isVisible();
                        if (visible) {
                            foundElements[check.name] = selector;
                            console.log(`✅ Found ${check.name}: ${selector} (${elements.length} elements)`);
                            break;
                        }
                    }
                } catch (error) {
                    // Continue to next selector
                }
            }
            
            if (!foundElements[check.name]) {
                console.log(`❌ ${check.name} not found`);
            }
        }
        
        return foundElements;
    }

    async placeFuturesOrderFast() {
        this.startTime = Date.now();
        console.log('🚀 Starting FAST futures order placement...');
        
        try {
            // Quick element analysis
            const elements = await this.analyzePageAndFindElements();
            
            // Step 1: Click Buy/Long (fastest approach)
            console.log('📈 Step 1: Clicking Buy...');
            let buySuccess = false;
            
            const buySelectors = [
                'button:has-text("Buy")',
                'button:has-text("Long")', 
                '.buy-btn',
                '.long-btn',
                '[class*="buy"]:not(input)',
                '[data-testid*="buy"]'
            ];
            
            for (const selector of buySelectors) {
                try {
                    const isVisible = await this.page.locator(selector).first().isVisible({ timeout: 500 });
                    if (isVisible) {
                        await this.humanTouch(selector);
                        buySuccess = true;
                        break;
                    }
                } catch (error) {
                    continue;
                }
            }
            
            if (!buySuccess) {
                throw new Error('Could not find Buy button');
            }

            // Step 2: Select Market order (if needed)
            console.log('📊 Step 2: Selecting Market order...');
            const marketSelectors = [
                'button:has-text("Market")',
                '.market-btn',
                '[class*="market"]:not(input)',
                '[data-testid*="market"]'
            ];
            
            for (const selector of marketSelectors) {
                try {
                    const isVisible = await this.page.locator(selector).first().isVisible({ timeout: 300 });
                    if (isVisible) {
                        await this.humanTouch(selector);
                        break;
                    }
                } catch (error) {
                    continue;
                }
            }

            // Step 3: Enter quantity FAST
            console.log('🔢 Step 3: Entering quantity...');
            let quantitySuccess = false;
            
            const quantitySelectors = [
                'input[placeholder*="amount"]',
                'input[placeholder*="quantity"]',
                'input[placeholder*="size"]',
                'input[type="number"]',
                '.amount-input input',
                '.quantity-input input'
            ];
            
            for (const selector of quantitySelectors) {
                try {
                    const isVisible = await this.page.locator(selector).first().isVisible({ timeout: 300 });
                    if (isVisible) {
                        await this.humanType(selector, '40');
                        quantitySuccess = true;
                        break;
                    }
                } catch (error) {
                    continue;
                }
            }
            
            if (!quantitySuccess) {
                console.log('⚠️ Trying percentage buttons...');
                const percentSelectors = ['button:has-text("25%")', 'button:has-text("50%")'];
                for (const selector of percentSelectors) {
                    try {
                        const isVisible = await this.page.locator(selector).first().isVisible({ timeout: 200 });
                        if (isVisible) {
                            await this.humanTouch(selector);
                            quantitySuccess = true;
                            break;
                        }
                    } catch (error) {
                        continue;
                    }
                }
            }

            // Step 4: Submit order IMMEDIATELY
            console.log('🚀 Step 4: Submitting order...');
            let submitSuccess = false;
            
            const submitSelectors = [
                'button:has-text("Buy")',
                'button:has-text("Long")',
                'button:has-text("Place")',
                'button:has-text("Submit")',
                'button:has-text("Confirm")',
                '.submit-btn',
                '.place-order-btn',
                '.confirm-btn'
            ];
            
            for (const selector of submitSelectors) {
                try {
                    const isVisible = await this.page.locator(selector).first().isVisible({ timeout: 300 });
                    if (isVisible) {
                        await this.humanTouch(selector);
                        submitSuccess = true;
                        break;
                    }
                } catch (error) {
                    continue;
                }
            }

            const executionTime = Date.now() - this.startTime;
            
            // Quick check for success indicators
            let confirmationFound = false;
            try {
                const successSelectors = ['.success', '.toast', '[class*="success"]', 'text=success', 'text=placed'];
                for (const selector of successSelectors) {
                    const isVisible = await this.page.locator(selector).first().isVisible({ timeout: 500 });
                    if (isVisible) {
                        confirmationFound = true;
                        break;
                    }
                }
            } catch (error) {
                // No confirmation found
            }

            console.log('\n🏆 EXECUTION RESULTS:');
            console.log('====================');
            console.log(`⏱️ Execution time: ${executionTime}ms`);
            console.log(`🎯 Under 2 seconds: ${executionTime < 2000 ? '✅ YES!' : '❌ NO'}`);
            console.log(`📋 Order submitted: ${submitSuccess ? '✅ YES' : '❌ NO'}`);
            console.log(`🎉 Confirmation: ${confirmationFound ? '✅ DETECTED' : '⚠️ CHECKING...'}`);

            // Save results
            const result = {
                success: submitSuccess,
                executionTime,
                targetAchieved: executionTime < 2000,
                confirmationDetected: confirmationFound,
                timestamp: new Date().toISOString()
            };

            require('fs').writeFileSync('remote-trader-results.json', JSON.stringify(result, null, 2));

            return result;

        } catch (error) {
            const executionTime = Date.now() - this.startTime;
            console.error(`❌ Order failed after ${executionTime}ms:`, error.message);
            
            return {
                success: false,
                executionTime,
                error: error.message
            };
        }
    }

    async checkOrderStatus() {
        console.log('🔍 Checking order status...');
        
        try {
            // Look for order confirmation, success messages, or order history
            const statusSelectors = [
                '.order-success',
                '.success-message',
                '.toast-success',
                '[class*="success"]',
                'text=Order placed',
                'text=Success',
                'text=Filled',
                'text=Executed'
            ];

            for (const selector of statusSelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    const isVisible = await element.isVisible({ timeout: 1000 });
                    if (isVisible) {
                        const text = await element.textContent();
                        console.log(`✅ Status found: ${text}`);
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }

            console.log('⚠️ No clear order status found');
            return false;
        } catch (error) {
            console.log('❌ Error checking status:', error.message);
            return false;
        }
    }
}

async function runRemoteTrader() {
    const trader = new RemoteMEXCTrader();
    
    try {
        // Connect to remote browser
        const connected = await trader.connectToRemoteBrowser();
        if (!connected) {
            throw new Error('Could not connect to remote browser');
        }

        // Navigate to MEXC
        const navigated = await trader.navigateToMEXC();
        if (!navigated) {
            throw new Error('Could not navigate to MEXC');
        }

        console.log('\n⚠️ WARNING: About to place REAL futures order!');
        console.log('🎯 Symbol: TRUUSDT');
        console.log('📈 Side: Buy/Long');
        console.log('🔢 Quantity: 40');
        console.log('⏱️ Target: <2000ms');
        console.log('\nStarting in 3 seconds...');
        
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Execute the trade
        const result = await trader.placeFuturesOrderFast();
        
        // Check final status
        await trader.checkOrderStatus();
        
        if (result.success && result.targetAchieved) {
            console.log('\n🏆 MISSION ACCOMPLISHED!');
            console.log('✅ Order placed successfully under 2 seconds!');
        } else if (result.success) {
            console.log('\n✅ Order placed successfully!');
            console.log(`⚠️ Execution time: ${result.executionTime}ms (target: <2000ms)`);
        } else {
            console.log('\n❌ Order placement failed');
        }
        
        return result;
        
    } catch (error) {
        console.error('💥 Remote trader failed:', error.message);
        return { success: false, error: error.message };
    }
}

// Run if called directly
if (require.main === module) {
    console.log('🚀 MEXC Remote Trader Starting...');
    console.log('================================');
    console.log('📋 Prerequisites:');
    console.log('1. Chrome must be running with remote debugging');
    console.log('2. Use: chrome.exe --remote-debugging-port=9222 --user-data-dir="./browser_data"');
    console.log('3. Navigate to MEXC and login if needed');
    console.log('');
    
    runRemoteTrader()
        .then(result => {
            console.log('\n🏁 Remote trading session completed');
            process.exit(result.success ? 0 : 1);
        })
        .catch(error => {
            console.error('💥 Session crashed:', error);
            process.exit(1);
        });
}

module.exports = RemoteMEXCTrader;
