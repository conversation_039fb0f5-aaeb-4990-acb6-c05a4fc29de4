const MexcFuturesTrader = require('./mexc-futures-trader/src/trader');

async function testOptimizedTrader() {
    console.log('🚀 Testing Optimized MEXC Futures Trader');
    console.log('=' .repeat(50));

    const trader = new MexcFuturesTrader(9223);
    
    try {
        // Connect to browser
        console.log('🔗 Connecting to browser...');
        const connected = await trader.connectToBrowser();
        
        if (!connected) {
            console.error('❌ Failed to connect to browser');
            return;
        }

        console.log('✅ Connected successfully');
        console.log('🔄 Background monitoring should now be active');
        
        // Wait a moment to let background monitoring start
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Test sequence: Complex pattern to test new strategy
        // Pattern: Open -> Close -> Close -> Close -> Open -> Close -> Close
        const testSequence = [
            { orderType: 'Open Long', quantity: '0.3600' },
            { orderType: 'Close Long', quantity: '0.3600' },
            { orderType: 'Close Long', quantity: '0.3600' },
            { orderType: 'Close Long', quantity: '0.3600' },
            { orderType: 'Open Short', quantity: '0.3600' },
            { orderType: 'Close Short', quantity: '0.3600' },
            { orderType: 'Close Short', quantity: '0.3600' }
        ];

        console.log('\n📊 Starting optimized trading sequence...');
        console.log('Expected optimizations:');
        console.log('- Default Open tab positioning (Open trades optimized)');
        console.log('- Close trades verify/switch to Close tab as needed');
        console.log('- Background cleanup every 2.5 minutes');
        console.log('- Reduced tab switching delays');
        console.log('- Optimized quantity field handling');
        
        const results = [];
        
        for (let i = 0; i < testSequence.length; i++) {
            const { orderType, quantity } = testSequence[i];
            
            console.log(`\n🎯 Test ${i + 1}/4: ${orderType}`);
            console.log('-'.repeat(30));
            
            const startTime = Date.now();
            
            try {
                const result = await trader.executeOrder(orderType, quantity);
                const totalTime = Date.now() - startTime;
                
                console.log(`⚡ Execution completed in ${totalTime}ms`);
                console.log(`🎉 Success: ${result.success ? '✅' : '❌'}`);
                console.log(`🎯 Target achieved (<2000ms): ${result.targetAchieved ? '✅' : '❌'}`);
                
                results.push({
                    test: i + 1,
                    orderType,
                    success: result.success,
                    executionTime: totalTime,
                    targetAchieved: result.targetAchieved,
                    optimized: true
                });
                
                // Short delay between trades to observe tab positioning
                if (i < testSequence.length - 1) {
                    console.log('⏳ Waiting 3 seconds to observe Open tab default positioning...');
                    await new Promise(resolve => setTimeout(resolve, 3000));
                }
                
            } catch (error) {
                console.error(`❌ Test ${i + 1} failed:`, error.message);
                results.push({
                    test: i + 1,
                    orderType,
                    success: false,
                    error: error.message,
                    executionTime: Date.now() - startTime,
                    targetAchieved: false
                });
            }
        }
        
        // Test background monitoring
        console.log('\n🧹 Testing background monitoring...');
        console.log('Waiting 30 seconds to observe background cleanup...');
        console.log('(In production, this runs every 2.5 minutes)');
        
        // Manually trigger background cleanup for testing
        if (trader.page) {
            try {
                await trader.backgroundCleanup();
                console.log('✅ Manual background cleanup test completed');
            } catch (error) {
                console.log('⚠️ Background cleanup test failed:', error.message);
            }
        }
        
        // Summary
        console.log('\n📊 OPTIMIZATION TEST RESULTS');
        console.log('=' .repeat(50));
        
        const successCount = results.filter(r => r.success).length;
        const targetAchievedCount = results.filter(r => r.targetAchieved).length;
        const avgExecutionTime = results.reduce((sum, r) => sum + r.executionTime, 0) / results.length;
        
        console.log(`✅ Successful trades: ${successCount}/${results.length}`);
        console.log(`🎯 Sub-2s executions: ${targetAchievedCount}/${results.length}`);
        console.log(`⚡ Average execution time: ${avgExecutionTime.toFixed(0)}ms`);
        
        console.log('\nDetailed Results:');
        results.forEach(result => {
            const status = result.success ? '✅' : '❌';
            const speed = result.targetAchieved ? '🚀' : '🐌';
            console.log(`  ${status} ${speed} Test ${result.test}: ${result.orderType} - ${result.executionTime}ms`);
        });
        
        console.log('\nOptimization Features Tested:');
        console.log('✅ Background monitoring system');
        console.log('✅ Default Open tab positioning strategy');
        console.log('✅ Close trade tab verification/switching');
        console.log('✅ Optimized quantity field handling');
        console.log('✅ Reduced wait times');
        console.log('✅ Execution flag management');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    } finally {
        // Cleanup
        try {
            await trader.disconnect();
            console.log('\n🛑 Test completed and cleaned up');
        } catch (error) {
            console.log('⚠️ Cleanup error:', error.message);
        }
    }
}

// Run the test
if (require.main === module) {
    testOptimizedTrader().catch(console.error);
}

module.exports = { testOptimizedTrader };
