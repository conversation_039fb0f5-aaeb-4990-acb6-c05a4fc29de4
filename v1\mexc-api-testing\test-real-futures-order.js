const ccxt = require('ccxt');
const axios = require('axios');
const crypto = require('crypto');
require('dotenv').config();

console.log('🚨 WARNING: This script will attempt to place REAL futures orders!');
console.log('🚨 Make sure you understand the risks before proceeding.');
console.log('🚨 Use small amounts and monitor your account closely.');
console.log('');

// Custom MEXC Futures API implementation
class MexcFuturesAPI {
    constructor(apiKey, apiSecret) {
        this.apiKey = apiKey;
        this.apiSecret = apiSecret;
        this.baseURL = 'https://contract.mexc.com';
    }

    generateSignature(queryString) {
        return crypto
            .createHmac('sha256', this.apiSecret)
            .update(queryString)
            .digest('hex');
    }

    async makeRequest(method, endpoint, params = {}) {
        const timestamp = Date.now();
        params.timestamp = timestamp;
        
        const queryString = Object.keys(params)
            .sort()
            .map(key => `${key}=${params[key]}`)
            .join('&');
        
        const signature = this.generateSignature(queryString);
        params.signature = signature;
        
        const config = {
            method,
            url: `${this.baseURL}${endpoint}`,
            headers: {
                'X-MEXC-APIKEY': this.apiKey,
                'Content-Type': 'application/json'
            }
        };

        if (method === 'GET') {
            config.params = params;
        } else {
            config.data = params;
        }

        return axios(config);
    }

    async getAccountAssets() {
        return this.makeRequest('GET', '/api/v1/private/account/assets');
    }

    async getPositions() {
        return this.makeRequest('GET', '/api/v1/private/position/open_positions');
    }

    async createOrder(params) {
        return this.makeRequest('POST', '/api/v1/private/order/submit', params);
    }

    async getOrderHistory() {
        return this.makeRequest('GET', '/api/v1/private/order/list/history_orders');
    }
}

async function testCCXTFuturesOrder() {
    console.log('\n=== Testing CCXT Futures Order Placement ===');
    
    try {
        const exchange = new ccxt.mexc({
            apiKey: process.env.MEXC_API_KEY,
            secret: process.env.MEXC_API_SECRET,
            sandbox: false,
            enableRateLimit: true,
            options: {
                defaultType: 'swap', // Use futures/swap market
            }
        });

        await exchange.loadMarkets();
        console.log('✓ CCXT exchange loaded');

        // Try to get futures balance
        try {
            const balance = await exchange.fetchBalance();
            console.log('✓ Balance fetched:', Object.keys(balance.total).length, 'currencies');
            
            // Show USDT balance if available
            if (balance.USDT) {
                console.log('USDT Balance:', balance.USDT);
            }
        } catch (error) {
            console.log('⚠ Balance fetch failed:', error.message);
        }

        // Try to create a small futures order
        const symbol = 'BTC/USDT:USDT'; // Futures symbol format
        const side = 'buy';
        const amount = 0.001; // Very small amount
        const type = 'market'; // Market order for immediate execution

        console.log(`\nAttempting to place ${type} ${side} order for ${amount} ${symbol}`);
        
        try {
            const order = await exchange.createOrder(symbol, type, side, amount);
            console.log('🎉 SUCCESS! Futures order placed:', order);
            return { success: true, method: 'ccxt', order: order };
        } catch (orderError) {
            console.log('❌ Order placement failed:', orderError.message);
            return { success: false, method: 'ccxt', error: orderError.message };
        }

    } catch (error) {
        console.log('❌ CCXT test failed:', error.message);
        return { success: false, method: 'ccxt', error: error.message };
    }
}

async function testCustomFuturesOrder() {
    console.log('\n=== Testing Custom Futures API Order Placement ===');
    
    try {
        const client = new MexcFuturesAPI(
            process.env.MEXC_API_KEY,
            process.env.MEXC_API_SECRET
        );

        // Test account access
        try {
            const assets = await client.getAccountAssets();
            console.log('✓ Account assets retrieved:', assets.data);
        } catch (error) {
            console.log('⚠ Account assets failed:', error.response?.data || error.message);
        }

        // Test positions
        try {
            const positions = await client.getPositions();
            console.log('✓ Positions retrieved:', positions.data);
        } catch (error) {
            console.log('⚠ Positions failed:', error.response?.data || error.message);
        }

        // Try to place a futures order
        const orderParams = {
            symbol: 'BTC_USDT',
            side: 1, // 1 = buy, 2 = sell
            type: 5, // 5 = market order
            vol: '0.001', // Very small volume
            leverage: '1'
        };

        console.log('\nAttempting to place futures order:', orderParams);
        
        try {
            const order = await client.createOrder(orderParams);
            console.log('🎉 SUCCESS! Futures order placed:', order.data);
            return { success: true, method: 'custom', order: order.data };
        } catch (orderError) {
            console.log('❌ Order placement failed:', orderError.response?.data || orderError.message);
            return { success: false, method: 'custom', error: orderError.response?.data || orderError.message };
        }

    } catch (error) {
        console.log('❌ Custom API test failed:', error.message);
        return { success: false, method: 'custom', error: error.message };
    }
}

async function main() {
    console.log('Starting REAL futures order tests...\n');
    
    const results = [];
    
    // Test CCXT
    const ccxtResult = await testCCXTFuturesOrder();
    results.push(ccxtResult);
    
    // Wait between tests
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Test Custom API
    const customResult = await testCustomFuturesOrder();
    results.push(customResult);
    
    // Summary
    console.log('\n' + '='.repeat(50));
    console.log('📊 REAL ORDER TEST RESULTS');
    console.log('='.repeat(50));
    
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    
    console.log(`✅ Successful: ${successful.length}/${results.length}`);
    console.log(`❌ Failed: ${failed.length}/${results.length}`);
    
    if (successful.length > 0) {
        console.log('\n🎉 WORKING METHODS:');
        successful.forEach(result => {
            console.log(`  ✓ ${result.method}`);
            if (result.order) {
                console.log(`    Order ID: ${result.order.orderId || result.order.id || 'N/A'}`);
            }
        });
    }
    
    if (failed.length > 0) {
        console.log('\n💔 FAILED METHODS:');
        failed.forEach(result => {
            console.log(`  ✗ ${result.method}: ${result.error}`);
        });
    }
    
    // Save results
    const report = {
        timestamp: new Date().toISOString(),
        warning: 'REAL ORDERS WERE ATTEMPTED',
        results: results
    };
    
    require('fs').writeFileSync(
        'real-futures-order-test-report.json',
        JSON.stringify(report, null, 2)
    );
    
    console.log('\n📄 Report saved to: real-futures-order-test-report.json');
    
    return results;
}

if (require.main === module) {
    // Ask for confirmation
    console.log('⚠️  This will attempt to place REAL futures orders with real money!');
    console.log('⚠️  Make sure you have:');
    console.log('   - Futures trading enabled on your MEXC account');
    console.log('   - API permissions for futures trading');
    console.log('   - Small amounts you can afford to lose');
    console.log('   - Understanding of the risks involved');
    console.log('');
    console.log('Press Ctrl+C to cancel, or wait 10 seconds to continue...');
    
    setTimeout(() => {
        main().then(results => {
            const successCount = results.filter(r => r.success).length;
            process.exit(successCount > 0 ? 0 : 1);
        }).catch(error => {
            console.error('Test failed:', error);
            process.exit(1);
        });
    }, 10000);
}

module.exports = main;
