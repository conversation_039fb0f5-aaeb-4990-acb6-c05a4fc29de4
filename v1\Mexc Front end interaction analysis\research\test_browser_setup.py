#!/usr/bin/env python3
"""
Test script to verify browser automation setup
This script tests the basic browser connection and MEXC page access
"""

import sys
import time
import json
from playwright.sync_api import sync_playwright

def test_browser_connection(debug_port=9222):
    """Test connection to browser debug port"""
    print(f"🔍 Testing browser connection on port {debug_port}...")
    
    try:
        playwright = sync_playwright().start()
        browser = playwright.chromium.connect_over_cdp(f'http://127.0.0.1:{debug_port}')
        
        if not browser.contexts:
            print("❌ No browser contexts found")
            print("💡 Make sure Chrome is running with: --remote-debugging-port=9222")
            return False
        
        print(f"✅ Connected to browser with {len(browser.contexts)} context(s)")
        
        context = browser.contexts[0]
        print(f"📋 Found {len(context.pages)} page(s)")
        
        for i, page in enumerate(context.pages):
            print(f"  Page {i+1}: {page.url}")
        
        # Test creating a new page
        test_page = context.new_page()
        test_page.goto('https://www.google.com', wait_until='domcontentloaded')
        print(f"✅ Successfully navigated to: {test_page.url}")
        
        test_page.close()
        playwright.stop()
        return True
        
    except Exception as e:
        print(f"❌ Browser connection failed: {e}")
        return False

def test_mexc_access():
    """Test access to MEXC futures page"""
    print("\n🌐 Testing MEXC futures access...")

    try:
        # Check if we can connect to existing browser first
        playwright = sync_playwright().start()
        browser = playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')

        if not browser.contexts:
            print("❌ No browser contexts found")
            playwright.stop()
            return False

        context = browser.contexts[0]

        # Check existing pages first
        mexc_page = None
        for page in context.pages:
            if 'mexc.com' in (page.url or ''):
                mexc_page = page
                break

        if mexc_page:
            print(f"✅ Found existing MEXC page: {mexc_page.url}")
            print(f"📄 Page title: {mexc_page.title()}")
        else:
            print("📍 Creating new MEXC page...")
            mexc_page = context.new_page()
            mexc_page.goto('https://futures.mexc.com/exchange/TRU_USDT', wait_until='domcontentloaded')
            print(f"✅ Successfully loaded: {mexc_page.url}")
            print(f"📄 Page title: {mexc_page.title()}")

        # Check for common elements
        elements_to_check = [
            ('Trading interface', '.trading-panel, .order-form, button:has-text("Buy"), button:has-text("Sell")'),
            ('Login button', 'button:has-text("Log In"), a:has-text("Log In")'),
            ('User menu', '.user-info, .account-balance, [data-testid="user-menu"]')
        ]

        for name, selector in elements_to_check:
            try:
                if mexc_page.locator(selector).first.is_visible(timeout=3000):
                    print(f"✅ Found {name}")
                else:
                    print(f"⚠️ {name} not visible")
            except:
                print(f"❌ {name} not found")

        playwright.stop()
        return True

    except Exception as e:
        print(f"❌ MEXC access failed: {e}")
        return False

def test_debug_server_port(port=8080):
    """Test if debug server port is available"""
    print(f"\n🔌 Testing debug server port {port}...")
    
    import socket
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        
        if result == 0:
            print(f"⚠️ Port {port} is already in use")
            return False
        else:
            print(f"✅ Port {port} is available")
            return True
            
    except Exception as e:
        print(f"❌ Port test failed: {e}")
        return False

def main():
    """Run all setup tests"""
    print("🚀 MEXC Browser Automation Setup Test")
    print("=" * 40)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Browser connection
    if test_browser_connection():
        tests_passed += 1
    
    # Test 2: MEXC access
    if test_mexc_access():
        tests_passed += 1
    
    # Test 3: Debug server port
    if test_debug_server_port():
        tests_passed += 1
    
    print(f"\n📊 Test Results: {tests_passed}/{total_tests} passed")
    
    if tests_passed == total_tests:
        print("✅ All tests passed! Ready to run browser automation.")
        print("\nNext steps:")
        print("1. Run: python mexc_browser_automation_debug.py")
        print("2. Access debug interface at: http://localhost:8080")
    else:
        print("❌ Some tests failed. Please fix issues before running automation.")
        
        if tests_passed == 0:
            print("\n💡 Quick fixes:")
            print("1. Start Chrome with: chrome.exe --remote-debugging-port=9222")
            print("2. Make sure you can access https://futures.mexc.com")
            print("3. Check firewall settings for ports 8080 and 9222")

if __name__ == "__main__":
    main()
