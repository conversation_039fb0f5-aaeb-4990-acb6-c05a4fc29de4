#!/usr/bin/env node

/**
 * Test Individual Monitoring Functions
 * Focused tests for each monitoring component
 */

const path = require('path');

// Mock the OptimizedMexcTrader class for testing
class MockOptimizedMexcTrader {
    constructor(port = 9223) {
        this.browser = null;
        this.page = null;
        this.port = port;
        this.isExecutingTrade = false;
        this.monitoringActive = false;
        this.monitoringInterval = null;
        this.lastBalance = null;
        this.balanceUpdateTime = null;
        
        // Test state tracking
        this.testState = {
            currentTab: 'close', // Start on close tab to test switching
            quantityValue: '1.2500', // Start with leftover value
            hasPopups: true, // Start with popups present
            balance: '2.29 USDT'
        };
    }

    // Mock page object with realistic MEXC behavior
    createMockPage() {
        return {
            locator: (selector) => ({
                first: () => ({
                    isVisible: async (options = {}) => {
                        await this.simulateDelay(options.timeout || 100);
                        
                        if (selector.includes('contract-trade-order-form-tab-open') || 
                            selector.includes('contract-trade-order-form-tab-close')) {
                            return true;
                        }
                        if (selector.includes('Quantity') || selector.includes('quantity')) {
                            return true;
                        }
                        if (selector.includes('AssetsItem_num')) {
                            return true;
                        }
                        return Math.random() > 0.3; // 70% chance of being visible
                    },
                    
                    getAttribute: async (attr) => {
                        if (attr === 'class') {
                            if (selector.includes('tab-open')) {
                                return this.testState.currentTab === 'open' ? 'handle_active__Yy6UA' : 'handle_inactive';
                            }
                            if (selector.includes('tab-close')) {
                                return this.testState.currentTab === 'close' ? 'handle_active__Yy6UA' : 'handle_inactive';
                            }
                        }
                        return '';
                    },
                    
                    click: async () => {
                        await this.simulateDelay(50);
                        console.log(`🖱️ MOCK: Clicked ${selector}`);
                        
                        if (selector.includes('tab-open')) {
                            this.testState.currentTab = 'open';
                            console.log('📊 MOCK: Switched to Open tab');
                        }
                        if (selector.includes('tab-close')) {
                            this.testState.currentTab = 'close';
                            console.log('📊 MOCK: Switched to Close tab');
                        }
                    },
                    
                    inputValue: async () => {
                        if (selector.includes('Quantity') || selector.includes('quantity')) {
                            return this.testState.quantityValue;
                        }
                        return '';
                    },
                    
                    fill: async (value) => {
                        await this.simulateDelay(30);
                        console.log(`📝 MOCK: Filled ${selector} with "${value}"`);
                        if (selector.includes('Quantity') || selector.includes('quantity')) {
                            this.testState.quantityValue = value;
                        }
                    },
                    
                    textContent: async () => {
                        if (selector.includes('AssetsItem_num')) {
                            return this.testState.balance;
                        }
                        return '';
                    }
                }),
                
                all: async () => {
                    if (selector.includes('Confirm') || selector.includes('Close') || 
                        selector.includes('Cancel') || selector.includes('OK')) {
                        
                        if (this.testState.hasPopups) {
                            return [{
                                isVisible: async () => true,
                                click: async () => {
                                    await this.simulateDelay(50);
                                    console.log(`🗑️ MOCK: Closed popup ${selector}`);
                                    this.testState.hasPopups = false;
                                }
                            }];
                        }
                    }
                    return [];
                }
            }),
            
            waitForTimeout: async (ms) => {
                await this.simulateDelay(Math.min(ms, 100)); // Cap delay for testing
                console.log(`⏱️ MOCK: Waited ${ms}ms`);
            }
        };
    }

    async simulateDelay(ms) {
        return new Promise(resolve => setTimeout(resolve, Math.min(ms, 50)));
    }

    // Copy the actual monitoring methods from the real class
    async ensureOpenPanel() {
        try {
            const openTab = this.page.locator('span[data-testid="contract-trade-order-form-tab-open"]').first();
            const closeTab = this.page.locator('span[data-testid="contract-trade-order-form-tab-close"]').first();
            
            if (await openTab.isVisible({ timeout: 1000 })) {
                const openTabClass = await openTab.getAttribute('class');
                const closeTabClass = await closeTab.getAttribute('class');
                
                const isOpenActive = openTabClass && openTabClass.includes('handle_active');
                const isCloseActive = closeTabClass && closeTabClass.includes('handle_active');
                
                if (!isOpenActive || isCloseActive) {
                    console.log('🔄 Panel State Monitor: Switching to Open panel...');
                    await openTab.click();
                    await this.page.waitForTimeout(300);
                    console.log('✅ Panel State Monitor: Switched to Open panel');
                } else {
                    console.log('✅ Panel State Monitor: Already on Open panel');
                }
            }
        } catch (error) {
            console.log('⚠️ Panel State Monitor: Could not ensure Open panel -', error.message);
        }
    }

    async closeAnyPopups() {
        const popupSelectors = [
            'button:has-text("Confirm")',
            'button:has-text("Cancel")', 
            'button:has-text("Close")',
            'button:has-text("OK")',
            'button:has-text("Got it")',
            '.modal-close',
            '.ant-modal-close',
            '[aria-label="Close"]',
            '.close-btn',
            '.popup-close'
        ];

        let popupsFound = 0;
        for (const selector of popupSelectors) {
            try {
                const elements = await this.page.locator(selector).all();
                for (const element of elements) {
                    if (await element.isVisible({ timeout: 100 })) {
                        await element.click({ timeout: 200 });
                        popupsFound++;
                        console.log(`🗑️ Field Cleanup Monitor: Closed popup - ${selector}`);
                        await this.page.waitForTimeout(100);
                    }
                }
            } catch (error) {
                // Continue to next selector
            }
        }
        
        if (popupsFound === 0) {
            console.log('✅ Field Cleanup Monitor: No popups found');
        } else {
            console.log(`✅ Field Cleanup Monitor: Closed ${popupsFound} popup(s)`);
        }
    }

    async clearQuantityField() {
        try {
            const quantitySelectors = [
                'text=Quantity(USDT) >> xpath=following::input[1]',
                'input[placeholder*="Quantity"]',
                'input[placeholder*="USDT"]',
                '.quantity-input input',
                '[data-testid*="quantity"] input'
            ];

            let fieldCleared = false;
            for (const selector of quantitySelectors) {
                try {
                    const quantityInput = this.page.locator(selector).first();
                    
                    if (await quantityInput.isVisible({ timeout: 300 })) {
                        const currentValue = await quantityInput.inputValue();
                        if (currentValue && currentValue.trim() !== '') {
                            await quantityInput.click();
                            await quantityInput.fill('');
                            console.log(`🧹 Field Cleanup Monitor: Cleared quantity field (${currentValue}) using selector: ${selector}`);
                            fieldCleared = true;
                            break;
                        }
                    }
                } catch (error) {
                    continue;
                }
            }
            
            if (!fieldCleared) {
                console.log('✅ Field Cleanup Monitor: Quantity field is clean');
            }
        } catch (error) {
            console.log('⚠️ Field Cleanup Monitor: Could not access quantity field -', error.message);
        }
    }

    async performBackgroundMaintenance() {
        try {
            console.log('🧹 Background maintenance...');

            await this.ensureOpenPanel();
            await this.closeAnyPopups();
            await this.clearQuantityField();

            console.log('✅ Background maintenance completed');
        } catch (error) {
            console.log('⚠️ Background maintenance error:', error.message);
        }
    }
}

class MonitoringFunctionTester {
    constructor() {
        this.results = [];
    }

    async runTests() {
        console.log('🔧 Testing Individual Monitoring Functions');
        console.log('==========================================\n');

        await this.testPanelStateMonitor();
        await this.testFieldCleanupMonitor();
        await this.testCompleteMaintenanceCycle();

        this.printResults();
    }

    async testPanelStateMonitor() {
        console.log('📊 Testing Panel State Monitor');
        console.log('-------------------------------');

        const trader = new MockOptimizedMexcTrader();
        trader.page = trader.createMockPage();

        try {
            // Test: Switch from Close to Open
            console.log('🔄 Scenario: Currently on Close tab');
            trader.testState.currentTab = 'close';
            
            await trader.ensureOpenPanel();
            
            if (trader.testState.currentTab === 'open') {
                this.addResult('Panel State Monitor - Tab Switching', true, 'Successfully switched from Close to Open tab');
            } else {
                this.addResult('Panel State Monitor - Tab Switching', false, 'Failed to switch tabs');
            }

        } catch (error) {
            this.addResult('Panel State Monitor', false, error.message);
        }

        console.log('');
    }

    async testFieldCleanupMonitor() {
        console.log('🧹 Testing Field Cleanup Monitor');
        console.log('----------------------------------');

        const trader = new MockOptimizedMexcTrader();
        trader.page = trader.createMockPage();

        try {
            // Test: Clear quantity field
            console.log('🔄 Scenario: Quantity field has leftover value');
            trader.testState.quantityValue = '1.5000';
            
            await trader.clearQuantityField();
            
            if (trader.testState.quantityValue === '') {
                this.addResult('Field Cleanup - Quantity Clear', true, 'Successfully cleared quantity field');
            } else {
                this.addResult('Field Cleanup - Quantity Clear', false, 'Failed to clear quantity field');
            }

            // Test: Close popups
            console.log('🔄 Scenario: Popups are present');
            trader.testState.hasPopups = true;
            
            await trader.closeAnyPopups();
            
            if (!trader.testState.hasPopups) {
                this.addResult('Field Cleanup - Popup Closure', true, 'Successfully closed popups');
            } else {
                this.addResult('Field Cleanup - Popup Closure', false, 'Failed to close popups');
            }

        } catch (error) {
            this.addResult('Field Cleanup Monitor', false, error.message);
        }

        console.log('');
    }

    async testCompleteMaintenanceCycle() {
        console.log('🔄 Testing Complete Maintenance Cycle');
        console.log('--------------------------------------');

        const trader = new MockOptimizedMexcTrader();
        trader.page = trader.createMockPage();

        try {
            // Set up messy state
            trader.testState.currentTab = 'close';
            trader.testState.quantityValue = '2.7500';
            trader.testState.hasPopups = true;

            console.log('🔄 Scenario: Complete maintenance with messy state');
            await trader.performBackgroundMaintenance();

            // Check if everything was cleaned up
            const tabFixed = trader.testState.currentTab === 'open';
            const quantityCleared = trader.testState.quantityValue === '';
            const popupsClosed = !trader.testState.hasPopups;

            if (tabFixed && quantityCleared && popupsClosed) {
                this.addResult('Complete Maintenance Cycle', true, 'All components cleaned up successfully');
            } else {
                this.addResult('Complete Maintenance Cycle', false, 
                    `Cleanup incomplete - Tab: ${tabFixed}, Quantity: ${quantityCleared}, Popups: ${popupsClosed}`);
            }

        } catch (error) {
            this.addResult('Complete Maintenance Cycle', false, error.message);
        }

        console.log('');
    }

    addResult(name, passed, message) {
        this.results.push({ name, passed, message });
    }

    printResults() {
        console.log('📋 Test Results');
        console.log('================');

        const passed = this.results.filter(r => r.passed).length;
        const total = this.results.length;

        this.results.forEach(result => {
            const icon = result.passed ? '✅' : '❌';
            console.log(`${icon} ${result.name}: ${result.message}`);
        });

        console.log(`\n📊 Summary: ${passed}/${total} tests passed`);

        if (passed === total) {
            console.log('🎉 All monitoring functions are working correctly!');
        } else {
            console.log('⚠️ Some monitoring functions need attention.');
        }
    }
}

// Run tests
if (require.main === module) {
    const tester = new MonitoringFunctionTester();
    tester.runTests().catch(error => {
        console.error('❌ Test failed:', error);
        process.exit(1);
    });
}

module.exports = MonitoringFunctionTester;
