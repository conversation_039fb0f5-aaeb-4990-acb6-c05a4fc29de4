class WebhookHandler {
    constructor(configManager, moneyManager, tradingExecutor, marketDataService, positionManager, logger, telegramBot = null) {
        this.configManager = configManager;
        this.moneyManager = moneyManager;
        this.tradingExecutor = tradingExecutor;
        this.marketDataService = marketDataService;
        this.positionManager = positionManager;
        this.logger = logger;
        this.telegramBot = telegramBot;

        this.signalsReceived = 0;
        this.tradesExecuted = 0;
        this.tradesSkipped = 0;
        this.lastSignalTime = null;
        this.tradesHistory = [];
    }

    async handleWebhook(payload) {
        const startTime = Date.now();
        this.signalsReceived++;
        this.lastSignalTime = new Date().toISOString();

        // Log the raw message received with detailed information
        this.logger.info('📨 RAW WEBHOOK MESSAGE RECEIVED', {
            signalNumber: this.signalsReceived,
            timestamp: this.lastSignalTime,
            payloadType: typeof payload,
            payloadKeys: payload ? Object.keys(payload) : 'null',
            payloadLength: payload ? Object.keys(payload).length : 0,
            rawPayload: payload
        });

        this.logger.info('Webhook received', { payload, signalNumber: this.signalsReceived });

        try {
            const config = this.configManager.getConfig();

            this.logger.info('🔧 WEBHOOK PROCESSING CONFIG', {
                useNewTradingViewFormat: config.useNewTradingViewFormat,
                slTpCalculationSource: config.slTpCalculationSource,
                botActive: config.botActive
            });

            // Check if we should use the new TradingView format
            if (config.useNewTradingViewFormat) {
                this.logger.info('🆕 Using NEW TradingView format processing');

                // Handle empty payload as close message (ignore as per specification)
                if (!payload || Object.keys(payload).length === 0) {
                    this.logger.info('📭 Empty payload detected - this is a CLOSE message in new format');
                    this.logger.info('🚫 Ignoring close message as per configuration (handled by internal TP/SL system)');
                    return {
                        success: true,
                        message: 'Close message ignored. Close mechanism handled by TP/SL system.',
                        signal: payload,
                        timestamp: this.lastSignalTime,
                        type: 'close_ignored',
                        format: 'new_tradingview'
                    };
                }

                // Check if this is the new TradingView format with direct SL/TP values
                if (this.isNewTradingViewFormat(payload)) {
                    this.logger.info('✅ Detected NEW format with direct SL/TP values');
                    return await this.handleNewTradingViewFormat(payload, startTime);
                }

                // Handle "New SL" messages
                if (this.isNewSLMessage(payload)) {
                    this.logger.info('✅ Detected NEW SL message');
                    return await this.handleNewSLMessage(payload);
                }

                this.logger.info('⚠️ NEW format enabled but message doesn\'t match new format patterns');
            } else {
                this.logger.info('🔄 Using LEGACY TradingView format processing');
            }
            // Validate payload - use different validation based on format
            let validationResult;
            if (config.useNewTradingViewFormat) {
                this.logger.info('🔍 Using NEW format validation');
                // For new format, use more flexible validation
                validationResult = this.validateNewFormatPayload(payload);
            } else {
                this.logger.info('🔍 Using LEGACY format validation');
                // For old format, use strict validation
                validationResult = this.validatePayload(payload);
            }

            this.logger.info('📋 VALIDATION RESULT', {
                valid: validationResult.valid,
                error: validationResult.error || 'none'
            });

            if (!validationResult.valid) {
                throw new Error(`Invalid payload: ${validationResult.error}`);
            }

            // Check if bot is active
            if (!config.botActive) {
                this.logger.info('Bot is inactive, ignoring signal');
                return {
                    success: false,
                    message: 'Bot is inactive',
                    signal: payload,
                    timestamp: this.lastSignalTime
                };
            }

            // Check if system is properly configured
            if (!this.configManager.isConfigured()) {
                throw new Error('System not properly configured. Please set API keys and money management settings.');
            }

            // Ignore all 'close' signals from TradingView webhook
            if (payload.trade && payload.trade.toLowerCase() === 'close') {
                this.logger.info('Ignoring close signal from TradingView webhook as per configuration');
                return {
                    success: true,
                    message: 'Close signals are ignored. Close mechanism handled by TP/SL.',
                    signal: payload,
                    timestamp: this.lastSignalTime
                };
            }

            // Process the signal based on format
            let processedSignal;
            if (config.useNewTradingViewFormat) {
                this.logger.info('⚙️ Processing with NEW format signal processor');
                // Use new format processing
                processedSignal = this.processNewFormatSignal(payload);
            } else {
                this.logger.info('⚙️ Processing with LEGACY format signal processor');
                // Use old format processing
                processedSignal = this.processSignal(payload);
            }

            this.logger.info('📊 PROCESSED SIGNAL', {
                symbol: processedSignal.symbol,
                orderType: processedSignal.orderType,
                action: processedSignal.action,
                direction: processedSignal.direction,
                price: processedSignal.price,
                stopLoss: processedSignal.stopLoss,
                takeProfitsCount: processedSignal.takeProfits ? processedSignal.takeProfits.length : 0
            });

            // Validate execution conditions
            const executionValidation = await this.validateExecution(processedSignal);
            if (!executionValidation.valid) {
                this.tradesSkipped++;
                this.logger.info('Trade skipped due to validation failure', {
                    reason: executionValidation.reason,
                    details: executionValidation.details
                });

                const skippedRecord = {
                    id: `skipped_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    timestamp: this.lastSignalTime,
                    signal: payload,
                    processedSignal,
                    positionSize: 'Skipped',
                    tradeResult: { executionTime: 0 },
                    reason: executionValidation.reason,
                    details: executionValidation.details,
                    success: false,
                    skipped: true
                };

                this.tradesHistory.unshift(skippedRecord);
                if (this.tradesHistory.length > 100) {
                    this.tradesHistory = this.tradesHistory.slice(0, 100);
                }

                return {
                    success: false,
                    message: `Trade skipped: ${executionValidation.reason}`,
                    signal: payload,
                    processedSignal,
                    reason: executionValidation.reason,
                    details: executionValidation.details,
                    skipped: true,
                    timestamp: this.lastSignalTime
                };
            }

            // Calculate position size using money management
            const positionSize = await this.moneyManager.calculatePositionSize(processedSignal);

            // Force close existing positions before opening new ones (only for open trades)
            let forceCloseResult = null;
            if (processedSignal.action === 'open') {
                this.logger.info('Checking for existing positions to force close before opening new position');

                forceCloseResult = await this.positionManager.forceCloseAllPositions('force_close_for_new_trade');

                if (forceCloseResult.closedPositions.length > 0) {
                    this.logger.info('Force closed existing positions', {
                        closedCount: forceCloseResult.closedPositions.length,
                        failedCount: forceCloseResult.failedPositions.length
                    });

                    // Send Telegram notification about force close
                    if (this.telegramBot && this.telegramBot.isReady()) {
                        await this.telegramBot.sendForceCloseAlert(
                            forceCloseResult.closedPositions,
                            `${processedSignal.direction} ${processedSignal.symbol}`
                        );
                    }
                }

                if (forceCloseResult.failedPositions.length > 0) {
                    this.logger.error('Some positions failed to force close', {
                        failedPositions: forceCloseResult.failedPositions
                    });

                    // Send warning about failed force closes
                    if (this.telegramBot && this.telegramBot.isReady()) {
                        await this.telegramBot.sendSystemAlert(
                            'Force Close Partial Failure',
                            `Failed to close ${forceCloseResult.failedPositions.length} positions before opening new trade. Check system status.`,
                            'WARNING'
                        );
                    }
                }
            }

            // Execute the trade with timeout
            const executionStartTime = Date.now();

            const tradeResult = await Promise.race([
                this.tradingExecutor.executeTrade({
                    ...processedSignal,
                    quantity: positionSize.toString()
                }),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Execution timeout')), config.maxExecutionTime)
                )
            ]);

            this.tradesExecuted++;
            const executionTime = Date.now() - startTime;

            // Add position to monitoring if trade was successful and it's an open order
            let positionAdded = null;
            if (tradeResult.success && processedSignal.action === 'open') {
                try {
                    // Determine entry price based on configuration
                    let entryPrice;
                    const config = this.configManager.getConfig();

                    if (config.slTpCalculationSource === 'tradingview_last_price') {
                        // Use last_price from TradingView webhook
                        entryPrice = processedSignal.price;
                        this.logger.info('Using TradingView last_price for SL/TP calculations', {
                            price: entryPrice,
                            source: 'tradingview_webhook'
                        });
                    } else {
                        // Default: Use current price from MEXC exchange
                        const currentPrice = await this.marketDataService.getCurrentPrice(processedSignal.symbol);
                        entryPrice = currentPrice.price;
                        this.logger.info('Using MEXC exchange price for SL/TP calculations', {
                            price: entryPrice,
                            source: 'mexc_exchange'
                        });
                    }

                    positionAdded = await this.positionManager.addPosition({
                        symbol: processedSignal.symbol,
                        direction: processedSignal.direction, // Use actual direction from signal
                        entryPrice: entryPrice,
                        quantity: parseFloat(positionSize),
                        tradeId: `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                        requestId: tradeResult.requestId,
                        originalSignal: processedSignal.originalSignal // Pass original signal for reference
                    });

                    this.logger.info('Position added to SL/TP monitoring', {
                        positionId: positionAdded?.id,
                        symbol: processedSignal.symbol,
                        entryPrice: entryPrice,
                        quantity: positionSize,
                        calculationSource: config.slTpCalculationSource
                    });
                } catch (error) {
                    this.logger.error('Failed to add position for SL/TP monitoring', {
                        error: error.message,
                        tradeResult
                    });
                }
            }

            // Record trade in history
            const tradeRecord = {
                id: `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                timestamp: this.lastSignalTime,
                signal: payload,
                processedSignal,
                positionSize,
                tradeResult,
                executionTime,
                success: tradeResult.success,
                positionId: positionAdded?.id,
                slTpEnabled: this.configManager.getConfig().slTpEnabled,
                forceCloseResult: forceCloseResult // Include force close information
            };

            this.tradesHistory.unshift(tradeRecord); // Add to beginning
            
            // Keep only last 100 trades
            if (this.tradesHistory.length > 100) {
                this.tradesHistory = this.tradesHistory.slice(0, 100);
            }

            this.logger.info('Trade executed', { 
                tradeRecord,
                totalExecutionTime: executionTime
            });

            // Handle different result types
            let message;
            if (tradeResult.skipped) {
                message = `Trade skipped: ${tradeResult.skipReason}`;
            } else if (tradeResult.success) {
                message = 'Trade executed successfully';
            } else {
                message = 'Trade execution failed';
            }

            return {
                success: tradeResult.success,
                skipped: tradeResult.skipped || false,
                skipReason: tradeResult.skipReason,
                message: message,
                signal: payload,
                processedSignal,
                positionSize,
                tradeResult,
                executionTime,
                tradeId: tradeRecord.id,
                timestamp: this.lastSignalTime
            };

        } catch (error) {
            const executionTime = Date.now() - startTime;

            this.logger.error('Webhook processing failed', {
                error: error.message,
                stack: error.stack,
                payload,
                executionTime
            });

            // Try to process signal for error record, fallback if it fails
            let processedSignalForError;
            try {
                const config = this.configManager.getConfig();
                if (config.useNewTradingViewFormat) {
                    processedSignalForError = this.processNewFormatSignal(payload);
                } else {
                    processedSignalForError = this.processSignal(payload);
                }
            } catch (processError) {
                processedSignalForError = { orderType: 'Failed to process' };
            }

            // Record failed trade with more details for frontend display
            const failedTradeRecord = {
                id: `failed_trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                timestamp: this.lastSignalTime,
                signal: payload,
                processedSignal: processedSignalForError,
                positionSize: 'N/A',
                tradeResult: { executionTime },
                error: error.message,
                executionTime,
                success: false
            };

            this.tradesHistory.unshift(failedTradeRecord);
            
            if (this.tradesHistory.length > 100) {
                this.tradesHistory = this.tradesHistory.slice(0, 100);
            }

            return {
                success: false,
                error: error.message,
                signal: payload,
                executionTime,
                tradeId: failedTradeRecord.id,
                timestamp: this.lastSignalTime
            };
        }
    }

    validatePayload(payload) {
        if (!payload || typeof payload !== 'object') {
            return { valid: false, error: 'Payload must be an object' };
        }

        // Required fields for new TradingView format
        const requiredFields = ['symbol', 'trade', 'last_price'];
        for (const field of requiredFields) {
            if (!payload[field]) {
                return { valid: false, error: `Missing required field: ${field}` };
            }
        }

        // Leverage is now optional (will use default from config if not provided)

        // Validate symbol (support both TRUUSDT and TRUUSDT.P)
        const supportedSymbols = ['TRUUSDT', 'TRUUSDT.P'];
        if (!supportedSymbols.includes(payload.symbol)) {
            return { valid: false, error: `Unsupported symbol: ${payload.symbol}. Supported symbols: ${supportedSymbols.join(', ')}.` };
        }

        // Validate trade action - New TradingView format supports 'buy' and 'sell'
        const validActions = [
            'buy', 'sell', // New TradingView format
            'open', 'close', 'long', 'short', 'exit', // Legacy support
            'open_long', 'open_short', 'close_long', 'close_short',
            'buy_close', 'sell_open'
        ];
        if (!validActions.includes(payload.trade.toLowerCase())) {
            return { valid: false, error: `Invalid trade action: ${payload.trade}. Supported actions: ${validActions.join(', ')}.` };
        }

        // Validate price
        const price = parseFloat(payload.last_price);
        if (isNaN(price) || price <= 0) {
            return { valid: false, error: `Invalid price: ${payload.last_price}` };
        }

        // Validate leverage if provided (support both spellings)
        const leverageValue = payload.leverage || payload.leverege;
        if (leverageValue) {
            const leverage = parseFloat(leverageValue);
            if (isNaN(leverage) || leverage <= 0 || leverage > 100) {
                return { valid: false, error: `Invalid leverage: ${leverageValue}. Must be between 1 and 100.` };
            }
        }

        return { valid: true };
    }

    processSignal(payload) {
        const { symbol, trade, last_price } = payload;
        const leverage = payload.leverage || payload.leverege; // Support both spellings
        const config = this.configManager.getConfig();

        // Determine order type and direction based on trade field
        let orderType, action, direction;
        const tradeType = trade.toLowerCase();

        switch (tradeType) {
            case 'buy':
            case 'open':
            case 'long':
            case 'open_long':
                orderType = 'Open Long';
                action = 'open';
                direction = 'long';
                break;
            case 'sell':
            case 'open_short':
            case 'short':
            case 'sell_open':
                orderType = 'Open Short';
                action = 'open';
                direction = 'short';
                break;
            case 'close':
            case 'exit':
            case 'close_long':
                orderType = 'Close Long';
                action = 'close';
                direction = 'long';
                break;
            case 'close_short':
            case 'buy_close':
                orderType = 'Close Short';
                action = 'close';
                direction = 'short';
                break;
            default:
                throw new Error(`Unsupported trade type: ${trade}`);
        }

        // Normalize symbol (remove .P suffix for internal processing)
        const normalizedSymbol = symbol.replace('.P', '');

        return {
            symbol: normalizedSymbol.toUpperCase(),
            orderType,
            action,
            direction,
            price: parseFloat(last_price),
            leverage: leverage ? parseFloat(leverage) : config.defaultLeverage,
            originalSignal: payload
        };
    }

    getTotalSignalsReceived() {
        return this.signalsReceived;
    }

    async validateExecution(processedSignal) {
        const config = this.configManager.getConfig();

        try {
            // Get current market price
            const currentPriceData = await this.marketDataService.getCurrentPrice(processedSignal.symbol);
            const currentPrice = currentPriceData.price;
            const expectedPrice = processedSignal.price;

            // Validate price difference
            const priceValidation = this.marketDataService.validatePriceDifference(
                currentPrice,
                expectedPrice,
                config.maxPriceDifference
            );

            if (!priceValidation.valid) {
                return {
                    valid: false,
                    reason: 'Price difference too large',
                    details: {
                        currentPrice,
                        expectedPrice,
                        difference: priceValidation.difference,
                        maxAllowed: config.maxPriceDifference
                    }
                };
            }

            return {
                valid: true,
                currentPrice,
                expectedPrice,
                priceDifference: priceValidation.difference
            };

        } catch (error) {
            return {
                valid: false,
                reason: 'Market data validation failed',
                details: {
                    error: error.message
                }
            };
        }
    }

    getTotalSignalsReceived() {
        return this.signalsReceived;
    }

    getTotalTradesExecuted() {
        return this.tradesExecuted;
    }

    getTotalTradesSkipped() {
        return this.tradesSkipped;
    }

    getLastSignalTime() {
        return this.lastSignalTime;
    }

    getTradesHistory() {
        return this.tradesHistory;
    }

    // Get statistics
    getStatistics() {
        const successfulTrades = this.tradesHistory.filter(t => t.success && !t.skipped).length;
        const failedTrades = this.tradesHistory.filter(t => !t.success && !t.skipped).length;
        const skippedTrades = this.tradesHistory.filter(t => t.skipped).length;

        const executedTrades = this.tradesHistory.filter(t => !t.skipped);
        const avgExecutionTime = executedTrades.length > 0
            ? executedTrades.reduce((sum, t) => sum + (t.executionTime || 0), 0) / executedTrades.length
            : 0;

        return {
            totalSignalsReceived: this.signalsReceived,
            totalTradesExecuted: this.tradesExecuted,
            totalTradesSkipped: this.tradesSkipped,
            successfulTrades,
            failedTrades,
            skippedTrades,
            successRate: executedTrades.length > 0 ? (successfulTrades / executedTrades.length * 100).toFixed(2) : 0,
            skipRate: this.signalsReceived > 0 ? (skippedTrades / this.signalsReceived * 100).toFixed(2) : 0,
            averageExecutionTime: Math.round(avgExecutionTime),
            lastSignalTime: this.lastSignalTime,
            totalTradesInHistory: this.tradesHistory.length,
            activePositions: this.positionManager.getActivePositions().length
        };
    }

    // Reset statistics (for testing)
    resetStatistics() {
        this.signalsReceived = 0;
        this.tradesExecuted = 0;
        this.lastSignalTime = null;
        this.tradesHistory = [];
    }

    // Check if payload is the new TradingView format with direct SL/TP values
    isNewTradingViewFormat(payload) {
        return payload &&
               typeof payload === 'object' &&
               payload.symbol &&
               payload.trade &&
               payload.last_price &&
               (payload.sl || payload.tp1 || payload.tp2 || payload.tp3);
    }

    // Check if payload is a "New SL" message
    isNewSLMessage(payload) {
        return typeof payload === 'string' && payload.startsWith('New SL:');
    }

    // Handle new TradingView format with direct SL/TP values
    async handleNewTradingViewFormat(payload, startTime) {
        this.logger.info('Processing new TradingView format with direct SL/TP values', { payload });

        try {
            // Validate new format payload
            const validationResult = this.validateNewFormatPayload(payload);
            if (!validationResult.valid) {
                throw new Error(`Invalid new format payload: ${validationResult.error}`);
            }

            // Check if bot is active
            const config = this.configManager.getConfig();
            if (!config.botActive) {
                this.logger.info('Bot is inactive, ignoring signal');
                return {
                    success: false,
                    message: 'Bot is inactive',
                    signal: payload,
                    timestamp: this.lastSignalTime
                };
            }

            // Check if system is properly configured
            if (!this.configManager.isConfigured()) {
                throw new Error('System not properly configured. Please set API keys and money management settings.');
            }

            // Process the new format signal
            const processedSignal = this.processNewFormatSignal(payload);

            // Validate execution conditions
            const executionValidation = await this.validateExecution(processedSignal);
            if (!executionValidation.valid) {
                this.tradesSkipped++;
                this.logger.info('Trade skipped due to validation failure', {
                    reason: executionValidation.reason,
                    details: executionValidation.details
                });

                const skippedRecord = {
                    id: `skipped_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    timestamp: this.lastSignalTime,
                    signal: payload,
                    processedSignal,
                    positionSize: 'Skipped',
                    tradeResult: { executionTime: 0 },
                    reason: executionValidation.reason,
                    details: executionValidation.details,
                    success: false,
                    skipped: true,
                    format: 'new_tradingview'
                };

                this.tradesHistory.unshift(skippedRecord);
                if (this.tradesHistory.length > 100) {
                    this.tradesHistory = this.tradesHistory.slice(0, 100);
                }

                return {
                    success: false,
                    message: `Trade skipped: ${executionValidation.reason}`,
                    signal: payload,
                    processedSignal,
                    reason: executionValidation.reason,
                    details: executionValidation.details,
                    skipped: true,
                    timestamp: this.lastSignalTime
                };
            }

            // Calculate position size using money management
            const positionSize = await this.moneyManager.calculatePositionSize(processedSignal);

            // Force close existing positions before opening new ones (only for open trades)
            let forceCloseResult = null;
            if (processedSignal.action === 'open') {
                this.logger.info('Checking for existing positions to force close before opening new position');

                forceCloseResult = await this.positionManager.forceCloseAllPositions('force_close_for_new_trade');

                if (forceCloseResult.closedPositions.length > 0) {
                    this.logger.info('Force closed existing positions', {
                        closedCount: forceCloseResult.closedPositions.length,
                        failedCount: forceCloseResult.failedPositions.length
                    });

                    // Send Telegram notification about force close
                    if (this.telegramBot && this.telegramBot.isReady()) {
                        await this.telegramBot.sendForceCloseAlert(
                            forceCloseResult.closedPositions,
                            `${processedSignal.direction} ${processedSignal.symbol}`
                        );
                    }
                }

                if (forceCloseResult.failedPositions.length > 0) {
                    this.logger.error('Some positions failed to force close', {
                        failedPositions: forceCloseResult.failedPositions
                    });

                    // Send warning about failed force closes
                    if (this.telegramBot && this.telegramBot.isReady()) {
                        await this.telegramBot.sendSystemAlert(
                            'Force Close Partial Failure',
                            `Failed to close ${forceCloseResult.failedPositions.length} positions before opening new trade. Check system status.`,
                            'WARNING'
                        );
                    }
                }
            }

            // Execute the trade with timeout
            const executionStartTime = Date.now();

            const tradeResult = await Promise.race([
                this.tradingExecutor.executeTrade({
                    ...processedSignal,
                    quantity: positionSize.toString()
                }),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Execution timeout')), config.maxExecutionTime)
                )
            ]);

            this.tradesExecuted++;
            const executionTime = Date.now() - startTime;

            // Add position to monitoring if trade was successful and it's an open order
            let positionAdded = null;
            if (tradeResult.success && processedSignal.action === 'open') {
                try {
                    // For new format, use the provided entry price (last_price)
                    const entryPrice = processedSignal.price;

                    positionAdded = await this.positionManager.addPositionWithDirectSLTP({
                        symbol: processedSignal.symbol,
                        direction: processedSignal.direction,
                        entryPrice: entryPrice,
                        quantity: parseFloat(positionSize),
                        tradeId: `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                        requestId: tradeResult.requestId,
                        originalSignal: payload,
                        // Direct SL/TP values from TradingView
                        directSLTP: {
                            stopLoss: processedSignal.stopLoss,
                            takeProfits: processedSignal.takeProfits
                        }
                    });

                    this.logger.info('Position added to SL/TP monitoring with direct values', {
                        positionId: positionAdded?.id,
                        symbol: processedSignal.symbol,
                        entryPrice: entryPrice,
                        quantity: positionSize,
                        stopLoss: processedSignal.stopLoss,
                        takeProfits: processedSignal.takeProfits.length
                    });
                } catch (error) {
                    this.logger.error('Failed to add position for SL/TP monitoring', {
                        error: error.message,
                        tradeResult
                    });
                }
            }

            // Record trade in history
            const tradeRecord = {
                id: `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                timestamp: this.lastSignalTime,
                signal: payload,
                processedSignal,
                positionSize,
                tradeResult,
                executionTime,
                success: tradeResult.success,
                positionId: positionAdded?.id,
                slTpEnabled: true, // Always enabled for new format
                forceCloseResult: forceCloseResult,
                format: 'new_tradingview'
            };

            this.tradesHistory.unshift(tradeRecord);

            // Keep only last 100 trades
            if (this.tradesHistory.length > 100) {
                this.tradesHistory = this.tradesHistory.slice(0, 100);
            }

            this.logger.info('New format trade executed', {
                tradeRecord,
                totalExecutionTime: executionTime
            });

            // Handle different result types
            let message;
            if (tradeResult.skipped) {
                message = `Trade skipped: ${tradeResult.skipReason}`;
            } else if (tradeResult.success) {
                message = 'New format trade executed successfully';
            } else {
                message = 'New format trade execution failed';
            }

            return {
                success: tradeResult.success,
                skipped: tradeResult.skipped || false,
                skipReason: tradeResult.skipReason,
                message: message,
                signal: payload,
                processedSignal,
                positionSize,
                tradeResult,
                executionTime,
                tradeId: tradeRecord.id,
                timestamp: this.lastSignalTime,
                format: 'new_tradingview'
            };

        } catch (error) {
            const executionTime = Date.now() - startTime;

            this.logger.error('New format webhook processing failed', {
                error: error.message,
                stack: error.stack,
                payload,
                executionTime
            });

            // Try to process signal for error record, fallback if it fails
            let processedSignalForError;
            try {
                processedSignalForError = this.processNewFormatSignal(payload);
            } catch (processError) {
                processedSignalForError = { orderType: 'Failed to process new format' };
            }

            // Record failed trade with more details for frontend display
            const failedTradeRecord = {
                id: `failed_trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                timestamp: this.lastSignalTime,
                signal: payload,
                processedSignal: processedSignalForError,
                positionSize: 'N/A',
                tradeResult: { executionTime },
                error: error.message,
                executionTime,
                success: false,
                format: 'new_tradingview'
            };

            this.tradesHistory.unshift(failedTradeRecord);

            if (this.tradesHistory.length > 100) {
                this.tradesHistory = this.tradesHistory.slice(0, 100);
            }

            return {
                success: false,
                error: error.message,
                signal: payload,
                executionTime,
                tradeId: failedTradeRecord.id,
                timestamp: this.lastSignalTime,
                format: 'new_tradingview'
            };
        }
    }

    // Handle "New SL" messages
    async handleNewSLMessage(payload) {
        this.logger.info('Processing New SL message', { payload });

        try {
            // Extract SL value from message like "New SL: 0.8487"
            const slMatch = payload.match(/New SL:\s*([\d.]+)/);
            if (!slMatch) {
                throw new Error('Invalid New SL message format');
            }

            const newSL = parseFloat(slMatch[1]);
            if (isNaN(newSL) || newSL <= 0) {
                throw new Error(`Invalid SL value: ${slMatch[1]}`);
            }

            // Update all active positions with new SL
            const updateResult = await this.positionManager.updateAllStopLosses(newSL);

            this.logger.info('Updated stop losses for active positions', {
                newSL,
                updatedPositions: updateResult.updated,
                failedPositions: updateResult.failed
            });

            return {
                success: true,
                message: `Updated SL to ${newSL} for ${updateResult.updated} positions`,
                newSL,
                updatedPositions: updateResult.updated,
                failedPositions: updateResult.failed,
                timestamp: this.lastSignalTime,
                type: 'sl_update'
            };

        } catch (error) {
            this.logger.error('Failed to process New SL message', {
                error: error.message,
                payload
            });

            return {
                success: false,
                error: error.message,
                message: 'Failed to update stop loss',
                timestamp: this.lastSignalTime,
                type: 'sl_update'
            };
        }
    }

    // Validate new format payload
    validateNewFormatPayload(payload) {
        // Handle empty payloads or non-objects
        if (!payload) {
            return { valid: false, error: 'Empty payload received' };
        }

        if (typeof payload !== 'object') {
            return { valid: false, error: 'Payload must be an object' };
        }

        // Check if payload is completely empty
        if (Object.keys(payload).length === 0) {
            return { valid: false, error: 'Empty payload object received' };
        }

        // For new format, symbol is optional - if missing, default to TRUUSDT
        // Required fields for new TradingView format
        const requiredFields = ['trade', 'last_price'];
        for (const field of requiredFields) {
            if (!payload[field]) {
                return { valid: false, error: `Missing required field: ${field}` };
            }
        }

        // Validate symbol if provided (support both TRUUSDT and TRUUSDT.P)
        if (payload.symbol) {
            const supportedSymbols = ['TRUUSDT', 'TRUUSDT.P'];
            if (!supportedSymbols.includes(payload.symbol)) {
                return { valid: false, error: `Unsupported symbol: ${payload.symbol}. Supported symbols: ${supportedSymbols.join(', ')}.` };
            }
        }

        // Validate trade action - New format supports 'buy' and 'sell'
        const validActions = ['buy', 'sell'];
        if (!validActions.includes(payload.trade.toLowerCase())) {
            return { valid: false, error: `Invalid trade action: ${payload.trade}. Supported actions: ${validActions.join(', ')}.` };
        }

        // Validate price
        const price = this.parsePrice(payload.last_price);
        if (isNaN(price) || price <= 0) {
            return { valid: false, error: `Invalid price: ${payload.last_price}` };
        }

        // Validate SL if provided
        if (payload.sl) {
            const sl = this.parsePrice(payload.sl);
            if (isNaN(sl) || sl <= 0) {
                return { valid: false, error: `Invalid SL: ${payload.sl}` };
            }
        }

        // Validate TP levels if provided
        for (const tpKey of ['tp1', 'tp2', 'tp3']) {
            if (payload[tpKey]) {
                if (!payload[tpKey].price || !payload[tpKey].percent) {
                    return { valid: false, error: `Invalid ${tpKey}: missing price or percent` };
                }

                const tpPrice = this.parsePrice(payload[tpKey].price);
                if (isNaN(tpPrice) || tpPrice <= 0) {
                    return { valid: false, error: `Invalid ${tpKey} price: ${payload[tpKey].price}` };
                }

                const tpPercent = parseFloat(payload[tpKey].percent.replace('%', ''));
                if (isNaN(tpPercent) || tpPercent < 0 || tpPercent > 100) {
                    return { valid: false, error: `Invalid ${tpKey} percent: ${payload[tpKey].percent}` };
                }
            }
        }

        return { valid: true };
    }

    // Process new format signal
    processNewFormatSignal(payload) {
        const { trade, last_price } = payload;
        const config = this.configManager.getConfig();

        // Default to TRUUSDT if symbol is not provided
        const symbol = payload.symbol || 'TRUUSDT';

        // Determine order type and direction based on trade field
        let orderType, action, direction;
        const tradeType = trade.toLowerCase();

        switch (tradeType) {
            case 'buy':
                orderType = 'Open Long';
                action = 'open';
                direction = 'long';
                break;
            case 'sell':
                orderType = 'Open Short';
                action = 'open';
                direction = 'short';
                break;
            default:
                throw new Error(`Unsupported trade type: ${trade}`);
        }

        // Parse SL and TP values
        const stopLoss = payload.sl ? this.parsePrice(payload.sl) : null;
        const takeProfits = [];

        // Process TP levels
        for (const tpKey of ['tp1', 'tp2', 'tp3']) {
            if (payload[tpKey] && payload[tpKey].price && payload[tpKey].percent) {
                const tpPrice = this.parsePrice(payload[tpKey].price);
                const tpPercent = parseFloat(payload[tpKey].percent.replace('%', ''));

                if (!isNaN(tpPrice) && tpPrice > 0 && !isNaN(tpPercent) && tpPercent >= 0) {
                    takeProfits.push({
                        level: parseInt(tpKey.replace('tp', '')),
                        price: tpPrice,
                        percent: tpPercent,
                        enabled: tpPercent > 0 // Only enable if percent > 0
                    });
                }
            }
        }

        // Normalize symbol (remove .P suffix for internal processing)
        const normalizedSymbol = symbol.replace('.P', '');

        return {
            symbol: normalizedSymbol.toUpperCase(),
            orderType,
            action,
            direction,
            price: this.parsePrice(last_price),
            leverage: config.defaultLeverage, // Use default leverage
            stopLoss,
            takeProfits,
            originalSignal: payload
        };
    }

    // Helper method to parse price values (handles ℮ prefix and template variables)
    parsePrice(priceStr) {
        if (typeof priceStr === 'number') return priceStr;
        if (typeof priceStr === 'string') {
            // Remove ℮ prefix if present
            let cleanPrice = priceStr.replace(/^℮/, '');

            // Handle template variables like {{0.03184}}
            const templateMatch = cleanPrice.match(/\{\{([^}]+)\}\}/);
            if (templateMatch) {
                cleanPrice = templateMatch[1];
            }

            return parseFloat(cleanPrice);
        }
        return NaN;
    }
}

module.exports = WebhookHandler;
