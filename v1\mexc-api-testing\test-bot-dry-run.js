const { chromium } = require('playwright');
require('dotenv').config();

async function testBotDryRun() {
    console.log('🧪 Testing Fast MEXC Bot (Dry Run - No Real Orders)');
    console.log('====================================================');
    
    const startTime = Date.now();
    let browser, page;
    
    try {
        // Initialize browser with speed optimizations
        console.log('🚀 Launching browser...');
        browser = await chromium.launch({
            headless: false, // Show browser for testing
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ]
        });

        page = await browser.newPage();
        await page.setViewportSize({ width: 1920, height: 1080 });

        // Block resources for speed
        await page.route('**/*', (route) => {
            const resourceType = route.request().resourceType();
            if (['image', 'stylesheet', 'font', 'media'].includes(resourceType)) {
                route.abort();
            } else {
                route.continue();
            }
        });

        const initTime = Date.now() - startTime;
        console.log(`✅ Browser initialized in ${initTime}ms`);

        // Test 1: Navigate to MEXC futures
        console.log('\n📊 Test 1: Navigation Speed');
        const navStart = Date.now();
        
        await page.goto('https://futures.mexc.com/exchange/TRU_USDT', {
            waitUntil: 'domcontentloaded',
            timeout: 5000
        });

        const navTime = Date.now() - navStart;
        console.log(`✅ Navigated to futures page in ${navTime}ms`);

        // Test 2: Check if login is required
        console.log('\n🔐 Test 2: Login Status Check');
        
        try {
            // Look for login indicators
            const loginRequired = await page.locator('text=Login, text=Sign In, .login-btn').first().isVisible({ timeout: 2000 });
            
            if (loginRequired) {
                console.log('⚠️ Login required - would need credentials for real trading');
                
                // Navigate to login page to test speed
                const loginNavStart = Date.now();
                await page.goto('https://www.mexc.com/login', {
                    waitUntil: 'domcontentloaded',
                    timeout: 3000
                });
                const loginNavTime = Date.now() - loginNavStart;
                console.log(`✅ Login page loaded in ${loginNavTime}ms`);
                
            } else {
                console.log('✅ Already logged in or no login required');
            }
        } catch (error) {
            console.log('⚠️ Could not determine login status');
        }

        // Test 3: Trading interface elements
        console.log('\n🎯 Test 3: Trading Interface Detection');
        
        await page.goto('https://futures.mexc.com/exchange/TRU_USDT', {
            waitUntil: 'domcontentloaded',
            timeout: 3000
        });

        // Look for common trading interface elements
        const elements = [
            { name: 'Buy Button', selectors: ['.buy-btn', '.long-btn', '[data-side="buy"]', 'button:has-text("Buy")', 'button:has-text("Long")'] },
            { name: 'Sell Button', selectors: ['.sell-btn', '.short-btn', '[data-side="sell"]', 'button:has-text("Sell")', 'button:has-text("Short")'] },
            { name: 'Quantity Input', selectors: ['.quantity-input', 'input[name="quantity"]', 'input[name="amount"]', '.amount-input'] },
            { name: 'Market Order', selectors: ['.market-order', '[data-type="market"]', 'button:has-text("Market")'] },
            { name: 'Price Display', selectors: ['.price-display', '.current-price', '.last-price'] }
        ];

        for (const element of elements) {
            let found = false;
            for (const selector of element.selectors) {
                try {
                    const isVisible = await page.locator(selector).first().isVisible({ timeout: 1000 });
                    if (isVisible) {
                        console.log(`✅ ${element.name} found: ${selector}`);
                        found = true;
                        break;
                    }
                } catch {
                    continue;
                }
            }
            if (!found) {
                console.log(`❌ ${element.name} not found`);
            }
        }

        // Test 4: Speed simulation
        console.log('\n⚡ Test 4: Speed Simulation (No Real Orders)');
        
        const speedTests = [
            { action: 'Click Buy Button', selectors: ['.buy-btn', '.long-btn', 'button:has-text("Buy")'] },
            { action: 'Select Market Order', selectors: ['.market-order', '[data-type="market"]'] },
            { action: 'Find Quantity Input', selectors: ['.quantity-input', 'input[name="quantity"]'] }
        ];

        for (const test of speedTests) {
            const testStart = Date.now();
            let success = false;
            
            for (const selector of test.selectors) {
                try {
                    const element = page.locator(selector).first();
                    const isVisible = await element.isVisible({ timeout: 500 });
                    if (isVisible) {
                        // Don't actually click, just measure detection time
                        const testTime = Date.now() - testStart;
                        console.log(`✅ ${test.action}: ${testTime}ms (${selector})`);
                        success = true;
                        break;
                    }
                } catch {
                    continue;
                }
            }
            
            if (!success) {
                const testTime = Date.now() - testStart;
                console.log(`❌ ${test.action}: Failed after ${testTime}ms`);
            }
        }

        // Test 5: Overall performance assessment
        console.log('\n📈 Test 5: Performance Assessment');
        
        const totalTime = Date.now() - startTime;
        console.log(`Total test time: ${totalTime}ms`);
        
        const performance = {
            browserInit: initTime,
            navigation: navTime,
            totalTime: totalTime,
            speedRating: totalTime < 3000 ? 'Excellent' : totalTime < 5000 ? 'Good' : 'Needs Improvement'
        };

        console.log('\n📊 PERFORMANCE SUMMARY:');
        console.log(`Browser Init: ${performance.browserInit}ms`);
        console.log(`Navigation: ${performance.navigation}ms`);
        console.log(`Total Time: ${performance.totalTime}ms`);
        console.log(`Speed Rating: ${performance.speedRating}`);

        if (performance.totalTime < 2000) {
            console.log('🎉 EXCELLENT: Sub-2 second performance achieved!');
        } else if (performance.totalTime < 3000) {
            console.log('✅ GOOD: Close to 2-second target');
        } else {
            console.log('⚠️ SLOW: May need optimization for 2-second target');
        }

        return performance;

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        return { success: false, error: error.message };
    } finally {
        if (browser) {
            await browser.close();
            console.log('\n✅ Browser closed');
        }
    }
}

// Run the test
if (require.main === module) {
    testBotDryRun()
        .then(result => {
            console.log('\n🏁 Dry run test completed');
            if (result.speedRating === 'Excellent') {
                console.log('🚀 Ready for real trading!');
            } else {
                console.log('🔧 Consider optimizations before real trading');
            }
        })
        .catch(error => {
            console.error('💥 Test crashed:', error);
        });
}

module.exports = testBotDryRun;
