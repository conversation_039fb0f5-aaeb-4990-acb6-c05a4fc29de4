#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Trading System Integration Test
=======================================

Comprehensive end-to-end test of the enhanced trading system with blur prevention.
Tests the complete integration from webhook to trade execution.
"""

import asyncio
import json
import time
import requests
from datetime import datetime
from typing import Dict, Any

# Test configuration
TEST_CONFIG = {
    "webhook_url": "http://localhost:8000/webhook/tradingview/enhanced",
    "manual_url": "http://localhost:8000/webhook/manual/enhanced",
    "status_url": "http://localhost:8000/status",
    "test_symbol": "TRU_USDT",
    "test_quantity": 1.0,
    "test_price": 0.03334,
    "test_leverage": 1
}

class EnhancedSystemIntegrationTest:
    """Comprehensive integration test for enhanced trading system"""
    
    def __init__(self):
        self.test_results = {
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "test_details": [],
            "start_time": datetime.now(),
            "end_time": None
        }
        
        print("Enhanced Trading System Integration Test")
        print("=" * 50)
        print("Testing complete integration with blur prevention")
        print()
    
    def log_test_result(self, test_name: str, success: bool, details: str = "", data: Dict[str, Any] = None):
        """Log test result"""
        self.test_results["total_tests"] += 1
        
        if success:
            self.test_results["passed_tests"] += 1
            status = "PASS"
            symbol = "✅"
        else:
            self.test_results["failed_tests"] += 1
            status = "FAIL"
            symbol = "❌"
        
        result = {
            "test_name": test_name,
            "status": status,
            "details": details,
            "data": data or {},
            "timestamp": datetime.now().isoformat()
        }
        
        self.test_results["test_details"].append(result)
        
        print(f"{symbol} {test_name}: {status}")
        if details:
            print(f"   Details: {details}")
        if data:
            print(f"   Data: {json.dumps(data, indent=2)}")
        print()
    
    def test_system_status(self) -> bool:
        """Test if the enhanced trading system is running"""
        try:
            response = requests.get(TEST_CONFIG["status_url"], timeout=10)
            
            if response.status_code == 200:
                status_data = response.json()
                
                # Check if enhanced system is initialized
                enhanced_status = status_data.get("enhanced_system", {})
                initialized = enhanced_status.get("initialized", False)
                
                self.log_test_result(
                    "System Status Check",
                    initialized,
                    f"System initialized: {initialized}",
                    status_data
                )
                
                return initialized
            else:
                self.log_test_result(
                    "System Status Check",
                    False,
                    f"HTTP {response.status_code}: {response.text}"
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "System Status Check",
                False,
                f"Connection error: {str(e)}"
            )
            return False
    
    def test_enhanced_webhook_endpoint(self) -> bool:
        """Test enhanced webhook endpoint"""
        try:
            webhook_payload = {
                "action": "buy",
                "symbol": TEST_CONFIG["test_symbol"],
                "side": "long",
                "quantity": TEST_CONFIG["test_quantity"],
                "price": TEST_CONFIG["test_price"],
                "leverage": TEST_CONFIG["test_leverage"],
                "order_type": "limit",
                "strategy": "integration_test",
                "timeframe": "1h"
            }
            
            response = requests.post(
                TEST_CONFIG["webhook_url"],
                json=webhook_payload,
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                success = response_data.get("success", False)
                message = response_data.get("message", "")
                
                # Check if message indicates enhanced processing
                enhanced_processing = "ENHANCED" in message and "blur prevention" in message
                
                self.log_test_result(
                    "Enhanced Webhook Endpoint",
                    success and enhanced_processing,
                    f"Response: {message}",
                    response_data
                )
                
                return success and enhanced_processing
            else:
                self.log_test_result(
                    "Enhanced Webhook Endpoint",
                    False,
                    f"HTTP {response.status_code}: {response.text}"
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Enhanced Webhook Endpoint",
                False,
                f"Request error: {str(e)}"
            )
            return False
    
    def test_enhanced_manual_trade_endpoint(self) -> bool:
        """Test enhanced manual trade endpoint"""
        try:
            manual_payload = {
                "action": "sell",
                "symbol": TEST_CONFIG["test_symbol"],
                "side": "short",
                "quantity": TEST_CONFIG["test_quantity"],
                "price": TEST_CONFIG["test_price"],
                "leverage": TEST_CONFIG["test_leverage"],
                "order_type": "limit",
                "strategy": "manual_integration_test"
            }
            
            response = requests.post(
                TEST_CONFIG["manual_url"],
                json=manual_payload,
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                success = response_data.get("success", False)
                message = response_data.get("message", "")
                
                # Check if message indicates enhanced processing
                enhanced_processing = "ENHANCED" in message and "blur prevention" in message
                
                self.log_test_result(
                    "Enhanced Manual Trade Endpoint",
                    success and enhanced_processing,
                    f"Response: {message}",
                    response_data
                )
                
                return success and enhanced_processing
            else:
                self.log_test_result(
                    "Enhanced Manual Trade Endpoint",
                    False,
                    f"HTTP {response.status_code}: {response.text}"
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Enhanced Manual Trade Endpoint",
                False,
                f"Request error: {str(e)}"
            )
            return False
    
    def test_browser_automation_availability(self) -> bool:
        """Test if browser automation is available"""
        try:
            # Test if Chrome is running with remote debugging
            import requests
            response = requests.get("http://127.0.0.1:9222/json", timeout=5)
            
            if response.status_code == 200:
                tabs = response.json()
                mexc_tabs = [tab for tab in tabs if 'mexc.com' in tab.get('url', '')]
                
                self.log_test_result(
                    "Browser Automation Availability",
                    len(mexc_tabs) > 0,
                    f"Found {len(mexc_tabs)} MEXC tabs out of {len(tabs)} total tabs",
                    {"total_tabs": len(tabs), "mexc_tabs": len(mexc_tabs)}
                )
                
                return len(mexc_tabs) > 0
            else:
                self.log_test_result(
                    "Browser Automation Availability",
                    False,
                    f"Chrome remote debugging not accessible: HTTP {response.status_code}"
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Browser Automation Availability",
                False,
                f"Chrome remote debugging error: {str(e)}"
            )
            return False
    
    def test_comprehensive_trade_simulation(self) -> bool:
        """Test comprehensive trade simulation (dry run)"""
        try:
            # This would test the actual browser automation without executing real trades
            # For now, we'll simulate this test
            
            simulation_payload = {
                "action": "buy",
                "symbol": TEST_CONFIG["test_symbol"],
                "side": "long",
                "quantity": 0.1,  # Small test quantity
                "order_type": "limit",
                "price": TEST_CONFIG["test_price"],
                "strategy": "comprehensive_simulation_test",
                "dry_run": True  # This would be handled by the system
            }
            
            # Send to enhanced webhook
            response = requests.post(
                TEST_CONFIG["webhook_url"],
                json=simulation_payload,
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                success = response_data.get("success", False)
                
                # Wait a moment for background processing
                time.sleep(2)
                
                self.log_test_result(
                    "Comprehensive Trade Simulation",
                    success,
                    "Trade simulation completed successfully",
                    response_data
                )
                
                return success
            else:
                self.log_test_result(
                    "Comprehensive Trade Simulation",
                    False,
                    f"HTTP {response.status_code}: {response.text}"
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Comprehensive Trade Simulation",
                False,
                f"Simulation error: {str(e)}"
            )
            return False
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all integration tests"""
        print("Starting Enhanced Trading System Integration Tests...")
        print()
        
        # Test 1: System Status
        system_running = self.test_system_status()
        
        # Test 2: Browser Automation Availability
        browser_available = self.test_browser_automation_availability()
        
        # Test 3: Enhanced Webhook Endpoint
        webhook_working = self.test_enhanced_webhook_endpoint()
        
        # Test 4: Enhanced Manual Trade Endpoint
        manual_working = self.test_enhanced_manual_trade_endpoint()
        
        # Test 5: Comprehensive Trade Simulation
        simulation_working = self.test_comprehensive_trade_simulation()
        
        # Calculate results
        self.test_results["end_time"] = datetime.now()
        duration = (self.test_results["end_time"] - self.test_results["start_time"]).total_seconds()
        
        success_rate = 0
        if self.test_results["total_tests"] > 0:
            success_rate = (self.test_results["passed_tests"] / self.test_results["total_tests"]) * 100
        
        # Print summary
        print("=" * 50)
        print("ENHANCED TRADING SYSTEM INTEGRATION TEST RESULTS")
        print("=" * 50)
        print(f"Total Tests: {self.test_results['total_tests']}")
        print(f"Passed: {self.test_results['passed_tests']}")
        print(f"Failed: {self.test_results['failed_tests']}")
        print(f"Success Rate: {success_rate:.1f}%")
        print(f"Duration: {duration:.2f}s")
        print()
        
        if success_rate == 100:
            print("🎉 ALL TESTS PASSED! Enhanced trading system is ready for production!")
            print("✅ Blur prevention system working")
            print("✅ Complete UI automation functional")
            print("✅ End-to-end integration successful")
        elif success_rate >= 80:
            print("⚠️  Most tests passed, but some issues detected")
            print("🔧 Review failed tests and fix issues before production")
        else:
            print("❌ Multiple test failures detected")
            print("🚨 System not ready for production use")
        
        print()
        
        # Save detailed results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"enhanced_integration_test_report_{timestamp}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        
        print(f"📊 Detailed test report saved: {report_file}")
        
        return self.test_results


def main():
    """Main test function"""
    test_runner = EnhancedSystemIntegrationTest()
    results = test_runner.run_all_tests()
    
    # Return appropriate exit code
    if results["failed_tests"] == 0:
        exit(0)  # Success
    else:
        exit(1)  # Failure


if __name__ == "__main__":
    main()
