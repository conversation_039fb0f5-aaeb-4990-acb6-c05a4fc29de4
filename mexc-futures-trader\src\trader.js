const { chromium } = require('playwright');
const axios = require('axios');

class MexcFuturesTrader {
    constructor(port = 9223) { // Changed to single browser on port 9223
        this.browser = null;
        this.page = null;
        this.port = port;
        this.telegramBotToken = '**********************************************';
        this.telegramChatId = '243673531';
        this.lastBalance = null;
        this.lastBalanceUpdate = null;

        // Optimization features
        this.lastExecutedOrderType = null; // Track last order type for tab pre-positioning
        this.backgroundMonitoringInterval = null; // Background cleanup interval
        this.isExecutingTrade = false; // Flag to prevent monitoring during trades
        this.monitoringIntervalMs = 2.5 * 60 * 1000; // 2.5 minutes

        // Error logging rate limiter
        this.errorLogLimiter = new Map(); // Track error types and timestamps
        this.errorLogCooldown = 30000; // 30 seconds between same error types
    }

    async connectToBrowser() {
        console.log(`🔗 Connecting to browser on port ${this.port}...`);

        try {
            // Add shorter timeout for connection attempts
            const connectionTimeout = 10000; // 10 seconds instead of 30

            const connectPromise = chromium.connectOverCDP(`http://localhost:${this.port}`);
            const timeoutPromise = new Promise((_, reject) =>
                setTimeout(() => reject(new Error(`Connection timeout after ${connectionTimeout}ms`)), connectionTimeout)
            );

            this.browser = await Promise.race([connectPromise, timeoutPromise]);
            const contexts = this.browser.contexts();

            if (contexts.length > 0) {
                const pages = contexts[0].pages();
                this.page = pages.length > 0 ? pages[0] : await contexts[0].newPage();
            } else {
                const context = await this.browser.newContext();
                this.page = await context.newPage();
            }

            console.log(`✅ Connected to browser on port ${this.port}`);

            // Start background monitoring after successful connection
            this.startBackgroundMonitoring();

            return true;
        } catch (error) {
            console.error(`❌ Connection failed to port ${this.port}:`, error.message);

            // Clean up any partial connection
            if (this.browser) {
                try {
                    await this.browser.close();
                } catch (cleanupError) {
                    console.error('Error cleaning up browser connection:', cleanupError.message);
                }
                this.browser = null;
                this.page = null;
            }

            return false;
        }
    }

    logErrorWithRateLimit(errorType, message, details = {}) {
        const now = Date.now();
        const lastLogged = this.errorLogLimiter.get(errorType);

        if (!lastLogged || (now - lastLogged) > this.errorLogCooldown) {
            console.error(`❌ ${message}`, details);
            this.errorLogLimiter.set(errorType, now);
            return true; // Logged
        }
        return false; // Rate limited
    }

    // Background monitoring system for proactive cleanup
    startBackgroundMonitoring() {
        if (this.backgroundMonitoringInterval) {
            return; // Already running
        }

        console.log(`🔄 Starting background monitoring (every ${this.monitoringIntervalMs / 60000} minutes)...`);

        this.backgroundMonitoringInterval = setInterval(async () => {
            if (!this.isExecutingTrade && this.page) {
                try {
                    console.log('🧹 Background cleanup: Checking quantity fields and popups...');
                    await this.backgroundCleanup();
                } catch (error) {
                    this.logErrorWithRateLimit('background_cleanup', 'Background cleanup error:', { error: error.message });
                }
            }
        }, this.monitoringIntervalMs);
    }

    stopBackgroundMonitoring() {
        if (this.backgroundMonitoringInterval) {
            clearInterval(this.backgroundMonitoringInterval);
            this.backgroundMonitoringInterval = null;
            console.log('🛑 Background monitoring stopped');
        }
    }

    async backgroundCleanup() {
        try {
            // Check if quantity field has existing value and clear it
            const hasExistingValue = await this.checkAndClearQuantityField();

            // Check for persistent popups and close them
            const hadPopups = await this.checkAndClosePopups();

            // Always ensure we're positioned on Open tab as default ready state
            const tabPositioned = await this.ensureOpenTabDefault();

            if (hasExistingValue || hadPopups || tabPositioned) {
                console.log('✅ Background cleanup completed - UI ready for next trade (Open tab default)');
            }
        } catch (error) {
            console.log('⚠️ Background cleanup failed:', error.message);
        }
    }

    async ensureOpenTabDefault() {
        try {
            const openTabSelector = 'span[data-testid="contract-trade-order-form-tab-open"]';

            // Check if we're already on Open tab
            const isOnOpenTab = await this.isTabActive(openTabSelector);
            if (isOnOpenTab) {
                return false; // No change needed
            }

            // Switch to Open tab as default
            console.log('🔄 Background: Positioning to Open tab as default ready state');
            await this.page.waitForSelector(openTabSelector, { timeout: 1000 });
            await this.page.click(openTabSelector);
            await this.page.waitForTimeout(200);

            return true; // Tab was repositioned
        } catch (error) {
            console.log('⚠️ Could not ensure Open tab default:', error.message);
            return false;
        }
    }

    async checkAndClearQuantityField() {
        try {
            const input = this.page.locator('text=Quantity(USDT) >> xpath=following::input[1]').first();
            if (await input.isVisible({ timeout: 500 })) {
                const currentValue = await input.inputValue();
                if (currentValue && currentValue.trim() !== '') {
                    console.log(`🔢 Found existing quantity value: "${currentValue}" - clearing...`);
                    await this.clearInputField(input);
                    return true;
                }
            }
            return false;
        } catch (error) {
            return false;
        }
    }

    async checkAndClosePopups() {
        try {
            const modalSelectors = [
                '.ant-modal-wrap.ant-modal-wrap-footer-custom',
                '.ant-modal-wrap',
                '.modal-wrap'
            ];

            let foundPopups = false;
            for (const selector of modalSelectors) {
                const modal = this.page.locator(selector).first();
                if (await modal.isVisible({ timeout: 200 })) {
                    console.log(`🚨 Found persistent popup: ${selector} - closing...`);

                    const closeSelectors = [
                        'button:has-text("Close")',
                        'button:has-text("Cancel")',
                        '.ant-modal-close'
                    ];

                    for (const closeSelector of closeSelectors) {
                        try {
                            const closeBtn = modal.locator(closeSelector).first();
                            if (await closeBtn.isVisible({ timeout: 100 })) {
                                await closeBtn.click();
                                foundPopups = true;
                                break;
                            }
                        } catch (error) {
                            continue;
                        }
                    }
                }
            }
            return foundPopups;
        } catch (error) {
            return false;
        }
    }

    async executeOrder(orderType, quantity = '0.3600') {
        const startTime = Date.now();
        console.log(`🎯 EXECUTING ${orderType.toUpperCase()} ORDER...`);
        console.log(`💰 Quantity: ${quantity} USDT`);

        // Set execution flag to prevent background monitoring interference
        this.isExecutingTrade = true;

        try {
            // Check current page (don't navigate if already on MEXC)
            const url = this.page.url();
            if (url.includes('mexc.com')) {
                console.log('✅ Already on MEXC page');
            } else {
                console.log('🌐 Navigating to TRU_USDT...');
                await this.page.goto('https://www.mexc.com/futures/TRU_USDT');
                await this.page.waitForTimeout(1000); // Reduced from 2000ms
            }

            // Step 1: Optimized tab selection - check if we're already positioned correctly
            await this.selectTabOptimized(orderType);

            // Check for login requirement before proceeding
            const loginRequired = await this.checkLoginRequired();
            if (loginRequired) {
                await this.sendTelegramAlert('🚨 MEXC Login Required',
                    `MEXC session expired. Please login manually.\nPort: ${this.port}\nOrder: ${orderType}`);
                throw new Error('MEXC login required. Session expired.');
            }

            // Fill quantity with optimized error handling
            await this.fillQuantityWithOptimizedHandling(orderType, quantity);

            // Click order button with error handling
            await this.clickOrderButtonWithErrorHandling(orderType);

            // Quick error check for insufficient closeable quantity (no popup waiting)
            await this.quickErrorCheck(orderType);

            const executionTime = Date.now() - startTime;
            // Quick verification - reduced timeout for speed
            const verified = await this.quickVerifySuccess(orderType);

            console.log(`⚡ ${orderType} completed in ${executionTime}ms`);
            console.log(`🎉 Verified: ${verified ? '✅ YES' : '❌ NO'}`);

            // Post-execution optimization: prepare for next expected trade
            await this.prepareForNextTrade(orderType);

            return {
                success: verified,
                executionTime,
                verified,
                orderType,
                quantity,
                targetAchieved: executionTime < 2000,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            const executionTime = Date.now() - startTime;

            // Handle SKIP_TRADE errors (insufficient closeable quantity, etc.)
            if (error.message.startsWith('SKIP_TRADE:')) {
                const skipReason = error.message.replace('SKIP_TRADE: ', '');
                console.log(`⏭️ Skipping trade: ${skipReason}`);

                return {
                    success: false,
                    skipped: true,
                    skipReason: skipReason,
                    executionTime,
                    verified: false,
                    orderType,
                    quantity,
                    targetAchieved: false,
                    timestamp: new Date().toISOString()
                };
            }

            console.error(`❌ ${orderType} failed: ${error.message}`);

            // Emergency recovery for other errors
            console.log('🚨 Emergency recovery...');
            await this.aggressiveCleanup();
            await new Promise(resolve => setTimeout(resolve, 1000));

            const verified = await this.verifySuccess(orderType);
            if (verified) {
                console.log('✅ Emergency recovery successful!');
                return {
                    success: true,
                    executionTime,
                    verified: true,
                    orderType,
                    quantity,
                    emergencyRecovery: true,
                    timestamp: new Date().toISOString()
                };
            }
            
            return {
                success: false,
                executionTime,
                error: error.message,
                orderType,
                quantity,
                timestamp: new Date().toISOString()
            };
        } finally {
            // Reset execution flag to allow background monitoring
            this.isExecutingTrade = false;
        }
    }

    // Optimized tab selection based on trade type and default positioning strategy
    async selectTabOptimized(orderType) {
        try {
            console.log(`📋 Optimized tab selection for ${orderType}...`);

            if (orderType.includes('Open')) {
                // Open trades: Should already be on Open tab (default positioning)
                const openTabSelector = 'span[data-testid="contract-trade-order-form-tab-open"]';
                const isOnOpenTab = await this.isTabActive(openTabSelector);

                if (isOnOpenTab) {
                    console.log(`✅ Open trade ready - already on Open tab (optimized!)`)
                    return;
                }

                // Fallback: Switch to Open tab if somehow not positioned correctly
                console.log(`🔄 Open trade: Switching to Open tab (fallback)...`);
                await this.page.waitForSelector(openTabSelector, { timeout: 3000 });
                await this.page.click(openTabSelector);
                await this.page.waitForTimeout(300);

                const switchSuccessful = await this.isTabActive(openTabSelector);
                if (!switchSuccessful) {
                    throw new Error(`Failed to switch to Open tab`);
                }
                console.log(`✅ Successfully switched to Open tab`);

            } else if (orderType.includes('Close')) {
                // Close trades: Always verify and switch to Close tab
                const closeTabSelector = 'span[data-testid="contract-trade-order-form-tab-close"]';
                const isOnCloseTab = await this.isTabActive(closeTabSelector);

                if (isOnCloseTab) {
                    console.log(`✅ Close trade ready - already on Close tab`);
                    return;
                }

                // Switch to Close tab (expected for Close trades)
                console.log(`🔄 Close trade: Switching to Close tab...`);
                await this.page.waitForSelector(closeTabSelector, { timeout: 3000 });
                await this.page.click(closeTabSelector);
                await this.page.waitForTimeout(300);

                const switchSuccessful = await this.isTabActive(closeTabSelector);
                if (!switchSuccessful) {
                    throw new Error(`Failed to switch to Close tab`);
                }
                console.log(`✅ Successfully switched to Close tab`);

            } else {
                throw new Error(`Unknown order type: ${orderType}`);
            }
        } catch (error) {
            console.error('❌ Failed to select tab:', error.message);
            throw error;
        }
    }

    // Post-execution positioning: Always return to Open tab as default ready state
    async prepareForNextTrade(currentOrderType) {
        try {
            // Store the last executed order type for reference
            this.lastExecutedOrderType = currentOrderType;

            console.log(`🎯 Post-execution: Positioning to Open tab as default ready state`);

            // Always position to Open tab as default since:
            // 1. Open trades are more time-critical
            // 2. Trading patterns are unpredictable
            // 3. Close trades can verify/switch tabs during execution
            const openTabSelector = 'span[data-testid="contract-trade-order-form-tab-open"]';

            // Check if we're already on Open tab
            const isAlreadyOnOpenTab = await this.isTabActive(openTabSelector);
            if (isAlreadyOnOpenTab) {
                console.log(`✅ Already on Open tab - ready for next trade`);
                return;
            }

            // Switch to Open tab as default ready state
            try {
                await this.page.waitForSelector(openTabSelector, { timeout: 2000 });
                await this.page.click(openTabSelector);
                await this.page.waitForTimeout(200); // Quick wait
                console.log(`✅ Positioned to Open tab as default ready state`);
            } catch (error) {
                console.log(`⚠️ Could not position to Open tab: ${error.message}`);
            }
        } catch (error) {
            console.log(`⚠️ Error in post-execution positioning: ${error.message}`);
        }
    }

    // Optimized quantity filling with reduced error handling overhead
    async fillQuantityWithOptimizedHandling(orderType, quantity) {
        console.log(`🔢 Optimized quantity filling: ${quantity}...`);

        try {
            // First attempt - direct fill (background monitoring should have cleared any existing values)
            await this.fillQuantity(quantity);
            return;
        } catch (error) {
            console.log('⚠️ First quantity attempt failed - applying targeted fix...');

            // Only clear quantity fields on first retry (background monitoring handles most cases)
            try {
                await this.clearQuantityFields();
                await this.fillQuantity(quantity);
                return;
            } catch (secondError) {
                console.log('⚠️ Second quantity attempt failed - applying full cleanup...');

                // Full cleanup only as last resort
                await this.closePersistentPopups();
                await this.page.waitForTimeout(300); // Reduced from 500ms
                await this.fillQuantity(quantity);
            }
        }
    }

    async fillQuantityWithErrorHandling(orderType, quantity) {
        console.log(`🔢 Filling quantity: ${quantity}...`);
        
        try {
            // First attempt - no cleanup
            await this.fillQuantity(quantity);
            return;
        } catch (error) {
            console.log('⚠️ First quantity attempt failed, clearing quantity fields...');
            
            try {
                // First error: Clean quantity only
                await this.clearQuantityFields();
                await this.fillQuantity(quantity);
                return;
            } catch (secondError) {
                console.log('⚠️ Second quantity attempt failed, closing popups...');
                
                // Second error: Close popups then try again
                await this.closePersistentPopups();
                await this.page.waitForTimeout(500);
                await this.fillQuantity(quantity);
            }
        }
    }

    async clickOrderButtonWithErrorHandling(orderType) {
        console.log(`📊 Clicking ${orderType} button...`);
        
        try {
            // First attempt - no cleanup
            await this.clickOrderButton(orderType);
            return;
        } catch (error) {
            console.log('⚠️ First button click failed, clearing quantity fields...');
            
            try {
                // First error: Clean quantity only (might be quantity issue)
                await this.clearQuantityFields();
                await this.fillQuantity();
                await this.clickOrderButton(orderType);
                return;
            } catch (secondError) {
                console.log('⚠️ Second button click failed, closing popups...');
                
                // Second error: Close popups then try again
                await this.closePersistentPopups();
                await this.page.waitForTimeout(500);
                await this.clickOrderButton(orderType);
            }
        }
    }

    async selectTab(orderType) {
        try {
            console.log(`📋 Selecting tab for ${orderType}...`);

            let targetTabSelector, targetTabName;
            if (orderType.includes('Open')) {
                targetTabSelector = 'span[data-testid="contract-trade-order-form-tab-open"]';
                targetTabName = 'Open';
            } else if (orderType.includes('Close')) {
                targetTabSelector = 'span[data-testid="contract-trade-order-form-tab-close"]';
                targetTabName = 'Close';
            } else {
                throw new Error(`Unknown order type: ${orderType}`);
            }

            // Check if we're already on the correct tab (performance optimization)
            const isCurrentTabActive = await this.isTabActive(targetTabSelector);
            if (isCurrentTabActive) {
                console.log(`✅ Already on ${targetTabName} tab, skipping click`);
                return;
            }

            console.log(`🔄 Switching to ${targetTabName} tab...`);

            // Wait for tab to be visible and click it
            await this.page.waitForSelector(targetTabSelector, { timeout: 3000 });
            await this.page.click(targetTabSelector);

            // Wait a moment for tab content to load
            await this.page.waitForTimeout(500);

            // Verify the tab switch was successful
            const switchSuccessful = await this.isTabActive(targetTabSelector);
            if (!switchSuccessful) {
                throw new Error(`Failed to switch to ${targetTabName} tab`);
            }

            console.log(`✅ Successfully switched to ${targetTabName} tab`);
        } catch (error) {
            console.error('❌ Failed to select tab:', error.message);
            throw error;
        }
    }

    async isTabActive(tabSelector) {
        try {
            // Check if the tab has active/selected class or attribute
            const tabElement = this.page.locator(tabSelector).first();

            // Wait for element to be visible
            if (!await tabElement.isVisible({ timeout: 1000 })) {
                return false;
            }

            // Check various ways a tab might indicate it's active
            const activeChecks = [
                // Check for active class
                async () => {
                    const className = await tabElement.getAttribute('class');
                    return className && (className.includes('active') || className.includes('selected'));
                },
                // Check for aria-selected attribute
                async () => {
                    const ariaSelected = await tabElement.getAttribute('aria-selected');
                    return ariaSelected === 'true';
                },
                // Check parent element for active state
                async () => {
                    const parent = tabElement.locator('..');
                    const parentClass = await parent.getAttribute('class');
                    return parentClass && (parentClass.includes('active') || parentClass.includes('selected'));
                },
                // Check if the tab content is visible (Open/Close specific content)
                async () => {
                    if (tabSelector.includes('open')) {
                        // Check for Open Long/Short buttons
                        const openButtons = this.page.locator('button:has-text("Open Long"), button:has-text("Open Short")');
                        return await openButtons.first().isVisible({ timeout: 500 });
                    } else {
                        // Check for Close Long/Short buttons
                        const closeButtons = this.page.locator('button:has-text("Close Long"), button:has-text("Close Short")');
                        return await closeButtons.first().isVisible({ timeout: 500 });
                    }
                }
            ];

            // Try each check method
            for (const check of activeChecks) {
                try {
                    if (await check()) {
                        return true;
                    }
                } catch (error) {
                    // Continue to next check
                }
            }

            return false;
        } catch (error) {
            console.log('⚠️ Tab active check failed:', error.message);
            return false;
        }
    }

    async forceTabSwitch(orderType) {
        console.log(`🔧 Force switching tab for ${orderType}...`);

        try {
            let targetTabSelector;
            if (orderType.includes('Open')) {
                targetTabSelector = 'span[data-testid="contract-trade-order-form-tab-open"]';
            } else {
                targetTabSelector = 'span[data-testid="contract-trade-order-form-tab-close"]';
            }

            // Multiple click attempts with different strategies
            const clickStrategies = [
                // Strategy 1: Direct click
                async () => {
                    await this.page.click(targetTabSelector);
                },
                // Strategy 2: Click with force
                async () => {
                    await this.page.click(targetTabSelector, { force: true });
                },
                // Strategy 3: Click parent element
                async () => {
                    const parent = this.page.locator(targetTabSelector).locator('..');
                    await parent.click();
                },
                // Strategy 4: JavaScript click
                async () => {
                    await this.page.evaluate((selector) => {
                        const element = document.querySelector(selector);
                        if (element) element.click();
                    }, targetTabSelector);
                }
            ];

            for (let i = 0; i < clickStrategies.length; i++) {
                try {
                    await clickStrategies[i]();
                    await this.page.waitForTimeout(300);

                    // Check if switch was successful
                    if (await this.isTabActive(targetTabSelector)) {
                        console.log(`✅ Force tab switch successful with strategy ${i + 1}`);
                        return true;
                    }
                } catch (error) {
                    console.log(`⚠️ Force tab switch strategy ${i + 1} failed:`, error.message);
                }
            }

            throw new Error('All force tab switch strategies failed');
        } catch (error) {
            console.error('❌ Force tab switch failed:', error.message);
            throw error;
        }
    }

    async fillQuantity(quantity = '0.3600') {
        // Check if we're in close mode and handle differently
        const isCloseMode = await this.isInCloseMode();
        if (isCloseMode) {
            console.log('🔄 Detected close mode - using close-specific quantity strategies');
            return await this.fillQuantityForClose(quantity);
        }

        const strategies = [
            // Strategy 1: XPath following Quantity(USDT) - Fast version
            async () => {
                const input = this.page.locator('text=Quantity(USDT) >> xpath=following::input[1]').first();
                if (await input.isVisible({ timeout: 500 })) {
                    // Try direct fill first (fastest)
                    await input.click();
                    await input.fill(quantity);

                    const value = await input.inputValue();
                    if (value === quantity) {
                        return true;
                    }

                    // If direct fill failed, clear and retry
                    await this.clearInputField(input);
                    await input.click();
                    await input.fill(quantity);

                    const retryValue = await input.inputValue();
                    return retryValue === quantity;
                }
                return false;
            },

            // Strategy 2: XPath following Quantity - Fast version
            async () => {
                const input = this.page.locator('text=Quantity >> xpath=following::input[1]').first();
                if (await input.isVisible({ timeout: 300 })) {
                    const type = await input.getAttribute('type');
                    if (type !== 'checkbox') {
                        await input.click();
                        await input.fill(quantity);

                        const value = await input.inputValue();
                        if (value === quantity) {
                            return true;
                        }

                        await this.clearInputField(input);
                        await input.click();
                        await input.fill(quantity);

                        const retryValue = await input.inputValue();
                        return retryValue === quantity;
                    }
                }
                return false;
            },

            // Strategy 3: Direct text/number inputs - Fast version
            async () => {
                const selectors = ['input[type="text"]', 'input[type="number"]'];
                for (const selector of selectors) {
                    const inputs = await this.page.locator(selector).all();
                    for (const input of inputs) {
                        if (await input.isVisible({ timeout: 100 })) {
                            try {
                                await input.click();
                                await input.fill(quantity);

                                const value = await input.inputValue();
                                if (value === quantity) {
                                    return true;
                                }

                                await this.clearInputField(input);
                                await input.click();
                                await input.fill(quantity);

                                const retryValue = await input.inputValue();
                                if (retryValue === quantity) {
                                    return true;
                                }
                            } catch (error) {
                                continue;
                            }
                        }
                    }
                }
                return false;
            }
        ];

        for (let i = 0; i < strategies.length; i++) {
            try {
                if (await strategies[i]()) {
                    console.log(`✅ Quantity filled using strategy ${i + 1}`);
                    return;
                }
            } catch (error) {
                console.log(`⚠️ Strategy ${i + 1} failed:`, error.message);
                continue;
            }
        }

        throw new Error('Could not fill quantity field');
    }

    async isInCloseMode() {
        try {
            // Check if Close tab is active by looking for the active class
            const closeTab = this.page.locator('span[data-testid="contract-trade-order-form-tab-close"]').first();
            const hasActiveClass = await closeTab.getAttribute('class');
            const isActive = hasActiveClass && (hasActiveClass.includes('active') || hasActiveClass.includes('handle_active'));

            console.log(`🔍 Close mode check: ${isActive ? 'YES' : 'NO'} (class: ${hasActiveClass})`);
            return isActive;
        } catch (error) {
            console.log('⚠️ Close mode detection failed, assuming open mode');
            return false;
        }
    }

    async fillQuantityForClose(quantity = '0.3600') {
        console.log(`🔢 Filling quantity for close mode: ${quantity}...`);

        const closeStrategies = [
            // Strategy 1: Use the correct .ant-input element (3rd one, index 2)
            async () => {
                try {
                    const input = this.page.locator('.ant-input').nth(2); // 3rd element (index 2)
                    if (await input.isVisible({ timeout: 1000 })) {
                        console.log('✅ Found correct .ant-input field (index 2) in close mode');

                        await input.click({ timeout: 2000 });
                        await input.fill(quantity);

                        const value = await input.inputValue();
                        if (value === quantity) {
                            console.log(`✅ Close quantity filled successfully: ${value}`);
                            return true;
                        }

                        // If direct fill failed, clear and retry
                        await this.clearInputField(input);
                        await input.click({ timeout: 2000 });
                        await input.fill(quantity);

                        const retryValue = await input.inputValue();
                        if (retryValue === quantity) {
                            console.log(`✅ Close quantity filled after clearing: ${retryValue}`);
                            return true;
                        }
                    }
                } catch (error) {
                    console.log(`❌ Strategy 1 failed: ${error.message}`);
                }
                return false;
            },

            // Strategy 2: Try all .ant-input elements until one works
            async () => {
                try {
                    const inputs = await this.page.locator('.ant-input').all();
                    console.log(`🔍 Found ${inputs.length} .ant-input elements, testing each...`);

                    for (let i = 0; i < inputs.length; i++) {
                        const input = inputs[i];
                        try {
                            if (await input.isVisible({ timeout: 500 })) {
                                console.log(`🧪 Testing .ant-input element ${i + 1}...`);

                                await input.click({ timeout: 1000 });
                                await input.fill(quantity);

                                const value = await input.inputValue();
                                if (value === quantity) {
                                    console.log(`✅ Close quantity filled using element ${i + 1}: ${value}`);
                                    return true;
                                }
                            }
                        } catch (error) {
                            console.log(`❌ Element ${i + 1} failed: ${error.message}`);
                            continue;
                        }
                    }
                } catch (error) {
                    console.log(`❌ Strategy 2 failed: ${error.message}`);
                }
                return false;
            },

            // Strategy 3: Force click with different selectors
            async () => {
                const selectors = [
                    'input[type="text"]:nth-child(3)',
                    'input.ant-input:not(.ant-input-sm)',
                    'input[autocomplete="off"]'
                ];

                for (const selector of selectors) {
                    try {
                        const input = this.page.locator(selector).first();
                        if (await input.isVisible({ timeout: 500 })) {
                            console.log(`🧪 Testing selector: ${selector}`);

                            await input.click({ force: true, timeout: 1000 });
                            await input.fill(quantity);

                            const value = await input.inputValue();
                            if (value === quantity) {
                                console.log(`✅ Close quantity filled using ${selector}: ${value}`);
                                return true;
                            }
                        }
                    } catch (error) {
                        console.log(`❌ Selector ${selector} failed: ${error.message}`);
                        continue;
                    }
                }
                return false;
            }
        ];

        for (let i = 0; i < closeStrategies.length; i++) {
            try {
                if (await closeStrategies[i]()) {
                    console.log(`✅ Close quantity strategy ${i + 1} succeeded`);
                    return;
                }
            } catch (error) {
                console.log(`⚠️ Close strategy ${i + 1} failed:`, error.message);
                continue;
            }
        }

        throw new Error('Could not fill quantity field in close mode');
    }

    async clickOrderButton(orderType) {
        console.log(`📊 Clicking ${orderType} button...`);

        // Check if this is a close order and if there are positions to close
        if (orderType.includes('Close')) {
            const hasPositions = await this.checkForOpenPositions();
            if (!hasPositions) {
                throw new Error(`No open positions to close. Cannot execute ${orderType}.`);
            }
        }

        const buttonMap = {
            'Open Long': [
                'button:has-text("Open Long")',
                'text=Open Long',
                '.buy-button',
                'button[class*="buy"]',
                'button[class*="long"]'
            ],
            'Open Short': [
                'button:has-text("Open Short")',
                'text=Open Short',
                '.sell-button',
                'button[class*="sell"]',
                'button[class*="short"]'
            ],
            'Close Long': [
                'button:has-text("Close Long")',
                'text=Close Long',
                'button:has-text("Close")',
                '.close-long-button',
                'button[class*="close"][class*="long"]'
            ],
            'Close Short': [
                'button:has-text("Close Short")',
                'text=Close Short',
                'button:has-text("Close")',
                '.close-short-button',
                'button[class*="close"][class*="short"]'
            ]
        };

        const selectors = buttonMap[orderType];
        if (!selectors) {
            throw new Error(`Unknown order type: ${orderType}`);
        }

        // Try each selector with increasing timeout
        for (let i = 0; i < selectors.length; i++) {
            const selector = selectors[i];
            try {
                const button = this.page.locator(selector).first();
                const timeout = i === 0 ? 2000 : 1000;

                if (await button.isVisible({ timeout })) {
                    await button.click();
                    console.log(`✅ ${orderType} button clicked using selector: ${selector}`);
                    return;
                }
            } catch (error) {
                console.log(`⚠️ Selector ${i + 1} failed: ${selector}`);
                continue;
            }
        }

        // If button not found, try force tab switching and retry
        console.log(`⚠️ ${orderType} button not found, trying force tab switch...`);
        try {
            await this.forceTabSwitch(orderType);

            // Retry button search after force tab switch
            for (let i = 0; i < Math.min(selectors.length, 2); i++) {
                const selector = selectors[i];
                try {
                    const button = this.page.locator(selector).first();
                    if (await button.isVisible({ timeout: 1000 })) {
                        await button.click();
                        console.log(`✅ ${orderType} button clicked after force tab switch: ${selector}`);
                        return;
                    }
                } catch (error) {
                    continue;
                }
            }
        } catch (tabError) {
            console.log(`⚠️ Force tab switch failed: ${tabError.message}`);
        }

        if (orderType.includes('Close')) {
            throw new Error(`${orderType} button not found. This usually means there are no open ${orderType.includes('Long') ? 'long' : 'short'} positions to close.`);
        }

        throw new Error(`${orderType} button not found`);
    }

    async checkForOpenPositions() {
        console.log('🔍 Checking for open positions...');

        try {
            const positionIndicators = [
                '.position-row',
                '.open-position',
                '[class*="position"]',
                'text=Position',
                'text=PnL',
                'text=Unrealized'
            ];

            for (const indicator of positionIndicators) {
                try {
                    const element = this.page.locator(indicator).first();
                    if (await element.isVisible({ timeout: 500 })) {
                        console.log(`✅ Found position indicator: ${indicator}`);
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }

            const closeButtons = await this.page.locator('button:has-text("Close Long"), button:has-text("Close Short")').all();
            if (closeButtons.length > 0) {
                for (const btn of closeButtons) {
                    if (await btn.isVisible({ timeout: 100 })) {
                        console.log('✅ Found close buttons - positions exist');
                        return true;
                    }
                }
            }

            console.log('⚠️ No open positions found');
            return false;
        } catch (error) {
            console.log('⚠️ Error checking positions:', error.message);
            return false;
        }
    }

    async quickErrorCheck(orderType) {
        console.log(`🔍 Quick error check for ${orderType}...`);

        // Only check for critical errors that should cause us to skip the trade
        const errorSelectors = [
            'text=Insufficient closeable quantity',
            'text=insufficient closeable quantity',
            'text=Insufficient closeable quantity!',
            'text=No position to close',
            'text=Position not found',
            '[class*="error"]:has-text("closeable")',
            '[class*="warning"]:has-text("closeable")'
        ];

        for (const selector of errorSelectors) {
            try {
                const errorElement = this.page.locator(selector).first();
                if (await errorElement.isVisible({ timeout: 100 })) {
                    const errorText = await errorElement.textContent();
                    console.log(`⚠️ Error detected: "${errorText}"`);

                    // Try to close the error popup quickly
                    const closeButtons = [
                        'button:has-text("Close")',
                        'button:has-text("×")',
                        '.close-btn',
                        '[data-testid*="close"]'
                    ];

                    for (const closeBtn of closeButtons) {
                        try {
                            const closeElement = this.page.locator(closeBtn).first();
                            if (await closeElement.isVisible({ timeout: 100 })) {
                                await closeElement.click();
                                console.log('✅ Closed error popup');
                                break;
                            }
                        } catch (e) {
                            // Continue
                        }
                    }

                    // Throw special error to skip this trade
                    throw new Error(`SKIP_TRADE: ${errorText}`);
                }
            } catch (error) {
                if (error.message.startsWith('SKIP_TRADE:')) {
                    throw error; // Re-throw skip errors
                }
                // Continue checking other selectors
            }
        }

        console.log('✅ No critical errors detected');
        return true;
    }

    async handlePopup(orderType) {
        // DEPRECATED: This function is no longer used for MEXC futures trading
        // as there are no popups for Open Long/Short and Close Long/Short operations.
        // Keeping for backward compatibility but should not be called.
        console.log(`⚠️ handlePopup called for ${orderType} - this should not happen for MEXC futures`);
        return true;
    }

    async verifySuccess() {
        try {
            const successSelectors = [
                'text=Purchased successfully',
                'text=Success',
                'text=success',
                '.success'
            ];

            for (const selector of successSelectors) {
                try {
                    if (await this.page.locator(selector).first().isVisible({ timeout: 500 })) {
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }
            return false;
        } catch (error) {
            return false;
        }
    }

    async clearInputField(input) {
        try {
            await input.click();
            await this.page.waitForTimeout(50);

            await input.press('Control+a');
            await this.page.waitForTimeout(50);
            await input.press('Delete');
            await this.page.waitForTimeout(50);

            await input.fill('');
            await this.page.waitForTimeout(50);

            const value = await input.inputValue();
            if (value && value.trim() !== '') {
                await input.click();
                for (let i = 0; i < 20; i++) {
                    await input.press('Backspace');
                }
                await input.fill('');
            }

            console.log('✅ Input field cleared');
        } catch (error) {
            console.log('⚠️ Error clearing input field:', error.message);
        }
    }

    async clearQuantityFields() {
        console.log('🔢 Clearing quantity fields...');

        const clearStrategies = [
            async () => {
                const input = this.page.locator('text=Quantity(USDT) >> xpath=following::input[1]').first();
                if (await input.isVisible({ timeout: 300 })) {
                    await this.clearInputField(input);
                    return true;
                }
                return false;
            },
            async () => {
                const selectors = ['input[type="text"]', 'input[type="number"]'];
                for (const selector of selectors) {
                    const inputs = await this.page.locator(selector).all();
                    for (const input of inputs) {
                        if (await input.isVisible({ timeout: 100 })) {
                            try {
                                const currentValue = await input.inputValue();
                                if (currentValue && currentValue.trim() !== '') {
                                    await this.clearInputField(input);
                                }
                            } catch (error) {
                                continue;
                            }
                        }
                    }
                }
                return true;
            }
        ];

        for (let i = 0; i < clearStrategies.length; i++) {
            try {
                if (await clearStrategies[i]()) {
                    console.log(`✅ Fields cleared using strategy ${i + 1}`);
                    break;
                }
            } catch (error) {
                continue;
            }
        }
    }

    async closePersistentPopups() {
        console.log('🚨 Closing persistent popups...');

        try {
            const modalWrapSelectors = [
                '.ant-modal-wrap.ant-modal-wrap-footer-custom',
                '.ant-modal-wrap',
                '.modal-wrap'
            ];

            for (const selector of modalWrapSelectors) {
                try {
                    const modalWrap = this.page.locator(selector).first();
                    if (await modalWrap.isVisible({ timeout: 200 })) {
                        const closeSelectors = [
                            'button:has-text("Close")',
                            'button:has-text("Cancel")',
                            '.ant-modal-close'
                        ];

                        for (const closeSelector of closeSelectors) {
                            try {
                                const closeBtn = modalWrap.locator(closeSelector).first();
                                if (await closeBtn.isVisible({ timeout: 100 })) {
                                    await closeBtn.click();
                                    console.log(`✅ Closed modal via: ${closeSelector}`);
                                    await this.page.waitForTimeout(300);
                                    break;
                                }
                            } catch (error) {
                                continue;
                            }
                        }
                    }
                } catch (error) {
                    continue;
                }
            }

            try {
                await this.page.keyboard.press('Escape');
                await this.page.waitForTimeout(200);
                console.log('✅ Pressed ESC to close any remaining modals');
            } catch (error) {
                // Continue
            }

        } catch (error) {
            console.log('⚠️ Error closing persistent popups:', error.message);
        }
    }

    async aggressiveCleanup() {
        console.log('🧹 Aggressive cleanup...');

        try {
            await this.closePersistentPopups();
            await this.clearQuantityFields();
            console.log('✅ Cleanup completed');
        } catch (error) {
            console.log('⚠️ Cleanup had issues, continuing...');
        }
    }

    async checkLoginRequired() {
        try {
            // Check for login button or login text
            const loginSelectors = [
                'button:has-text("Login")',
                'button:has-text("Log in")',
                'button:has-text("Sign in")',
                'a:has-text("Login")',
                'a:has-text("Log in")',
                'text=Sign up to get',
                'text=Login',
                'text=Log in'
            ];

            for (const selector of loginSelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    if (await element.isVisible({ timeout: 500 })) {
                        console.log(`🚨 Login required detected: ${selector}`);
                        return true;
                    }
                } catch (error) {
                    // Continue checking other selectors
                }
            }

            return false;
        } catch (error) {
            console.log('⚠️ Error checking login status:', error.message);
            return false;
        }
    }

    async sendTelegramAlert(title, message) {
        try {
            const fullMessage = `${title}\n\n${message}\n\nTime: ${new Date().toISOString()}`;

            await axios.post(`https://api.telegram.org/bot${this.telegramBotToken}/sendMessage`, {
                chat_id: this.telegramChatId,
                text: fullMessage,
                parse_mode: 'HTML'
            });

            console.log('📱 Telegram alert sent successfully');
        } catch (error) {
            console.log('❌ Failed to send Telegram alert:', error.message);
        }
    }

    async quickVerifySuccess(orderType) {
        try {
            // Quick verification with reduced timeout
            await this.page.waitForTimeout(500); // Reduced from longer waits

            // Check for success indicators
            const successSelectors = [
                'text=Order submitted',
                'text=Success',
                'text=Completed',
                '.success',
                '.order-success'
            ];

            for (const selector of successSelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    if (await element.isVisible({ timeout: 300 })) {
                        return true;
                    }
                } catch (error) {
                    // Continue checking
                }
            }

            return true; // Assume success if no error indicators
        } catch (error) {
            console.log('⚠️ Quick verification failed:', error.message);
            return false;
        }
    }

    async fetchBalance() {
        try {
            console.log('💰 Fetching balance from MEXC frontend...');

            if (!this.page) {
                throw new Error('Browser not connected');
            }

            // Try multiple balance selectors for MEXC frontend
            const balanceSelectors = [
                'span.AssetsItem_num__9eLwJ',
                '.AssetsItem_num__9eLwJ',
                '[class*="AssetsItem_num"]',
                '[class*="assets"] [class*="num"]',
                'span:has-text("USDT")',
                'text=/\\d+\\.\\d+\\s*USDT/',
                '.balance-value',
                '.wallet-balance',
                '[data-testid*="balance"]'
            ];

            let balanceText = null;
            let usedSelector = null;

            for (const selector of balanceSelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    if (await element.isVisible({ timeout: 1000 })) {
                        balanceText = await element.textContent();
                        usedSelector = selector;
                        console.log(`✅ Found balance with selector: ${selector}`);
                        break;
                    }
                } catch (error) {
                    // Continue to next selector
                }
            }

            if (!balanceText) {
                // Try to find any element containing USDT with numbers
                try {
                    const usdtElements = await this.page.locator('text=/\\d+\\.\\d+.*USDT/').all();
                    if (usdtElements.length > 0) {
                        balanceText = await usdtElements[0].textContent();
                        usedSelector = 'USDT pattern match';
                        console.log('✅ Found balance with USDT pattern match');
                    }
                } catch (error) {
                    // Continue
                }
            }

            if (balanceText) {
                // Extract numeric value from text like "‎2.2951 USDT" or "2.2951USDT"
                const balanceMatch = balanceText.match(/[\d,]+\.?\d*/);
                if (balanceMatch) {
                    const balance = parseFloat(balanceMatch[0].replace(/,/g, ''));

                    this.lastBalance = {
                        raw: balanceText.trim(),
                        value: balance,
                        currency: 'USDT',
                        selector: usedSelector,
                        timestamp: new Date().toISOString()
                    };
                    this.lastBalanceUpdate = Date.now();

                    console.log(`💰 Balance fetched: ${balance} USDT`);
                    console.log(`   Raw text: "${balanceText.trim()}"`);
                    console.log(`   Selector: ${usedSelector}`);

                    return this.lastBalance;
                } else {
                    console.log(`⚠️ Could not parse balance from: "${balanceText}"`);
                }
            } else {
                console.log('⚠️ No balance element found');
            }

            return null;

        } catch (error) {
            console.error('❌ Failed to fetch balance:', error.message);
            return null;
        }
    }

    async getBalance(forceRefresh = false) {
        try {
            // Return cached balance if recent (less than 30 seconds old)
            if (!forceRefresh && this.lastBalance && this.lastBalanceUpdate) {
                const age = Date.now() - this.lastBalanceUpdate;
                if (age < 30000) { // 30 seconds
                    console.log(`💰 Using cached balance: ${this.lastBalance.value} USDT (${Math.round(age/1000)}s old)`);
                    return this.lastBalance;
                }
            }

            // Fetch fresh balance
            return await this.fetchBalance();

        } catch (error) {
            console.error('❌ Failed to get balance:', error.message);
            return this.lastBalance; // Return cached balance as fallback
        }
    }

    async disconnect() {
        try {
            if (this.browser) {
                await this.browser.close();
                console.log('✅ Browser connection closed');
            }
        } catch (error) {
            console.log('⚠️ Error closing browser:', error.message);
        }
    }

    async disconnect() {
        try {
            // Stop background monitoring
            this.stopBackgroundMonitoring();

            // Close browser connection if exists
            if (this.browser) {
                await this.browser.close();
                this.browser = null;
                this.page = null;
            }

            console.log('✅ Trader disconnected and cleaned up');
        } catch (error) {
            console.log('⚠️ Error during disconnect:', error.message);
        }
    }
}

module.exports = MexcFuturesTrader;
