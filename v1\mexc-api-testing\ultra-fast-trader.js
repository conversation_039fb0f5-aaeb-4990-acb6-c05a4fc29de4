const { chromium } = require('playwright');

class UltraFastMEXCTrader {
    constructor() {
        this.browser = null;
        this.page = null;
        this.startTime = null;
    }

    async connectToRemoteBrowser() {
        console.log('🔗 Connecting to remote Chrome...');
        
        try {
            this.browser = await chromium.connectOverCDP('http://localhost:9222');
            const contexts = this.browser.contexts();
            
            if (contexts.length > 0) {
                const context = contexts[0];
                const pages = context.pages();
                this.page = pages.length > 0 ? pages[0] : await context.newPage();
            } else {
                const context = await this.browser.newContext();
                this.page = await context.newPage();
            }
            
            console.log('✅ Connected');
            return true;
        } catch (error) {
            console.error('❌ Connection failed:', error.message);
            return false;
        }
    }

    async ultraFastOrder() {
        this.startTime = Date.now();
        console.log('⚡ ULTRA FAST ORDER EXECUTION...');
        
        try {
            // Navigate if not already on MEXC
            const url = this.page.url();
            if (!url.includes('mexc.com') || !url.includes('TRU')) {
                await this.page.goto('https://www.mexc.com/futures/TRU_USDT', { 
                    waitUntil: 'domcontentloaded',
                    timeout: 5000 
                });
                await this.page.waitForTimeout(1000);
            }

            // STEP 1: Click Buy (no analysis, just execute)
            const buySelectors = [
                'button:has-text("Buy")',
                'button:has-text("Long")',
                '.buy-btn',
                '[class*="buy"]:not(input)'
            ];

            let buySuccess = false;
            for (const selector of buySelectors) {
                try {
                    await this.page.locator(selector).first().click({ timeout: 300 });
                    buySuccess = true;
                    console.log('✅ Buy clicked');
                    break;
                } catch (error) {
                    continue;
                }
            }

            if (!buySuccess) {
                throw new Error('Buy button not found');
            }

            // STEP 2: Market order (parallel with quantity)
            const marketPromise = (async () => {
                const marketSelectors = ['button:has-text("Market")', '.market-btn'];
                for (const selector of marketSelectors) {
                    try {
                        await this.page.locator(selector).first().click({ timeout: 200 });
                        console.log('✅ Market selected');
                        break;
                    } catch (error) {
                        continue;
                    }
                }
            })();

            // STEP 3: Enter quantity (parallel)
            const quantityPromise = (async () => {
                await this.page.waitForTimeout(200); // Small delay for UI update
                
                const quantitySelectors = [
                    'input[placeholder*="amount"]',
                    'input[placeholder*="quantity"]',
                    'input[type="number"]'
                ];

                for (const selector of quantitySelectors) {
                    try {
                        const input = this.page.locator(selector).first();
                        await input.click({ timeout: 200 });
                        await input.fill('40');
                        console.log('✅ Quantity entered');
                        return true;
                    } catch (error) {
                        continue;
                    }
                }

                // Fallback: try percentage buttons
                const percentSelectors = ['button:has-text("25%")', 'button:has-text("50%)'];
                for (const selector of percentSelectors) {
                    try {
                        await this.page.locator(selector).first().click({ timeout: 200 });
                        console.log('✅ Percentage used');
                        return true;
                    } catch (error) {
                        continue;
                    }
                }
                
                return false;
            })();

            // Wait for both market and quantity operations
            await Promise.all([marketPromise, quantityPromise]);

            // STEP 4: Submit immediately
            await this.page.waitForTimeout(100); // Minimal delay for UI update

            const submitSelectors = [
                'button:has-text("Buy")',
                'button:has-text("Long")',
                'button:has-text("Place")',
                'button:has-text("Submit")',
                '.submit-btn',
                '.place-order-btn'
            ];

            let submitSuccess = false;
            for (const selector of submitSelectors) {
                try {
                    await this.page.locator(selector).first().click({ timeout: 300 });
                    submitSuccess = true;
                    console.log('✅ Order submitted');
                    break;
                } catch (error) {
                    continue;
                }
            }

            const executionTime = Date.now() - this.startTime;

            // Quick confirmation check (non-blocking)
            let confirmationFound = false;
            try {
                const confirmationSelectors = [
                    'text=successfully',
                    'text=Success',
                    'text=placed',
                    'text=Purchased',
                    '.success',
                    '.toast'
                ];

                for (const selector of confirmationSelectors) {
                    try {
                        const isVisible = await this.page.locator(selector).first().isVisible({ timeout: 300 });
                        if (isVisible) {
                            const text = await this.page.locator(selector).first().textContent();
                            console.log(`🎉 Confirmation: ${text}`);
                            confirmationFound = true;
                            break;
                        }
                    } catch (error) {
                        continue;
                    }
                }
            } catch (error) {
                // Non-blocking
            }

            console.log('\n⚡ ULTRA FAST RESULTS:');
            console.log('=====================');
            console.log(`⏱️ Execution time: ${executionTime}ms`);
            console.log(`🎯 Under 2 seconds: ${executionTime < 2000 ? '🏆 YES!' : '❌ NO'}`);
            console.log(`📋 Order submitted: ${submitSuccess ? '✅ YES' : '❌ NO'}`);
            console.log(`🎉 Confirmation: ${confirmationFound ? '✅ YES' : '⚠️ CHECKING...'}`);

            const result = {
                success: submitSuccess,
                executionTime,
                targetAchieved: executionTime < 2000,
                confirmationDetected: confirmationFound,
                timestamp: new Date().toISOString()
            };

            // Save results
            require('fs').writeFileSync('ultra-fast-results.json', JSON.stringify(result, null, 2));

            return result;

        } catch (error) {
            const executionTime = Date.now() - this.startTime;
            console.error(`❌ Ultra fast order failed after ${executionTime}ms:`, error.message);
            
            return {
                success: false,
                executionTime,
                error: error.message
            };
        }
    }

    async verifyOrderPlaced() {
        console.log('🔍 Final verification...');
        
        try {
            // Wait a bit for any delayed confirmations
            await this.page.waitForTimeout(2000);
            
            // Look for success indicators
            const successIndicators = [
                'text=Purchased successfully',
                'text=Order placed',
                'text=Success',
                'text=Filled',
                'text=Executed',
                '.order-success',
                '.success-message'
            ];

            for (const indicator of successIndicators) {
                try {
                    const element = this.page.locator(indicator).first();
                    const isVisible = await element.isVisible({ timeout: 1000 });
                    if (isVisible) {
                        const text = await element.textContent();
                        console.log(`✅ Final confirmation: ${text}`);
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }

            console.log('⚠️ No final confirmation found');
            return false;
        } catch (error) {
            console.log('❌ Verification error:', error.message);
            return false;
        }
    }
}

async function runUltraFastTrader() {
    const trader = new UltraFastMEXCTrader();
    
    try {
        const connected = await trader.connectToRemoteBrowser();
        if (!connected) {
            throw new Error('Could not connect to remote browser');
        }

        console.log('\n🚨 ULTRA FAST MODE ACTIVATED');
        console.log('============================');
        console.log('⚡ Target: Sub-2 second execution');
        console.log('🎯 Symbol: TRUUSDT');
        console.log('📈 Side: Buy/Long');
        console.log('🔢 Quantity: 40');
        console.log('\nExecuting in 2 seconds...');
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const result = await trader.ultraFastOrder();
        
        // Final verification
        const verified = await trader.verifyOrderPlaced();
        
        if (result.success && result.targetAchieved) {
            console.log('\n🏆 MISSION ACCOMPLISHED!');
            console.log('⚡ ULTRA FAST ORDER PLACED UNDER 2 SECONDS!');
        } else if (result.success) {
            console.log('\n✅ Order placed successfully!');
            console.log(`⏱️ Time: ${result.executionTime}ms (target: <2000ms)`);
        } else {
            console.log('\n❌ Order placement failed');
        }

        if (verified) {
            console.log('🎉 Order placement VERIFIED!');
        }
        
        return result;
        
    } catch (error) {
        console.error('💥 Ultra fast trader failed:', error.message);
        return { success: false, error: error.message };
    }
}

if (require.main === module) {
    console.log('⚡ MEXC ULTRA FAST TRADER');
    console.log('========================');
    console.log('🚀 Optimized for sub-2 second execution');
    console.log('📋 Requires: Chrome with --remote-debugging-port=9222');
    console.log('');
    
    runUltraFastTrader()
        .then(result => {
            console.log('\n🏁 Ultra fast session completed');
            process.exit(result.success ? 0 : 1);
        })
        .catch(error => {
            console.error('💥 Session crashed:', error);
            process.exit(1);
        });
}

module.exports = UltraFastMEXCTrader;
