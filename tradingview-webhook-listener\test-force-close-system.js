const axios = require('axios');

// Test configuration
const BASE_URL = 'http://localhost:80';
const TEST_TELEGRAM_CONFIG = {
    telegramBotToken: '**********************************************',
    telegramChatId: '243673531',
    telegramEnabled: true
};

async function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function testTelegramConfiguration() {
    console.log('\n🔧 Testing Telegram Configuration...');
    
    try {
        const response = await axios.post(`${BASE_URL}/api/config`, TEST_TELEGRAM_CONFIG);
        
        if (response.data.success) {
            console.log('✅ Telegram configuration updated successfully');
            return true;
        } else {
            console.log('❌ Failed to update Telegram configuration:', response.data.error);
            return false;
        }
    } catch (error) {
        console.log('❌ Error updating Telegram configuration:', error.message);
        return false;
    }
}

async function checkSystemStatus() {
    console.log('\n📊 Checking System Status...');
    
    try {
        const response = await axios.get(`${BASE_URL}/api/status`);
        const status = response.data;
        
        console.log('Bot Active:', status.botActive);
        console.log('MEXC Connected:', status.mexcConnected);
        console.log('Telegram Status:', status.telegramStatus);
        console.log('Total Signals Received:', status.totalSignalsReceived);
        console.log('Total Trades Executed:', status.totalTradesExecuted);
        
        return status;
    } catch (error) {
        console.log('❌ Error checking system status:', error.message);
        return null;
    }
}

async function sendTestSignal(tradeType, symbol = 'TRUUSDT') {
    console.log(`\n📡 Sending ${tradeType} signal for ${symbol}...`);
    
    const signal = {
        symbol: symbol,
        trade: tradeType,
        last_price: "0.03295"
    };
    
    try {
        const response = await axios.post(`${BASE_URL}/webhook`, signal);
        
        if (response.data.success) {
            console.log(`✅ ${tradeType} signal processed successfully`);
            console.log('Execution Time:', response.data.executionTime + 'ms');
            console.log('Force Close Result:', response.data.forceCloseResult ? 
                `${response.data.forceCloseResult.closedPositions.length} positions closed` : 'No positions to close');
            return response.data;
        } else {
            console.log(`❌ ${tradeType} signal failed:`, response.data.error);
            return null;
        }
    } catch (error) {
        console.log(`❌ Error sending ${tradeType} signal:`, error.message);
        return null;
    }
}

async function getActivePositions() {
    console.log('\n📈 Checking Active Positions...');
    
    try {
        const response = await axios.get(`${BASE_URL}/api/positions`);
        const positions = response.data;
        
        console.log(`Active Positions: ${positions.length}`);
        positions.forEach((pos, index) => {
            console.log(`  ${index + 1}. ${pos.symbol} ${pos.direction.toUpperCase()} - ${pos.quantity} USDT (${pos.status})`);
        });
        
        return positions;
    } catch (error) {
        console.log('❌ Error getting positions:', error.message);
        return [];
    }
}

async function testForceCloseScenario() {
    console.log('\n🔄 Testing Force Close Scenario...');
    console.log('This test will:');
    console.log('1. Send a BUY signal to open a long position');
    console.log('2. Wait 3 seconds');
    console.log('3. Send a SELL signal to force close the long and open a short');
    console.log('4. Wait 3 seconds');
    console.log('5. Send another BUY signal to force close the short and open a new long');
    
    // Step 1: Open long position
    console.log('\n--- Step 1: Opening Long Position ---');
    await sendTestSignal('buy');
    await sleep(3000);
    await getActivePositions();
    
    // Step 2: Force close long and open short
    console.log('\n--- Step 2: Force Close Long, Open Short ---');
    await sendTestSignal('sell');
    await sleep(3000);
    await getActivePositions();
    
    // Step 3: Force close short and open new long
    console.log('\n--- Step 3: Force Close Short, Open New Long ---');
    await sendTestSignal('buy');
    await sleep(3000);
    await getActivePositions();
    
    console.log('\n✅ Force close scenario test completed!');
}

async function testRetryMechanism() {
    console.log('\n🔄 Testing Retry Mechanism...');
    console.log('Note: This test requires the MEXC Futures Trader service to be stopped');
    console.log('to simulate failures and test the retry + Telegram alert system.');
    
    // Send a signal that should fail due to service unavailability
    const result = await sendTestSignal('buy');
    
    if (result && result.retryLimitExceeded) {
        console.log('✅ Retry mechanism working - limit exceeded as expected');
        console.log('📱 Check Telegram for critical alert notification');
    } else if (result && result.success) {
        console.log('⚠️ Trade succeeded - retry test requires MEXC service to be stopped');
    } else {
        console.log('❓ Unexpected result from retry test');
    }
}

async function runAllTests() {
    console.log('🚀 Starting Force Close System Tests...');
    console.log('='.repeat(50));
    
    // Test 1: Configure Telegram
    const telegramConfigured = await testTelegramConfiguration();
    if (!telegramConfigured) {
        console.log('⚠️ Continuing tests without Telegram configuration');
    }
    
    await sleep(2000);
    
    // Test 2: Check system status
    const status = await checkSystemStatus();
    if (!status) {
        console.log('❌ Cannot proceed - system status check failed');
        return;
    }
    
    if (!status.botActive) {
        console.log('⚠️ Bot is not active - some tests may not work properly');
    }
    
    await sleep(2000);
    
    // Test 3: Check initial positions
    await getActivePositions();
    
    await sleep(2000);
    
    // Test 4: Force close scenario
    await testForceCloseScenario();
    
    await sleep(2000);
    
    // Test 5: Final status check
    console.log('\n📊 Final System Status:');
    await checkSystemStatus();
    await getActivePositions();
    
    console.log('\n' + '='.repeat(50));
    console.log('🎉 All tests completed!');
    console.log('📱 Check your Telegram for notifications during the test');
    console.log('📋 Check the dashboard at http://localhost:80 for detailed logs');
}

// Run tests if this script is executed directly
if (require.main === module) {
    runAllTests().catch(error => {
        console.error('❌ Test execution failed:', error.message);
        process.exit(1);
    });
}

module.exports = {
    testTelegramConfiguration,
    checkSystemStatus,
    sendTestSignal,
    getActivePositions,
    testForceCloseScenario,
    testRetryMechanism,
    runAllTests
};
