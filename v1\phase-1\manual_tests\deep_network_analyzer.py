#!/usr/bin/env python3
"""
Deep Network Traffic & JavaScript Obfuscation Analyzer
Advanced analysis to crack MEXC's signature algorithm
"""

import json
import time
import re
import hashlib
import hmac
import base64
from playwright.sync_api import sync_playwright
from curl_cffi import requests
from dotenv import dotenv_values

class DeepNetworkAnalyzer:
    """Deep analysis of network traffic and obfuscated JavaScript"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        self.uc_token = vals.get('uc_token')
        self.session = requests.Session(impersonate='chrome124')
        
        print("🔬 Deep Network Traffic & JS Obfuscation Analyzer")
        print("="*55)
    
    def setup_advanced_browser_monitoring(self):
        """Setup advanced browser monitoring with network interception"""
        
        print("🌐 Setting up advanced browser monitoring...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                print("❌ No browser contexts found")
                return False
            
            context = self.browser.contexts[0]
            
            if context.pages:
                self.page = context.pages[0]
            else:
                self.page = context.new_page()
            
            # Navigate to MEXC
            if 'mexc.com' not in self.page.url:
                self.page.goto('https://www.mexc.com/futures/BTC_USDT', wait_until='domcontentloaded')
                time.sleep(5)
            
            # Inject session tokens
            self.page.evaluate(f"""
                () => {{
                    localStorage.setItem('authorization', '{self.auth}');
                    localStorage.setItem('u_id', '{self.auth}');
                    {f"localStorage.setItem('uc_token', '{self.uc_token}');" if self.uc_token else ""}
                }}
            """)
            
            # Reload to apply session
            self.page.reload(wait_until='domcontentloaded')
            time.sleep(5)
            
            # Setup advanced monitoring
            self._setup_deep_monitoring()
            
            print("✅ Advanced browser monitoring setup completed")
            return True
            
        except Exception as e:
            print(f"❌ Browser setup failed: {e}")
            return False
    
    def _setup_deep_monitoring(self):
        """Setup deep monitoring and obfuscation analysis"""
        
        monitoring_code = """
            window.deepAnalyzer = {
                capturedData: [],
                originalFunctions: {},
                
                init() {
                    console.log('🔬 Deep analyzer initializing...');
                    
                    // Store original functions
                    this.originalFunctions.fetch = window.fetch;
                    this.originalFunctions.XMLHttpRequest = window.XMLHttpRequest;
                    
                    // Setup comprehensive interception
                    this.setupFetchInterception();
                    this.setupXHRInterception();
                    this.setupCryptoInterception();
                    this.setupObfuscationAnalysis();
                    
                    console.log('✅ Deep analyzer ready');
                },
                
                setupFetchInterception() {
                    const self = this;
                    
                    window.fetch = function(...args) {
                        const [url, options] = args;
                        
                        // Capture all requests with detailed analysis
                        if (options && options.method === 'POST') {
                            const headers = options.headers || {};
                            let body = null;
                            
                            try {
                                if (options.body) {
                                    body = typeof options.body === 'string' ? JSON.parse(options.body) : options.body;
                                }
                            } catch (e) {
                                body = options.body;
                            }
                            
                            // Detailed capture
                            const capture = {
                                type: 'fetch',
                                url: url,
                                method: options.method,
                                headers: headers,
                                body: body,
                                timestamp: Date.now(),
                                stackTrace: new Error().stack
                            };
                            
                            // Special handling for order requests
                            if (url.includes('order')) {
                                capture.isOrderRequest = true;
                                capture.signature = headers['x-mxc-sign'];
                                capture.nonce = headers['x-mxc-nonce'];
                                
                                console.log('🎯 Order request captured:', {
                                    url: url,
                                    signature: capture.signature,
                                    nonce: capture.nonce,
                                    body: body
                                });
                                
                                // Try to trace signature generation
                                self.traceSignatureGeneration(capture);
                            }
                            
                            self.capturedData.push(capture);
                        }
                        
                        return self.originalFunctions.fetch.apply(this, args);
                    };
                },
                
                setupXHRInterception() {
                    const self = this;
                    const OriginalXHR = window.XMLHttpRequest;
                    
                    window.XMLHttpRequest = function() {
                        const xhr = new OriginalXHR();
                        const originalOpen = xhr.open;
                        const originalSend = xhr.send;
                        const originalSetRequestHeader = xhr.setRequestHeader;
                        
                        let method, url;
                        let headers = {};
                        let body = null;
                        
                        xhr.open = function(m, u, ...args) {
                            method = m;
                            url = u;
                            return originalOpen.apply(this, [m, u, ...args]);
                        };
                        
                        xhr.setRequestHeader = function(name, value) {
                            headers[name] = value;
                            
                            // Special monitoring for signature header
                            if (name === 'x-mxc-sign') {
                                console.log('🔐 Signature header set:', value);
                                self.analyzeSignatureContext(value);
                            }
                            
                            return originalSetRequestHeader.apply(this, [name, value]);
                        };
                        
                        xhr.send = function(data) {
                            if (url && url.includes('order') && method === 'POST') {
                                try {
                                    body = data ? JSON.parse(data) : null;
                                } catch (e) {
                                    body = data;
                                }
                                
                                const capture = {
                                    type: 'xhr',
                                    url: url,
                                    method: method,
                                    headers: headers,
                                    body: body,
                                    timestamp: Date.now(),
                                    stackTrace: new Error().stack
                                };
                                
                                self.capturedData.push(capture);
                                console.log('🎯 XHR order request captured');
                            }
                            
                            return originalSend.apply(this, [data]);
                        };
                        
                        return xhr;
                    };
                },
                
                setupCryptoInterception() {
                    const self = this;
                    
                    // Monitor crypto operations
                    if (window.crypto && window.crypto.subtle) {
                        const originalSign = window.crypto.subtle.sign;
                        const originalDigest = window.crypto.subtle.digest;
                        
                        window.crypto.subtle.sign = function(...args) {
                            console.log('🔐 Crypto.subtle.sign called:', args);
                            self.capturedData.push({
                                type: 'crypto_sign',
                                args: args,
                                timestamp: Date.now(),
                                stackTrace: new Error().stack
                            });
                            return originalSign.apply(this, args);
                        };
                        
                        window.crypto.subtle.digest = function(...args) {
                            console.log('🔐 Crypto.subtle.digest called:', args);
                            self.capturedData.push({
                                type: 'crypto_digest',
                                args: args,
                                timestamp: Date.now(),
                                stackTrace: new Error().stack
                            });
                            return originalDigest.apply(this, args);
                        };
                    }
                    
                    // Monitor common crypto libraries
                    if (window.CryptoJS) {
                        const originalHmac = window.CryptoJS.HmacSHA256;
                        const originalSHA256 = window.CryptoJS.SHA256;
                        
                        window.CryptoJS.HmacSHA256 = function(...args) {
                            console.log('🔐 CryptoJS.HmacSHA256 called:', args);
                            self.capturedData.push({
                                type: 'cryptojs_hmac',
                                args: args,
                                timestamp: Date.now(),
                                stackTrace: new Error().stack
                            });
                            return originalHmac.apply(this, args);
                        };
                        
                        window.CryptoJS.SHA256 = function(...args) {
                            console.log('🔐 CryptoJS.SHA256 called:', args);
                            self.capturedData.push({
                                type: 'cryptojs_sha256',
                                args: args,
                                timestamp: Date.now(),
                                stackTrace: new Error().stack
                            });
                            return originalSHA256.apply(this, args);
                        };
                    }
                },
                
                setupObfuscationAnalysis() {
                    // Analyze obfuscated code patterns
                    const scripts = document.querySelectorAll('script');
                    
                    scripts.forEach((script, index) => {
                        if (script.textContent) {
                            const content = script.textContent;
                            
                            // Look for obfuscation patterns
                            const obfuscationPatterns = [
                                /\\x[0-9a-f]{2}/gi,  // Hex encoding
                                /\\u[0-9a-f]{4}/gi,  // Unicode encoding
                                /eval\\s*\\(/gi,      // Eval usage
                                /Function\\s*\\(/gi,  // Function constructor
                                /atob\\s*\\(/gi,      // Base64 decode
                                /btoa\\s*\\(/gi,      // Base64 encode
                                /String\\.fromCharCode/gi, // Character code conversion
                                /\\['\\\\x[0-9a-f]+'/gi    // Bracket notation with hex
                            ];
                            
                            const foundPatterns = [];
                            obfuscationPatterns.forEach((pattern, i) => {
                                const matches = content.match(pattern);
                                if (matches && matches.length > 5) { // Threshold for obfuscation
                                    foundPatterns.push({
                                        pattern: pattern.toString(),
                                        count: matches.length
                                    });
                                }
                            });
                            
                            if (foundPatterns.length > 0) {
                                console.log(`🔍 Obfuscated script found (${index}):`, foundPatterns);
                                
                                // Try to deobfuscate
                                this.attemptDeobfuscation(content, index);
                            }
                        }
                    });
                },
                
                traceSignatureGeneration(capture) {
                    // Try to trace where signature was generated
                    const stackLines = capture.stackTrace.split('\\n');
                    
                    console.log('🔍 Tracing signature generation:');
                    stackLines.forEach((line, index) => {
                        if (index < 10) { // First 10 stack frames
                            console.log(`   ${index}: ${line}`);
                        }
                    });
                    
                    // Look for signature-related function names in stack
                    const signatureKeywords = ['sign', 'hash', 'crypto', 'hmac', 'sha'];
                    const relevantFrames = stackLines.filter(line => 
                        signatureKeywords.some(keyword => 
                            line.toLowerCase().includes(keyword)
                        )
                    );
                    
                    if (relevantFrames.length > 0) {
                        console.log('🎯 Relevant stack frames:', relevantFrames);
                    }
                },
                
                analyzeSignatureContext(signature) {
                    // Analyze the context when signature is set
                    console.log('🔐 Analyzing signature context:', signature);
                    
                    // Get current call stack
                    const stack = new Error().stack;
                    console.log('📍 Signature set from:', stack.split('\\n')[3]);
                    
                    // Try to find the function that generated this signature
                    this.findSignatureFunction(signature);
                },
                
                findSignatureFunction(signature) {
                    // Search for functions that might have generated this signature
                    const globalFunctions = [];
                    
                    for (const key in window) {
                        try {
                            if (typeof window[key] === 'function') {
                                const funcStr = window[key].toString();
                                
                                // Look for signature-related code
                                if (funcStr.includes('x-mxc-sign') || 
                                    funcStr.includes('signature') ||
                                    funcStr.includes('hmac') ||
                                    funcStr.includes('sha256')) {
                                    globalFunctions.push({
                                        name: key,
                                        code: funcStr.substring(0, 200)
                                    });
                                }
                            }
                        } catch (e) {}
                    }
                    
                    if (globalFunctions.length > 0) {
                        console.log('🎯 Found signature-related functions:', globalFunctions);
                    }
                },
                
                attemptDeobfuscation(content, scriptIndex) {
                    // Basic deobfuscation attempts
                    try {
                        // Try to decode hex strings
                        const hexMatches = content.match(/\\\\x[0-9a-f]{2}/gi);
                        if (hexMatches && hexMatches.length > 10) {
                            console.log(`🔓 Script ${scriptIndex} contains hex encoding`);
                        }
                        
                        // Try to decode unicode strings
                        const unicodeMatches = content.match(/\\\\u[0-9a-f]{4}/gi);
                        if (unicodeMatches && unicodeMatches.length > 10) {
                            console.log(`🔓 Script ${scriptIndex} contains unicode encoding`);
                        }
                        
                        // Look for base64 patterns
                        const base64Matches = content.match(/[A-Za-z0-9+/]{20,}={0,2}/g);
                        if (base64Matches && base64Matches.length > 5) {
                            console.log(`🔓 Script ${scriptIndex} contains base64 data`);
                            
                            // Try to decode some base64 strings
                            base64Matches.slice(0, 3).forEach((b64, i) => {
                                try {
                                    const decoded = atob(b64);
                                    if (decoded.includes('sign') || decoded.includes('crypto')) {
                                        console.log(`🎯 Decoded base64 ${i}:`, decoded.substring(0, 100));
                                    }
                                } catch (e) {}
                            });
                        }
                        
                    } catch (e) {
                        console.log(`❌ Deobfuscation failed for script ${scriptIndex}:`, e);
                    }
                },
                
                // Simulate order to trigger signature generation
                async simulateOrderForAnalysis(symbol, side, price, volume) {
                    console.log('🎯 Simulating order for deep analysis...');
                    
                    // Clear previous captures
                    this.capturedData = [];
                    
                    const orderData = {
                        symbol: symbol,
                        side: side,
                        openType: 1,
                        type: '2',
                        vol: volume,
                        leverage: 1,
                        marketCeiling: false,
                        price: price.toString(),
                        priceProtect: '0'
                    };
                    
                    // Try to trigger signature generation through various methods
                    
                    // Method 1: Direct API call
                    try {
                        await fetch('/api/v1/private/order/create', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'authorization': localStorage.getItem('authorization'),
                                'x-mxc-nonce': Date.now().toString(),
                                'x-mxc-sign': 'analysis_trigger'
                            },
                            body: JSON.stringify(orderData)
                        });
                    } catch (e) {
                        // Expected to fail
                    }
                    
                    // Method 2: Try to find and trigger order form
                    const orderForms = document.querySelectorAll('form, [class*="order"], [class*="trade"]');
                    console.log(`🔍 Found ${orderForms.length} potential order forms`);
                    
                    // Method 3: Look for order buttons and simulate events
                    const buttons = document.querySelectorAll('button, [role="button"]');
                    const orderButtons = Array.from(buttons).filter(btn => {
                        const text = btn.textContent?.toLowerCase() || '';
                        const className = btn.className?.toLowerCase() || '';
                        return text.includes('buy') || text.includes('sell') || 
                               text.includes('long') || text.includes('short') ||
                               className.includes('buy') || className.includes('sell');
                    });
                    
                    console.log(`🔍 Found ${orderButtons.length} potential order buttons`);
                    
                    return {
                        success: true,
                        capturedCount: this.capturedData.length,
                        orderForms: orderForms.length,
                        orderButtons: orderButtons.length
                    };
                },
                
                getAnalysisResults() {
                    return {
                        totalCaptures: this.capturedData.length,
                        orderRequests: this.capturedData.filter(c => c.isOrderRequest),
                        cryptoOperations: this.capturedData.filter(c => c.type.includes('crypto')),
                        allCaptures: this.capturedData
                    };
                }
            };
            
            // Initialize deep analyzer
            window.deepAnalyzer.init();
        """
        
        self.page.evaluate(monitoring_code)
        print("✅ Deep monitoring system configured")
    
    def run_deep_analysis(self, symbol: str = 'BTC_USDT'):
        """Run deep analysis to find signature algorithm"""
        
        print(f"🔬 Running deep analysis for {symbol}...")
        
        try:
            # Get market price for realistic test
            market_price = self.page.evaluate("""
                async () => {
                    try {
                        const response = await fetch('/api/v1/contract/ticker?symbol=BTC_USDT');
                        const data = await response.json();
                        if (data.code === 0 && data.data) {
                            const ticker = Array.isArray(data.data) ? data.data[0] : data.data;
                            return parseFloat(ticker.lastPrice || 0);
                        }
                        return 50000;
                    } catch (error) {
                        return 50000;
                    }
                }
            """)
            
            test_price = round(market_price * 0.3, 2) if market_price > 0 else 30000
            
            print(f"📊 Using test price: ${test_price:,.2f}")
            
            # Simulate order for analysis
            simulation_result = self.page.evaluate(f"""
                () => window.deepAnalyzer.simulateOrderForAnalysis('{symbol}', 1, {test_price}, 1)
            """)
            
            print(f"✅ Simulation completed: {simulation_result}")
            
            # Wait for potential async operations
            time.sleep(3)
            
            # Get analysis results
            analysis_results = self.page.evaluate("() => window.deepAnalyzer.getAnalysisResults()")
            
            return analysis_results
            
        except Exception as e:
            print(f"❌ Deep analysis failed: {e}")
            return None
    
    def analyze_results(self, results):
        """Analyze the deep analysis results"""
        
        if not results:
            print("❌ No analysis results to process")
            return
        
        print(f"\n🔍 DEEP ANALYSIS RESULTS")
        print("="*35)
        
        total_captures = results.get('totalCaptures', 0)
        order_requests = results.get('orderRequests', [])
        crypto_operations = results.get('cryptoOperations', [])
        
        print(f"📊 Total captures: {total_captures}")
        print(f"🎯 Order requests: {len(order_requests)}")
        print(f"🔐 Crypto operations: {len(crypto_operations)}")
        
        # Analyze order requests
        if order_requests:
            print(f"\n📋 ORDER REQUEST ANALYSIS:")
            for i, req in enumerate(order_requests[:3]):  # First 3
                print(f"   Request {i+1}:")
                print(f"     URL: {req.get('url', 'Unknown')}")
                print(f"     Signature: {req.get('signature', 'None')}")
                print(f"     Nonce: {req.get('nonce', 'None')}")
        
        # Analyze crypto operations
        if crypto_operations:
            print(f"\n🔐 CRYPTO OPERATIONS:")
            for i, op in enumerate(crypto_operations[:5]):  # First 5
                print(f"   Operation {i+1}: {op.get('type', 'Unknown')}")
                if op.get('args'):
                    print(f"     Args: {str(op['args'])[:100]}...")
        
        # Look for patterns
        signatures = [req.get('signature') for req in order_requests if req.get('signature')]
        if signatures:
            print(f"\n🔍 SIGNATURE PATTERNS:")
            for i, sig in enumerate(signatures[:3]):
                if sig and sig != 'analysis_trigger':
                    print(f"   Signature {i+1}: {sig}")
                    print(f"     Length: {len(sig)}")
                    print(f"     Type: {'Hex' if all(c in '0123456789abcdef' for c in sig.lower()) else 'Mixed'}")
    
    def cleanup(self):
        """Cleanup resources"""
        if hasattr(self, 'browser'):
            self.browser.close()
        if hasattr(self, 'playwright'):
            self.playwright.stop()

def main():
    """Main deep analysis function"""
    
    analyzer = DeepNetworkAnalyzer()
    
    try:
        # Setup advanced monitoring
        if not analyzer.setup_advanced_browser_monitoring():
            print("❌ Failed to setup advanced monitoring")
            return
        
        # Run deep analysis
        results = analyzer.run_deep_analysis('BTC_USDT')
        
        # Analyze results
        analyzer.analyze_results(results)
        
        print(f"\n🎯 DEEP ANALYSIS COMPLETE!")
        print("If no real signatures were captured, try manually placing an order")
        print("in the browser while this analysis runs.")
        
    finally:
        analyzer.cleanup()

if __name__ == '__main__':
    main()
