#!/usr/bin/env python3
"""
SIGNATURE HEADER INTERCEPTOR
Focus specifically on intercepting the x-mxc-sign header setting
"""

import json
import time
import hashlib
import hmac
from playwright.sync_api import sync_playwright
from dotenv import dotenv_values

class SignatureHeaderInterceptor:
    """Intercept the exact moment the signature header is set"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        
        print("🎯 SIGNATURE HEADER INTERCEPTOR")
        print("="*40)
        print("🔥 TARGETING x-mxc-sign HEADER SETTING")
    
    def setup_header_hooks(self):
        """Setup hooks specifically for header setting"""
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                return False
            
            self.context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                mexc_page = self.context.new_page()
                mexc_page.goto('https://futures.mexc.com/exchange/BTC_USDT', wait_until='domcontentloaded')
            
            self.page = mexc_page
            
            # Inject FOCUSED header hooks
            self.page.evaluate("""
                window.signatureCaptures = [];
                window.allHeaderSets = [];
                
                console.log('🎯 Installing FOCUSED signature header hooks...');
                
                // Hook XMLHttpRequest.setRequestHeader with EXTREME detail
                const originalSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;
                XMLHttpRequest.prototype.setRequestHeader = function(name, value) {
                    // Capture ALL header sets for analysis
                    window.allHeaderSets.push({
                        name: name,
                        value: value,
                        timestamp: Date.now(),
                        stack: new Error().stack
                    });
                    
                    // Special handling for signature headers
                    if (name.toLowerCase().includes('sign') || name.toLowerCase().includes('nonce')) {
                        console.log(`🔥🔥🔥 HEADER SET: ${name} = ${value} 🔥🔥🔥`);
                        
                        const capture = {
                            type: 'header_set',
                            name: name,
                            value: value,
                            timestamp: Date.now(),
                            stack: new Error().stack,
                            // Try to capture the current state
                            currentUrl: window.location.href,
                            userAgent: navigator.userAgent
                        };
                        
                        window.signatureCaptures.push(capture);
                        
                        if (name.toLowerCase() === 'x-mxc-sign') {
                            console.log('🎉🎉🎉 SIGNATURE HEADER CAPTURED! 🎉🎉🎉');
                            console.log('Value:', value);
                            console.log('Stack:', new Error().stack);
                            
                            // Try to find where this value came from
                            console.log('Searching for signature generation...');
                            
                            // Look for this value in global variables
                            const searchResults = [];
                            
                            function searchForValue(obj, path = '', depth = 0) {
                                if (depth > 3) return;
                                
                                try {
                                    for (const key in obj) {
                                        const val = obj[key];
                                        
                                        if (typeof val === 'string' && val === value) {
                                            searchResults.push(`Found at: ${path}.${key}`);
                                        } else if (typeof val === 'object' && val !== null && depth < 2) {
                                            searchForValue(val, `${path}.${key}`, depth + 1);
                                        }
                                    }
                                } catch (e) {
                                    // Ignore access errors
                                }
                            }
                            
                            searchForValue(window, 'window');
                            
                            if (searchResults.length > 0) {
                                console.log('🔍 Signature found in:', searchResults);
                            }
                            
                            // Alert user immediately
                            alert(`SIGNATURE INTERCEPTED!\\n\\nValue: ${value}\\n\\nCheck console for stack trace and analysis!`);
                        }
                    }
                    
                    return originalSetRequestHeader.apply(this, arguments);
                };
                
                // Hook fetch headers as well
                const originalFetch = window.fetch;
                window.fetch = function(...args) {
                    const [url, options] = args;
                    
                    if (options && options.headers) {
                        for (const [name, value] of Object.entries(options.headers)) {
                            if (name.toLowerCase().includes('sign')) {
                                console.log(`🔥🔥🔥 FETCH SIGNATURE: ${name} = ${value} 🔥🔥🔥`);
                                
                                window.signatureCaptures.push({
                                    type: 'fetch_header',
                                    name: name,
                                    value: value,
                                    url: url,
                                    timestamp: Date.now(),
                                    stack: new Error().stack
                                });
                                
                                alert(`FETCH SIGNATURE INTERCEPTED!\\n\\nValue: ${value}\\n\\nURL: ${url}`);
                            }
                        }
                    }
                    
                    return originalFetch.apply(this, args);
                };
                
                // Hook any function that might generate signatures
                const originalMD5 = window.md5;
                if (originalMD5) {
                    window.md5 = function(...args) {
                        const result = originalMD5.apply(this, args);
                        console.log('🔥 MD5 called:', args, '=>', result);
                        
                        if (result && result.length === 32) {
                            window.signatureCaptures.push({
                                type: 'md5_call',
                                args: args,
                                result: result,
                                timestamp: Date.now(),
                                stack: new Error().stack
                            });
                        }
                        
                        return result;
                    };
                }
                
                // Hook any crypto operations
                if (window.CryptoJS) {
                    ['MD5', 'SHA1', 'SHA256', 'HmacMD5', 'HmacSHA1', 'HmacSHA256'].forEach(method => {
                        if (window.CryptoJS[method]) {
                            const original = window.CryptoJS[method];
                            window.CryptoJS[method] = function(...args) {
                                const result = original.apply(this, args);
                                
                                console.log(`🔥 CryptoJS.${method} called:`, args, '=>', result.toString());
                                
                                window.signatureCaptures.push({
                                    type: `cryptojs_${method.toLowerCase()}`,
                                    args: args.map(arg => arg.toString().substring(0, 100)),
                                    result: result.toString(),
                                    timestamp: Date.now(),
                                    stack: new Error().stack
                                });
                                
                                return result;
                            };
                        }
                    });
                }
                
                console.log('✅ FOCUSED signature header hooks installed!');
                console.log('🎯 Ready to intercept x-mxc-sign header setting!');
            """)
            
            print("✅ Header hooks setup completed")
            return True
            
        except Exception as e:
            print(f"❌ Setup failed: {e}")
            return False
    
    def wait_for_signature_header(self):
        """Wait specifically for signature header to be set"""
        
        print("\n🎯 WAITING FOR SIGNATURE HEADER")
        print("="*40)
        print()
        print("🚀 PLACE AN ORDER NOW!")
        print("   - The system will immediately alert when signature is captured")
        print("   - Use very low price to avoid fills")
        print("   - We'll get the exact stack trace and generation method")
        print()
        
        timeout = 300  # 5 minutes
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # Check for signature captures
                captures = self.page.evaluate("() => window.signatureCaptures || []")
                
                if captures:
                    print(f"\n🎉 CAPTURED {len(captures)} SIGNATURE-RELATED EVENTS!")
                    
                    for i, capture in enumerate(captures):
                        print(f"\n📋 CAPTURE #{i+1}:")
                        print(f"   Type: {capture['type']}")
                        
                        if capture['type'] in ['header_set', 'fetch_header']:
                            print(f"   Header: {capture['name']} = {capture['value']}")
                            
                            if 'sign' in capture['name'].lower():
                                print(f"🎉 SIGNATURE FOUND: {capture['value']}")
                                
                                # Analyze this signature
                                self.analyze_captured_signature(capture)
                                return True
                        
                        elif capture['type'].startswith('cryptojs_') or capture['type'] == 'md5_call':
                            print(f"   Method: {capture['type']}")
                            print(f"   Args: {capture['args']}")
                            print(f"   Result: {capture['result']}")
                            
                            # Check if this result could be our signature
                            if len(capture['result']) == 32:
                                print(f"🔍 Potential signature: {capture['result']}")
                
                # Show progress
                elapsed = int(time.time() - start_time)
                if elapsed % 20 == 0 and elapsed > 0:
                    print(f"⏱️  Waiting for signature... ({elapsed}s elapsed)")
                
                time.sleep(1)
                
            except Exception as e:
                print(f"⚠️  Error: {e}")
                time.sleep(1)
        
        print(f"\n⏰ Timeout reached")
        return False
    
    def analyze_captured_signature(self, capture):
        """Analyze the captured signature in detail"""
        
        print(f"\n🔍 ANALYZING CAPTURED SIGNATURE")
        print("="*45)
        
        signature = capture['value']
        stack = capture.get('stack', '')
        
        print(f"🔐 Signature: {signature}")
        print(f"📍 Header: {capture['name']}")
        print(f"⏰ Timestamp: {capture['timestamp']}")
        
        # Analyze stack trace
        print(f"\n📚 STACK TRACE ANALYSIS:")
        stack_lines = stack.split('\n')
        for i, line in enumerate(stack_lines[:10]):
            if line.strip():
                print(f"   {i+1}: {line.strip()}")
        
        # Try to reverse engineer this specific signature
        print(f"\n🧪 REVERSE ENGINEERING ATTEMPT:")
        
        # Get current timestamp and nonce
        current_time = int(time.time() * 1000)
        test_nonce = str(current_time)
        
        # Test common signature patterns
        test_patterns = [
            f"{self.auth}{test_nonce}",
            f"{test_nonce}{self.auth}",
            f"{self.auth}{test_nonce}BTC_USDT11000",
            f"{test_nonce}BTC_USDT11000{self.auth}",
        ]
        
        for i, pattern in enumerate(test_patterns):
            # Test MD5
            test_sig = hashlib.md5(pattern.encode()).hexdigest()
            print(f"   Pattern {i+1} MD5: {test_sig}")
            
            if test_sig == signature:
                print(f"🎉 MATCH FOUND! Pattern: {pattern}")
                return True
        
        print(f"❌ No immediate match found, but signature captured successfully!")
        return True
    
    def run_header_interception(self):
        """Run the header interception"""
        
        print("="*60)
        print("🎯 SIGNATURE HEADER INTERCEPTION")
        print("="*60)
        
        # Setup hooks
        if not self.setup_header_hooks():
            return False
        
        try:
            # Wait for signature
            if self.wait_for_signature_header():
                print("\n🎉 SIGNATURE HEADER SUCCESSFULLY INTERCEPTED!")
                return True
            else:
                print("\n🔍 No signature header captured")
                return False
            
        finally:
            # Cleanup
            if hasattr(self, 'browser'):
                self.browser.close()
            if hasattr(self, 'playwright'):
                self.playwright.stop()

def main():
    """Main function"""
    
    interceptor = SignatureHeaderInterceptor()
    interceptor.run_header_interception()

if __name__ == '__main__':
    main()
