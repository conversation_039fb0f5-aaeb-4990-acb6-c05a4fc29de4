const colors = require('colors');
require('dotenv').config();

// Import all test modules
const testCCXT = require('./tests/test-ccxt');
const testMexcFuturesSDK = require('./tests/test-mexc-futures-sdk');
const testGothamSDK = require('./tests/test-gotham-sdk');
const testMexcApiSDK = require('./tests/test-mexc-api-sdk');
// const testNodeMexcAPI = require('./tests/test-node-mexc-api'); // Removed due to build issues
const testCustomAPI = require('./tests/test-custom-api');

async function runAllTests() {
    console.log('🚀 MEXC API Package Testing Suite'.cyan.bold);
    console.log('====================================='.cyan);
    console.log(`Testing with API Key: ${process.env.MEXC_API_KEY?.substring(0, 10)}...`.gray);
    console.log(`Test Symbol: ${process.env.TEST_SYMBOL || 'BTCUSDT'}`.gray);
    console.log('');

    const tests = [
        { name: 'CCXT Library', testFn: testCCXT },
        { name: 'mexc-futures-sdk', testFn: testMexcFuturesSDK },
        { name: '@theothergothamdev/mexc-sdk', testFn: testGothamSDK },
        { name: 'mexc-api-sdk', testFn: testMexcApiSDK },
        // { name: 'node-mexc-api', testFn: testNodeMexcAPI }, // Removed due to build issues
        { name: 'Custom API Implementation', testFn: testCustomAPI }
    ];

    const results = [];

    for (const test of tests) {
        console.log(`\n${'='.repeat(50)}`.yellow);
        console.log(`Testing: ${test.name}`.yellow.bold);
        console.log(`${'='.repeat(50)}`.yellow);

        try {
            const result = await test.testFn();
            results.push(result);
            
            if (result.success) {
                console.log(`✅ ${test.name} - SUCCESS`.green.bold);
            } else {
                console.log(`❌ ${test.name} - FAILED: ${result.error}`.red.bold);
            }
        } catch (error) {
            console.log(`💥 ${test.name} - CRASHED: ${error.message}`.red.bold);
            results.push({
                success: false,
                library: test.name,
                error: `Test crashed: ${error.message}`
            });
        }

        // Add delay between tests
        await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // Generate summary report
    console.log('\n' + '='.repeat(60).cyan);
    console.log('📊 FINAL RESULTS SUMMARY'.cyan.bold);
    console.log('='.repeat(60).cyan);

    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);

    console.log(`\n✅ Successful: ${successful.length}/${results.length}`.green.bold);
    console.log(`❌ Failed: ${failed.length}/${results.length}`.red.bold);

    if (successful.length > 0) {
        console.log('\n🎉 WORKING PACKAGES:'.green.bold);
        successful.forEach(result => {
            console.log(`  ✓ ${result.library}`.green);
            if (result.features) {
                console.log(`    Features: ${result.features.join(', ')}`.gray);
            }
        });
    }

    if (failed.length > 0) {
        console.log('\n💔 FAILED PACKAGES:'.red.bold);
        failed.forEach(result => {
            console.log(`  ✗ ${result.library}`.red);
            console.log(`    Error: ${result.error}`.gray);
        });
    }

    // Recommendations
    console.log('\n🔍 RECOMMENDATIONS:'.blue.bold);
    
    const futuresCapable = successful.filter(r => 
        r.features && (r.features.includes('futures') || r.features.includes('futures_partial'))
    );
    
    if (futuresCapable.length > 0) {
        console.log('  📈 For futures trading, consider:'.blue);
        futuresCapable.forEach(result => {
            console.log(`    • ${result.library}`.blue);
        });
    } else {
        console.log('  ⚠️  No packages with confirmed futures support found'.yellow);
        console.log('  💡 Consider using the custom API implementation or browser automation'.yellow);
    }

    // Save detailed report
    const report = {
        timestamp: new Date().toISOString(),
        summary: {
            total: results.length,
            successful: successful.length,
            failed: failed.length
        },
        results: results,
        recommendations: {
            futuresCapable: futuresCapable.map(r => r.library),
            bestOptions: successful.slice(0, 3).map(r => r.library)
        }
    };

    require('fs').writeFileSync(
        'mexc-api-test-report.json',
        JSON.stringify(report, null, 2)
    );

    console.log('\n📄 Detailed report saved to: mexc-api-test-report.json'.cyan);
    
    return results;
}

// Run tests if this file is executed directly
if (require.main === module) {
    runAllTests()
        .then(results => {
            const successCount = results.filter(r => r.success).length;
            process.exit(successCount > 0 ? 0 : 1);
        })
        .catch(error => {
            console.error('Test suite crashed:', error);
            process.exit(1);
        });
}

module.exports = runAllTests;
