const axios = require('axios');

async function testWebhook() {
    console.log('🧪 Testing webhook...');
    
    const testPayload = {
        symbol: "TRUUSDT",
        trade: "buy",
        last_price: "0.000012064",
        leverege: "2"
    };

    try {
        const response = await axios.post('http://localhost:4000/webhook', testPayload, {
            timeout: 10000,
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log('✅ Webhook test successful!');
        console.log('Response:', JSON.stringify(response.data, null, 2));
        
    } catch (error) {
        console.error('❌ Webhook test failed:');
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Data:', error.response.data);
        } else {
            console.error('Error:', error.message);
        }
    }
}

testWebhook();
