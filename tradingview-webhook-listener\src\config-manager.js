const fs = require('fs');
const path = require('path');

class ConfigManager {
    constructor() {
        this.configPath = path.join(__dirname, '../config.json');
        this.config = this.loadConfig();
    }

    loadConfig() {
        try {
            if (fs.existsSync(this.configPath)) {
                const configData = fs.readFileSync(this.configPath, 'utf8');
                return { ...this.getDefaultConfig(), ...JSON.parse(configData) };
            }
        } catch (error) {
            console.error('Error loading config:', error.message);
        }
        
        return this.getDefaultConfig();
    }

    getDefaultConfig() {
        return {
            // Bot Control
            botActive: false,
            useNewTradingViewFormat: true,
            
            // MEXC API Configuration
            mexcApiKey: null,
            mexcSecretKey: null,

            // Telegram Configuration
            telegramBotToken: null,
            telegramChatId: null,
            telegramEnabled: false,

            // Money Management
            moneyManagementEnabled: true,
            moneyManagementMode: 'percentage', // 'percentage' or 'fixed'
            
            // Percentage-based settings
            positionSizePercentage: 50, // Use 50% of available balance
            
            // Fixed amount settings
            fixedTradeAmount: 100, // Fixed $100 per trade
            
            // Trade limits
            minTradeAmount: 0.1,     // Minimum trade size in USDT
            maxTradeAmount: 10000,   // Maximum trade size in USDT
            minRemainingBalance: 10, // Minimum balance to keep after trade
            
            // Default settings
            defaultQuantity: '0.3600', // Used when money management is disabled
            
            // Risk Management
            dailyTradeLimit: 0, // 0 = no limit
            maxDrawdown: 0,     // 0 = no limit
            
            // Trading Settings
            supportedSymbols: ['TRUUSDT', 'TRUUSDT.P'],
            defaultLeverage: 1,
            
            // Webhook Settings
            webhookSecret: null, // Optional webhook authentication
            
            // Logging
            logLevel: 'info',
            keepLogDays: 30,
            
            // UI Settings
            theme: 'dark',
            autoRefresh: true,
            refreshInterval: 5000, // 5 seconds
            
            // SL & TP Configuration
            slTpEnabled: true,
            slTpCalculationSource: 'mexc_entry_price',
            atrLength: 10,
            atrSmoothing: 'RMA',
            slMultiplier: 1.5,

            // Take Profit Settings
            tp1Enabled: true,
            tp1Reward: 2,
            tp1Percent: 100,
            tp2Enabled: false,
            tp2Reward: 8,
            tp2Percent: 30,
            tp3Enabled: false,
            tp3Reward: 10,
            tp3Percent: 40,

            // Stop Loss Settings
            slType: 'Normal', // Normal, Trailing, MoveToTPs
            startTrailingAtProfit: 1,
            trailingSource: 'Close', // Close, Open, High, Low, HLCO4
            trailingSourceAdd: 0, // Percentage to add to source
            trailingValue: 0.5,

            // Execution Validation
            maxExecutionTime: 2000, // 2 seconds in ms for sub-2 second execution
            maxPriceDifference: 2, // 2% max difference

            // System
            version: '1.0.0',
            lastUpdated: new Date().toISOString()
        };
    }

    getConfig() {
        return { ...this.config };
    }

    async updateConfig(updates) {
        try {
            // Validate updates
            const validatedUpdates = this.validateConfig(updates);
            
            // Merge with existing config
            this.config = { ...this.config, ...validatedUpdates };
            this.config.lastUpdated = new Date().toISOString();
            
            // Save to file
            await this.saveConfig();
            
            return { success: true, message: 'Configuration updated successfully' };
        } catch (error) {
            throw new Error(`Failed to update configuration: ${error.message}`);
        }
    }

    validateConfig(updates) {
        const validated = {};
        
        // Bot Control
        if (updates.botActive !== undefined) {
            validated.botActive = Boolean(updates.botActive);
        }

        if (updates.useNewTradingViewFormat !== undefined) {
            validated.useNewTradingViewFormat = Boolean(updates.useNewTradingViewFormat);
        }
        
        // API Keys
        if (updates.mexcApiKey !== undefined) {
            if (updates.mexcApiKey && typeof updates.mexcApiKey === 'string' && updates.mexcApiKey.trim()) {
                validated.mexcApiKey = updates.mexcApiKey.trim();
            } else {
                validated.mexcApiKey = null;
            }
        }
        
        if (updates.mexcSecretKey !== undefined) {
            if (updates.mexcSecretKey && typeof updates.mexcSecretKey === 'string' && updates.mexcSecretKey.trim()) {
                validated.mexcSecretKey = updates.mexcSecretKey.trim();
            } else {
                validated.mexcSecretKey = null;
            }
        }

        // Telegram Configuration
        if (updates.telegramBotToken !== undefined) {
            if (updates.telegramBotToken && typeof updates.telegramBotToken === 'string' && updates.telegramBotToken.trim()) {
                validated.telegramBotToken = updates.telegramBotToken.trim();
            } else {
                validated.telegramBotToken = null;
            }
        }

        if (updates.telegramChatId !== undefined) {
            if (updates.telegramChatId && typeof updates.telegramChatId === 'string' && updates.telegramChatId.trim()) {
                validated.telegramChatId = updates.telegramChatId.trim();
            } else {
                validated.telegramChatId = null;
            }
        }

        if (updates.telegramEnabled !== undefined) {
            validated.telegramEnabled = Boolean(updates.telegramEnabled);
        }

        // Money Management
        if (updates.moneyManagementEnabled !== undefined) {
            validated.moneyManagementEnabled = Boolean(updates.moneyManagementEnabled);
        }
        
        if (updates.moneyManagementMode !== undefined) {
            const validModes = ['percentage', 'fixed'];
            if (validModes.includes(updates.moneyManagementMode)) {
                validated.moneyManagementMode = updates.moneyManagementMode;
            } else {
                throw new Error(`Invalid money management mode. Must be one of: ${validModes.join(', ')}`);
            }
        }
        
        // Percentage settings
        if (updates.positionSizePercentage !== undefined) {
            const percentage = parseFloat(updates.positionSizePercentage);
            if (isNaN(percentage) || percentage < 1 || percentage > 100) {
                throw new Error('Position size percentage must be between 1 and 100');
            }
            validated.positionSizePercentage = percentage;
        }
        
        // Fixed amount settings
        if (updates.fixedTradeAmount !== undefined) {
            const amount = parseFloat(updates.fixedTradeAmount);
            if (isNaN(amount) || amount <= 0) {
                throw new Error('Fixed trade amount must be a positive number');
            }
            validated.fixedTradeAmount = amount;
        }
        
        // Trade limits
        if (updates.minTradeAmount !== undefined) {
            const amount = parseFloat(updates.minTradeAmount);
            if (isNaN(amount) || amount < 0) {
                throw new Error('Minimum trade amount must be a non-negative number');
            }
            validated.minTradeAmount = amount;
        }
        
        if (updates.maxTradeAmount !== undefined) {
            const amount = parseFloat(updates.maxTradeAmount);
            if (isNaN(amount) || amount <= 0) {
                throw new Error('Maximum trade amount must be a positive number');
            }
            validated.maxTradeAmount = amount;
        }
        
        if (updates.minRemainingBalance !== undefined) {
            const amount = parseFloat(updates.minRemainingBalance);
            if (isNaN(amount) || amount < 0) {
                throw new Error('Minimum remaining balance must be a non-negative number');
            }
            validated.minRemainingBalance = amount;
        }
        
        // Default quantity
        if (updates.defaultQuantity !== undefined) {
            const quantity = parseFloat(updates.defaultQuantity);
            if (isNaN(quantity) || quantity <= 0) {
                throw new Error('Default quantity must be a positive number');
            }
            validated.defaultQuantity = updates.defaultQuantity.toString();
        }
        
        // Risk management
        if (updates.dailyTradeLimit !== undefined) {
            const limit = parseInt(updates.dailyTradeLimit);
            if (isNaN(limit) || limit < 0) {
                throw new Error('Daily trade limit must be a non-negative integer');
            }
            validated.dailyTradeLimit = limit;
        }
        
        // UI Settings
        if (updates.theme !== undefined) {
            const validThemes = ['light', 'dark'];
            if (validThemes.includes(updates.theme)) {
                validated.theme = updates.theme;
            }
        }
        
        if (updates.autoRefresh !== undefined) {
            validated.autoRefresh = Boolean(updates.autoRefresh);
        }
        
        if (updates.refreshInterval !== undefined) {
            const interval = parseInt(updates.refreshInterval);
            if (isNaN(interval) || interval < 1000) {
                throw new Error('Refresh interval must be at least 1000ms');
            }
            validated.refreshInterval = interval;
        }
        
        // SL & TP Settings
        if (updates.slTpEnabled !== undefined) {
            validated.slTpEnabled = Boolean(updates.slTpEnabled);
        }

        if (updates.slTpCalculationSource !== undefined) {
            const validSources = ['mexc_entry_price', 'tradingview_last_price'];
            if (validSources.includes(updates.slTpCalculationSource)) {
                validated.slTpCalculationSource = updates.slTpCalculationSource;
            }
        }

        if (updates.atrLength !== undefined) {
            const length = parseInt(updates.atrLength);
            if (isNaN(length) || length < 1 || length > 100) {
                throw new Error('ATR Length must be between 1 and 100');
            }
            validated.atrLength = length;
        }

        if (updates.atrSmoothing !== undefined) {
            const validSmoothings = ['RMA', 'SMA', 'EMA'];
            if (validSmoothings.includes(updates.atrSmoothing)) {
                validated.atrSmoothing = updates.atrSmoothing;
            }
        }

        if (updates.slMultiplier !== undefined) {
            const multiplier = parseFloat(updates.slMultiplier);
            if (isNaN(multiplier) || multiplier <= 0) {
                throw new Error('SL Multiplier must be a positive number');
            }
            validated.slMultiplier = multiplier;
        }

        // Take Profit Settings
        ['tp1', 'tp2', 'tp3'].forEach(tp => {
            if (updates[`${tp}Enabled`] !== undefined) {
                validated[`${tp}Enabled`] = Boolean(updates[`${tp}Enabled`]);
            }

            if (updates[`${tp}Reward`] !== undefined) {
                const reward = parseFloat(updates[`${tp}Reward`]);
                if (isNaN(reward) || reward <= 0) {
                    throw new Error(`${tp.toUpperCase()} Reward must be a positive number`);
                }
                validated[`${tp}Reward`] = reward;
            }

            if (updates[`${tp}Percent`] !== undefined) {
                const percent = parseFloat(updates[`${tp}Percent`]);
                if (isNaN(percent) || percent <= 0 || percent > 100) {
                    throw new Error(`${tp.toUpperCase()} Percent must be between 1 and 100`);
                }
                validated[`${tp}Percent`] = percent;
            }
        });

        // Stop Loss Type Settings
        if (updates.slType !== undefined) {
            const validTypes = ['Normal', 'Trailing', 'MoveToTPs'];
            if (validTypes.includes(updates.slType)) {
                validated.slType = updates.slType;
            }
        }

        if (updates.trailingSource !== undefined) {
            const validSources = ['Close', 'Open', 'High', 'Low', 'HLCO4'];
            if (validSources.includes(updates.trailingSource)) {
                validated.trailingSource = updates.trailingSource;
            }
        }

        if (updates.trailingSourceAdd !== undefined) {
            const add = parseFloat(updates.trailingSourceAdd);
            if (isNaN(add) || add < 0 || add > 100) {
                throw new Error('Trailing source add must be between 0 and 100%');
            }
            validated.trailingSourceAdd = add;
        }

        // Execution Validation Settings
        if (updates.maxExecutionTime !== undefined) {
            const time = parseInt(updates.maxExecutionTime);
            if (isNaN(time) || time < 1000) {
                throw new Error('Max execution time must be at least 1000ms');
            }
            validated.maxExecutionTime = time;
        }

        if (updates.maxPriceDifference !== undefined) {
            const diff = parseFloat(updates.maxPriceDifference);
            if (isNaN(diff) || diff < 0 || diff > 50) {
                throw new Error('Max price difference must be between 0 and 50%');
            }
            validated.maxPriceDifference = diff;
        }

        // Cross-validation
        if (validated.minTradeAmount !== undefined && validated.maxTradeAmount !== undefined) {
            if (validated.minTradeAmount > validated.maxTradeAmount) {
                throw new Error('Minimum trade amount cannot be greater than maximum trade amount');
            }
        } else if (validated.minTradeAmount !== undefined && this.config.maxTradeAmount) {
            if (validated.minTradeAmount > this.config.maxTradeAmount) {
                throw new Error('Minimum trade amount cannot be greater than maximum trade amount');
            }
        } else if (validated.maxTradeAmount !== undefined && this.config.minTradeAmount) {
            if (this.config.minTradeAmount > validated.maxTradeAmount) {
                throw new Error('Maximum trade amount cannot be less than minimum trade amount');
            }
        }
        
        return validated;
    }

    async saveConfig() {
        try {
            const configData = JSON.stringify(this.config, null, 2);
            fs.writeFileSync(this.configPath, configData, 'utf8');
        } catch (error) {
            throw new Error(`Failed to save configuration: ${error.message}`);
        }
    }

    isConfigured() {
        return !!(this.config.mexcApiKey && this.config.mexcSecretKey);
    }

    isBotActive() {
        return this.config.botActive && this.isConfigured();
    }

    // Get configuration for specific component
    getMoneyManagementConfig() {
        return {
            enabled: this.config.moneyManagementEnabled,
            mode: this.config.moneyManagementMode,
            positionSizePercentage: this.config.positionSizePercentage,
            fixedTradeAmount: this.config.fixedTradeAmount,
            minTradeAmount: this.config.minTradeAmount,
            maxTradeAmount: this.config.maxTradeAmount,
            minRemainingBalance: this.config.minRemainingBalance,
            defaultQuantity: this.config.defaultQuantity
        };
    }

    getApiConfig() {
        return {
            mexcApiKey: this.config.mexcApiKey,
            mexcSecretKey: this.config.mexcSecretKey,
            configured: this.isConfigured()
        };
    }

    getTelegramConfig() {
        return {
            telegramBotToken: this.config.telegramBotToken,
            telegramChatId: this.config.telegramChatId,
            telegramEnabled: this.config.telegramEnabled,
            configured: !!(this.config.telegramBotToken && this.config.telegramChatId)
        };
    }

    // Reset configuration to defaults
    async resetConfig() {
        this.config = this.getDefaultConfig();
        await this.saveConfig();
        return { success: true, message: 'Configuration reset to defaults' };
    }

    // Export configuration (without sensitive data)
    exportConfig() {
        const exportData = { ...this.config };
        delete exportData.mexcApiKey;
        delete exportData.mexcSecretKey;
        delete exportData.webhookSecret;
        delete exportData.telegramBotToken;
        return exportData;
    }
}

module.exports = ConfigManager;
