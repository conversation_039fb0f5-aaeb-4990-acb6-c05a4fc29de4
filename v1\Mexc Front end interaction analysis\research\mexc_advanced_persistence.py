#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MEXC Advanced Persistence
Prevent MEXC from clearing the quantity value using advanced techniques.

ISSUE: MEXC clears the quantity value immediately after it's entered
SOLUTION: Use advanced persistence techniques to keep the value in the field
"""

import os
import sys
import time
import logging
from datetime import datetime
from playwright.sync_api import sync_playwright

class MEXCAdvancedPersistence:
    """Advanced persistence to prevent MEXC from clearing values"""
    
    def __init__(self, symbol="TRU_USDT", side="BUY", quantity=2.5):
        self.symbol = symbol
        self.side = side
        self.quantity = quantity
        
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
        self.logger = logging.getLogger(__name__)
        
        self.playwright = None
        self.browser = None
        self.page = None
        
        self.logger.info(f"🔒 ADVANCED PERSISTENCE: {side} {quantity} {symbol}")
    
    def connect(self):
        """Connect to MEXC"""
        self.logger.info("🔌 Connecting...")
        
        self.playwright = sync_playwright().start()
        self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
        
        context = self.browser.contexts[0]
        for page in context.pages:
            if 'mexc.com' in (page.url or '') and 'testnet' not in (page.url or ''):
                self.page = page
                break
        
        if not self.page:
            self.logger.error("❌ MEXC page not found")
            return False
        
        self.logger.info(f"✅ Connected: {self.page.url}")
        return True
    
    def advanced_persistent_fill(self):
        """Use advanced techniques to persistently fill quantity field"""
        self.logger.info("🔒 APPLYING ADVANCED PERSISTENCE TECHNIQUES...")
        
        persistence_script = f"""
        () => {{
            console.log('🔒 ADVANCED PERSISTENCE - Preventing MEXC from clearing values...');
            
            const testValue = '{self.quantity}';
            const results = {{
                target_field: null,
                persistence_attempts: [],
                final_verification: false,
                monitoring_active: false
            }};
            
            // STEP 1: Find the target quantity field (position 668, 523 from previous success)
            console.log('Step 1: Finding target quantity field...');
            
            let targetInput = null;
            const allInputs = document.querySelectorAll('input.ant-input');
            
            for (const input of allInputs) {{
                const rect = input.getBoundingClientRect();
                if (Math.abs(rect.x - 668) < 10 && Math.abs(rect.y - 523) < 50) {{
                    targetInput = input;
                    console.log(`✅ Found target field at (${{rect.x}}, ${{rect.y}})`);
                    break;
                }}
            }}
            
            if (!targetInput) {{
                // Fallback: find any input in order form
                const orderForms = document.querySelectorAll('[class*="order"], [class*="trading"], [class*="trade"]');
                for (const form of orderForms) {{
                    const inputs = form.querySelectorAll('input.ant-input[type="text"], input.ant-input:not([type])');
                    if (inputs.length > 0) {{
                        targetInput = inputs[0]; // Take first input in order form
                        console.log(`✅ Found fallback field in order form`);
                        break;
                    }}
                }}
            }}
            
            if (!targetInput) {{
                console.log('❌ No target field found');
                return {{ success: false, error: 'No target field found' }};
            }}
            
            results.target_field = {{
                placeholder: targetInput.placeholder,
                className: targetInput.className,
                position: {{
                    x: Math.round(targetInput.getBoundingClientRect().x),
                    y: Math.round(targetInput.getBoundingClientRect().y)
                }}
            }};
            
            console.log(`Target field: placeholder="${{targetInput.placeholder}}", class="${{targetInput.className}}"`);
            
            // STEP 2: ADVANCED PERSISTENCE TECHNIQUE 1 - Continuous Monitoring
            console.log('Step 2: Setting up continuous monitoring...');
            
            let monitoringInterval;
            let persistenceCount = 0;
            
            const maintainValue = () => {{
                if (targetInput.value !== testValue) {{
                    console.log(`🔒 Value cleared! Restoring: "${{targetInput.value}}" → "${{testValue}}"`);
                    
                    // Restore value with multiple methods
                    targetInput.value = testValue;
                    targetInput.setAttribute('value', testValue);
                    
                    // Trigger events to make it "stick"
                    targetInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                    targetInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    
                    persistenceCount++;
                    
                    results.persistence_attempts.push({{
                        attempt: persistenceCount,
                        timestamp: Date.now(),
                        restored_value: testValue,
                        method: 'continuous_monitoring'
                    }});
                }}
            }};
            
            // Start continuous monitoring (every 100ms)
            monitoringInterval = setInterval(maintainValue, 100);
            results.monitoring_active = true;
            
            // STEP 3: ADVANCED PERSISTENCE TECHNIQUE 2 - React/Vue Override
            console.log('Step 3: Applying React/Vue value override...');
            
            try {{
                // Override React/Vue controlled input
                const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set;
                nativeInputValueSetter.call(targetInput, testValue);
                
                // Trigger React/Vue events
                targetInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                targetInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
                
                results.persistence_attempts.push({{
                    attempt: ++persistenceCount,
                    timestamp: Date.now(),
                    method: 'react_vue_override',
                    success: true
                }});
                
                console.log('✅ React/Vue override applied');
                
            }} catch (error) {{
                console.log(`❌ React/Vue override failed: ${{error.message}}`);
                results.persistence_attempts.push({{
                    attempt: ++persistenceCount,
                    timestamp: Date.now(),
                    method: 'react_vue_override',
                    success: false,
                    error: error.message
                }});
            }}
            
            // STEP 4: ADVANCED PERSISTENCE TECHNIQUE 3 - Event Blocking
            console.log('Step 4: Blocking value-clearing events...');
            
            const blockClearingEvents = (event) => {{
                if (event.target === targetInput) {{
                    // Block events that might clear the value
                    if (event.type === 'blur' || event.type === 'focusout') {{
                        console.log(`🔒 Blocked ${{event.type}} event that might clear value`);
                        event.preventDefault();
                        event.stopPropagation();
                        
                        // Restore value after blocked event
                        setTimeout(() => {{
                            if (targetInput.value !== testValue) {{
                                targetInput.value = testValue;
                                targetInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                            }}
                        }}, 10);
                        
                        results.persistence_attempts.push({{
                            attempt: ++persistenceCount,
                            timestamp: Date.now(),
                            method: 'event_blocking',
                            blocked_event: event.type
                        }});
                    }}
                }}
            }};
            
            // Add event listeners to block clearing
            ['blur', 'focusout', 'change'].forEach(eventType => {{
                targetInput.addEventListener(eventType, blockClearingEvents, true);
            }});
            
            // STEP 5: Initial fill with all techniques
            console.log('Step 5: Initial fill with all techniques...');
            
            targetInput.focus();
            targetInput.select();
            targetInput.value = '';
            
            // Method 1: Direct value setting
            targetInput.value = testValue;
            targetInput.setAttribute('value', testValue);
            
            // Method 2: Character-by-character typing
            targetInput.value = '';
            for (let i = 0; i < testValue.length; i++) {{
                targetInput.value += testValue[i];
                targetInput.dispatchEvent(new KeyboardEvent('keydown', {{ key: testValue[i], bubbles: true }}));
                targetInput.dispatchEvent(new KeyboardEvent('keyup', {{ key: testValue[i], bubbles: true }}));
                targetInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
            }}
            
            // Method 3: Final events
            targetInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
            
            console.log(`Initial fill completed: "${{targetInput.value}}"`);
            
            // STEP 6: Wait and verify persistence
            return new Promise((resolve) => {{
                setTimeout(() => {{
                    console.log('Step 6: Final verification...');
                    
                    const finalValue = targetInput.value;
                    results.final_verification = finalValue === testValue;
                    
                    console.log(`Final verification: "${{finalValue}}" (expected: "${{testValue}}")`);
                    console.log(`Persistence attempts: ${{persistenceCount}}`);
                    console.log(`Monitoring active: ${{results.monitoring_active}}`);
                    
                    // Keep monitoring active for button click
                    // Don't clear the interval - let it keep running
                    
                    resolve({{
                        success: results.final_verification,
                        results: results,
                        summary: {{
                            target_found: true,
                            final_value: finalValue,
                            persistence_attempts: persistenceCount,
                            monitoring_active: results.monitoring_active,
                            verification_success: results.final_verification
                        }}
                    }});
                }}, 5000); // Wait 5 seconds for persistence test
            }});
        }}
        """
        
        try:
            result = self.page.evaluate(persistence_script)
            
            if result.get('success'):
                summary = result.get('summary', {})
                results_data = result.get('results', {})
                
                self.logger.info("🔒 ADVANCED PERSISTENCE RESULTS:")
                self.logger.info(f"   Target found: {summary.get('target_found', False)}")
                self.logger.info(f"   Final value: '{summary.get('final_value', '')}'")
                self.logger.info(f"   Persistence attempts: {summary.get('persistence_attempts', 0)}")
                self.logger.info(f"   Monitoring active: {summary.get('monitoring_active', False)}")
                self.logger.info(f"   Verification success: {summary.get('verification_success', False)}")
                
                # Show target field info
                target_field = results_data.get('target_field', {})
                if target_field:
                    self.logger.info(f"   Target field position: {target_field.get('position', {})}")
                    self.logger.info(f"   Target field class: {target_field.get('className', '')}")
                
                return summary.get('verification_success', False)
            else:
                self.logger.error("❌ Advanced persistence failed")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Advanced persistence error: {e}")
            return False
    
    def click_with_monitoring(self):
        """Click button while monitoring maintains the quantity value"""
        self.logger.info("🔘 CLICKING BUTTON WITH ACTIVE MONITORING...")
        
        # Determine button class
        if self.side == "BUY":
            button_class = "component_longBtn__eazYU"
        else:
            button_class = "component_shortBtn__x5P3I"
        
        click_script = f"""
        () => {{
            console.log('🔘 Clicking button while monitoring maintains value...');
            
            const results = {{
                button_found: false,
                button_clicked: false,
                quantity_maintained: false,
                dom_changes: 0,
                error_messages: []
            }};
            
            // Find button
            const button = document.querySelector('button.{button_class}');
            if (!button) {{
                console.log('❌ Button not found');
                return {{ success: false, error: 'Button not found' }};
            }}
            
            results.button_found = true;
            console.log(`Found button: "${{button.textContent}}" at (${{button.getBoundingClientRect().x}}, ${{button.getBoundingClientRect().y}})`);
            
            // Check quantity before click
            const quantityInputs = document.querySelectorAll('input.ant-input');
            let quantityBefore = '';
            for (const input of quantityInputs) {{
                if (input.value === '{self.quantity}') {{
                    quantityBefore = input.value;
                    console.log(`✅ Quantity verified before click: "${{quantityBefore}}"`);
                    break;
                }}
            }}
            
            // Record DOM state
            const beforeModals = document.querySelectorAll('.ant-modal, .modal, [role="dialog"]').length;
            const beforeNotifications = document.querySelectorAll('.ant-notification, .ant-message').length;
            
            try {{
                // Click button
                button.focus();
                button.click();
                button.dispatchEvent(new Event('click', {{ bubbles: true }}));
                
                results.button_clicked = true;
                console.log('✅ Button clicked');
                
                // Wait for response
                setTimeout(() => {{
                    // Check DOM changes
                    const afterModals = document.querySelectorAll('.ant-modal, .modal, [role="dialog"]').length;
                    const afterNotifications = document.querySelectorAll('.ant-notification, .ant-message').length;
                    
                    results.dom_changes = (afterModals - beforeModals) + (afterNotifications - beforeNotifications);
                    console.log(`DOM changes: ${{results.dom_changes}}`);
                    
                    // Check if quantity is still there
                    let quantityAfter = '';
                    for (const input of quantityInputs) {{
                        if (input.value === '{self.quantity}') {{
                            quantityAfter = input.value;
                            results.quantity_maintained = true;
                            console.log(`✅ Quantity maintained after click: "${{quantityAfter}}"`);
                            break;
                        }}
                    }}
                    
                    if (!results.quantity_maintained) {{
                        console.log('❌ Quantity lost after button click');
                    }}
                    
                    // Check for error messages
                    const errorElements = document.querySelectorAll('.ant-message-error, .error, [class*="error"]');
                    for (const errorEl of errorElements) {{
                        const errorText = errorEl.textContent || '';
                        if (errorText.includes('quantity') || errorText.includes('amount') || errorText.includes('enter')) {{
                            results.error_messages.push(errorText);
                            console.log(`❌ Error message: ${{errorText}}`);
                        }}
                    }}
                    
                }}, 2000);
                
            }} catch (error) {{
                console.log(`❌ Button click error: ${{error.message}}`);
                results.error_messages.push(error.message);
            }}
            
            return {{
                success: true,
                results: results
            }};
        }}
        """
        
        try:
            result = self.page.evaluate(click_script)
            
            # Wait for results
            time.sleep(3)
            
            if result.get('success'):
                results_data = result.get('results', {})
                
                self.logger.info("🔘 BUTTON CLICK WITH MONITORING RESULTS:")
                self.logger.info(f"   Button found: {results_data.get('button_found', False)}")
                self.logger.info(f"   Button clicked: {results_data.get('button_clicked', False)}")
                self.logger.info(f"   Quantity maintained: {results_data.get('quantity_maintained', False)}")
                self.logger.info(f"   DOM changes: {results_data.get('dom_changes', 0)}")
                
                error_messages = results_data.get('error_messages', [])
                if error_messages:
                    self.logger.error("   Error messages:")
                    for error in error_messages:
                        self.logger.error(f"     - {error}")
                
                # Success if button clicked and either quantity maintained OR DOM changes occurred
                return (results_data.get('button_clicked', False) and 
                       (results_data.get('quantity_maintained', False) or 
                        results_data.get('dom_changes', 0) > 0) and
                       len(error_messages) == 0)
            else:
                self.logger.error("❌ Button click with monitoring failed")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Button click error: {e}")
            return False
    
    def execute_advanced_persistence(self):
        """Execute advanced persistence automation"""
        self.logger.info("🔒 EXECUTING ADVANCED PERSISTENCE AUTOMATION...")
        
        try:
            # Connect
            if not self.connect():
                return False
            
            # Apply advanced persistence
            if not self.advanced_persistent_fill():
                return False
            
            # Click button with monitoring active
            if not self.click_with_monitoring():
                return False
            
            self.logger.info("🎉 ADVANCED PERSISTENCE SUCCESSFUL!")
            return True
            
        except Exception as e:
            self.logger.error(f"Advanced persistence error: {e}")
            return False
    
    def cleanup(self):
        try:
            if self.playwright:
                self.playwright.stop()
        except:
            pass

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="MEXC Advanced Persistence")
    parser.add_argument("--symbol", default="TRU_USDT", help="Trading symbol")
    parser.add_argument("--side", choices=["BUY", "SELL"], default="BUY", help="Order side")
    parser.add_argument("--quantity", type=float, default=2.5, help="Order quantity")
    
    args = parser.parse_args()
    
    print(f"""
🔒 MEXC ADVANCED PERSISTENCE
============================
ISSUE: MEXC clears quantity value immediately after entry
SOLUTION: Advanced persistence techniques

TECHNIQUES:
1. 🔄 Continuous monitoring (every 100ms)
2. ⚛️ React/Vue value override
3. 🚫 Event blocking (blur, focusout)
4. 🔒 Persistent value restoration

TARGET: {args.side} {args.quantity} {args.symbol}
    """)
    
    persistence = MEXCAdvancedPersistence(args.symbol, args.side, args.quantity)
    
    try:
        success = persistence.execute_advanced_persistence()
        
        if success:
            print("\n🎉 ADVANCED PERSISTENCE SUCCESSFUL!")
            print("Quantity maintained and trade executed.")
        else:
            print("\n❌ ADVANCED PERSISTENCE FAILED!")
            print("Could not maintain quantity value.")
            
    except KeyboardInterrupt:
        print("\n👋 Persistence interrupted")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        persistence.cleanup()

if __name__ == "__main__":
    main()
