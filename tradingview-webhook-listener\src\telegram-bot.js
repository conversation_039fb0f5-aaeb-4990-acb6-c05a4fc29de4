const axios = require('axios');

class TelegramBot {
    constructor(configManager, logger) {
        this.configManager = configManager;
        this.logger = logger;
        this.isConnected = false;
        this.messageQueue = [];
        this.rateLimitDelay = 1000; // 1 second between messages
        this.lastMessageTime = 0;
    }

    async initialize() {
        try {
            const telegramConfig = this.configManager.getTelegramConfig();
            
            if (!telegramConfig.configured || !telegramConfig.telegramEnabled) {
                this.logger.info('Telegram bot not configured or disabled');
                return false;
            }

            this.botToken = telegramConfig.telegramBotToken;
            this.chatId = telegramConfig.telegramChatId;

            // Test connection by getting bot info
            const response = await axios.get(`https://api.telegram.org/bot${this.botToken}/getMe`, {
                timeout: 10000
            });

            if (response.data.ok) {
                this.isConnected = true;
                this.logger.info('Telegram bot initialized successfully', {
                    botUsername: response.data.result.username,
                    botId: response.data.result.id
                });

                // Send initialization message
                await this.sendMessage(
                    '🤖 MEXC Trading Bot initialized successfully!\n' +
                    `Bot: @${response.data.result.username}\n` +
                    `Chat ID: ${this.chatId}\n` +
                    'Ready to receive trading alerts.',
                    'SUCCESS'
                );

                return true;
            } else {
                throw new Error('Invalid bot token');
            }
        } catch (error) {
            this.logger.error('Failed to initialize Telegram bot', {
                error: error.message
            });
            this.isConnected = false;
            return false;
        }
    }

    async sendMessage(message, level = 'INFO', parseMode = 'HTML') {
        if (!this.isConnected) {
            this.logger.warning('Telegram bot not connected, queuing message');
            this.messageQueue.push({ message, level, parseMode, timestamp: Date.now() });
            return false;
        }

        try {
            // Rate limiting
            const now = Date.now();
            const timeSinceLastMessage = now - this.lastMessageTime;
            if (timeSinceLastMessage < this.rateLimitDelay) {
                await new Promise(resolve => setTimeout(resolve, this.rateLimitDelay - timeSinceLastMessage));
            }

            // Format message with level emoji
            const levelEmojis = {
                'SUCCESS': '✅',
                'INFO': 'ℹ️',
                'WARNING': '⚠️',
                'ERROR': '❌',
                'CRITICAL': '🚨'
            };

            const emoji = levelEmojis[level] || 'ℹ️';
            const formattedMessage = `${emoji} <b>${level}</b>\n\n${message}\n\n<i>Time: ${new Date().toISOString()}</i>`;

            const response = await axios.post(`https://api.telegram.org/bot${this.botToken}/sendMessage`, {
                chat_id: this.chatId,
                text: formattedMessage,
                parse_mode: parseMode,
                disable_notification: level === 'INFO'
            }, {
                timeout: 10000
            });

            this.lastMessageTime = Date.now();

            if (response.data.ok) {
                this.logger.info('Telegram message sent successfully', { level });
                return true;
            } else {
                throw new Error(`Telegram API error: ${response.data.description}`);
            }
        } catch (error) {
            this.logger.error('Failed to send Telegram message', {
                error: error.message,
                level,
                message: message.substring(0, 100) + '...'
            });
            return false;
        }
    }

    async sendTradeAlert(tradeType, symbol, quantity, success, error = null, executionTime = null) {
        const status = success ? '✅ SUCCESS' : '❌ FAILED';
        const level = success ? 'SUCCESS' : 'ERROR';
        
        let message = `<b>TRADE EXECUTION</b>\n\n`;
        message += `Type: ${tradeType}\n`;
        message += `Symbol: ${symbol}\n`;
        message += `Quantity: ${quantity} USDT\n`;
        message += `Status: ${status}\n`;
        
        if (executionTime) {
            message += `Execution Time: ${executionTime}ms\n`;
        }
        
        if (error) {
            message += `\nError: ${error}`;
        }

        return await this.sendMessage(message, level);
    }

    async sendPositionAlert(action, positionId, symbol, direction, reason = null) {
        const actionEmojis = {
            'opened': '📈',
            'closed': '📉',
            'force_closed': '🔄'
        };

        const emoji = actionEmojis[action] || '📊';
        
        let message = `${emoji} <b>POSITION ${action.toUpperCase()}</b>\n\n`;
        message += `Position ID: ${positionId}\n`;
        message += `Symbol: ${symbol}\n`;
        message += `Direction: ${direction.toUpperCase()}\n`;
        
        if (reason) {
            message += `Reason: ${reason}\n`;
        }

        const level = action === 'force_closed' ? 'WARNING' : 'INFO';
        return await this.sendMessage(message, level);
    }

    async sendSystemAlert(title, details, level = 'WARNING') {
        let message = `<b>${title}</b>\n\n${details}`;
        return await this.sendMessage(message, level);
    }

    async sendRetryLimitAlert(tradeType, symbol, attempts, lastError) {
        const message = `<b>TRADE EXECUTION FAILED</b>\n\n` +
                       `❌ Maximum retry attempts (${attempts}) exceeded\n\n` +
                       `Trade Type: ${tradeType}\n` +
                       `Symbol: ${symbol}\n` +
                       `Last Error: ${lastError}\n\n` +
                       `🔧 <b>ACTION REQUIRED:</b> Please check the MEXC trading system and resolve any issues.`;
        
        return await this.sendMessage(message, 'CRITICAL');
    }

    async sendForceCloseAlert(closedPositions, newTradeType) {
        if (closedPositions.length === 0) return;

        let message = `<b>FORCE CLOSE EXECUTED</b>\n\n`;
        message += `🔄 Closed ${closedPositions.length} existing position(s) before opening new ${newTradeType} position:\n\n`;
        
        closedPositions.forEach((pos, index) => {
            message += `${index + 1}. ${pos.symbol} ${pos.direction.toUpperCase()} - ${pos.quantity} USDT\n`;
        });

        return await this.sendMessage(message, 'WARNING');
    }

    // Process queued messages when connection is restored
    async processMessageQueue() {
        if (!this.isConnected || this.messageQueue.length === 0) {
            return;
        }

        this.logger.info(`Processing ${this.messageQueue.length} queued Telegram messages`);
        
        const queue = [...this.messageQueue];
        this.messageQueue = [];

        for (const queuedMessage of queue) {
            await this.sendMessage(queuedMessage.message, queuedMessage.level, queuedMessage.parseMode);
            // Small delay between queued messages
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }

    isReady() {
        return this.isConnected;
    }

    getStatus() {
        return {
            connected: this.isConnected,
            queuedMessages: this.messageQueue.length,
            lastMessageTime: this.lastMessageTime
        };
    }
}

module.exports = TelegramBot;
