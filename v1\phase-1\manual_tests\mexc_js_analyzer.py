#!/usr/bin/env python3
"""
MEXC JavaScript Analyzer
Deep analysis of MEXC's JavaScript to find the real signature algorithm
"""

import json
import time
import re
import hashlib
import hmac
import base64
from playwright.sync_api import sync_playwright
from curl_cffi import requests
from dotenv import dotenv_values

class MEXCJavaScriptAnalyzer:
    """Analyze MEXC's JavaScript to find signature algorithm"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        self.uc_token = vals.get('uc_token')
        
        print("🔬 MEXC JavaScript Deep Analyzer")
        print("="*40)
    
    def connect_and_analyze(self):
        """Connect to browser and analyze JavaScript"""
        
        print("🌐 Connecting to browser for JS analysis...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                print("❌ No browser contexts found")
                return False
            
            context = self.browser.contexts[0]
            
            if context.pages:
                self.page = context.pages[0]
            else:
                self.page = context.new_page()
            
            # Navigate to MEXC
            if 'mexc.com' not in self.page.url:
                self.page.goto('https://www.mexc.com/futures/BTC_USDT', wait_until='domcontentloaded')
                time.sleep(5)  # Give more time for JS to load
            
            # Inject session tokens
            self.page.evaluate(f"""
                () => {{
                    localStorage.setItem('authorization', '{self.auth}');
                    localStorage.setItem('u_id', '{self.auth}');
                    {f"localStorage.setItem('uc_token', '{self.uc_token}');" if self.uc_token else ""}
                }}
            """)
            
            print("✅ Browser connected and configured")
            return True
            
        except Exception as e:
            print(f"❌ Browser connection failed: {e}")
            return False
    
    def extract_all_javascript_functions(self):
        """Extract all JavaScript functions related to signing"""
        
        print("🔍 Extracting JavaScript functions...")
        
        try:
            # Get all global functions and objects
            js_analysis = self.page.evaluate("""
                () => {
                    const analysis = {
                        globalFunctions: [],
                        cryptoFunctions: [],
                        signFunctions: [],
                        mexcObjects: {},
                        windowProperties: [],
                        possibleSigningCode: []
                    };
                    
                    // 1. Scan all window properties
                    for (const key in window) {
                        try {
                            const value = window[key];
                            const keyLower = key.toLowerCase();
                            
                            if (typeof value === 'function') {
                                const funcStr = value.toString();
                                
                                // Look for signing-related functions
                                if (keyLower.includes('sign') || 
                                    keyLower.includes('hash') || 
                                    keyLower.includes('crypto') ||
                                    keyLower.includes('hmac') ||
                                    keyLower.includes('sha') ||
                                    keyLower.includes('md5')) {
                                    analysis.signFunctions.push({
                                        name: key,
                                        code: funcStr.substring(0, 500),
                                        length: funcStr.length
                                    });
                                }
                                
                                // Look for crypto in function body
                                if (funcStr.includes('sha256') || 
                                    funcStr.includes('md5') || 
                                    funcStr.includes('hmac') ||
                                    funcStr.includes('signature') ||
                                    funcStr.includes('x-mxc-sign')) {
                                    analysis.cryptoFunctions.push({
                                        name: key,
                                        code: funcStr.substring(0, 500),
                                        hasSignature: funcStr.includes('x-mxc-sign'),
                                        hasCrypto: funcStr.includes('sha256') || funcStr.includes('md5')
                                    });
                                }
                            } else if (typeof value === 'object' && value !== null) {
                                // Check objects for methods
                                if (keyLower.includes('mexc') || 
                                    keyLower.includes('api') || 
                                    keyLower.includes('request') ||
                                    keyLower.includes('crypto')) {
                                    try {
                                        const methods = Object.getOwnPropertyNames(value);
                                        analysis.mexcObjects[key] = methods.filter(method => 
                                            typeof value[method] === 'function'
                                        );
                                    } catch (e) {}
                                }
                            }
                        } catch (e) {
                            // Skip inaccessible properties
                        }
                    }
                    
                    // 2. Look for common crypto libraries
                    const cryptoLibs = ['CryptoJS', 'crypto', 'sjcl', 'forge', 'jsrsasign'];
                    cryptoLibs.forEach(lib => {
                        if (window[lib]) {
                            analysis.mexcObjects[lib] = Object.getOwnPropertyNames(window[lib]);
                        }
                    });
                    
                    // 3. Check for AMD/CommonJS modules
                    if (window.require) {
                        analysis.windowProperties.push('require (AMD/CommonJS available)');
                    }
                    
                    if (window.define) {
                        analysis.windowProperties.push('define (AMD available)');
                    }
                    
                    return analysis;
                }
            """)
            
            return js_analysis
            
        except Exception as e:
            print(f"❌ JavaScript analysis failed: {e}")
            return None
    
    def search_for_signature_algorithm(self):
        """Search for the actual signature algorithm in page scripts"""
        
        print("🔍 Searching for signature algorithm in scripts...")
        
        try:
            # Get all script contents
            script_analysis = self.page.evaluate("""
                () => {
                    const scripts = [];
                    const scriptElements = document.querySelectorAll('script');
                    
                    scriptElements.forEach((script, index) => {
                        if (script.src) {
                            scripts.push({
                                type: 'external',
                                src: script.src,
                                index: index
                            });
                        } else if (script.textContent) {
                            const content = script.textContent;
                            
                            // Look for signature-related patterns
                            const patterns = [
                                'x-mxc-sign',
                                'signature',
                                'sign(',
                                'hmac',
                                'sha256',
                                'md5',
                                'crypto',
                                'encrypt'
                            ];
                            
                            const foundPatterns = patterns.filter(pattern => 
                                content.toLowerCase().includes(pattern.toLowerCase())
                            );
                            
                            if (foundPatterns.length > 0) {
                                scripts.push({
                                    type: 'inline',
                                    index: index,
                                    patterns: foundPatterns,
                                    content: content.substring(0, 1000),
                                    length: content.length
                                });
                            }
                        }
                    });
                    
                    return scripts;
                }
            """)
            
            return script_analysis
            
        except Exception as e:
            print(f"❌ Script analysis failed: {e}")
            return None
    
    def extract_network_request_patterns(self):
        """Extract network request patterns to understand signature usage"""
        
        print("🔍 Analyzing network request patterns...")
        
        try:
            # Inject network monitoring
            network_analysis = self.page.evaluate("""
                () => {
                    const analysis = {
                        interceptedRequests: [],
                        originalFetch: window.fetch
                    };
                    
                    // Override fetch to analyze request patterns
                    window.fetch = function(...args) {
                        const [url, options] = args;
                        
                        if (options && options.headers) {
                            const headers = options.headers;
                            
                            // Look for signature patterns
                            if (headers['x-mxc-sign'] || headers['x-mxc-nonce']) {
                                analysis.interceptedRequests.push({
                                    url: url,
                                    method: options.method || 'GET',
                                    hasSignature: !!headers['x-mxc-sign'],
                                    hasNonce: !!headers['x-mxc-nonce'],
                                    signatureLength: headers['x-mxc-sign'] ? headers['x-mxc-sign'].length : 0,
                                    timestamp: Date.now()
                                });
                            }
                        }
                        
                        return analysis.originalFetch.apply(this, args);
                    };
                    
                    return analysis;
                }
            """)
            
            return network_analysis
            
        except Exception as e:
            print(f"❌ Network analysis failed: {e}")
            return None
    
    def test_signature_algorithms_advanced(self):
        """Test advanced signature algorithms based on analysis"""
        
        print("🧪 Testing advanced signature algorithms...")
        
        # Test data
        test_order = {
            'symbol': 'BTC_USDT',
            'side': 1,
            'openType': 1,
            'type': '2',
            'vol': 1,
            'leverage': 1,
            'marketCeiling': False,
            'price': '30000',
            'priceProtect': '0'
        }
        
        nonce = str(int(time.time() * 1000))
        
        # Advanced algorithms based on common trading platform patterns
        algorithms = [
            self._mexc_algorithm_v1,
            self._mexc_algorithm_v2,
            self._mexc_algorithm_v3,
            self._mexc_algorithm_v4,
            self._mexc_algorithm_v5,
            self._mexc_algorithm_v6,
            self._mexc_algorithm_v7,
            self._mexc_algorithm_v8
        ]
        
        results = []
        
        for algo in algorithms:
            try:
                signature = algo(test_order, nonce)
                results.append({
                    'algorithm': algo.__name__,
                    'signature': signature,
                    'length': len(signature) if signature else 0
                })
                print(f"✅ {algo.__name__}: {signature[:16] if signature else 'None'}...")
            except Exception as e:
                print(f"❌ {algo.__name__}: {e}")
        
        return results
    
    def _mexc_algorithm_v1(self, order_data, nonce):
        """MEXC Algorithm V1: Standard HMAC-SHA256"""
        message = json.dumps(order_data, separators=(',', ':'), sort_keys=True) + nonce
        return hmac.new(self.auth.encode(), message.encode(), hashlib.sha256).hexdigest()
    
    def _mexc_algorithm_v2(self, order_data, nonce):
        """MEXC Algorithm V2: Query string + HMAC"""
        params = []
        for key in sorted(order_data.keys()):
            params.append(f"{key}={order_data[key]}")
        query_string = '&'.join(params)
        message = query_string + nonce
        return hmac.new(self.auth.encode(), message.encode(), hashlib.sha256).hexdigest()[:32]
    
    def _mexc_algorithm_v3(self, order_data, nonce):
        """MEXC Algorithm V3: Base64 + Double hash"""
        json_str = json.dumps(order_data, separators=(',', ':'), sort_keys=True)
        message = base64.b64encode((nonce + json_str).encode()).decode()
        first_hash = hashlib.sha256((self.auth + message).encode()).hexdigest()
        return hashlib.md5(first_hash.encode()).hexdigest()
    
    def _mexc_algorithm_v4(self, order_data, nonce):
        """MEXC Algorithm V4: Timestamp-based with salt"""
        timestamp = str(int(time.time()))
        salt = "mexc_futures_trading_api"
        json_str = json.dumps(order_data, separators=(',', ':'), sort_keys=True)
        message = f"{salt}{timestamp}{nonce}{json_str}{self.auth}"
        return hashlib.sha256(message.encode()).hexdigest()[:32]
    
    def _mexc_algorithm_v5(self, order_data, nonce):
        """MEXC Algorithm V5: Multi-step with XOR"""
        # Step 1: Create base hash
        json_str = json.dumps(order_data, separators=(',', ':'), sort_keys=True)
        base_content = self.auth + nonce + json_str
        base_hash = hashlib.sha256(base_content.encode()).digest()
        
        # Step 2: XOR with nonce
        nonce_bytes = nonce.encode().ljust(32, b'0')[:32]
        xor_result = bytes(a ^ b for a, b in zip(base_hash, nonce_bytes))
        
        # Step 3: Final hash
        return hashlib.md5(xor_result).hexdigest()
    
    def _mexc_algorithm_v6(self, order_data, nonce):
        """MEXC Algorithm V6: Nested HMAC"""
        json_str = json.dumps(order_data, separators=(',', ':'), sort_keys=True)
        
        # First HMAC
        first_hmac = hmac.new(self.auth.encode(), json_str.encode(), hashlib.sha256).hexdigest()
        
        # Second HMAC with nonce
        second_hmac = hmac.new(nonce.encode(), first_hmac.encode(), hashlib.sha256).hexdigest()
        
        return second_hmac[:32]
    
    def _mexc_algorithm_v7(self, order_data, nonce):
        """MEXC Algorithm V7: Custom encoding"""
        # Create sorted parameter string
        params = []
        for key in sorted(order_data.keys()):
            params.append(f"{key}={order_data[key]}")
        param_string = '&'.join(params)
        
        # Custom encoding pattern
        message = f"POST\n/api/v1/private/order/create\n{param_string}\n{nonce}"
        
        # HMAC with custom message
        return hmac.new(self.auth.encode(), message.encode(), hashlib.sha256).hexdigest()[:32]
    
    def _mexc_algorithm_v8(self, order_data, nonce):
        """MEXC Algorithm V8: Trading platform standard"""
        # Common pattern in trading platforms
        timestamp = str(int(time.time() * 1000))
        
        # Create canonical request
        sorted_params = []
        for key in sorted(order_data.keys()):
            sorted_params.append(f"{key}={order_data[key]}")
        
        canonical_request = f"POST\n/api/v1/private/order/create\n{'&'.join(sorted_params)}\ntimestamp={timestamp}&nonce={nonce}"
        
        # Sign canonical request
        signature = hmac.new(self.auth.encode(), canonical_request.encode(), hashlib.sha256).hexdigest()
        
        return signature[:32]
    
    def run_complete_analysis(self):
        """Run complete JavaScript analysis"""
        
        print("🚀 Starting Complete JavaScript Analysis...")
        print("="*50)
        
        # Step 1: Extract JavaScript functions
        js_analysis = self.extract_all_javascript_functions()
        if js_analysis:
            print(f"✅ Found {len(js_analysis.get('signFunctions', []))} signing functions")
            print(f"✅ Found {len(js_analysis.get('cryptoFunctions', []))} crypto functions")
            print(f"✅ Found {len(js_analysis.get('mexcObjects', {}))} MEXC objects")
        
        # Step 2: Search scripts
        script_analysis = self.search_for_signature_algorithm()
        if script_analysis:
            print(f"✅ Analyzed {len(script_analysis)} scripts with crypto patterns")
        
        # Step 3: Network patterns
        network_analysis = self.extract_network_request_patterns()
        if network_analysis:
            print("✅ Network monitoring setup")
        
        # Step 4: Test advanced algorithms
        signature_results = self.test_signature_algorithms_advanced()
        print(f"✅ Generated {len(signature_results)} signature candidates")
        
        # Results summary
        print("\n" + "="*50)
        print("JAVASCRIPT ANALYSIS RESULTS")
        print("="*50)
        
        if js_analysis and js_analysis.get('cryptoFunctions'):
            print("🔍 CRYPTO FUNCTIONS FOUND:")
            for func in js_analysis['cryptoFunctions'][:3]:
                print(f"   - {func['name']}: {func['code'][:100]}...")
        
        if script_analysis:
            print("📄 SCRIPTS WITH CRYPTO PATTERNS:")
            for script in script_analysis[:3]:
                if script['type'] == 'inline':
                    print(f"   - Inline script: {script['patterns']}")
                else:
                    print(f"   - External: {script['src']}")
        
        print("🔧 SIGNATURE CANDIDATES:")
        for result in signature_results:
            print(f"   - {result['algorithm']}: {result['signature'][:16]}... (len: {result['length']})")
        
        return {
            'js_analysis': js_analysis,
            'script_analysis': script_analysis,
            'signature_results': signature_results
        }
    
    def cleanup(self):
        """Cleanup resources"""
        if hasattr(self, 'browser'):
            self.browser.close()
        if hasattr(self, 'playwright'):
            self.playwright.stop()

def main():
    """Main analysis function"""
    
    analyzer = MEXCJavaScriptAnalyzer()
    
    try:
        # Connect and analyze
        if not analyzer.connect_and_analyze():
            print("❌ Failed to connect to browser")
            return
        
        # Run complete analysis
        results = analyzer.run_complete_analysis()
        
        print(f"\n🎯 ANALYSIS COMPLETE!")
        print("Next step: Test these signature algorithms with real API calls")
        
    finally:
        analyzer.cleanup()

if __name__ == '__main__':
    main()
