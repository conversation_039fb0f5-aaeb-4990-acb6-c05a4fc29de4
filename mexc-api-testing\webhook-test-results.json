{"directExecution": {"success": false, "executionTime": 855, "exitCode": 1, "stdout": "🎯 MEXC WORKING TRADER\n=======================\n📊 Order: Open Long\n🌐 Port: 9222\n⚡ Target: <2 seconds\n\n🔗 Connecting to browser on port 9222...\n", "stderr": "❌ Connection failed to port 9222: browserType.connectOverCDP: connect ECONNREFUSED ::1:9222\nCall log:\n\u001b[2m  - <ws preparing> retrieving websocket url from http://localhost:9222\u001b[22m\n\n💥 Failed: Failed to connect to port 9222\n"}, "webhookSimulation": {"success": false, "executionTime": 876, "signal": {"symbol": "TRUUSDT", "trade": "buy", "last_price": "0.03295", "leverage": "2"}, "orderType": "Open Long", "targetAchieved": true}, "timestamp": "2025-08-17T10:21:23.080Z"}