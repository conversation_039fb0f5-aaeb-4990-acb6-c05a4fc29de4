class MoneyManager {
    constructor(configManager, mexcApiClient, tradingExecutor = null) {
        this.configManager = configManager;
        this.mexcApiClient = mexcApiClient;
        this.tradingExecutor = tradingExecutor; // For browser-fetched balance
    }

    updateConfig(configManager, mexcApiClient, tradingExecutor = null) {
        this.configManager = configManager;
        this.mexcApiClient = mexcApiClient;
        this.tradingExecutor = tradingExecutor;
    }

    // Get balance from browser frontend (preferred) or API fallback
    async getBalance() {
        try {
            // Try browser-fetched balance first (more accurate)
            if (this.tradingExecutor) {
                const browserBalance = await this.tradingExecutor.getBalance();
                if (browserBalance.success) {
                    return {
                        free: browserBalance.balance,
                        locked: 0,
                        total: browserBalance.balance,
                        source: 'frontend',
                        timestamp: browserBalance.timestamp
                    };
                }
            }

            // Fallback to API balance
            const apiBalance = await this.mexcApiClient.getCachedBalance();
            return {
                ...apiBalance,
                source: 'api'
            };
        } catch (error) {
            console.error('Failed to get balance from both frontend and API:', error.message);
            throw new Error('Unable to fetch balance for money management');
        }
    }

    async calculatePositionSize(signal) {
        const config = this.configManager.getConfig();
        
        // If money management is disabled, use default quantity
        if (!config.moneyManagementEnabled) {
            return parseFloat(config.defaultQuantity || '0.3600');
        }

        // Get current balance (from browser frontend preferred)
        const balance = await this.getBalance();
        const availableBalance = balance.free;

        console.log(`💰 Money Management using balance: ${availableBalance} USDT (source: ${balance.source || 'unknown'})`);

        if (availableBalance <= 0) {
            throw new Error('Insufficient balance for trading');
        }

        let positionSize;

        if (config.moneyManagementMode === 'percentage') {
            // Percentage-based position sizing
            positionSize = this.calculatePercentageBasedSize(availableBalance, config);
        } else if (config.moneyManagementMode === 'fixed') {
            // Fixed amount position sizing
            positionSize = this.calculateFixedAmountSize(availableBalance, config);
        } else {
            // Default to percentage mode
            positionSize = this.calculatePercentageBasedSize(availableBalance, config);
        }

        // Apply minimum and maximum limits
        positionSize = this.applyLimits(positionSize, config, availableBalance);

        // Round to appropriate decimal places (USDT typically 4 decimal places)
        positionSize = Math.round(positionSize * 10000) / 10000;

        return positionSize;
    }

    calculatePercentageBasedSize(availableBalance, config) {
        const percentage = parseFloat(config.positionSizePercentage || 50) / 100;
        return availableBalance * percentage;
    }

    calculateFixedAmountSize(availableBalance, config) {
        const fixedAmount = parseFloat(config.fixedTradeAmount || 100);
        
        // If balance is less than fixed amount and percentage is 100%, use entire balance
        if (availableBalance < fixedAmount && parseFloat(config.positionSizePercentage || 50) === 100) {
            return availableBalance;
        }
        
        // If balance is more than fixed amount, use only the fixed amount
        if (availableBalance >= fixedAmount) {
            return fixedAmount;
        }
        
        // If balance is less than fixed amount and percentage is not 100%, use percentage of available
        const percentage = parseFloat(config.positionSizePercentage || 50) / 100;
        return availableBalance * percentage;
    }

    applyLimits(positionSize, config, availableBalance) {
        // Apply minimum trade amount
        const minTradeAmount = parseFloat(config.minTradeAmount || 0.1);
        if (positionSize < minTradeAmount) {
            if (availableBalance >= minTradeAmount) {
                positionSize = minTradeAmount;
            } else {
                throw new Error(`Position size ${positionSize} is below minimum ${minTradeAmount} and insufficient balance`);
            }
        }

        // Apply maximum trade amount
        const maxTradeAmount = parseFloat(config.maxTradeAmount || 10000);
        if (positionSize > maxTradeAmount) {
            positionSize = maxTradeAmount;
        }

        // Ensure position size doesn't exceed available balance
        if (positionSize > availableBalance) {
            positionSize = availableBalance;
        }

        return positionSize;
    }

    async validateTradeSize(positionSize) {
        try {
            // Get symbol info to check minimum requirements
            const symbolInfo = await this.mexcApiClient.getSymbolInfo('TRUUSDT');
            const minNotional = parseFloat(symbolInfo.minNotional || 0.1);
            
            if (positionSize < minNotional) {
                throw new Error(`Position size ${positionSize} is below minimum notional value ${minNotional} for TRUUSDT`);
            }

            return true;
        } catch (error) {
            throw new Error(`Trade size validation failed: ${error.message}`);
        }
    }

    async getPositionSizePreview(signal) {
        try {
            const config = this.configManager.getConfig();
            const balance = await this.getBalance();
            
            let preview = {
                availableBalance: balance.free,
                moneyManagementEnabled: config.moneyManagementEnabled,
                mode: config.moneyManagementMode || 'percentage',
                settings: {}
            };

            if (!config.moneyManagementEnabled) {
                preview.positionSize = parseFloat(config.defaultQuantity || '0.3600');
                preview.reasoning = 'Money management disabled, using default quantity';
                return preview;
            }

            if (config.moneyManagementMode === 'percentage') {
                const percentage = parseFloat(config.positionSizePercentage || 50);
                preview.settings.percentage = percentage;
                preview.positionSize = this.calculatePercentageBasedSize(balance.free, config);
                preview.reasoning = `Using ${percentage}% of available balance (${balance.free} USDT)`;
            } else if (config.moneyManagementMode === 'fixed') {
                const fixedAmount = parseFloat(config.fixedTradeAmount || 100);
                preview.settings.fixedAmount = fixedAmount;
                preview.positionSize = this.calculateFixedAmountSize(balance.free, config);
                
                if (balance.free < fixedAmount && parseFloat(config.positionSizePercentage || 50) === 100) {
                    preview.reasoning = `Fixed amount (${fixedAmount}) exceeds balance, using entire balance (${balance.free} USDT)`;
                } else if (balance.free >= fixedAmount) {
                    preview.reasoning = `Using fixed amount (${fixedAmount} USDT)`;
                } else {
                    const percentage = parseFloat(config.positionSizePercentage || 50);
                    preview.reasoning = `Fixed amount (${fixedAmount}) exceeds balance, using ${percentage}% of available balance`;
                }
            }

            // Apply limits
            const originalSize = preview.positionSize;
            preview.positionSize = this.applyLimits(preview.positionSize, config, balance.free);
            
            if (preview.positionSize !== originalSize) {
                preview.reasoning += ` → Adjusted to ${preview.positionSize} due to limits`;
            }

            // Round
            preview.positionSize = Math.round(preview.positionSize * 10000) / 10000;

            // Add limit information
            preview.limits = {
                minTradeAmount: parseFloat(config.minTradeAmount || 0.1),
                maxTradeAmount: parseFloat(config.maxTradeAmount || 10000)
            };

            return preview;
        } catch (error) {
            throw new Error(`Failed to generate position size preview: ${error.message}`);
        }
    }

    // Risk management checks
    async performRiskChecks(positionSize, signal) {
        const config = this.configManager.getConfig();
        const balance = await this.getBalance();

        const checks = {
            passed: true,
            warnings: [],
            errors: []
        };

        // Check if position size exceeds a large percentage of balance
        const positionPercentage = (positionSize / balance.free) * 100;
        if (positionPercentage > 80) {
            checks.warnings.push(`Position size (${positionSize}) is ${positionPercentage.toFixed(1)}% of available balance`);
        }

        // Check minimum balance after trade
        const remainingBalance = balance.free - positionSize;
        const minRemainingBalance = parseFloat(config.minRemainingBalance || 10);
        if (remainingBalance < minRemainingBalance) {
            checks.errors.push(`Trade would leave insufficient balance (${remainingBalance} < ${minRemainingBalance})`);
            checks.passed = false;
        }

        // Check daily trade limit (if implemented)
        if (config.dailyTradeLimit && config.dailyTradeLimit > 0) {
            // This would require tracking daily trades - simplified for now
            checks.warnings.push('Daily trade limit checking not implemented');
        }

        return checks;
    }

    // Get money management statistics
    getStatistics() {
        const config = this.configManager.getConfig();
        
        return {
            moneyManagementEnabled: config.moneyManagementEnabled,
            mode: config.moneyManagementMode || 'percentage',
            settings: {
                positionSizePercentage: config.positionSizePercentage || 50,
                fixedTradeAmount: config.fixedTradeAmount || 100,
                minTradeAmount: config.minTradeAmount || 0.1,
                maxTradeAmount: config.maxTradeAmount || 10000,
                minRemainingBalance: config.minRemainingBalance || 10
            },
            lastCalculation: {
                timestamp: new Date().toISOString()
            }
        };
    }
}

module.exports = MoneyManager;
