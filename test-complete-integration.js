const axios = require('axios');

class CompleteIntegrationTester {
    constructor() {
        this.mexcTraderUrl = 'http://localhost:3000';
        this.webhookListenerUrl = 'http://localhost:4000';
        this.testResults = [];
    }

    async log(message) {
        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] ${message}`);
    }

    async testServiceHealth() {
        await this.log('🔍 Testing Service Health...');
        
        try {
            const mexcResponse = await axios.get(`${this.mexcTraderUrl}/health`);
            await this.log(`✅ MEXC Trader: ${mexcResponse.data.status}`);
        } catch (error) {
            await this.log(`❌ MEXC Trader: ${error.message}`);
            return false;
        }

        try {
            const webhookResponse = await axios.get(`${this.webhookListenerUrl}/health`);
            await this.log(`✅ Webhook Listener: ${webhookResponse.data.status}`);
        } catch (error) {
            await this.log(`❌ Webhook Listener: ${error.message}`);
            return false;
        }

        return true;
    }

    async testSystemStatus() {
        await this.log('📊 Testing System Status...');
        
        try {
            const response = await axios.get(`${this.webhookListenerUrl}/api/status`);
            await this.log(`   Bot Active: ${response.data.botActive}`);
            await this.log(`   MEXC Connected: ${response.data.mexcConnected}`);
            await this.log(`   Balance: ${response.data.balance?.total || 'N/A'} USDT`);
            return response.data;
        } catch (error) {
            await this.log(`❌ System status failed: ${error.message}`);
            return null;
        }
    }

    async testDirectTraderExecution(orderType, quantity = '0.0001') {
        await this.log(`🎯 Testing Direct Trader: ${orderType}...`);
        
        try {
            const startTime = Date.now();
            const response = await axios.post(`${this.mexcTraderUrl}/trade`, {
                orderType,
                quantity
            });
            const executionTime = Date.now() - startTime;

            if (response.data.success) {
                await this.log(`✅ ${orderType} successful!`);
                await this.log(`   Execution Time: ${response.data.executionTime}ms`);
                await this.log(`   Total Time: ${executionTime}ms`);
                await this.log(`   Target Achieved: ${response.data.targetAchieved ? '✅ YES' : '❌ NO'}`);
                
                this.testResults.push({
                    type: 'direct',
                    orderType,
                    success: true,
                    executionTime: response.data.executionTime,
                    totalTime: executionTime,
                    targetAchieved: response.data.targetAchieved
                });
            } else {
                await this.log(`❌ ${orderType} failed: ${response.data.error}`);
                this.testResults.push({
                    type: 'direct',
                    orderType,
                    success: false,
                    error: response.data.error
                });
            }
            
            return response.data;
        } catch (error) {
            await this.log(`❌ ${orderType} request failed: ${error.message}`);
            this.testResults.push({
                type: 'direct',
                orderType,
                success: false,
                error: error.message
            });
            return null;
        }
    }

    async testWebhookExecution(orderType, price = '0.03295') {
        await this.log(`📡 Testing Webhook: ${orderType}...`);
        
        try {
            const startTime = Date.now();
            const signal = {
                symbol: "TRUUSDT",
                trade: orderType.toLowerCase().replace(' ', '_'),
                last_price: price,
                leverage: "2"
            };

            const response = await axios.post(`${this.webhookListenerUrl}/webhook`, signal);
            const executionTime = Date.now() - startTime;

            if (response.data.success) {
                await this.log(`✅ Webhook ${orderType} successful!`);
                await this.log(`   Trade ID: ${response.data.tradeId}`);
                await this.log(`   Execution Time: ${response.data.executionTime}ms`);
                await this.log(`   Total Time: ${executionTime}ms`);
                
                this.testResults.push({
                    type: 'webhook',
                    orderType,
                    success: true,
                    executionTime: response.data.executionTime,
                    totalTime: executionTime,
                    tradeId: response.data.tradeId
                });
            } else {
                await this.log(`❌ Webhook ${orderType} failed: ${response.data.error}`);
                this.testResults.push({
                    type: 'webhook',
                    orderType,
                    success: false,
                    error: response.data.error
                });
            }
            
            return response.data;
        } catch (error) {
            await this.log(`❌ Webhook ${orderType} request failed: ${error.message}`);
            this.testResults.push({
                type: 'webhook',
                orderType,
                success: false,
                error: error.message
            });
            return null;
        }
    }

    async runCompleteTest() {
        await this.log('🚀 Starting Complete Integration Test');
        await this.log('=====================================');

        // Test 1: Service Health
        await this.log('\n1️⃣ Service Health Check...');
        const servicesHealthy = await this.testServiceHealth();
        if (!servicesHealthy) {
            await this.log('❌ Services not healthy. Aborting test.');
            return;
        }

        // Test 2: System Status
        await this.log('\n2️⃣ System Status Check...');
        const status = await this.testSystemStatus();
        if (!status?.mexcConnected) {
            await this.log('❌ MEXC not connected. Aborting test.');
            return;
        }

        // Test 3: Direct Trader Tests
        await this.log('\n3️⃣ Direct Trader Tests...');
        const orderTypes = ['Open Long', 'Open Short', 'Close Long', 'Close Short'];
        
        for (const orderType of orderTypes) {
            await this.testDirectTraderExecution(orderType);
            await new Promise(resolve => setTimeout(resolve, 3000)); // Wait between tests
        }

        // Test 4: Webhook Tests
        await this.log('\n4️⃣ Webhook Integration Tests...');
        
        for (const orderType of orderTypes) {
            await this.testWebhookExecution(orderType);
            await new Promise(resolve => setTimeout(resolve, 3000)); // Wait between tests
        }

        // Test Results Summary
        await this.log('\n📊 COMPLETE TEST RESULTS');
        await this.log('=========================');
        
        const directTests = this.testResults.filter(r => r.type === 'direct');
        const webhookTests = this.testResults.filter(r => r.type === 'webhook');
        
        const directSuccess = directTests.filter(r => r.success).length;
        const webhookSuccess = webhookTests.filter(r => r.success).length;
        
        await this.log(`🎯 Direct Trader: ${directSuccess}/${directTests.length} successful`);
        await this.log(`📡 Webhook Tests: ${webhookSuccess}/${webhookTests.length} successful`);
        
        const fastDirect = directTests.filter(r => r.success && r.targetAchieved).length;
        const totalSuccess = directSuccess + webhookSuccess;
        const totalTests = directTests.length + webhookTests.length;
        
        await this.log(`⚡ Under 2 seconds: ${fastDirect}/${directSuccess} direct tests`);
        await this.log(`📈 Overall Success: ${totalSuccess}/${totalTests}`);

        // Performance Analysis
        await this.log('\n📈 PERFORMANCE ANALYSIS');
        await this.log('========================');
        
        for (const result of this.testResults) {
            if (result.success) {
                const speed = result.targetAchieved ? '🟢 FAST' : '🟡 SLOW';
                const type = result.type === 'direct' ? 'DIRECT' : 'WEBHOOK';
                await this.log(`${speed} ${type} ${result.orderType}: ${result.executionTime}ms`);
            } else {
                const type = result.type === 'direct' ? 'DIRECT' : 'WEBHOOK';
                await this.log(`🔴 FAIL ${type} ${result.orderType}: ${result.error}`);
            }
        }

        if (totalSuccess === totalTests) {
            await this.log('\n🎉 ALL TESTS PASSED! System is fully operational!');
        } else {
            await this.log('\n⚠️ Some tests failed. System needs attention.');
        }

        await this.log('\n🏁 Complete Integration Test Finished');
    }
}

// Run the test
async function main() {
    const tester = new CompleteIntegrationTester();
    await tester.runCompleteTest();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = CompleteIntegrationTester;
