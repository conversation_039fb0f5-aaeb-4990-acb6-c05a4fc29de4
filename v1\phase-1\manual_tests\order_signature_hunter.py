#!/usr/bin/env python3
"""
Order Signature Hunter
Specifically target order placement signatures
"""

import json
import time
import hashlib
import hmac
from playwright.sync_api import sync_playwright
from dotenv import dotenv_values

class OrderSignatureHunter:
    """Hunt specifically for order placement signatures"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        self.uc_token = vals.get('uc_token')
        
        print("🎯 Order Signature Hunter")
        print("="*30)
    
    def setup_targeted_capture(self):
        """Setup targeted capture for order signatures only"""
        
        print("🌐 Setting up targeted order signature capture...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                print("❌ No browser contexts found")
                return False
            
            context = self.browser.contexts[0]
            
            if context.pages:
                self.page = context.pages[0]
            else:
                self.page = context.new_page()
            
            # Navigate to MEXC trading page
            if 'mexc.com' not in self.page.url:
                self.page.goto('https://www.mexc.com/futures/BTC_USDT', wait_until='domcontentloaded')
                time.sleep(5)
            
            # Inject session tokens
            self.page.evaluate(f"""
                () => {{
                    localStorage.setItem('authorization', '{self.auth}');
                    localStorage.setItem('u_id', '{self.auth}');
                    {f"localStorage.setItem('uc_token', '{self.uc_token}');" if self.uc_token else ""}
                }}
            """)
            
            # Reload to apply session
            self.page.reload(wait_until='domcontentloaded')
            time.sleep(5)
            
            # Setup targeted capture
            self._setup_order_capture()
            
            print("✅ Targeted order capture system ready")
            return True
            
        except Exception as e:
            print(f"❌ Setup failed: {e}")
            return False
    
    def _setup_order_capture(self):
        """Setup capture system specifically for order endpoints"""
        
        capture_code = """
            window.orderSignatureHunter = {
                orderSignatures: [],
                
                init() {
                    console.log('🎯 Order signature hunter initializing...');
                    
                    // Override fetch with specific order targeting
                    this.setupOrderFetchCapture();
                    this.setupOrderXHRCapture();
                    this.setupOrderFormMonitoring();
                    
                    console.log('✅ Order signature hunter ready');
                },
                
                setupOrderFetchCapture() {
                    const self = this;
                    const originalFetch = window.fetch;
                    
                    window.fetch = function(...args) {
                        const [url, options] = args;
                        
                        // Target only order-related endpoints
                        if (url.includes('/order/create') || 
                            url.includes('/order/submit') ||
                            url.includes('/private/order/')) {
                            
                            const headers = options?.headers || {};
                            const signature = headers['x-mxc-sign'];
                            const nonce = headers['x-mxc-nonce'];
                            
                            if (signature && nonce) {
                                console.log('🎯 ORDER SIGNATURE CAPTURED!');
                                console.log('URL:', url);
                                console.log('Signature:', signature);
                                console.log('Nonce:', nonce);
                                console.log('Body:', options.body);
                                
                                self.orderSignatures.push({
                                    type: 'order_create',
                                    url: url,
                                    signature: signature,
                                    nonce: nonce,
                                    body: options.body,
                                    headers: headers,
                                    timestamp: Date.now(),
                                    method: options.method || 'POST'
                                });
                                
                                // Immediately analyze
                                self.analyzeOrderSignature(signature, nonce, options.body);
                            }
                        }
                        
                        return originalFetch.apply(this, args);
                    };
                },
                
                setupOrderXHRCapture() {
                    const self = this;
                    const OriginalXHR = window.XMLHttpRequest;
                    
                    window.XMLHttpRequest = function() {
                        const xhr = new OriginalXHR();
                        const originalOpen = xhr.open;
                        const originalSend = xhr.send;
                        const originalSetRequestHeader = xhr.setRequestHeader;
                        
                        let method, url;
                        let headers = {};
                        let body = null;
                        
                        xhr.open = function(m, u, ...args) {
                            method = m;
                            url = u;
                            return originalOpen.apply(this, [m, u, ...args]);
                        };
                        
                        xhr.setRequestHeader = function(name, value) {
                            headers[name] = value;
                            return originalSetRequestHeader.apply(this, [name, value]);
                        };
                        
                        xhr.send = function(data) {
                            body = data;
                            
                            // Target order endpoints
                            if (url && (url.includes('/order/create') || 
                                       url.includes('/order/submit') ||
                                       url.includes('/private/order/'))) {
                                
                                const signature = headers['x-mxc-sign'];
                                const nonce = headers['x-mxc-nonce'];
                                
                                if (signature && nonce) {
                                    console.log('🎯 ORDER SIGNATURE CAPTURED via XHR!');
                                    
                                    self.orderSignatures.push({
                                        type: 'order_create_xhr',
                                        url: url,
                                        signature: signature,
                                        nonce: nonce,
                                        body: body,
                                        headers: headers,
                                        timestamp: Date.now(),
                                        method: method
                                    });
                                    
                                    self.analyzeOrderSignature(signature, nonce, body);
                                }
                            }
                            
                            return originalSend.apply(this, [data]);
                        };
                        
                        return xhr;
                    };
                },
                
                setupOrderFormMonitoring() {
                    const self = this;
                    
                    // Monitor for order form interactions
                    document.addEventListener('click', function(event) {
                        const target = event.target;
                        const text = target.textContent?.toLowerCase() || '';
                        const className = target.className?.toLowerCase() || '';
                        
                        // Look for order buttons
                        if ((text.includes('buy') || text.includes('sell') || 
                             text.includes('long') || text.includes('short') ||
                             text.includes('place') || text.includes('submit')) &&
                            (className.includes('button') || className.includes('btn') ||
                             target.tagName === 'BUTTON')) {
                            
                            console.log('🎯 Order button clicked:', target);
                            
                            // Set expectation for order signature
                            self.expectingOrderSignature = true;
                            
                            // Clear expectation after 10 seconds
                            setTimeout(() => {
                                self.expectingOrderSignature = false;
                            }, 10000);
                        }
                    }, true);
                },
                
                analyzeOrderSignature(signature, nonce, body) {
                    console.log('🔍 Analyzing order signature...');
                    
                    const auth = localStorage.getItem('authorization') || '';
                    let orderData = null;
                    
                    try {
                        orderData = typeof body === 'string' ? JSON.parse(body) : body;
                    } catch (e) {
                        orderData = body;
                    }
                    
                    if (!orderData) {
                        console.log('❌ No order data to analyze');
                        return;
                    }
                    
                    console.log('📋 Order Data:', orderData);
                    console.log('🔐 Signature:', signature);
                    console.log('🔢 Nonce:', nonce);
                    console.log('🔑 Auth:', auth.substring(0, 10) + '...');
                    
                    // Try comprehensive reverse engineering
                    const result = this.comprehensiveReverseEngineering(signature, nonce, orderData, auth);
                    
                    if (result) {
                        console.log('🎉 SIGNATURE ALGORITHM FOUND!');
                        console.log('Result:', result);
                        
                        // Store the working algorithm globally
                        window.crackedAlgorithm = result;
                    } else {
                        console.log('❌ Could not crack this signature');
                    }
                },
                
                comprehensiveReverseEngineering(signature, nonce, orderData, auth) {
                    console.log('🧪 Comprehensive reverse engineering...');
                    
                    // Test all possible combinations
                    const contentMethods = [
                        // JSON variations
                        () => JSON.stringify(orderData),
                        () => JSON.stringify(orderData, Object.keys(orderData).sort()),
                        () => JSON.stringify(orderData, null, 0),
                        
                        // Query string variations
                        () => this.toQueryString(orderData),
                        () => this.toQueryStringSorted(orderData),
                        
                        // Combined variations
                        () => auth + nonce + JSON.stringify(orderData, Object.keys(orderData).sort()),
                        () => nonce + auth + JSON.stringify(orderData, Object.keys(orderData).sort()),
                        () => JSON.stringify(orderData, Object.keys(orderData).sort()) + nonce + auth,
                        () => JSON.stringify(orderData, Object.keys(orderData).sort()) + auth + nonce,
                        
                        () => auth + nonce + this.toQueryStringSorted(orderData),
                        () => nonce + auth + this.toQueryStringSorted(orderData),
                        () => this.toQueryStringSorted(orderData) + nonce + auth,
                        () => this.toQueryStringSorted(orderData) + auth + nonce,
                        
                        // With separators
                        () => auth + '|' + nonce + '|' + JSON.stringify(orderData, Object.keys(orderData).sort()),
                        () => auth + '&' + nonce + '&' + this.toQueryStringSorted(orderData),
                        () => auth + nonce + '&' + this.toQueryStringSorted(orderData),
                        
                        // Timestamp variations
                        () => auth + nonce + Math.floor(Date.now()/1000) + JSON.stringify(orderData, Object.keys(orderData).sort()),
                        () => auth + nonce + Date.now() + JSON.stringify(orderData, Object.keys(orderData).sort()),
                    ];
                    
                    const hashMethods = [
                        {name: 'MD5', func: (str) => this.simpleMD5(str)},
                        {name: 'SHA256', func: (str) => this.simpleSHA256(str)},
                        {name: 'HMAC-MD5-auth', func: (str) => this.simpleHMAC(str, auth, 'md5')},
                        {name: 'HMAC-SHA256-auth', func: (str) => this.simpleHMAC(str, auth, 'sha256')},
                        {name: 'HMAC-MD5-nonce', func: (str) => this.simpleHMAC(str, nonce, 'md5')},
                        {name: 'HMAC-SHA256-nonce', func: (str) => this.simpleHMAC(str, nonce, 'sha256')},
                    ];
                    
                    // Test all combinations
                    for (let i = 0; i < contentMethods.length; i++) {
                        try {
                            const content = contentMethods[i]();
                            
                            for (let j = 0; j < hashMethods.length; j++) {
                                try {
                                    const hash = hashMethods[j].func(content);
                                    
                                    // Test different lengths
                                    for (let len of [16, 24, 32, 40, 48, 64]) {
                                        const truncated = hash.substring(0, len);
                                        
                                        if (truncated.toLowerCase() === signature.toLowerCase()) {
                                            return {
                                                contentMethod: i,
                                                hashMethod: hashMethods[j].name,
                                                length: len,
                                                content: content,
                                                hash: hash,
                                                signature: truncated
                                            };
                                        }
                                    }
                                } catch (e) {
                                    continue;
                                }
                            }
                        } catch (e) {
                            continue;
                        }
                    }
                    
                    return null;
                },
                
                toQueryString(obj) {
                    const params = [];
                    for (const key in obj) {
                        params.push(`${key}=${obj[key]}`);
                    }
                    return params.join('&');
                },
                
                toQueryStringSorted(obj) {
                    const params = [];
                    const sortedKeys = Object.keys(obj).sort();
                    for (const key of sortedKeys) {
                        params.push(`${key}=${obj[key]}`);
                    }
                    return params.join('&');
                },
                
                // Simple hash implementations for testing
                simpleMD5(str) {
                    let hash = 0;
                    for (let i = 0; i < str.length; i++) {
                        const char = str.charCodeAt(i);
                        hash = ((hash << 3) - hash) + char;
                        hash = hash & hash;
                    }
                    return Math.abs(hash).toString(16).padStart(32, '0');
                },
                
                simpleSHA256(str) {
                    let hash = 0;
                    for (let i = 0; i < str.length; i++) {
                        const char = str.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash & hash;
                    }
                    return Math.abs(hash).toString(16).padStart(64, '0');
                },
                
                simpleHMAC(message, secret, type) {
                    const combined = secret + message + secret;
                    return type === 'md5' ? this.simpleMD5(combined) : this.simpleSHA256(combined);
                },
                
                getOrderSignatures() {
                    return this.orderSignatures;
                },
                
                getCrackedAlgorithm() {
                    return window.crackedAlgorithm || null;
                }
            };
            
            // Initialize order signature hunter
            window.orderSignatureHunter.init();
        """
        
        self.page.evaluate(capture_code)
        print("✅ Order signature capture system configured")
    
    def wait_for_order_signature(self, timeout_seconds=300):
        """Wait specifically for order placement signature"""
        
        print(f"⏳ Waiting for ORDER PLACEMENT signature (timeout: {timeout_seconds}s)...")
        print("💡 Please place an actual order in the browser:")
        print("   1. Set price and amount in the trading form")
        print("   2. Click 'Buy Long' or 'Sell Short' button")
        print("   3. Confirm the order if prompted")
        print("   4. The order signature will be captured automatically")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout_seconds:
            try:
                # Check for captured order signatures
                signatures = self.page.evaluate("() => window.orderSignatureHunter.getOrderSignatures()")
                
                if signatures and len(signatures) > 0:
                    print(f"🎉 CAPTURED {len(signatures)} ORDER SIGNATURES!")
                    
                    # Check if algorithm was cracked
                    cracked_algorithm = self.page.evaluate("() => window.orderSignatureHunter.getCrackedAlgorithm()")
                    
                    if cracked_algorithm:
                        print(f"🎉 ORDER SIGNATURE ALGORITHM CRACKED!")
                        return signatures, cracked_algorithm
                    else:
                        print(f"📋 Order signatures captured, analyzing...")
                        return signatures, None
                
                # Show progress
                elapsed = int(time.time() - start_time)
                if elapsed % 30 == 0 and elapsed > 0:  # Every 30 seconds
                    print(f"⏳ Still waiting for order signature... ({elapsed}s elapsed)")
                
                time.sleep(1)
                
            except Exception as e:
                print(f"❌ Error during capture: {e}")
                time.sleep(5)
        
        print(f"⏰ Timeout reached ({timeout_seconds}s)")
        return None, None
    
    def cleanup(self):
        """Cleanup resources"""
        if hasattr(self, 'browser'):
            self.browser.close()
        if hasattr(self, 'playwright'):
            self.playwright.stop()

def main():
    """Main order signature hunting"""
    
    hunter = OrderSignatureHunter()
    
    try:
        # Setup targeted capture
        if not hunter.setup_targeted_capture():
            print("❌ Failed to setup targeted capture")
            return
        
        # Wait for order signature
        signatures, cracked_algorithm = hunter.wait_for_order_signature(timeout_seconds=300)
        
        if cracked_algorithm:
            print(f"\n🎉 ORDER SIGNATURE ALGORITHM SUCCESSFULLY CRACKED!")
            print(f"Algorithm details: {cracked_algorithm}")
            print(f"\n🚀 Ready to implement in production!")
            
            # Save the algorithm for implementation
            with open('cracked_algorithm.json', 'w') as f:
                json.dump(cracked_algorithm, f, indent=2)
            print(f"💾 Algorithm saved to cracked_algorithm.json")
            
        elif signatures:
            print(f"\n📋 Order signatures captured but not cracked")
            print(f"Captured {len(signatures)} signatures")
            for i, sig in enumerate(signatures):
                print(f"   Signature {i+1}: {sig.get('signature', 'None')}")
        else:
            print(f"\n⏰ No order signatures captured")
            print(f"Make sure to actually place an order (not just click buttons)")
        
    finally:
        hunter.cleanup()

if __name__ == '__main__':
    main()
