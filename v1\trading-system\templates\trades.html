{% extends "base.html" %}

{% block title %}Trading History - MEXC Trading System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">Trading History</h1>
                <div class="d-sm-flex align-items-center">
                    <button class="btn btn-primary btn-sm" onclick="refreshTrades()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Trading Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Trades</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ recent_trades|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Active Trades</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ active_trades|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-play-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Success Rate</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ "%.1f"|format(trading_stats.success_rate or 0) }}%
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Total P&L</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ "%.2f"|format(trading_stats.total_pnl or 0) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Trades -->
    {% if active_trades %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Active Trades</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Order ID</th>
                            <th>Symbol</th>
                            <th>Side</th>
                            <th>Quantity</th>
                            <th>Price</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for trade in active_trades %}
                        <tr>
                            <td><span class="badge badge-secondary">{{ trade.order_id or 'N/A' }}</span></td>
                            <td><strong>{{ trade.symbol }}</strong></td>
                            <td>
                                {% if trade.side == 'buy' %}
                                    <span class="badge badge-success">{{ trade.side|title }}</span>
                                {% else %}
                                    <span class="badge badge-danger">{{ trade.side|title }}</span>
                                {% endif %}
                            </td>
                            <td>{{ trade.quantity }}</td>
                            <td>${{ "%.4f"|format(trade.price) if trade.price else 'Market' }}</td>
                            <td>
                                {% if trade.status == 'executed' %}
                                    <span class="badge badge-success">{{ trade.status|title }}</span>
                                {% elif trade.status == 'pending' %}
                                    <span class="badge badge-warning">{{ trade.status|title }}</span>
                                {% elif trade.status == 'failed' %}
                                    <span class="badge badge-danger">{{ trade.status|title }}</span>
                                {% else %}
                                    <span class="badge badge-secondary">{{ trade.status|title }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if trade.created_at %}
                                    {% if trade.created_at is string %}
                                        {{ trade.created_at[:16] }}
                                    {% else %}
                                        {{ trade.created_at.strftime('%m/%d %H:%M') }}
                                    {% endif %}
                                {% else %}
                                    N/A
                                {% endif %}
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                        onclick="viewTrade('{{ trade.id }}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Recent Trades -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Recent Trades</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="tradesTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Webhook ID</th>
                            <th>Symbol</th>
                            <th>Action</th>
                            <th>Side</th>
                            <th>Quantity</th>
                            <th>Price</th>
                            <th>Leverage</th>
                            <th>Status</th>
                            <th>P&L</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for trade in recent_trades %}
                        <tr>
                            <td><span class="badge badge-info">{{ trade.webhook_id[:8] if trade.webhook_id else 'N/A' }}...</span></td>
                            <td><strong>{{ trade.symbol }}</strong></td>
                            <td>
                                {% if trade.action == 'open' %}
                                    <span class="badge badge-primary">{{ trade.action|title }}</span>
                                {% elif trade.action == 'close' %}
                                    <span class="badge badge-secondary">{{ trade.action|title }}</span>
                                {% else %}
                                    <span class="badge badge-light">{{ trade.action|title }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if trade.side == 'buy' %}
                                    <span class="badge badge-success">{{ trade.side|title }}</span>
                                {% else %}
                                    <span class="badge badge-danger">{{ trade.side|title }}</span>
                                {% endif %}
                            </td>
                            <td>{{ trade.quantity }}</td>
                            <td>
                                {% if trade.executed_price %}
                                    ${{ "%.4f"|format(trade.executed_price) }}
                                {% elif trade.price %}
                                    ${{ "%.4f"|format(trade.price) }}
                                {% else %}
                                    Market
                                {% endif %}
                            </td>
                            <td>{{ trade.leverage }}x</td>
                            <td>
                                {% if trade.status == 'executed' %}
                                    <span class="badge badge-success">{{ trade.status|title }}</span>
                                {% elif trade.status == 'pending' %}
                                    <span class="badge badge-warning">{{ trade.status|title }}</span>
                                {% elif trade.status == 'failed' %}
                                    <span class="badge badge-danger">{{ trade.status|title }}</span>
                                {% else %}
                                    <span class="badge badge-secondary">{{ trade.status|title }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if trade.pnl %}
                                    {% if trade.pnl > 0 %}
                                        <span class="text-success">+${{ "%.2f"|format(trade.pnl) }}</span>
                                    {% else %}
                                        <span class="text-danger">${{ "%.2f"|format(trade.pnl) }}</span>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if trade.created_at %}
                                    {% if trade.created_at is string %}
                                        {{ trade.created_at[:16] }}
                                    {% else %}
                                        {{ trade.created_at.strftime('%m/%d %H:%M') }}
                                    {% endif %}
                                {% else %}
                                    N/A
                                {% endif %}
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                        onclick="viewTrade('{{ trade.id }}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="11" class="text-center text-muted">No trades found</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
function refreshTrades() {
    location.reload();
}

function viewTrade(tradeId) {
    // Implement trade viewing functionality
    alert('View trade: ' + tradeId);
}

// Auto-refresh every 30 seconds
setInterval(refreshTrades, 30000);
</script>
{% endblock %}
