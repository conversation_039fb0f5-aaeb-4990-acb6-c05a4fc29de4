const axios = require('axios');
const fs = require('fs');

class SimpleComprehensiveTester {
    constructor() {
        this.webhookUrl = 'http://localhost:4000/webhook';
        this.statusUrl = 'http://localhost:4000/api/status';
        this.testResults = [];
        this.currentTRUPrice = 0.03225; // Current TRUUSDT price
    }

    log(message, data = null) {
        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] ${message}`);
        if (data && typeof data === 'object') {
            console.log(JSON.stringify(data, null, 2));
        } else if (data) {
            console.log(data);
        }
    }

    addTestResult(testName, success, details = null) {
        const result = {
            testName,
            success,
            details,
            timestamp: new Date().toISOString()
        };
        this.testResults.push(result);
        
        const status = success ? '✅' : '❌';
        this.log(`${status} ${testName}`);
        if (details) {
            console.log('   Details:', details);
        }
    }

    async testSystemStatus() {
        this.log('🔍 Testing System Status...');
        
        try {
            const response = await axios.get(this.statusUrl, { timeout: 5000 });
            const isHealthy = response.data.botActive && response.data.configured;
            
            this.addTestResult('System Status Check', isHealthy, {
                botActive: response.data.botActive,
                configured: response.data.configured,
                mexcConnected: response.data.mexcConnected,
                balance: response.data.balance
            });
            
            return response.data;
        } catch (error) {
            this.addTestResult('System Status Check', false, { error: error.message });
            return null;
        }
    }

    async testWebhookSignalProcessing() {
        this.log('📡 Testing Webhook Signal Processing...');

        const testCases = [
            {
                name: 'Buy Signal with Current Price',
                signal: {
                    symbol: "TRUUSDT",
                    trade: "buy",
                    last_price: this.currentTRUPrice.toString(),
                    leverege: "2"
                },
                expectedOrderType: 'Open Long'
            },
            {
                name: 'Sell Signal with Current Price',
                signal: {
                    symbol: "TRUUSDT",
                    trade: "sell", 
                    last_price: this.currentTRUPrice.toString(),
                    leverege: "2"
                },
                expectedOrderType: 'Open Short'
            },
            {
                name: 'Close Signal with Current Price',
                signal: {
                    symbol: "TRUUSDT",
                    trade: "close",
                    last_price: this.currentTRUPrice.toString(),
                    leverege: "2"
                },
                expectedOrderType: 'Close Long'
            },
            {
                name: 'Buy Signal with Slightly Different Price',
                signal: {
                    symbol: "TRUUSDT",
                    trade: "buy",
                    last_price: (this.currentTRUPrice * 1.01).toString(), // 1% higher
                    leverege: "2"
                },
                expectedOrderType: 'Open Long'
            }
        ];

        for (const testCase of testCases) {
            await this.testSingleSignal(testCase);
            await this.sleep(1500);
        }
    }

    async testSingleSignal(testCase) {
        try {
            const startTime = Date.now();
            const response = await axios.post(this.webhookUrl, testCase.signal, { 
                timeout: 15000,
                headers: { 'Content-Type': 'application/json' }
            });
            const executionTime = Date.now() - startTime;
            
            const orderTypeMatch = response.data.processedSignal?.orderType === testCase.expectedOrderType;
            const signalProcessed = response.data.processedSignal !== undefined;
            
            this.addTestResult(testCase.name, signalProcessed, {
                expectedOrderType: testCase.expectedOrderType,
                actualOrderType: response.data.processedSignal?.orderType,
                orderTypeMatch,
                success: response.data.success,
                message: response.data.message,
                skipped: response.data.skipped,
                reason: response.data.reason,
                executionTime: `${executionTime}ms`,
                quantity: response.data.processedSignal?.quantity,
                price: response.data.processedSignal?.price,
                leverage: response.data.processedSignal?.leverage
            });

        } catch (error) {
            this.addTestResult(testCase.name, false, { 
                error: error.message,
                signal: testCase.signal
            });
        }
    }

    async testDifferentSymbols() {
        this.log('🔄 Testing Different Trading Symbols...');

        const symbols = [
            { symbol: 'TRUUSDT', price: '0.03225' },
            { symbol: 'BTCUSDT', price: '60000' },
            { symbol: 'ETHUSDT', price: '2500' }
        ];

        for (const symbolTest of symbols) {
            const testSignal = {
                symbol: symbolTest.symbol,
                trade: "buy",
                last_price: symbolTest.price,
                leverege: "2"
            };

            try {
                const response = await axios.post(this.webhookUrl, testSignal, { timeout: 10000 });
                
                this.addTestResult(`${symbolTest.symbol} Signal Processing`, true, {
                    symbol: symbolTest.symbol,
                    processed: response.data.processedSignal !== undefined,
                    success: response.data.success,
                    message: response.data.message,
                    skipped: response.data.skipped
                });

            } catch (error) {
                this.addTestResult(`${symbolTest.symbol} Signal Processing`, false, { 
                    error: error.message 
                });
            }

            await this.sleep(1000);
        }
    }

    async testLeverageHandling() {
        this.log('⚖️ Testing Leverage Handling...');

        const leverageTests = [
            { leverage: "1", name: "1x Leverage" },
            { leverage: "2", name: "2x Leverage" },
            { leverage: "5", name: "5x Leverage" },
            { leverage: "10", name: "10x Leverage" }
        ];

        for (const leverageTest of leverageTests) {
            const testSignal = {
                symbol: "TRUUSDT",
                trade: "buy",
                last_price: this.currentTRUPrice.toString(),
                leverege: leverageTest.leverage
            };

            try {
                const response = await axios.post(this.webhookUrl, testSignal, { timeout: 10000 });
                
                this.addTestResult(leverageTest.name, true, {
                    requestedLeverage: leverageTest.leverage,
                    processedLeverage: response.data.processedSignal?.leverage,
                    success: response.data.success,
                    message: response.data.message
                });

            } catch (error) {
                this.addTestResult(leverageTest.name, false, { error: error.message });
            }

            await this.sleep(1000);
        }
    }

    async testErrorHandling() {
        this.log('🚨 Testing Error Handling...');

        const errorTests = [
            {
                name: 'Invalid Symbol',
                signal: {
                    symbol: "INVALIDUSDT",
                    trade: "buy",
                    last_price: "1.0",
                    leverege: "2"
                }
            },
            {
                name: 'Missing Symbol',
                signal: {
                    trade: "buy",
                    last_price: "1.0",
                    leverege: "2"
                }
            },
            {
                name: 'Invalid Trade Type',
                signal: {
                    symbol: "TRUUSDT",
                    trade: "invalid",
                    last_price: this.currentTRUPrice.toString(),
                    leverege: "2"
                }
            },
            {
                name: 'Missing Price',
                signal: {
                    symbol: "TRUUSDT",
                    trade: "buy",
                    leverege: "2"
                }
            }
        ];

        for (const errorTest of errorTests) {
            try {
                const response = await axios.post(this.webhookUrl, errorTest.signal, { timeout: 10000 });
                
                // For error tests, we expect either an error response or a skipped trade
                const handledCorrectly = !response.data.success || response.data.skipped;
                
                this.addTestResult(errorTest.name, handledCorrectly, {
                    success: response.data.success,
                    message: response.data.message,
                    skipped: response.data.skipped,
                    reason: response.data.reason
                });

            } catch (error) {
                // Catching an error is also acceptable for error handling tests
                this.addTestResult(errorTest.name, true, { 
                    errorCaught: true,
                    error: error.message 
                });
            }

            await this.sleep(1000);
        }
    }

    async testExecutionPerformance() {
        this.log('⏱️ Testing Execution Performance...');

        const testSignal = {
            symbol: "TRUUSDT",
            trade: "buy",
            last_price: this.currentTRUPrice.toString(),
            leverege: "2"
        };

        const executionTimes = [];

        for (let i = 0; i < 5; i++) {
            const startTime = Date.now();
            
            try {
                const response = await axios.post(this.webhookUrl, testSignal, { timeout: 15000 });
                const totalTime = Date.now() - startTime;
                
                executionTimes.push({
                    attempt: i + 1,
                    totalTime,
                    success: response.data.processedSignal !== undefined,
                    message: response.data.message,
                    skipped: response.data.skipped
                });

                this.log(`   Execution ${i + 1}: ${totalTime}ms`);
                
            } catch (error) {
                const totalTime = Date.now() - startTime;
                executionTimes.push({
                    attempt: i + 1,
                    totalTime,
                    success: false,
                    error: error.message
                });
            }

            await this.sleep(2000);
        }

        const avgTime = executionTimes.reduce((sum, t) => sum + t.totalTime, 0) / executionTimes.length;
        const successfulExecutions = executionTimes.filter(t => t.success).length;
        const sub2SecondCount = executionTimes.filter(t => t.totalTime < 2000).length;

        this.addTestResult('Execution Performance', successfulExecutions >= 3, {
            averageTime: `${avgTime.toFixed(0)}ms`,
            successfulExecutions: `${successfulExecutions}/5`,
            sub2SecondExecutions: `${sub2SecondCount}/5`,
            allExecutions: executionTimes
        });
    }

    async generateReport() {
        this.log('📊 Generating Comprehensive Test Report...');

        const report = {
            summary: {
                totalTests: this.testResults.length,
                passed: this.testResults.filter(r => r.success).length,
                failed: this.testResults.filter(r => !r.success).length,
                successRate: `${Math.round((this.testResults.filter(r => r.success).length / this.testResults.length) * 100)}%`
            },
            testResults: this.testResults,
            timestamp: new Date().toISOString(),
            environment: {
                webhookListenerUrl: this.webhookUrl,
                currentTRUPrice: this.currentTRUPrice,
                testDuration: new Date().toISOString()
            }
        };

        fs.writeFileSync('simple-comprehensive-test-report.json', JSON.stringify(report, null, 2));
        
        this.log('📋 Test Summary:');
        this.log(`   Total Tests: ${report.summary.totalTests}`);
        this.log(`   Passed: ${report.summary.passed}`);
        this.log(`   Failed: ${report.summary.failed}`);
        this.log(`   Success Rate: ${report.summary.successRate}`);
        
        // Show failed tests
        const failedTests = this.testResults.filter(r => !r.success);
        if (failedTests.length > 0) {
            this.log('\n❌ Failed Tests:');
            failedTests.forEach(test => {
                this.log(`   - ${test.testName}: ${test.details?.error || test.details?.reason || 'Unknown error'}`);
            });
        }
        
        return report;
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async runComprehensiveTest() {
        this.log('🚀 Starting Simple Comprehensive Test...');
        
        try {
            await this.testSystemStatus();
            await this.testWebhookSignalProcessing();
            await this.testDifferentSymbols();
            await this.testLeverageHandling();
            await this.testErrorHandling();
            await this.testExecutionPerformance();
            
            const report = await this.generateReport();
            
            this.log('\n✅ Comprehensive testing completed!');
            this.log('📄 Full report saved to: simple-comprehensive-test-report.json');
            
            return report;
            
        } catch (error) {
            this.log('❌ Testing failed:', error.message);
            throw error;
        }
    }
}

// Run the test
if (require.main === module) {
    const tester = new SimpleComprehensiveTester();
    tester.runComprehensiveTest()
        .then(report => {
            console.log('\n🎉 Testing completed successfully!');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n💥 Testing failed:', error.message);
            process.exit(1);
        });
}

module.exports = SimpleComprehensiveTester;
