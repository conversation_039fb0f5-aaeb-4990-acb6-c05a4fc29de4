#!/usr/bin/env node

/**
 * Test Persistent Background Monitoring
 * Verifies that background monitoring starts with the webhook listener and runs continuously
 */

const axios = require('axios');

class PersistentMonitoringTester {
    constructor() {
        this.webhookUrl = 'http://localhost:8080';
        this.testResults = [];
    }

    async runTests() {
        console.log('🔄 Testing Persistent Background Monitoring');
        console.log('============================================\n');

        try {
            await this.testServiceHealth();
            await this.testMonitoringStatus();
            await this.testMonitoringEndpoint();
            await this.testTradeExecution();

            this.printResults();

        } catch (error) {
            console.error('❌ Test suite failed:', error.message);
            process.exit(1);
        }
    }

    async testServiceHealth() {
        console.log('🔍 Test 1: Service Health Check');
        console.log('--------------------------------');

        try {
            const response = await axios.get(`${this.webhookUrl}/health`, { timeout: 5000 });
            
            console.log(`✅ Service Status: ${response.data.status}`);
            console.log(`🔧 Trading Executor: ${response.data.components.tradingExecutor ? 'Ready' : 'Not Ready'}`);
            
            // Check background monitoring status
            const monitoring = response.data.components.backgroundMonitoring;
            if (monitoring) {
                console.log(`🧹 Background Monitoring:`);
                console.log(`   - Initialized: ${monitoring.initialized ? '✅' : '❌'}`);
                console.log(`   - Active: ${monitoring.active ? '✅' : '❌'}`);
                console.log(`   - Executing Trade: ${monitoring.isExecutingTrade ? '⚡' : '🟢'}`);
                console.log(`   - Last Balance: ${monitoring.lastBalance || 'Not fetched'} USDT`);
                
                if (monitoring.initialized && monitoring.active) {
                    this.addResult('Background Monitoring Initialized', true, 'Monitoring system is active');
                } else {
                    this.addResult('Background Monitoring Initialized', false, 'Monitoring system not properly initialized');
                }
            } else {
                this.addResult('Background Monitoring Status', false, 'No monitoring status in health check');
            }

        } catch (error) {
            this.addResult('Service Health Check', false, error.message);
        }

        console.log('');
    }

    async testMonitoringStatus() {
        console.log('📊 Test 2: Detailed Monitoring Status');
        console.log('--------------------------------------');

        try {
            const response = await axios.get(`${this.webhookUrl}/api/monitoring`, { timeout: 5000 });
            
            if (response.data.success) {
                const monitoring = response.data.monitoring;
                const trading = response.data.trading;
                
                console.log('🧹 Monitoring Details:');
                console.log(`   - Initialized: ${monitoring.initialized}`);
                console.log(`   - Active: ${monitoring.active}`);
                console.log(`   - Is Executing Trade: ${monitoring.isExecutingTrade}`);
                console.log(`   - Last Balance: ${monitoring.lastBalance} USDT`);
                console.log(`   - Balance Update Time: ${monitoring.balanceUpdateTime || 'Never'}`);
                
                console.log('\n🔧 Trading Status:');
                console.log(`   - Ready: ${trading.ready}`);
                console.log(`   - Last Health Check: ${trading.lastHealthCheck || 'Never'}`);
                
                // Verify monitoring is properly initialized
                if (monitoring.initialized && monitoring.active && !monitoring.isExecutingTrade) {
                    this.addResult('Monitoring System Status', true, 'All monitoring components are operational');
                } else {
                    this.addResult('Monitoring System Status', false, 'Some monitoring components are not operational');
                }
                
            } else {
                this.addResult('Monitoring Status API', false, response.data.error);
            }

        } catch (error) {
            this.addResult('Monitoring Status API', false, error.message);
        }

        console.log('');
    }

    async testMonitoringEndpoint() {
        console.log('🔗 Test 3: Monitoring Endpoint Functionality');
        console.log('---------------------------------------------');

        try {
            // Test multiple calls to see if monitoring persists
            const calls = [];
            for (let i = 0; i < 3; i++) {
                const response = await axios.get(`${this.webhookUrl}/api/monitoring`, { timeout: 3000 });
                calls.push(response.data);
                await this.sleep(1000); // Wait 1 second between calls
            }

            // Check if monitoring status is consistent
            const allInitialized = calls.every(call => call.monitoring.initialized);
            const allActive = calls.every(call => call.monitoring.active);
            
            if (allInitialized && allActive) {
                this.addResult('Monitoring Persistence', true, 'Monitoring remains active across multiple checks');
            } else {
                this.addResult('Monitoring Persistence', false, 'Monitoring status is inconsistent');
            }

        } catch (error) {
            this.addResult('Monitoring Endpoint Functionality', false, error.message);
        }

        console.log('');
    }

    async testTradeExecution() {
        console.log('⚡ Test 4: Trade Execution with Monitoring');
        console.log('------------------------------------------');

        try {
            // Send a test trade signal
            const testSignal = {
                symbol: "TRUUSDT",
                trade: "buy",
                last_price: "0.03295",
                leverage: "2"
            };

            console.log('📤 Sending test trade signal...');
            const tradeResponse = await axios.post(`${this.webhookUrl}/webhook`, testSignal, {
                timeout: 30000,
                headers: { 'Content-Type': 'application/json' }
            });

            if (tradeResponse.data.success) {
                console.log('✅ Trade executed successfully');
                
                const tradeRecord = tradeResponse.data.tradeRecord;
                if (tradeRecord) {
                    console.log(`⚡ Execution Time: ${tradeRecord.executionTime}ms`);
                    console.log(`🧹 Monitoring Active: ${tradeRecord.serviceResponse?.monitoringActive || 'Unknown'}`);
                    
                    if (tradeRecord.executionTime < 2000) {
                        this.addResult('Trade Execution Performance', true, `Sub-2s execution: ${tradeRecord.executionTime}ms`);
                    } else {
                        this.addResult('Trade Execution Performance', false, `Slow execution: ${tradeRecord.executionTime}ms`);
                    }
                }

                // Check monitoring status after trade
                await this.sleep(2000); // Wait for post-trade cleanup
                const postTradeStatus = await axios.get(`${this.webhookUrl}/api/monitoring`, { timeout: 5000 });
                
                if (postTradeStatus.data.monitoring.active) {
                    this.addResult('Post-Trade Monitoring', true, 'Monitoring remains active after trade execution');
                } else {
                    this.addResult('Post-Trade Monitoring', false, 'Monitoring stopped after trade execution');
                }

            } else {
                this.addResult('Trade Execution', false, tradeResponse.data.error || 'Trade failed');
            }

        } catch (error) {
            if (error.response) {
                this.addResult('Trade Execution', false, `HTTP ${error.response.status}: ${error.response.data?.error || error.response.statusText}`);
            } else {
                this.addResult('Trade Execution', false, error.message);
            }
        }

        console.log('');
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    addResult(name, passed, message) {
        this.testResults.push({ name, passed, message });
    }

    printResults() {
        console.log('📋 Test Results Summary');
        console.log('========================');

        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;

        this.testResults.forEach(result => {
            const icon = result.passed ? '✅' : '❌';
            console.log(`${icon} ${result.name}: ${result.message}`);
        });

        console.log(`\n📊 Overall: ${passed}/${total} tests passed`);

        if (passed === total) {
            console.log('\n🎉 Perfect! Background monitoring is working correctly!');
            console.log('🧹 Your trading panel should now be automatically maintained:');
            console.log('   - Quantity fields cleared after trades');
            console.log('   - Panel switched to Open tab after close trades');
            console.log('   - Popups automatically closed');
            console.log('   - Monitoring runs every 10 seconds');
        } else {
            console.log('\n⚠️ Some issues detected with background monitoring.');
            console.log('💡 Recommendations:');
            console.log('   - Ensure browser is running on port 9223');
            console.log('   - Check that MEXC is logged in');
            console.log('   - Restart the webhook listener service');
        }
    }
}

// Run tests
if (require.main === module) {
    const tester = new PersistentMonitoringTester();
    tester.runTests().catch(error => {
        console.error('❌ Test suite failed:', error);
        process.exit(1);
    });
}

module.exports = PersistentMonitoringTester;
