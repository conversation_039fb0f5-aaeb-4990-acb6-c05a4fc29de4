"""
Enhanced Integration Module
Integrates the proven browser automation with the main trading system
"""

import asyncio
from typing import Dict, Any, Optional
from datetime import datetime

from src.core.enhanced_trading_engine import EnhancedTradingEngine, TradeRequest, OrderType
from src.core.session_manager import SessionManager
from src.utils.telegram_bot import TelegramBot
from src.utils.logger import get_logger
from src.config import settings


class EnhancedTradingSystem:
    """
    Enhanced trading system that integrates the proven browser automation
    with the existing trading infrastructure
    """
    
    def __init__(self):
        """Initialize enhanced trading system"""
        self.logger = get_logger(__name__)
        
        # Core components
        self.session_manager: Optional[SessionManager] = None
        self.telegram_bot: Optional[TelegramBot] = None
        self.trading_engine: Optional[EnhancedTradingEngine] = None
        
        # System state
        self.is_initialized = False
        self.is_running = False
        
        self.logger.info("Enhanced trading system initialized")
    
    async def initialize(self) -> bool:
        """
        Initialize all system components
        
        Returns:
            True if initialization successful
        """
        try:
            self.logger.info("Initializing enhanced trading system components...")
            
            # Initialize session manager
            self.session_manager = SessionManager()
            if not await self.session_manager.initialize():
                self.logger.error("Failed to initialize session manager")
                return False
            
            # Initialize Telegram bot
            if settings.TELEGRAM_ENABLED and settings.TELEGRAM_BOT_TOKEN and settings.TELEGRAM_CHAT_ID:
                try:
                    self.telegram_bot = TelegramBot(settings.TELEGRAM_BOT_TOKEN, settings.TELEGRAM_CHAT_ID)
                    if not await self.telegram_bot.initialize():
                        self.logger.warning("Failed to initialize Telegram bot, continuing without notifications")
                        self.telegram_bot = None
                except Exception as telegram_error:
                    self.logger.warning(f"Telegram bot initialization failed: {telegram_error}")
                    self.telegram_bot = None
            else:
                self.logger.info("Telegram notifications disabled or not configured")
            
            # Initialize enhanced trading engine
            self.trading_engine = EnhancedTradingEngine(
                session_manager=self.session_manager,
                telegram_bot=self.telegram_bot
            )
            
            if not await self.trading_engine.initialize():
                self.logger.error("Failed to initialize enhanced trading engine")
                return False
            
            self.is_initialized = True
            self.logger.info("Enhanced trading system initialization complete")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Enhanced trading system initialization failed: {e}")
            return False
    
    async def process_webhook_signal(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process TradingView webhook signal using enhanced automation
        
        Args:
            webhook_data: Webhook data from TradingView
            
        Returns:
            Processing result
        """
        if not self.is_initialized:
            return {
                "success": False,
                "error": "Enhanced trading system not initialized"
            }
        
        try:
            self.logger.info(f"Processing webhook signal with enhanced automation: {webhook_data}")
            
            # Parse webhook data into trade request
            trade_request = self._parse_webhook_to_trade_request(webhook_data)
            if not trade_request:
                return {
                    "success": False,
                    "error": "Failed to parse webhook data"
                }
            
            # Execute trade using enhanced automation
            result = await self.trading_engine.execute_trade(trade_request)
            
            self.logger.info(f"Enhanced webhook processing result: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"Enhanced webhook processing failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _parse_webhook_to_trade_request(self, webhook_data: Dict[str, Any]) -> Optional[TradeRequest]:
        """
        Parse webhook data into TradeRequest
        
        Args:
            webhook_data: Raw webhook data
            
        Returns:
            TradeRequest or None if parsing fails
        """
        try:
            # Extract required fields
            action = webhook_data.get("action", "").lower()
            symbol = webhook_data.get("symbol", "").upper()
            side = webhook_data.get("side", "long").lower()
            quantity = float(webhook_data.get("quantity", 0))
            
            # Extract optional fields
            price = webhook_data.get("price")
            if price:
                price = float(price)
            
            leverage = int(webhook_data.get("leverage", 1))
            
            # Determine order type
            order_type_str = webhook_data.get("order_type", "limit").lower()
            if order_type_str == "market":
                order_type = OrderType.MARKET
            elif order_type_str == "post_only":
                order_type = OrderType.POST_ONLY
            else:
                order_type = OrderType.LIMIT
            
            # Extract risk management
            stop_loss = webhook_data.get("stop_loss")
            if stop_loss:
                stop_loss = float(stop_loss)
            
            take_profit = webhook_data.get("take_profit")
            if take_profit:
                take_profit = float(take_profit)
            
            # Create trade request
            trade_request = TradeRequest(
                action=action,
                symbol=symbol,
                side=side,
                quantity=quantity,
                price=price,
                leverage=leverage,
                order_type=order_type,
                stop_loss=stop_loss,
                take_profit=take_profit,
                webhook_id=webhook_data.get("webhook_id"),
                **{k: v for k, v in webhook_data.items() if k not in [
                    "action", "symbol", "side", "quantity", "price", "leverage",
                    "order_type", "stop_loss", "take_profit", "webhook_id"
                ]}
            )
            
            self.logger.info(f"Parsed webhook to trade request: {trade_request.to_dict()}")
            return trade_request
            
        except Exception as e:
            self.logger.error(f"Failed to parse webhook data: {e}")
            return None
    
    async def execute_manual_trade(
        self,
        action: str,
        symbol: str,
        side: str,
        quantity: float,
        price: Optional[float] = None,
        leverage: int = 1,
        order_type: str = "limit",
        stop_loss: Optional[float] = None,
        take_profit: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Execute manual trade using enhanced automation
        
        Args:
            action: Trade action (buy/sell/close)
            symbol: Trading symbol
            side: Position side (long/short)
            quantity: Order quantity
            price: Order price (for limit orders)
            leverage: Leverage multiplier
            order_type: Order type (limit/market/post_only)
            stop_loss: Stop loss price
            take_profit: Take profit price
            
        Returns:
            Execution result
        """
        if not self.is_initialized:
            return {
                "success": False,
                "error": "Enhanced trading system not initialized"
            }
        
        try:
            # Convert order type
            if order_type.lower() == "market":
                order_type_enum = OrderType.MARKET
            elif order_type.lower() == "post_only":
                order_type_enum = OrderType.POST_ONLY
            else:
                order_type_enum = OrderType.LIMIT
            
            # Create trade request
            trade_request = TradeRequest(
                action=action,
                symbol=symbol,
                side=side,
                quantity=quantity,
                price=price,
                leverage=leverage,
                order_type=order_type_enum,
                stop_loss=stop_loss,
                take_profit=take_profit
            )
            
            # Execute trade
            result = await self.trading_engine.execute_trade(trade_request)
            
            self.logger.info(f"Enhanced manual trade result: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"Enhanced manual trade failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get enhanced trading system status"""
        status = {
            "initialized": self.is_initialized,
            "running": self.is_running,
            "timestamp": datetime.now().isoformat(),
            "system_type": "enhanced_with_blur_prevention"
        }
        
        if self.session_manager:
            status["session_manager"] = self.session_manager.get_status()
        
        if self.trading_engine:
            status["trading_engine"] = self.trading_engine.get_performance_metrics()
        
        if self.telegram_bot:
            status["telegram_bot"] = {"enabled": True, "connected": True}
        else:
            status["telegram_bot"] = {"enabled": False, "connected": False}
        
        return status
    
    async def start(self):
        """Start the enhanced trading system"""
        if not self.is_initialized:
            raise RuntimeError("Enhanced trading system not initialized")
        
        self.is_running = True
        self.logger.info("Enhanced trading system started")
    
    async def stop(self):
        """Stop the enhanced trading system"""
        try:
            self.is_running = False
            
            if self.trading_engine:
                await self.trading_engine.shutdown()
            
            if self.session_manager:
                await self.session_manager.shutdown()
            
            if self.telegram_bot:
                await self.telegram_bot.shutdown()
            
            self.logger.info("Enhanced trading system stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping enhanced trading system: {e}")


# Global enhanced trading system instance
enhanced_trading_system = EnhancedTradingSystem()


async def initialize_enhanced_system() -> bool:
    """Initialize the global enhanced trading system"""
    return await enhanced_trading_system.initialize()


async def process_enhanced_webhook(webhook_data: Dict[str, Any]) -> Dict[str, Any]:
    """Process webhook using enhanced automation"""
    return await enhanced_trading_system.process_webhook_signal(webhook_data)


async def execute_enhanced_manual_trade(**kwargs) -> Dict[str, Any]:
    """Execute manual trade using enhanced automation"""
    return await enhanced_trading_system.execute_manual_trade(**kwargs)


def get_enhanced_system_status() -> Dict[str, Any]:
    """Get enhanced system status"""
    return enhanced_trading_system.get_system_status()


async def start_enhanced_system():
    """Start enhanced system"""
    await enhanced_trading_system.start()


async def stop_enhanced_system():
    """Stop enhanced system"""
    await enhanced_trading_system.stop()
