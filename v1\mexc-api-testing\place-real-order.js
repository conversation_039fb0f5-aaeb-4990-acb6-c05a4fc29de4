const ccxt = require('ccxt');
require('dotenv').config();

async function placeRealOrder() {
    console.log('🚨 PLACING REAL FUTURES ORDER ON MEXC');
    console.log('====================================');
    console.log(`API Key: ${process.env.MEXC_API_KEY?.substring(0, 10)}...`);
    console.log('Symbol: TRUUSDT');
    console.log('Balance: ~$2 USDT');
    console.log('');

    try {
        // Initialize exchange
        const exchange = new ccxt.mexc({
            apiKey: process.env.MEXC_API_KEY,
            secret: process.env.MEXC_API_SECRET,
            sandbox: false,
            enableRateLimit: true,
            verbose: true, // Enable verbose logging
        });

        console.log('✅ CCXT MEXC exchange initialized');

        // Load markets
        await exchange.loadMarkets();
        console.log('✅ Markets loaded');

        // Check if TRUUSDT futures exists
        const futuresSymbol = 'TRU/USDT:USDT';
        const spotSymbol = 'TRU/USDT';
        
        let targetSymbol = null;
        if (exchange.markets[futuresSymbol]) {
            targetSymbol = futuresSymbol;
            console.log(`✅ Found futures symbol: ${futuresSymbol}`);
        } else if (exchange.markets[spotSymbol]) {
            targetSymbol = spotSymbol;
            console.log(`✅ Found spot symbol: ${spotSymbol}`);
        } else {
            // Try to find TRU symbol in any format
            const truSymbols = Object.keys(exchange.markets).filter(s => 
                s.includes('TRU') && s.includes('USDT')
            );
            console.log('Available TRU symbols:', truSymbols);
            if (truSymbols.length > 0) {
                targetSymbol = truSymbols[0];
                console.log(`✅ Using symbol: ${targetSymbol}`);
            } else {
                throw new Error('TRU/USDT symbol not found');
            }
        }

        // Get current market data
        const ticker = await exchange.fetchTicker(targetSymbol);
        console.log(`📊 Current ${targetSymbol} price: ${ticker.last}`);
        console.log(`📊 24h volume: ${ticker.baseVolume}`);

        // Check balance
        const balanceType = targetSymbol.includes(':') ? 'swap' : 'spot';
        const balance = await exchange.fetchBalance({ type: balanceType });
        console.log(`💰 USDT balance: ${JSON.stringify(balance.USDT)}`);

        const availableBalance = balance.USDT?.free || 0;
        console.log(`💰 Available USDT: ${availableBalance}`);

        if (availableBalance < 1) {
            throw new Error(`Insufficient balance: ${availableBalance} USDT`);
        }

        // Calculate order size
        const currentPrice = ticker.last;
        const maxOrderValue = Math.min(availableBalance * 0.9, 1.5); // Use 90% of balance, max $1.5
        const orderQuantity = Math.floor((maxOrderValue / currentPrice) * 1000) / 1000; // Round to 3 decimals

        console.log(`🎯 Order calculation:`);
        console.log(`   Current price: ${currentPrice}`);
        console.log(`   Max order value: $${maxOrderValue}`);
        console.log(`   Order quantity: ${orderQuantity}`);

        if (orderQuantity <= 0) {
            throw new Error('Calculated order quantity is too small');
        }

        // Try different order types
        const orderTypes = [
            {
                type: 'market',
                side: 'buy',
                description: 'Market buy order'
            },
            {
                type: 'limit',
                side: 'buy',
                price: currentPrice * 0.99, // 1% below current price
                description: 'Limit buy order (1% below market)'
            }
        ];

        let orderPlaced = false;
        let placedOrder = null;

        for (const orderConfig of orderTypes) {
            if (orderPlaced) break;

            try {
                console.log(`\n🎯 Attempting ${orderConfig.description}...`);
                console.log(`   Symbol: ${targetSymbol}`);
                console.log(`   Type: ${orderConfig.type}`);
                console.log(`   Side: ${orderConfig.side}`);
                console.log(`   Amount: ${orderQuantity}`);
                if (orderConfig.price) {
                    console.log(`   Price: ${orderConfig.price}`);
                }

                // Place the order
                const order = await exchange.createOrder(
                    targetSymbol,
                    orderConfig.type,
                    orderConfig.side,
                    orderQuantity,
                    orderConfig.price
                );

                console.log('🎉 SUCCESS! Order placed successfully!');
                console.log('📋 Order details:', JSON.stringify(order, null, 2));

                placedOrder = order;
                orderPlaced = true;

                // Try to fetch the order status
                try {
                    const orderStatus = await exchange.fetchOrder(order.id, targetSymbol);
                    console.log('📊 Order status:', JSON.stringify(orderStatus, null, 2));
                } catch (statusError) {
                    console.log('⚠️ Could not fetch order status:', statusError.message);
                }

            } catch (orderError) {
                console.log(`❌ ${orderConfig.description} failed:`, orderError.message);
                
                // If it's a specific error, try to understand it
                if (orderError.message.includes('insufficient')) {
                    console.log('💡 Trying with smaller quantity...');
                    try {
                        const smallerQuantity = orderQuantity * 0.5;
                        const smallOrder = await exchange.createOrder(
                            targetSymbol,
                            orderConfig.type,
                            orderConfig.side,
                            smallerQuantity,
                            orderConfig.price
                        );
                        
                        console.log('🎉 SUCCESS with smaller quantity!');
                        console.log('📋 Order details:', JSON.stringify(smallOrder, null, 2));
                        placedOrder = smallOrder;
                        orderPlaced = true;
                    } catch (smallOrderError) {
                        console.log('❌ Smaller order also failed:', smallOrderError.message);
                    }
                }
            }
        }

        if (orderPlaced) {
            console.log('\n🎉 MISSION ACCOMPLISHED!');
            console.log('✅ Successfully placed a real futures order on MEXC');
            console.log('✅ CCXT library works for MEXC futures trading');
            console.log('');
            console.log('📊 Final Summary:');
            console.log(`   Symbol: ${targetSymbol}`);
            console.log(`   Order ID: ${placedOrder.id}`);
            console.log(`   Status: ${placedOrder.status}`);
            console.log(`   Amount: ${placedOrder.amount}`);
            console.log(`   Price: ${placedOrder.price || 'Market'}`);
            
            return {
                success: true,
                order: placedOrder,
                symbol: targetSymbol,
                message: 'Order placed successfully using CCXT'
            };
        } else {
            throw new Error('All order placement attempts failed');
        }

    } catch (error) {
        console.error('❌ Order placement failed:', error.message);
        console.error('Stack trace:', error.stack);
        
        return {
            success: false,
            error: error.message,
            message: 'Order placement failed - will test other solutions'
        };
    }
}

if (require.main === module) {
    console.log('⚠️  This will place a REAL order with real money!');
    console.log('⚠️  Starting in 5 seconds... Press Ctrl+C to cancel');
    
    setTimeout(() => {
        placeRealOrder()
            .then(result => {
                console.log('\n📄 Final Result:', JSON.stringify(result, null, 2));
                
                // Save result
                require('fs').writeFileSync(
                    'real-order-result.json',
                    JSON.stringify(result, null, 2)
                );
                
                process.exit(result.success ? 0 : 1);
            })
            .catch(error => {
                console.error('Script crashed:', error);
                process.exit(1);
            });
    }, 5000);
}

module.exports = placeRealOrder;
