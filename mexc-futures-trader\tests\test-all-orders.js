const MexcFuturesTrader = require('../src/trader');

async function testAllOrders() {
    console.log('🧪 TESTING ALL ORDER TYPES');
    console.log('===========================');
    console.log('Testing optimized MEXC futures trader');
    console.log('');

    const testScenarios = [
        {
            name: 'Open Long Order',
            orderType: 'Open Long',
            port: 9222,
            quantity: '0.3600',
            description: 'Should create new long position'
        },
        {
            name: 'Open Short Order',
            orderType: 'Open Short',
            port: 9222,
            quantity: '0.3600',
            description: 'Should create new short position'
        },
        {
            name: 'Close Long Order',
            orderType: 'Close Long', 
            port: 9223,
            quantity: '0.3600',
            description: 'Should close existing long position'
        },
        {
            name: 'Close Short Order',
            orderType: 'Close Short',
            port: 9223,
            quantity: '0.3600',
            description: 'Should close existing short position'
        }
    ];

    const results = [];

    for (const scenario of testScenarios) {
        console.log(`\n🧪 TEST: ${scenario.name}`);
        console.log(`📝 ${scenario.description}`);
        console.log(`🌐 Port: ${scenario.port} | 💰 Quantity: ${scenario.quantity}`);
        console.log('─'.repeat(50));

        const trader = new MexcFuturesTrader(scenario.port);
        const startTime = Date.now();

        try {
            const connected = await trader.connectToBrowser();
            if (!connected) {
                throw new Error(`Failed to connect to port ${scenario.port}`);
            }

            const result = await trader.executeOrder(scenario.orderType, scenario.quantity);
            const testTime = Date.now() - startTime;

            console.log(`\n📊 RESULT: ${result.success ? '✅ SUCCESS' : '❌ FAILED'}`);
            console.log(`⏱️  Execution Time: ${result.executionTime}ms`);
            console.log(`🎯 Target (<2s): ${result.targetAchieved ? '✅ YES' : '❌ NO'}`);
            console.log(`🔄 Total Test Time: ${testTime}ms`);
            
            if (result.error) {
                console.log(`❌ Error: ${result.error}`);
            }

            if (result.emergencyRecovery) {
                console.log('🚨 Used emergency recovery');
            }

            results.push({
                scenario: scenario.name,
                orderType: scenario.orderType,
                port: scenario.port,
                quantity: scenario.quantity,
                success: result.success,
                executionTime: result.executionTime,
                targetAchieved: result.targetAchieved,
                error: result.error || null,
                testTime,
                emergencyRecovery: result.emergencyRecovery || false,
                timestamp: result.timestamp
            });

        } catch (error) {
            const testTime = Date.now() - startTime;
            console.log(`\n❌ TEST FAILED: ${error.message}`);
            
            results.push({
                scenario: scenario.name,
                orderType: scenario.orderType,
                port: scenario.port,
                quantity: scenario.quantity,
                success: false,
                executionTime: null,
                targetAchieved: false,
                error: error.message,
                testTime,
                timestamp: new Date().toISOString()
            });
        }

        // Wait between tests
        console.log('⏳ Waiting 2 seconds before next test...');
        await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // Generate comprehensive report
    console.log('\n🏁 COMPREHENSIVE TEST REPORT');
    console.log('=============================');
    
    const successful = results.filter(r => r.success).length;
    const total = results.length;
    const avgExecutionTime = results
        .filter(r => r.executionTime)
        .reduce((sum, r) => sum + r.executionTime, 0) / results.filter(r => r.executionTime).length;
    const targetAchieved = results.filter(r => r.targetAchieved).length;
    
    console.log(`📊 Success Rate: ${successful}/${total} (${Math.round(successful/total*100)}%)`);
    console.log(`⚡ Average Execution Time: ${Math.round(avgExecutionTime)}ms`);
    console.log(`🎯 Target Achieved: ${targetAchieved}/${total} (${Math.round(targetAchieved/total*100)}%)`);
    console.log('');

    // Individual results
    results.forEach((result, index) => {
        const status = result.success ? '✅' : '❌';
        const time = result.executionTime ? `${result.executionTime}ms` : 'N/A';
        const target = result.targetAchieved ? '🎯' : '⏰';
        const recovery = result.emergencyRecovery ? '🚨' : '';
        
        console.log(`${status} ${result.scenario}: ${time} ${target} ${recovery}`);
        if (result.error) {
            console.log(`   └─ ${result.error}`);
        }
    });

    // Performance analysis
    console.log('\n📈 PERFORMANCE ANALYSIS');
    console.log('========================');
    
    const openResults = results.filter(r => r.orderType.includes('Open'));
    const closeResults = results.filter(r => r.orderType.includes('Close'));
    
    if (openResults.length > 0) {
        const openAvg = openResults
            .filter(r => r.executionTime)
            .reduce((sum, r) => sum + r.executionTime, 0) / openResults.filter(r => r.executionTime).length;
        console.log(`📈 Open Orders Average: ${Math.round(openAvg)}ms`);
    }
    
    if (closeResults.length > 0) {
        const closeAvg = closeResults
            .filter(r => r.executionTime)
            .reduce((sum, r) => sum + r.executionTime, 0) / closeResults.filter(r => r.executionTime).length;
        console.log(`📉 Close Orders Average: ${Math.round(closeAvg)}ms`);
    }

    // Save detailed results
    const detailedResults = {
        timestamp: new Date().toISOString(),
        summary: {
            total,
            successful,
            successRate: Math.round(successful/total*100),
            averageExecutionTime: Math.round(avgExecutionTime),
            targetAchievedCount: targetAchieved,
            targetAchievedRate: Math.round(targetAchieved/total*100)
        },
        results
    };

    const fs = require('fs');
    const filename = `test-results-${Date.now()}.json`;
    fs.writeFileSync(filename, JSON.stringify(detailedResults, null, 2));
    console.log(`\n💾 Detailed results saved to: ${filename}`);

    // Recommendations
    console.log('\n💡 RECOMMENDATIONS');
    console.log('==================');
    
    if (successful === total) {
        console.log('✅ All tests passed! System is working correctly.');
    } else {
        console.log('⚠️ Some tests failed. Check error messages above.');
    }
    
    if (avgExecutionTime < 2000) {
        console.log('🎯 Average execution time meets target (<2 seconds)');
    } else {
        console.log('⏰ Average execution time above target. Consider optimization.');
    }

    console.log('\n🔧 NEXT STEPS:');
    console.log('1. Ensure browsers are running on correct ports (9222, 9223)');
    console.log('2. Check browser data directories are accessible');
    console.log('3. Verify MEXC page is loaded and logged in');
    console.log('4. Monitor execution times for performance optimization');

    return detailedResults;
}

if (require.main === module) {
    testAllOrders().catch(console.error);
}

module.exports = testAllOrders;
