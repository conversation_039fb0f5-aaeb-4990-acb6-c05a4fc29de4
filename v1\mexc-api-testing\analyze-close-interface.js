const { chromium } = require('playwright');

async function analyzeCloseInterface() {
    console.log('🔍 ANALYZING CLOSE TRADING INTERFACE');
    console.log('====================================');
    
    try {
        const browser = await chromium.connectOverCDP('http://localhost:9223');
        const contexts = browser.contexts();
        
        if (contexts.length > 0) {
            const pages = contexts[0].pages();
            const page = pages.length > 0 ? pages[0] : await contexts[0].newPage();
            
            // Navigate and set close mode
            const url = page.url();
            if (!url.includes('mexc.com/futures/TRU_USDT')) {
                await page.goto('https://www.mexc.com/futures/TRU_USDT');
                await page.waitForTimeout(3000);
            }
            
            // Set Close mode
            try {
                const closeBtn = page.locator('button:has-text("Close")').first();
                if (await closeBtn.isVisible({ timeout: 1000 })) {
                    await closeBtn.click();
                    await page.waitForTimeout(1000);
                    console.log('✅ Close mode activated');
                }
            } catch (error) {
                console.log('❌ Error setting Close mode');
            }
            
            // Look for all text content that might indicate quantity fields
            console.log('\n📋 SEARCHING FOR QUANTITY-RELATED TEXT:');
            const quantityTexts = [
                'Quantity',
                'Amount',
                'Size',
                'Volume',
                'USDT',
                'Close',
                'Position',
                'Qty'
            ];
            
            for (const text of quantityTexts) {
                try {
                    const elements = await page.locator(`text=${text}`).all();
                    if (elements.length > 0) {
                        console.log(`✅ Found "${text}" (${elements.length} instances)`);
                        
                        // Check what follows each instance
                        for (let i = 0; i < Math.min(elements.length, 3); i++) {
                            try {
                                const element = elements[i];
                                const parent = element.locator('xpath=..');
                                const parentText = await parent.textContent();
                                console.log(`   ${i + 1}. Context: "${parentText?.substring(0, 100)}..."`);
                                
                                // Look for inputs near this text
                                const nearbyInputs = await parent.locator('input').all();
                                for (const input of nearbyInputs) {
                                    if (await input.isVisible({ timeout: 100 })) {
                                        const type = await input.getAttribute('type');
                                        const placeholder = await input.getAttribute('placeholder');
                                        console.log(`      → Input: type="${type}", placeholder="${placeholder}"`);
                                    }
                                }
                            } catch (error) {
                                continue;
                            }
                        }
                    }
                } catch (error) {
                    continue;
                }
            }
            
            // Look for the trading panel structure
            console.log('\n🎯 ANALYZING TRADING PANEL STRUCTURE:');
            try {
                // Look for common trading panel classes
                const panelSelectors = [
                    '.trading-panel',
                    '.order-panel',
                    '.close-panel',
                    '[class*="trade"]',
                    '[class*="order"]',
                    '[class*="close"]'
                ];
                
                for (const selector of panelSelectors) {
                    try {
                        const panels = await page.locator(selector).all();
                        if (panels.length > 0) {
                            console.log(`✅ Found panels with selector: ${selector} (${panels.length})`);
                            
                            for (let i = 0; i < Math.min(panels.length, 2); i++) {
                                const panel = panels[i];
                                const inputs = await panel.locator('input').all();
                                console.log(`   Panel ${i + 1}: ${inputs.length} inputs`);
                                
                                for (const input of inputs) {
                                    if (await input.isVisible({ timeout: 100 })) {
                                        const type = await input.getAttribute('type');
                                        const placeholder = await input.getAttribute('placeholder');
                                        const value = await input.inputValue();
                                        console.log(`      → Input: type="${type}", placeholder="${placeholder}", value="${value}"`);
                                    }
                                }
                            }
                        }
                    } catch (error) {
                        continue;
                    }
                }
            } catch (error) {
                console.log('❌ Panel analysis failed');
            }
            
            // Check if close orders work differently (maybe percentage-based or position-based)
            console.log('\n🔍 CHECKING FOR ALTERNATIVE CLOSE METHODS:');
            
            // Look for percentage buttons or sliders
            const percentageSelectors = [
                'button:has-text("25%")',
                'button:has-text("50%")',
                'button:has-text("75%")',
                'button:has-text("100%")',
                '.percentage',
                '.slider',
                '[class*="percent"]'
            ];
            
            for (const selector of percentageSelectors) {
                try {
                    const elements = await page.locator(selector).all();
                    if (elements.length > 0) {
                        console.log(`✅ Found percentage controls: ${selector} (${elements.length})`);
                    }
                } catch (error) {
                    continue;
                }
            }
            
            // Look for position information
            console.log('\n📊 CHECKING FOR POSITION INFORMATION:');
            const positionSelectors = [
                'text=Position',
                'text=Size',
                'text=PnL',
                '.position',
                '[class*="position"]'
            ];
            
            for (const selector of positionSelectors) {
                try {
                    const elements = await page.locator(selector).all();
                    if (elements.length > 0) {
                        console.log(`✅ Found position info: ${selector} (${elements.length})`);
                        
                        // Get the text content
                        for (let i = 0; i < Math.min(elements.length, 2); i++) {
                            try {
                                const text = await elements[i].textContent();
                                console.log(`   ${i + 1}. "${text}"`);
                            } catch (error) {
                                continue;
                            }
                        }
                    }
                } catch (error) {
                    continue;
                }
            }
            
            // Take a screenshot for manual inspection
            console.log('\n📸 Taking screenshot for manual inspection...');
            try {
                await page.screenshot({ path: 'close-interface-screenshot.png', fullPage: true });
                console.log('✅ Screenshot saved as close-interface-screenshot.png');
            } catch (error) {
                console.log('❌ Screenshot failed');
            }
            
        }
    } catch (error) {
        console.error('❌ Analysis failed:', error.message);
    }
    
    console.log('\n🏁 Analysis completed');
    console.log('\n💡 HYPOTHESIS:');
    console.log('The close interface might work differently than open interface:');
    console.log('1. May not require quantity input (closes entire position)');
    console.log('2. May use percentage-based closing');
    console.log('3. May have different field structure');
    console.log('4. Check the screenshot for visual confirmation');
}

if (require.main === module) {
    analyzeCloseInterface().catch(console.error);
}

module.exports = analyzeCloseInterface;
