const FinalUltimateTrader = require('./final-ultimate-trader.js');

async function testQuantityClearing() {
    console.log('🧪 TESTING QUANTITY FIELD CLEARING');
    console.log('==================================');
    
    // Test both ports
    const tests = [
        { orderType: 'Open Long', port: 9222 },
        { orderType: 'Close Long', port: 9223 }
    ];
    
    for (const test of tests) {
        console.log(`\n🔍 Testing ${test.orderType} on port ${test.port}...`);
        
        const trader = new FinalUltimateTrader(test.port);
        
        try {
            const connected = await trader.connectToBrowser();
            if (!connected) {
                console.log(`❌ Failed to connect to port ${test.port}`);
                continue;
            }
            
            // Navigate to the page
            const url = trader.page.url();
            if (!url.includes('mexc.com/futures/TRU_USDT')) {
                console.log('🌐 Navigating to TRU_USDT...');
                await trader.page.goto('https://www.mexc.com/futures/TRU_USDT');
                await trader.page.waitForTimeout(2000);
            }
            
            // Set the correct mode
            if (test.orderType.includes('Close')) {
                await trader.setCloseMode();
            } else {
                await trader.setOpenMode();
            }
            
            // First, let's put some value in the quantity field to simulate existing value
            console.log('📝 Setting initial value in quantity field...');
            const input = trader.page.locator('text=Quantity(USDT) >> xpath=following::input[1]').first();
            if (await input.isVisible({ timeout: 1000 })) {
                await input.click();
                await input.fill('1.2345'); // Put some existing value
                console.log('✅ Initial value set: 1.2345');
                
                // Wait a moment
                await trader.page.waitForTimeout(500);
                
                // Now test our clearing and filling logic
                console.log('🧹 Testing quantity clearing and filling...');
                await trader.fillQuantity();
                
                // Verify the final value
                const finalValue = await input.inputValue();
                console.log(`📊 Final value: "${finalValue}"`);
                
                if (finalValue === '0.3600') {
                    console.log('✅ SUCCESS: Quantity field properly cleared and filled!');
                } else {
                    console.log('❌ FAILED: Quantity field not properly set');
                }
            } else {
                console.log('❌ Could not find quantity field');
            }
            
        } catch (error) {
            console.error(`❌ Test failed for ${test.orderType}:`, error.message);
        }
    }
    
    console.log('\n🏁 Testing completed');
}

if (require.main === module) {
    testQuantityClearing().catch(console.error);
}

module.exports = testQuantityClearing;
