#!/usr/bin/env node

/**
 * Test the Close Long fix
 */

const { chromium } = require('playwright');

async function testCloseFix() {
    console.log('🧪 Testing Close Long Fix...\n');
    
    try {
        const browser = await chromium.connectOverCDP('http://localhost:9223');
        const contexts = browser.contexts();
        const pages = contexts[0].pages();
        const page = pages[0];
        
        console.log('✅ Connected to browser');
        
        // Switch to Close panel
        console.log('🔄 Switching to Close panel...');
        const closeTab = page.locator('span[data-testid="contract-trade-order-form-tab-close"]').first();
        await closeTab.click();
        await page.waitForTimeout(1000);
        console.log('✅ Switched to Close panel');
        
        // Test the fixed selector
        console.log('\n🧪 Testing fixed selector: .ant-input (index 2)');
        try {
            const input = page.locator('.ant-input').nth(2); // 3rd element (index 2)
            const isVisible = await input.isVisible({ timeout: 1000 });
            
            if (isVisible) {
                console.log('✅ Element is visible');
                
                await input.click({ timeout: 2000 });
                console.log('✅ Click successful');
                
                await input.fill('1.1279');
                const value = await input.inputValue();
                console.log(`✅ Fill result: "${value}"`);
                
                if (value === '1.1279') {
                    console.log('🎉 SUCCESS! Close quantity field fix is working!');
                    
                    // Clear it
                    await input.fill('');
                    console.log('🧹 Cleared field');
                    
                    return { success: true, message: 'Close quantity field fix verified' };
                } else {
                    console.log('❌ FAILED: Field not populated correctly');
                    return { success: false, message: 'Field not populated correctly' };
                }
            } else {
                console.log('❌ Element not visible');
                return { success: false, message: 'Element not visible' };
            }
        } catch (error) {
            console.log(`❌ Test failed: ${error.message}`);
            return { success: false, message: error.message };
        }
        
    } catch (error) {
        console.error('❌ Test setup failed:', error.message);
        return { success: false, message: error.message };
    }
}

// Run test if called directly
if (require.main === module) {
    testCloseFix().then(result => {
        console.log('\n📊 Test Result:');
        console.log('================');
        console.log(`Status: ${result.success ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`Message: ${result.message}`);
        
        if (result.success) {
            console.log('\n🎯 The Close Long fix should now work!');
            console.log('Your SL/TP system should be able to execute close trades properly.');
        } else {
            console.log('\n⚠️ The fix needs more work. Please check the browser state.');
        }
        
        process.exit(result.success ? 0 : 1);
    }).catch(error => {
        console.error('❌ Test execution failed:', error);
        process.exit(1);
    });
}

module.exports = testCloseFix;
