const { chromium } = require('playwright');

class FinalUltimateTrader {
    constructor(port = 9222) {
        this.browser = null;
        this.page = null;
        this.port = port;
    }

    async connectToBrowser() {
        console.log(`🔗 Connecting to browser on port ${this.port}...`);
        
        try {
            this.browser = await chromium.connectOverCDP(`http://localhost:${this.port}`);
            const contexts = this.browser.contexts();
            
            if (contexts.length > 0) {
                const pages = contexts[0].pages();
                this.page = pages.length > 0 ? pages[0] : await contexts[0].newPage();
            } else {
                const context = await this.browser.newContext();
                this.page = await context.newPage();
            }
            
            console.log(`✅ Connected to browser on port ${this.port}`);
            return true;
        } catch (error) {
            console.error(`❌ Connection failed to port ${this.port}:`, error.message);
            return false;
        }
    }

    async aggressiveCleanup() {
        console.log('🧹 Aggressive cleanup...');

        try {
            // Close persistent popups from previous runs first
            await this.closePersistentPopups();

            // Close any other popups
            const popupSelectors = [
                'button:has-text("Confirm")',
                'button:has-text("Cancel")',
                'button:has-text("Close")',
                '.modal-close',
                '.ant-modal-close',
                '[aria-label="Close"]',
                'button:has-text("OK")',
                'button:has-text("Got it")'
            ];

            for (const selector of popupSelectors) {
                try {
                    const elements = await this.page.locator(selector).all();
                    for (const element of elements) {
                        if (await element.isVisible({ timeout: 50 })) {
                            await element.click({ timeout: 100 });
                            await this.page.waitForTimeout(100);
                        }
                    }
                } catch (error) {
                    // Continue
                }
            }

            // Clear quantity fields with proper value removal
            await this.clearQuantityFields();

            console.log('✅ Cleanup completed');
        } catch (error) {
            console.log('⚠️ Cleanup had issues, continuing...');
        }
    }

    async closePersistentPopups() {
        console.log('🚨 Closing persistent popups...');

        try {
            // First, handle modal wraps that intercept clicks
            const modalWrapSelectors = [
                '.ant-modal-wrap.ant-modal-wrap-footer-custom',
                '.ant-modal-wrap',
                '.modal-wrap'
            ];

            for (const selector of modalWrapSelectors) {
                try {
                    const modalWrap = this.page.locator(selector).first();
                    if (await modalWrap.isVisible({ timeout: 200 })) {
                        console.log(`🎯 Found modal wrap: ${selector}`);

                        // Try to find and click close button within this modal
                        const closeSelectors = [
                            'button:has-text("Close")',
                            'button:has-text("Cancel")',
                            'button:has-text("×")',
                            '.ant-modal-close',
                            '.close-btn',
                            'svg[data-icon="CloseOutlined"]'
                        ];

                        let closed = false;
                        for (const closeSelector of closeSelectors) {
                            try {
                                const closeBtn = modalWrap.locator(closeSelector).first();
                                if (await closeBtn.isVisible({ timeout: 100 })) {
                                    await closeBtn.click();
                                    console.log(`✅ Closed modal via: ${closeSelector}`);
                                    await this.page.waitForTimeout(500);
                                    closed = true;
                                    break;
                                }
                            } catch (error) {
                                continue;
                            }
                        }

                        // If no close button found, try clicking outside the modal
                        if (!closed) {
                            try {
                                const modalContent = modalWrap.locator('.ant-modal-content, .modal-content').first();
                                if (await modalContent.isVisible({ timeout: 100 })) {
                                    // Click outside the modal content
                                    await modalWrap.click({ position: { x: 10, y: 10 } });
                                    console.log('✅ Closed modal by clicking outside');
                                    await this.page.waitForTimeout(300);
                                }
                            } catch (error) {
                                // Continue
                            }
                        }
                    }
                } catch (error) {
                    continue;
                }
            }

            // Handle the specific close icon you mentioned
            const closeIconSelectors = [
                'body > div:nth-child(71) > div > div.ant-modal-wrap.ant-modal-wrap-footer-custom > div > div.ant-modal-content > button > span > svg',
                '.ant-modal-close-icon',
                'svg[data-icon="CloseOutlined"]',
                '.mx-icon.ant-modal-close-icon'
            ];

            for (const selector of closeIconSelectors) {
                try {
                    const closeIcon = this.page.locator(selector).first();
                    if (await closeIcon.isVisible({ timeout: 200 })) {
                        // Click the parent button instead of the SVG
                        const closeButton = closeIcon.locator('xpath=ancestor::button[1]');
                        if (await closeButton.isVisible({ timeout: 100 })) {
                            await closeButton.click();
                            console.log('✅ Closed persistent popup via close button');
                            await this.page.waitForTimeout(300);
                        } else {
                            await closeIcon.click();
                            console.log('✅ Closed persistent popup via SVG click');
                            await this.page.waitForTimeout(300);
                        }
                    }
                } catch (error) {
                    // Continue trying other selectors
                }
            }

            // Handle modal masks/overlays
            const maskSelectors = [
                '.ant-modal-mask',
                '.modal-overlay',
                '.overlay'
            ];

            for (const selector of maskSelectors) {
                try {
                    const mask = this.page.locator(selector).first();
                    if (await mask.isVisible({ timeout: 100 })) {
                        // Click on the mask to close modal
                        await mask.click();
                        console.log(`✅ Closed modal via mask: ${selector}`);
                        await this.page.waitForTimeout(200);
                    }
                } catch (error) {
                    // Continue
                }
            }

            // Force close any remaining modals with ESC key
            try {
                await this.page.keyboard.press('Escape');
                await this.page.waitForTimeout(200);
                console.log('✅ Pressed ESC to close any remaining modals');
            } catch (error) {
                // Continue
            }

        } catch (error) {
            console.log('⚠️ Error closing persistent popups:', error.message);
        }
    }

    async clearQuantityFields() {
        console.log('🔢 Clearing quantity fields...');

        const clearStrategies = [
            // Strategy 1: Clear the specific quantity field following "Quantity(USDT)"
            async () => {
                const input = this.page.locator('text=Quantity(USDT) >> xpath=following::input[1]').first();
                if (await input.isVisible({ timeout: 300 })) {
                    await this.clearInputField(input);
                    return true;
                }
                return false;
            },

            // Strategy 2: Clear field following "Quantity"
            async () => {
                const input = this.page.locator('text=Quantity >> xpath=following::input[1]').first();
                if (await input.isVisible({ timeout: 300 })) {
                    const type = await input.getAttribute('type');
                    if (type !== 'checkbox') {
                        await this.clearInputField(input);
                        return true;
                    }
                }
                return false;
            },

            // Strategy 3: Clear all text/number inputs
            async () => {
                const selectors = ['input[type="text"]', 'input[type="number"]'];
                for (const selector of selectors) {
                    const inputs = await this.page.locator(selector).all();
                    for (const input of inputs) {
                        if (await input.isVisible({ timeout: 100 })) {
                            try {
                                const currentValue = await input.inputValue();
                                if (currentValue && currentValue.trim() !== '') {
                                    await this.clearInputField(input);
                                }
                            } catch (error) {
                                continue;
                            }
                        }
                    }
                }
                return true;
            }
        ];

        for (let i = 0; i < clearStrategies.length; i++) {
            try {
                if (await clearStrategies[i]()) {
                    console.log(`✅ Fields cleared using strategy ${i + 1}`);
                    break;
                }
            } catch (error) {
                continue;
            }
        }
    }

    async clearInputField(input) {
        try {
            // Multiple clearing methods to ensure field is empty
            await input.click();
            await this.page.waitForTimeout(50);

            // Select all and delete
            await input.press('Control+a');
            await this.page.waitForTimeout(50);
            await input.press('Delete');
            await this.page.waitForTimeout(50);

            // Fill with empty string
            await input.fill('');
            await this.page.waitForTimeout(50);

            // Verify it's cleared
            const value = await input.inputValue();
            if (value && value.trim() !== '') {
                // Force clear with backspace
                await input.click();
                for (let i = 0; i < 20; i++) {
                    await input.press('Backspace');
                }
                await input.fill('');
            }

            console.log('✅ Input field cleared');
        } catch (error) {
            console.log('⚠️ Error clearing input field:', error.message);
        }
    }

    async executeOrder(orderType) {
        const startTime = Date.now();
        console.log(`🎯 EXECUTING ${orderType.toUpperCase()} ORDER...`);

        try {
            // Skip page check - assume we're already on the right page

            // Skip mode setting - assume correct mode is already set

            // Ultra-fast quantity fill
            await this.ultraFastFillQuantity(orderType);

            // Ultra-fast button click
            await this.ultraFastClickButton(orderType);

            // Skip popup handling - most orders don't need it

            // Skip verification - assume success if no errors
            const executionTime = Date.now() - startTime;

            console.log(`⚡ ${orderType} completed in ${executionTime}ms`);
            console.log(`� Target achieved: ${executionTime < 2000 ? '✅ YES' : '❌ NO'}`);

            return {
                success: true,
                executionTime,
                verified: true,
                orderType,
                targetAchieved: executionTime < 2000
            };

        } catch (error) {
            const executionTime = Date.now() - startTime;
            console.error(`❌ ${orderType} failed: ${error.message}`);

            // Quick recovery attempt
            console.log('🚨 Quick recovery...');
            try {
                // Try once more with minimal cleanup
                await this.clearQuantityFields();
                await this.ultraFastFillQuantity(orderType);
                await this.ultraFastClickButton(orderType);

                const recoveryTime = Date.now() - startTime;
                console.log(`✅ Recovery successful in ${recoveryTime}ms`);

                return {
                    success: true,
                    executionTime: recoveryTime,
                    verified: true,
                    orderType,
                    targetAchieved: recoveryTime < 2000,
                    recovery: true
                };
            } catch (recoveryError) {
                return {
                    success: false,
                    executionTime,
                    error: error.message,
                    orderType
                };
            }
        }
    }

    async ultraFastFillQuantity(orderType) {
        console.log('⚡ Ultra-fast quantity fill...');

        if (orderType.includes('Close')) {
            // Close mode - direct force click
            const input = this.page.locator('input.ant-input[type="text"]').first();
            await input.click({ force: true, timeout: 500 });
            await input.fill('0.3600');
            return;
        } else {
            // Open mode - XPath method
            const input = this.page.locator('text=Quantity(USDT) >> xpath=following::input[1]').first();
            await input.click({ timeout: 500 });
            await input.fill('0.3600');
            return;
        }
    }

    async ultraFastClickButton(orderType) {
        console.log(`⚡ Ultra-fast ${orderType} click...`);

        const buttonSelectors = {
            'Open Long': 'button:has-text("Open Long")',
            'Open Short': 'button:has-text("Open Short")',
            'Close Long': 'button:has-text("Close Long")',
            'Close Short': 'button:has-text("Close Short")'
        };

        const selector = buttonSelectors[orderType];
        if (!selector) {
            throw new Error(`Unknown order type: ${orderType}`);
        }

        const button = this.page.locator(selector).first();
        await button.click({ timeout: 1000 });
        console.log(`✅ ${orderType} clicked`);
    }

    async fillQuantityWithErrorHandling(orderType) {
        console.log('🔢 Filling quantity...');

        try {
            // First attempt - no cleanup
            await this.fillQuantity();
            return;
        } catch (error) {
            console.log('⚠️ First quantity attempt failed, clearing quantity fields...');

            try {
                // First error: Clean quantity only
                await this.clearQuantityFields();
                await this.fillQuantity();
                return;
            } catch (secondError) {
                console.log('⚠️ Second quantity attempt failed, closing popups...');

                // Second error: Close popups then try again
                await this.closePersistentPopups();
                await this.page.waitForTimeout(500);
                await this.fillQuantity();
            }
        }
    }

    async clickOrderButtonWithErrorHandling(orderType) {
        console.log(`📊 Clicking ${orderType} button...`);

        try {
            // First attempt - no cleanup
            await this.clickOrderButton(orderType);
            return;
        } catch (error) {
            console.log('⚠️ First button click failed, clearing quantity fields...');

            try {
                // First error: Clean quantity only (might be quantity issue)
                await this.clearQuantityFields();
                await this.fillQuantity();
                await this.clickOrderButton(orderType);
                return;
            } catch (secondError) {
                console.log('⚠️ Second button click failed, closing popups...');

                // Second error: Close popups then try again
                await this.closePersistentPopups();
                await this.page.waitForTimeout(500);
                await this.clickOrderButton(orderType);
            }
        }
    }

    async setOpenMode() {
        console.log('🔄 Setting Open mode...');
        try {
            const openBtn = this.page.locator('button:has-text("Open")').first();
            if (await openBtn.isVisible({ timeout: 1000 })) {
                await openBtn.click();
                await this.page.waitForTimeout(300);
            }
        } catch (error) {
            console.log('⚠️ Already in Open mode');
        }
    }

    async setCloseMode() {
        console.log('🔄 Setting Close mode...');
        try {
            const closeBtn = this.page.locator('button:has-text("Close")').first();
            if (await closeBtn.isVisible({ timeout: 1000 })) {
                await closeBtn.click();
                await this.page.waitForTimeout(300);
            }
        } catch (error) {
            console.log('⚠️ Already in Close mode');
        }
    }

    async fillQuantity() {
        // Check if we're in close mode and handle differently
        const isCloseMode = await this.isInCloseMode();
        if (isCloseMode) {
            console.log('🔄 Detected close mode - using close-specific quantity strategies');
            return await this.fillQuantityForClose();
        }

        const strategies = [
            // Strategy 1: XPath following Quantity(USDT) - Fast version (no initial clearing)
            async () => {
                const input = this.page.locator('text=Quantity(USDT) >> xpath=following::input[1]').first();
                if (await input.isVisible({ timeout: 500 })) {
                    // Try direct fill first (fastest)
                    await input.click();
                    await input.fill('0.3600');

                    // Verify the value was set
                    const value = await input.inputValue();
                    if (value === '0.3600') {
                        return true;
                    }

                    // If direct fill failed, clear and retry
                    await this.clearInputField(input);
                    await input.click();
                    await input.fill('0.3600');

                    const retryValue = await input.inputValue();
                    return retryValue === '0.3600';
                }
                return false;
            },

            // Strategy 2: XPath following Quantity - Fast version
            async () => {
                const input = this.page.locator('text=Quantity >> xpath=following::input[1]').first();
                if (await input.isVisible({ timeout: 300 })) {
                    const type = await input.getAttribute('type');
                    // Skip checkboxes
                    if (type !== 'checkbox') {
                        // Try direct fill first
                        await input.click();
                        await input.fill('0.3600');

                        const value = await input.inputValue();
                        if (value === '0.3600') {
                            return true;
                        }

                        // If failed, clear and retry
                        await this.clearInputField(input);
                        await input.click();
                        await input.fill('0.3600');

                        const retryValue = await input.inputValue();
                        return retryValue === '0.3600';
                    }
                }
                return false;
            },

            // Strategy 3: Direct text/number inputs - Fast version
            async () => {
                const selectors = ['input[type="text"]', 'input[type="number"]'];
                for (const selector of selectors) {
                    const inputs = await this.page.locator(selector).all();
                    for (const input of inputs) {
                        if (await input.isVisible({ timeout: 100 })) {
                            try {
                                // Try direct fill first
                                await input.click();
                                await input.fill('0.3600');

                                const value = await input.inputValue();
                                if (value === '0.3600') {
                                    return true;
                                }

                                // If failed, clear and retry
                                await this.clearInputField(input);
                                await input.click();
                                await input.fill('0.3600');

                                const retryValue = await input.inputValue();
                                if (retryValue === '0.3600') {
                                    return true;
                                }
                            } catch (error) {
                                continue;
                            }
                        }
                    }
                }
                return false;
            }
        ];

        for (let i = 0; i < strategies.length; i++) {
            try {
                if (await strategies[i]()) {
                    console.log(`✅ Quantity filled using strategy ${i + 1}`);
                    return;
                }
            } catch (error) {
                console.log(`⚠️ Strategy ${i + 1} failed:`, error.message);
                continue;
            }
        }

        throw new Error('Could not fill quantity field');
    }

    async isInCloseMode() {
        try {
            // Check if we're in close mode by looking for close-specific elements
            const closeIndicators = [
                'button:has-text("Close Long")',
                'button:has-text("Close Short")',
                'text=Close Position',
                '.close-panel'
            ];

            for (const indicator of closeIndicators) {
                try {
                    if (await this.page.locator(indicator).first().isVisible({ timeout: 200 })) {
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }
            return false;
        } catch (error) {
            return false;
        }
    }

    async fillQuantityForClose() {
        console.log('🔢 Filling quantity for close mode...');

        // Close mode strategies - optimized for speed
        const closeStrategies = [
            // Strategy 1: Fast force click on ant-input in trading area
            async () => {
                const inputs = await this.page.locator('input.ant-input[type="text"]').all();
                for (const input of inputs) {
                    try {
                        if (await input.isVisible({ timeout: 100 })) {
                            // Check if it's in the trading panel (not header)
                            const boundingBox = await input.boundingBox();
                            if (boundingBox && boundingBox.y > 200) { // Below header area
                                const className = await input.getAttribute('class');

                                // Skip search inputs and other non-quantity fields
                                if (className && !className.includes('search') && !className.includes('filter')) {
                                    console.log(`🎯 Fast force click on: ${className}`);

                                    // Try direct fill first (fastest)
                                    await input.click({ force: true });
                                    await input.fill('0.3600');

                                    const value = await input.inputValue();
                                    if (value === '0.3600') {
                                        console.log(`✅ Close quantity filled using fast force click`);
                                        return true;
                                    }

                                    // If direct fill failed, clear and retry
                                    await this.clearInputField(input);
                                    await input.click({ force: true });
                                    await input.fill('0.3600');

                                    const retryValue = await input.inputValue();
                                    if (retryValue === '0.3600') {
                                        console.log(`✅ Close quantity filled after clearing`);
                                        return true;
                                    }
                                }
                            }
                        }
                    } catch (error) {
                        continue;
                    }
                }
                return false;
            },

            // Strategy 2: XPath method (fallback)
            async () => {
                const input = this.page.locator('text=Quantity(USDT) >> xpath=following::input[1]').first();
                if (await input.isVisible({ timeout: 300 })) {
                    console.log('✅ Found Quantity(USDT) field in close mode');

                    // Try direct fill first
                    await input.click({ force: true });
                    await input.fill('0.3600');

                    const value = await input.inputValue();
                    if (value === '0.3600') {
                        return true;
                    }

                    // If failed, clear and retry
                    await this.clearInputField(input);
                    await input.click({ force: true });
                    await input.fill('0.3600');

                    const retryValue = await input.inputValue();
                    return retryValue === '0.3600';
                }
                return false;
            }
        ];

        for (let i = 0; i < closeStrategies.length; i++) {
            try {
                if (await closeStrategies[i]()) {
                    console.log(`✅ Close quantity strategy ${i + 1} succeeded`);
                    return;
                }
            } catch (error) {
                console.log(`⚠️ Close strategy ${i + 1} failed:`, error.message);
                continue;
            }
        }

        throw new Error('Could not fill quantity field in close mode');
    }

    async clickOrderButton(orderType) {
        console.log(`📊 Clicking ${orderType} button...`);

        // Check if this is a close order and if there are positions to close
        if (orderType.includes('Close')) {
            const hasPositions = await this.checkForOpenPositions();
            if (!hasPositions) {
                throw new Error(`No open positions to close. Cannot execute ${orderType}.`);
            }
        }

        const buttonMap = {
            'Open Long': [
                'button:has-text("Open Long")',
                'text=Open Long',
                '.buy-button',
                'button[class*="buy"]',
                'button[class*="long"]'
            ],
            'Open Short': [
                'button:has-text("Open Short")',
                'text=Open Short',
                '.sell-button',
                'button[class*="sell"]',
                'button[class*="short"]'
            ],
            'Close Long': [
                'button:has-text("Close Long")',
                'text=Close Long',
                'button:has-text("Close")',
                '.close-long-button',
                'button[class*="close"][class*="long"]'
            ],
            'Close Short': [
                'button:has-text("Close Short")',
                'text=Close Short',
                'button:has-text("Close")',
                '.close-short-button',
                'button[class*="close"][class*="short"]'
            ]
        };

        const selectors = buttonMap[orderType];
        if (!selectors) {
            throw new Error(`Unknown order type: ${orderType}`);
        }

        // Try each selector with increasing timeout
        for (let i = 0; i < selectors.length; i++) {
            const selector = selectors[i];
            try {
                const button = this.page.locator(selector).first();
                const timeout = i === 0 ? 2000 : 1000; // Give more time to first selector

                if (await button.isVisible({ timeout })) {
                    await button.click();
                    console.log(`✅ ${orderType} button clicked using selector: ${selector}`);
                    return;
                }
            } catch (error) {
                console.log(`⚠️ Selector ${i + 1} failed: ${selector}`);
                continue;
            }
        }

        // If close order, provide more specific error
        if (orderType.includes('Close')) {
            throw new Error(`${orderType} button not found. This usually means there are no open ${orderType.includes('Long') ? 'long' : 'short'} positions to close.`);
        }

        throw new Error(`${orderType} button not found`);
    }

    async checkForOpenPositions() {
        console.log('🔍 Checking for open positions...');

        try {
            // Look for position indicators
            const positionIndicators = [
                '.position-row',
                '.open-position',
                '[class*="position"]',
                'text=Position',
                'text=PnL',
                'text=Unrealized'
            ];

            for (const indicator of positionIndicators) {
                try {
                    const element = this.page.locator(indicator).first();
                    if (await element.isVisible({ timeout: 500 })) {
                        console.log(`✅ Found position indicator: ${indicator}`);
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }

            // Check for close buttons existence as another indicator
            const closeButtons = await this.page.locator('button:has-text("Close Long"), button:has-text("Close Short")').all();
            if (closeButtons.length > 0) {
                for (const btn of closeButtons) {
                    if (await btn.isVisible({ timeout: 100 })) {
                        console.log('✅ Found close buttons - positions exist');
                        return true;
                    }
                }
            }

            console.log('⚠️ No open positions found');
            return false;
        } catch (error) {
            console.log('⚠️ Error checking positions:', error.message);
            return false;
        }
    }

    async handlePopup(orderType) {
        console.log(`✅ Handling popup for ${orderType}...`);

        // Different popup handling strategies
        const popupStrategies = [
            // Strategy 1: Standard Confirm button
            async () => {
                const confirmBtn = this.page.locator('button:has-text("Confirm")').first();
                if (await confirmBtn.isVisible({ timeout: 200 })) {
                    await confirmBtn.click();
                    return true;
                }
                return false;
            },

            // Strategy 2: OK button
            async () => {
                const okBtn = this.page.locator('button:has-text("OK")').first();
                if (await okBtn.isVisible({ timeout: 200 })) {
                    await okBtn.click();
                    return true;
                }
                return false;
            },

            // Strategy 3: Submit button
            async () => {
                const submitBtn = this.page.locator('button:has-text("Submit")').first();
                if (await submitBtn.isVisible({ timeout: 200 })) {
                    await submitBtn.click();
                    return true;
                }
                return false;
            },

            // Strategy 4: Any button in modal
            async () => {
                const modalBtns = await this.page.locator('.ant-modal button, .modal button').all();
                for (const btn of modalBtns) {
                    if (await btn.isVisible({ timeout: 100 })) {
                        const text = await btn.textContent();
                        if (text && (text.includes('Confirm') || text.includes('OK') || text.includes('Submit'))) {
                            await btn.click();
                            return true;
                        }
                    }
                }
                return false;
            }
        ];

        for (let attempt = 1; attempt <= 15; attempt++) {
            try {
                // Try each strategy
                for (let i = 0; i < popupStrategies.length; i++) {
                    if (await popupStrategies[i]()) {
                        console.log(`✅ Popup handled with strategy ${i + 1} (attempt ${attempt})`);
                        await this.page.waitForTimeout(300); // Wait for popup to close
                        return true;
                    }
                }
            } catch (error) {
                // Continue trying
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        console.log('⚠️ No popup found or handled');
        return false;
    }

    async verifySuccess(orderType) {
        try {
            const successSelectors = [
                'text=Purchased successfully',
                'text=Success',
                'text=success',
                '.success'
            ];

            for (const selector of successSelectors) {
                try {
                    if (await this.page.locator(selector).first().isVisible({ timeout: 500 })) {
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }
            return false;
        } catch (error) {
            return false;
        }
    }
}

async function executeCommand(orderType, port) {
    const trader = new FinalUltimateTrader(port);
    
    try {
        console.log('🎯 MEXC FINAL ULTIMATE TRADER');
        console.log('==============================');
        console.log(`📊 Order: ${orderType}`);
        console.log(`🌐 Port: ${port}`);
        console.log('⚡ Target: <2 seconds');
        console.log('🧹 Enhanced cleanup & recovery');
        console.log('');

        const connected = await trader.connectToBrowser();
        if (!connected) {
            throw new Error(`Failed to connect to port ${port}`);
        }

        const result = await trader.executeOrder(orderType);

        if (result.success) {
            if (result.targetAchieved) {
                console.log('\n🏆 TARGET ACHIEVED!');
                console.log(`⚡ ${result.executionTime}ms (<2 seconds)`);
            } else {
                console.log('\n✅ SUCCESS!');
                console.log(`⏱️ ${result.executionTime}ms`);
            }
            
            if (result.emergencyRecovery) {
                console.log('🚨 Via emergency recovery!');
            }
        } else {
            console.log('\n❌ FAILED');
        }

        require('fs').writeFileSync(`final-${orderType.replace(' ', '')}-${port}.json`, JSON.stringify(result, null, 2));
        process.exit(result.success ? 0 : 1);
        
    } catch (error) {
        console.error('💥 Failed:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    const orderType = process.argv[2];
    const port = parseInt(process.argv[3]) || (orderType && orderType.includes('Close') ? 9223 : 9222);
    
    const validOrders = ['Open Long', 'Open Short', 'Close Long', 'Close Short'];
    
    if (!orderType || !validOrders.includes(orderType)) {
        console.log('🎯 MEXC FINAL ULTIMATE TRADER');
        console.log('==============================');
        console.log('📋 USAGE:');
        console.log('node final-ultimate-trader.js "Open Long"');
        console.log('node final-ultimate-trader.js "Open Short"');
        console.log('node final-ultimate-trader.js "Close Long"');
        console.log('node final-ultimate-trader.js "Close Short"');
        console.log('');
        console.log('🌐 Auto Port Selection:');
        console.log('- Open orders → 9222');
        console.log('- Close orders → 9223');
        console.log('');
        console.log('🚀 Start browsers:');
        console.log('chrome.exe --remote-debugging-port=9222 --user-data-dir="./browser_data"');
        console.log('chrome.exe --remote-debugging-port=9223 --user-data-dir="./browser_data2"');
        process.exit(1);
    }
    
    executeCommand(orderType, port);
}

module.exports = FinalUltimateTrader;
