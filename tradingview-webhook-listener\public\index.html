<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView Webhook Listener - Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .header a:hover {
            background: rgba(255, 255, 255, 0.3) !important;
            transform: translateY(-2px);
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(255,255,255,0.1);
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-indicator.active {
            background: #4CAF50;
        }

        .status-indicator.inactive {
            background: #f44336;
        }

        .status-indicator.warning {
            background: #ff9800;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .card h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-icon {
            font-size: 1.5rem;
            opacity: 0.8;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            opacity: 0.9;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: none;
            border-radius: 8px;
            background: rgba(255,255,255,0.1);
            color: #ffffff;
            font-size: 1rem;
            transition: background 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            background: rgba(255,255,255,0.2);
        }

        .form-group input::placeholder {
            color: rgba(255,255,255,0.6);
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255,255,255,0.3);
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #4CAF50;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }

        .btn-secondary:hover {
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
        }

        .btn-danger {
            background: linear-gradient(45deg, #f44336, #d32f2f);
        }

        .btn-danger:hover {
            box-shadow: 0 5px 15px rgba(244, 67, 54, 0.4);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .trades-list {
            max-height: 400px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .trade-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            margin-bottom: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            border-left: 4px solid;
        }

        .trade-item.success {
            border-left-color: #4CAF50;
        }

        .trade-item.failed {
            border-left-color: #f44336;
        }

        .trade-item.skipped {
            border-left-color: #ff9800;
        }

        .trade-info {
            flex: 1;
        }

        .trade-type {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .trade-details {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .trade-time {
            font-size: 0.8rem;
            opacity: 0.7;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: rgba(76, 175, 80, 0.2);
            border-left: 4px solid #4CAF50;
        }

        .alert-error {
            background: rgba(244, 67, 54, 0.2);
            border-left: 4px solid #f44336;
        }

        .alert-warning {
            background: rgba(255, 152, 0, 0.2);
            border-left: 4px solid #ff9800;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .status-bar {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-chart-line"></i> TradingView Webhook Listener</h1>
            <p>MEXC Futures Trading with Smart Money Management</p>
            <div style="margin-top: 15px;">
                <a href="/" style="color: #ffffff; text-decoration: none; padding: 8px 16px; margin: 0 5px; background: rgba(255, 255, 255, 0.2); border-radius: 20px; transition: all 0.3s ease;">
                    <i class="fas fa-home"></i> Dashboard
                </a>
                <a href="/diagnostics" style="color: #ffffff; text-decoration: none; padding: 8px 16px; margin: 0 5px; background: rgba(255, 255, 255, 0.2); border-radius: 20px; transition: all 0.3s ease;">
                    <i class="fas fa-stethoscope"></i> Diagnostics
                </a>
            </div>
        </div>

        <div class="status-bar">
            <div class="status-item">
                <div class="status-indicator" id="botStatus"></div>
                <span id="botStatusText">Bot Status</span>
            </div>
            <div class="status-item">
                <div class="status-indicator" id="apiStatus"></div>
                <span id="apiStatusText">MEXC API</span>
            </div>
            <div class="status-item">
                <div class="status-indicator" id="serviceStatus"></div>
                <span id="serviceStatusText">Trading Service</span>
            </div>
            <div class="status-item">
                <div class="status-indicator" id="telegramStatus"></div>
                <span id="telegramStatusText">Telegram</span>
            </div>
            <div class="status-item">
                <i class="fas fa-wallet"></i>
                <span id="balanceText">Balance: Loading...</span>
            </div>
        </div>

        <div id="alertContainer"></div>

        <div class="dashboard-grid">
            <!-- Bot Control Card -->
            <div class="card">
                <h3><i class="fas fa-power-off card-icon"></i> Bot Control</h3>
                <div class="form-group">
                    <label>
                        <span>Bot Active</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="botActiveToggle">
                            <span class="slider"></span>
                        </label>
                    </label>
                </div>

                <div class="form-group">
                    <label>
                        <span>Use New TradingView Format (Direct SL/TP)</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="useNewTradingViewFormat">
                            <span class="slider"></span>
                        </label>
                    </label>
                </div>
                <button class="btn" onclick="saveConfig()">
                    <i class="fas fa-save"></i> Save Settings
                </button>
            </div>

            <!-- API Configuration Card -->
            <div class="card">
                <h3><i class="fas fa-key card-icon"></i> MEXC API Configuration</h3>
                <div class="form-group">
                    <label for="mexcApiKey">API Key</label>
                    <input type="password" id="mexcApiKey" placeholder="Enter your MEXC API Key">
                </div>
                <div class="form-group">
                    <label for="mexcSecretKey">Secret Key</label>
                    <input type="password" id="mexcSecretKey" placeholder="Enter your MEXC Secret Key">
                </div>
                <button class="btn btn-secondary" onclick="testApiConnection()">
                    <i class="fas fa-plug"></i> Test Connection
                </button>
            </div>

            <!-- Telegram Configuration Card -->
            <div class="card">
                <h3><i class="fas fa-paper-plane card-icon"></i> Telegram Notifications</h3>
                <div class="form-group">
                    <label>
                        <span>Enable Telegram Notifications</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="telegramEnabled">
                            <span class="slider"></span>
                        </label>
                    </label>
                </div>
                <div class="form-group">
                    <label for="telegramBotToken">Bot Token</label>
                    <input type="password" id="telegramBotToken" placeholder="Enter your Telegram Bot Token">
                    <small>Get your bot token from @BotFather on Telegram</small>
                </div>
                <div class="form-group">
                    <label for="telegramChatId">Chat ID</label>
                    <input type="text" id="telegramChatId" placeholder="Enter your Telegram Chat ID">
                    <small>Send /start to your bot to get your Chat ID</small>
                </div>
                <button class="btn btn-secondary" onclick="testTelegramConnection()">
                    <i class="fas fa-paper-plane"></i> Test Telegram
                </button>
            </div>

            <!-- Money Management Card -->
            <div class="card">
                <h3><i class="fas fa-coins card-icon"></i> Money Management</h3>
                <div class="form-group">
                    <label>
                        <span>Enable Money Management</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="moneyManagementEnabled">
                            <span class="slider"></span>
                        </label>
                    </label>
                </div>
                <div class="form-group">
                    <label for="moneyManagementMode">Mode</label>
                    <select id="moneyManagementMode">
                        <option value="percentage">Percentage Based</option>
                        <option value="fixed">Fixed Amount</option>
                    </select>
                </div>
                <div class="form-group" id="percentageGroup">
                    <label for="positionSizePercentage">Position Size (%)</label>
                    <input type="number" id="positionSizePercentage" min="1" max="100" placeholder="50">
                </div>
                <div class="form-group" id="fixedAmountGroup">
                    <label for="fixedTradeAmount">Fixed Amount (USDT)</label>
                    <input type="number" id="fixedTradeAmount" min="0.1" step="0.1" placeholder="100">
                </div>
            </div>

            <!-- Trade Limits Card -->
            <div class="card">
                <h3><i class="fas fa-shield-alt card-icon"></i> Trade Limits</h3>
                <div class="form-group">
                    <label for="minTradeAmount">Minimum Trade (USDT)</label>
                    <input type="number" id="minTradeAmount" min="0" step="0.1" placeholder="0.1">
                </div>
                <div class="form-group">
                    <label for="maxTradeAmount">Maximum Trade (USDT)</label>
                    <input type="number" id="maxTradeAmount" min="0" step="1" placeholder="10000">
                </div>
                <div class="form-group">
                    <label for="minRemainingBalance">Min Remaining Balance (USDT)</label>
                    <input type="number" id="minRemainingBalance" min="0" step="1" placeholder="10">
                </div>
            </div>

            <!-- SL & TP Configuration Card -->
            <div class="card">
                <h3><i class="fas fa-crosshairs card-icon"></i> SL & TP Configuration</h3>
                <div class="form-group">
                    <label>
                        <span>Enable SL/TP Automation</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="slTpEnabled">
                            <span class="slider"></span>
                        </label>
                    </label>
                </div>

                <div class="form-group">
                    <label for="atrLength">ATR Length</label>
                    <input type="number" id="atrLength" min="1" max="100" value="10">
                </div>

                <div class="form-group">
                    <label for="atrSmoothing">ATR Smoothing</label>
                    <select id="atrSmoothing">
                        <option value="RMA">RMA</option>
                        <option value="SMA">SMA</option>
                        <option value="EMA">EMA</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="slTpCalculationSource">SL/TP Calculation Source</label>
                    <select id="slTpCalculationSource">
                        <option value="mexc_entry_price">MEXC Exchange Entry Price (Current)</option>
                        <option value="tradingview_last_price">TradingView Webhook Last Price</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="slMultiplier">SL Multiplier</label>
                    <input type="number" id="slMultiplier" min="0.1" step="0.1" value="1.5">
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="tp1Enabled"> TP-1 Reward
                        <input type="number" id="tp1Reward" min="0.1" step="0.1" value="2" style="width: 60px; margin: 0 5px;"> %:
                        <input type="number" id="tp1Percent" min="1" max="100" value="100" style="width: 60px; margin-left: 5px;">
                    </label>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="tp2Enabled"> TP-2 Reward
                        <input type="number" id="tp2Reward" min="0.1" step="0.1" value="8" style="width: 60px; margin: 0 5px;"> %:
                        <input type="number" id="tp2Percent" min="1" max="100" value="30" style="width: 60px; margin-left: 5px;">
                    </label>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="tp3Enabled"> TP-3 Reward
                        <input type="number" id="tp3Reward" min="0.1" step="0.1" value="10" style="width: 60px; margin: 0 5px;"> %:
                        <input type="number" id="tp3Percent" min="1" max="100" value="40" style="width: 60px; margin-left: 5px;">
                    </label>
                </div>

                <div class="form-group">
                    <label for="slType">SL Type</label>
                    <select id="slType">
                        <option value="Normal">Normal</option>
                        <option value="Trailing">Trailing SL</option>
                        <option value="MoveToTPs">Move to TPs</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="trailingSource">Source</label>
                    <select id="trailingSource">
                        <option value="Close">Close</option>
                        <option value="Open">Open</option>
                        <option value="High">High</option>
                        <option value="Low">Low</option>
                        <option value="HLCO4">HLCO/4</option>
                    </select>
                    <label for="trailingSourceAdd" style="margin-left: 10px;">Add (%)</label>
                    <input type="number" id="trailingSourceAdd" min="0" step="0.1" value="0" style="width: 80px; margin-left: 5px;">
                </div>

                <div class="form-group">
                    <label for="startTrailingAtProfit">Start Trailing at x profit (ATR Multiplier)</label>
                    <input type="number" id="startTrailingAtProfit" min="0" step="0.1" value="1">
                </div>

                <div class="form-group">
                    <label for="trailingValue">Trailing Value (ATR Multiplier)</label>
                    <input type="number" id="trailingValue" min="0.1" step="0.1" value="0.5">
                </div>

                <div class="form-group">
                    <label for="maxExecutionTime">Max Execution Time (seconds)</label>
                    <input type="number" id="maxExecutionTime" min="1" max="60" value="2">
                </div>

                <div class="form-group">
                    <label for="maxPriceDifference">Max Price Difference (%)</label>
                    <input type="number" id="maxPriceDifference" min="0" max="50" step="0.1" value="2">
                </div>
            </div>
        </div>

        <!-- Statistics Card -->
        <div class="card">
            <h3><i class="fas fa-chart-bar card-icon"></i> Trading Statistics</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="totalSignals">0</div>
                    <div class="stat-label">Signals Received</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalTrades">0</div>
                    <div class="stat-label">Trades Executed</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="tradesSkipped">0</div>
                    <div class="stat-label">Trades Skipped</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="successRate">0%</div>
                    <div class="stat-label">Success Rate</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="avgExecutionTime">0ms</div>
                    <div class="stat-label">Avg Execution</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="activePositions">0</div>
                    <div class="stat-label">Active Positions</div>
                </div>
            </div>
        </div>

        <!-- Recent Trades Card -->
        <div class="card">
            <h3><i class="fas fa-history card-icon"></i> Recent Trades</h3>
            <button class="btn btn-secondary" onclick="testWebhook()">
                <i class="fas fa-flask"></i> Test Webhook
            </button>
            <div class="trades-list" id="tradesList">
                <div style="text-align: center; opacity: 0.7; padding: 20px;">
                    No trades yet
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global state
        let config = {};
        let status = {};
        let autoRefreshInterval = null;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadConfig();
            loadStatus();
            setupEventListeners();
            startAutoRefresh();
        });

        function setupEventListeners() {
            // Money management mode change
            document.getElementById('moneyManagementMode').addEventListener('change', function() {
                toggleMoneyManagementMode();
            });

            // Auto-save on input changes
            const inputs = document.querySelectorAll('input, select');
            inputs.forEach(input => {
                if (input.type !== 'checkbox') {
                    input.addEventListener('blur', saveConfig);
                } else {
                    // Add change event listener for checkboxes to auto-save
                    input.addEventListener('change', saveConfig);
                }
            });
        }

        function toggleMoneyManagementMode() {
            const mode = document.getElementById('moneyManagementMode').value;
            const percentageGroup = document.getElementById('percentageGroup');
            const fixedAmountGroup = document.getElementById('fixedAmountGroup');

            if (mode === 'percentage') {
                percentageGroup.style.display = 'block';
                fixedAmountGroup.style.display = 'none';
            } else {
                percentageGroup.style.display = 'none';
                fixedAmountGroup.style.display = 'block';
            }
        }

        async function loadConfig() {
            try {
                const response = await fetch('/api/config');
                config = await response.json();
                updateConfigUI();
            } catch (error) {
                showAlert('Failed to load configuration', 'error');
            }
        }

        function updateConfigUI() {
            // Bot control
            document.getElementById('botActiveToggle').checked = config.botActive || false;
            document.getElementById('useNewTradingViewFormat').checked = config.useNewTradingViewFormat || false;

            // API configuration (don't show actual keys for security)
            document.getElementById('mexcApiKey').placeholder = config.mexcApiKey ? '***CONFIGURED***' : 'Enter your MEXC API Key';
            document.getElementById('mexcSecretKey').placeholder = config.mexcSecretKey ? '***CONFIGURED***' : 'Enter your MEXC Secret Key';

            // Telegram configuration
            document.getElementById('telegramEnabled').checked = config.telegramEnabled || false;
            document.getElementById('telegramBotToken').placeholder = config.telegramBotToken ? '***CONFIGURED***' : 'Enter your Telegram Bot Token';
            document.getElementById('telegramChatId').value = config.telegramChatId || '';

            // Money management
            document.getElementById('moneyManagementEnabled').checked = config.moneyManagementEnabled || false;
            document.getElementById('moneyManagementMode').value = config.moneyManagementMode || 'percentage';
            document.getElementById('positionSizePercentage').value = config.positionSizePercentage || 50;
            document.getElementById('fixedTradeAmount').value = config.fixedTradeAmount || 100;

            // Trade limits
            document.getElementById('minTradeAmount').value = config.minTradeAmount || 0.1;
            document.getElementById('maxTradeAmount').value = config.maxTradeAmount || 10000;
            document.getElementById('minRemainingBalance').value = config.minRemainingBalance || 10;

            // SL & TP Configuration
            document.getElementById('slTpEnabled').checked = config.slTpEnabled || false;
            document.getElementById('slTpCalculationSource').value = config.slTpCalculationSource || 'mexc_entry_price';
            document.getElementById('atrLength').value = config.atrLength || 10;
            document.getElementById('atrSmoothing').value = config.atrSmoothing || 'RMA';
            document.getElementById('slMultiplier').value = config.slMultiplier || 1.5;

            document.getElementById('tp1Enabled').checked = config.tp1Enabled || false;
            document.getElementById('tp1Reward').value = config.tp1Reward || 2;
            document.getElementById('tp1Percent').value = config.tp1Percent || 100;

            document.getElementById('tp2Enabled').checked = config.tp2Enabled || false;
            document.getElementById('tp2Reward').value = config.tp2Reward || 8;
            document.getElementById('tp2Percent').value = config.tp2Percent || 30;

            document.getElementById('tp3Enabled').checked = config.tp3Enabled || false;
            document.getElementById('tp3Reward').value = config.tp3Reward || 10;
            document.getElementById('tp3Percent').value = config.tp3Percent || 40;

            document.getElementById('slType').value = config.slType || 'Normal';
            document.getElementById('startTrailingAtProfit').value = config.startTrailingAtProfit || 1;
            document.getElementById('trailingSource').value = config.trailingSource || 'Close';
            document.getElementById('trailingSourceAdd').value = config.trailingSourceAdd || 0;
            document.getElementById('trailingValue').value = config.trailingValue || 0.5;
            document.getElementById('maxExecutionTime').value = (config.maxExecutionTime || 2000) / 1000; // Convert ms to seconds
            document.getElementById('maxPriceDifference').value = config.maxPriceDifference || 2;

            toggleMoneyManagementMode();
        }

        async function saveConfig() {
            const newConfig = {
                botActive: document.getElementById('botActiveToggle').checked,
                useNewTradingViewFormat: document.getElementById('useNewTradingViewFormat').checked,
                moneyManagementEnabled: document.getElementById('moneyManagementEnabled').checked,
                moneyManagementMode: document.getElementById('moneyManagementMode').value,
                positionSizePercentage: parseFloat(document.getElementById('positionSizePercentage').value) || 50,
                fixedTradeAmount: parseFloat(document.getElementById('fixedTradeAmount').value) || 100,
                minTradeAmount: parseFloat(document.getElementById('minTradeAmount').value) || 0.1,
                maxTradeAmount: parseFloat(document.getElementById('maxTradeAmount').value) || 10000,
                minRemainingBalance: parseFloat(document.getElementById('minRemainingBalance').value) || 10,

                // SL & TP Configuration
                slTpEnabled: document.getElementById('slTpEnabled').checked,
                slTpCalculationSource: document.getElementById('slTpCalculationSource').value,
                atrLength: parseInt(document.getElementById('atrLength').value) || 10,
                atrSmoothing: document.getElementById('atrSmoothing').value,
                slMultiplier: parseFloat(document.getElementById('slMultiplier').value) || 1.5,

                tp1Enabled: document.getElementById('tp1Enabled').checked,
                tp1Reward: parseFloat(document.getElementById('tp1Reward').value) || 2,
                tp1Percent: parseFloat(document.getElementById('tp1Percent').value) || 100,

                tp2Enabled: document.getElementById('tp2Enabled').checked,
                tp2Reward: parseFloat(document.getElementById('tp2Reward').value) || 8,
                tp2Percent: parseFloat(document.getElementById('tp2Percent').value) || 30,

                tp3Enabled: document.getElementById('tp3Enabled').checked,
                tp3Reward: parseFloat(document.getElementById('tp3Reward').value) || 10,
                tp3Percent: parseFloat(document.getElementById('tp3Percent').value) || 40,

                slType: document.getElementById('slType').value,
                startTrailingAtProfit: parseFloat(document.getElementById('startTrailingAtProfit').value) || 1,
                trailingSource: document.getElementById('trailingSource').value,
                trailingSourceAdd: parseFloat(document.getElementById('trailingSourceAdd').value) || 0,
                trailingValue: parseFloat(document.getElementById('trailingValue').value) || 0.5,
                maxExecutionTime: (parseInt(document.getElementById('maxExecutionTime').value) || 2) * 1000, // Convert seconds to ms
                maxPriceDifference: parseFloat(document.getElementById('maxPriceDifference').value) || 2
            };

            // Telegram configuration
            newConfig.telegramEnabled = document.getElementById('telegramEnabled').checked;

            // Only include Telegram credentials if they were actually entered
            const telegramBotToken = document.getElementById('telegramBotToken').value.trim();
            const telegramChatId = document.getElementById('telegramChatId').value.trim();

            if (telegramBotToken && telegramBotToken !== '***CONFIGURED***') {
                newConfig.telegramBotToken = telegramBotToken;
            }
            if (telegramChatId) {
                newConfig.telegramChatId = telegramChatId;
            }

            // Only include API keys if they were actually entered
            const apiKey = document.getElementById('mexcApiKey').value.trim();
            const secretKey = document.getElementById('mexcSecretKey').value.trim();

            if (apiKey && !apiKey.includes('***')) {
                newConfig.mexcApiKey = apiKey;
            }
            if (secretKey && !secretKey.includes('***')) {
                newConfig.mexcSecretKey = secretKey;
            }

            try {
                const response = await fetch('/api/config', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(newConfig)
                });

                if (response.ok) {
                    showAlert('Configuration saved successfully', 'success');
                    loadConfig(); // Reload to get updated config
                } else {
                    const error = await response.json();
                    showAlert(error.error || 'Failed to save configuration', 'error');
                }
            } catch (error) {
                showAlert('Failed to save configuration', 'error');
            }
        }

        async function loadStatus() {
            try {
                const response = await fetch('/api/status');
                status = await response.json();
                updateStatusUI();
            } catch (error) {
                console.error('Failed to load status:', error);
            }
        }

        function updateStatusUI() {
            // Bot status
            const botStatus = document.getElementById('botStatus');
            const botStatusText = document.getElementById('botStatusText');
            if (status.botActive && status.configured) {
                botStatus.className = 'status-indicator active';
                botStatusText.textContent = 'Bot Active';
            } else if (status.configured) {
                botStatus.className = 'status-indicator warning';
                botStatusText.textContent = 'Bot Inactive';
            } else {
                botStatus.className = 'status-indicator inactive';
                botStatusText.textContent = 'Not Configured';
            }

            // API status
            const apiStatus = document.getElementById('apiStatus');
            const apiStatusText = document.getElementById('apiStatusText');
            if (status.mexcConnected) {
                apiStatus.className = 'status-indicator active';
                apiStatusText.textContent = 'MEXC Connected';
            } else {
                apiStatus.className = 'status-indicator inactive';
                apiStatusText.textContent = 'MEXC Disconnected';
            }

            // Telegram status
            const telegramStatus = document.getElementById('telegramStatus');
            const telegramStatusText = document.getElementById('telegramStatusText');
            if (status.telegramStatus && status.telegramStatus.connected) {
                telegramStatus.className = 'status-indicator active';
                telegramStatusText.textContent = 'Telegram Connected';
            } else {
                telegramStatus.className = 'status-indicator inactive';
                telegramStatusText.textContent = 'Telegram Disconnected';
            }

            // Service status
            const serviceStatus = document.getElementById('serviceStatus');
            const serviceStatusText = document.getElementById('serviceStatusText');
            // This would need to be implemented in the backend
            serviceStatus.className = 'status-indicator active';
            serviceStatusText.textContent = 'Service Ready';

            // Balance (from browser frontend)
            const balanceText = document.getElementById('balanceText');
            if (status.balance) {
                const source = status.balance.source || 'unknown';
                const sourceIcon = source === 'frontend' ? '🌐' : '🔗';
                const cached = status.balance.cached ? ' (cached)' : '';
                balanceText.textContent = `${sourceIcon} Balance: ${status.balance.total.toFixed(4)} USDT${cached}`;
                balanceText.title = `Source: ${source} | Raw: ${status.balance.raw || 'N/A'} | Updated: ${status.balance.timestamp || 'N/A'}`;
            } else {
                balanceText.textContent = '❌ Balance: Not Available';
                balanceText.title = 'Balance could not be fetched from MEXC frontend';
            }

            // Statistics
            document.getElementById('totalSignals').textContent = status.totalSignalsReceived || 0;
            document.getElementById('totalTrades').textContent = status.totalTradesExecuted || 0;
            document.getElementById('tradesSkipped').textContent = status.totalTradesSkipped || 0;
            document.getElementById('successRate').textContent = status.successRate ? `${status.successRate}%` : '0%';
            document.getElementById('avgExecutionTime').textContent = status.averageExecutionTime ? `${status.averageExecutionTime}ms` : '0ms';
            document.getElementById('activePositions').textContent = status.activePositions || 0;
        }

        async function testApiConnection() {
            showAlert('Testing API connection...', 'warning');

            try {
                const response = await fetch('/api/balance');
                const result = await response.json();

                if (result.success) {
                    const source = result.source || 'unknown';
                    const sourceText = source === 'frontend' ? 'Browser Frontend' : 'API';
                    const balanceAmount = result.balance.total || result.balance.free || 0;
                    showAlert(`✅ Connection successful! Balance: ${balanceAmount.toFixed(4)} USDT (Source: ${sourceText})`, 'success');
                } else {
                    showAlert(result.error || 'Connection failed', 'error');
                }
            } catch (error) {
                showAlert('API connection test failed', 'error');
            }
        }

        async function testTelegramConnection() {
            showAlert('Testing Telegram connection...', 'warning');

            try {
                // First save the current Telegram configuration
                const telegramConfig = {
                    telegramEnabled: document.getElementById('telegramEnabled').checked,
                };

                const telegramBotToken = document.getElementById('telegramBotToken').value.trim();
                const telegramChatId = document.getElementById('telegramChatId').value.trim();

                if (telegramBotToken && telegramBotToken !== '***CONFIGURED***') {
                    telegramConfig.telegramBotToken = telegramBotToken;
                }
                if (telegramChatId) {
                    telegramConfig.telegramChatId = telegramChatId;
                }

                // Update configuration
                const configResponse = await fetch('/api/config', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(telegramConfig)
                });

                if (!configResponse.ok) {
                    throw new Error('Failed to update Telegram configuration');
                }

                // Check status to see if Telegram is connected
                const statusResponse = await fetch('/api/status');
                const status = await statusResponse.json();

                if (status.telegramStatus && status.telegramStatus.connected) {
                    showAlert('✅ Telegram connection successful! Check your Telegram for a test message.', 'success');
                } else {
                    showAlert('❌ Telegram connection failed. Please check your Bot Token and Chat ID.', 'error');
                }
            } catch (error) {
                showAlert('Telegram connection test failed: ' + error.message, 'error');
            }
        }

        async function testWebhook() {
            showAlert('Sending test webhook...', 'warning');

            try {
                const response = await fetch('/api/test-webhook', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('Test webhook executed successfully!', 'success');
                    loadTrades(); // Refresh trades list
                } else {
                    showAlert(result.error || 'Test webhook failed', 'error');
                }
            } catch (error) {
                showAlert('Test webhook failed', 'error');
            }
        }

        async function loadTrades() {
            try {
                const response = await fetch('/api/trades');
                const result = await response.json();

                if (result.success) {
                    updateTradesList(result.trades);
                }
            } catch (error) {
                console.error('Failed to load trades:', error);
            }
        }

        function updateTradesList(trades) {
            const tradesList = document.getElementById('tradesList');

            if (!trades || trades.length === 0) {
                tradesList.innerHTML = '<div style="text-align: center; opacity: 0.7; padding: 20px;">No trades yet</div>';
                return;
            }

            tradesList.innerHTML = trades.slice(0, 10).map(trade => {
                const statusClass = trade.success ? 'success' : (trade.skipped ? 'skipped' : 'failed');
                const statusIcon = trade.success ? 'fa-check-circle' : (trade.skipped ? 'fa-pause-circle' : 'fa-times-circle');
                const orderType = trade.processedSignal?.orderType || (trade.skipped ? 'Skipped' : 'Failed');
                const quantity = trade.positionSize || 'N/A';
                const executionTime = trade.tradeResult?.executionTime || trade.executionTime || 0;

                return `
                    <div class="trade-item ${statusClass}">
                        <div class="trade-info">
                            <div class="trade-type">${orderType}</div>
                            <div class="trade-details">
                                Quantity: ${quantity} ${quantity !== 'N/A' && quantity !== 'Skipped' ? 'USDT' : ''}
                                ${executionTime > 0 ? `| ${executionTime}ms` : ''}
                                ${trade.error ? `| Error: ${trade.error}` : ''}
                                ${trade.reason ? `| Reason: ${trade.reason}` : ''}
                            </div>
                            <div class="trade-time">${new Date(trade.timestamp).toLocaleString()}</div>
                        </div>
                        <div class="trade-status">
                            <i class="fas ${statusIcon}"></i>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertId = 'alert-' + Date.now();

            const alertHTML = `
                <div id="${alertId}" class="alert alert-${type}">
                    <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;

            alertContainer.insertAdjacentHTML('beforeend', alertHTML);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }

        function startAutoRefresh() {
            autoRefreshInterval = setInterval(() => {
                loadStatus();
                loadTrades();
            }, 5000); // Refresh every 5 seconds
        }

        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
        }

        // Handle page visibility changes
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                stopAutoRefresh();
            } else {
                startAutoRefresh();
            }
        });
    </script>
</body>
</html>
