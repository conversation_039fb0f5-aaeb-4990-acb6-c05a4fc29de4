#!/usr/bin/env python3
"""
SIMPLE ENTROPY ANALYZER
Analyze captured signatures and entropy without alerts
"""

import json
import time
import hashlib
import hmac
from playwright.sync_api import sync_playwright
from dotenv import dotenv_values

class SimpleEntropyAnalyzer:
    """Simple analyzer without alerts that cause crashes"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        
        print("🎲 SIMPLE ENTROPY ANALYZER")
        print("="*30)
        print("🔥 ANALYZING CAPTURED ENTROPY DATA")
    
    def setup_simple_hooks(self):
        """Setup simple hooks without alerts"""
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                return False
            
            self.context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                mexc_page = self.context.new_page()
                mexc_page.goto('https://futures.mexc.com/exchange/BTC_USDT', wait_until='domcontentloaded')
            
            self.page = mexc_page
            
            # Inject SIMPLE hooks (no alerts!)
            self.page.evaluate("""
                window.entropyData = [];
                window.signatureData = [];
                
                console.log('🎲 Installing SIMPLE entropy hooks...');
                
                // Hook crypto.getRandomValues
                if (window.crypto && window.crypto.getRandomValues) {
                    const originalGetRandomValues = window.crypto.getRandomValues;
                    window.crypto.getRandomValues = function(array) {
                        const result = originalGetRandomValues.apply(this, arguments);
                        
                        const randomData = Array.from(array);
                        const randomHex = randomData.map(b => b.toString(16).padStart(2, '0')).join('');
                        
                        window.entropyData.push({
                            type: 'crypto_random',
                            hex: randomHex,
                            length: array.length,
                            timestamp: Date.now()
                        });
                        
                        return result;
                    };
                }
                
                // Hook Math.random
                const originalMathRandom = Math.random;
                Math.random = function() {
                    const result = originalMathRandom.apply(this, arguments);
                    
                    window.entropyData.push({
                        type: 'math_random',
                        value: result,
                        timestamp: Date.now()
                    });
                    
                    return result;
                };
                
                // Hook signature headers (NO ALERTS!)
                const originalSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;
                XMLHttpRequest.prototype.setRequestHeader = function(name, value) {
                    if (name.toLowerCase() === 'x-mxc-sign') {
                        console.log('🔥 SIGNATURE CAPTURED:', value);
                        
                        // Get recent entropy (last 30 seconds)
                        const recentEntropy = window.entropyData.filter(
                            entry => Date.now() - entry.timestamp < 30000
                        );
                        
                        window.signatureData.push({
                            signature: value,
                            timestamp: Date.now(),
                            recentEntropy: recentEntropy
                        });
                        
                        console.log('🎲 Entropy count:', recentEntropy.length);
                    }
                    
                    return originalSetRequestHeader.apply(this, arguments);
                };
                
                console.log('✅ Simple hooks installed!');
            """)
            
            print("✅ Simple hooks setup completed")
            return True
            
        except Exception as e:
            print(f"❌ Setup failed: {e}")
            return False
    
    def analyze_captured_data(self):
        """Analyze the captured signature and entropy data"""
        
        print("\n🔍 ANALYZING CAPTURED DATA")
        print("="*35)
        
        timeout = 300  # 5 minutes
        start_time = time.time()
        last_signature_count = 0
        
        while time.time() - start_time < timeout:
            try:
                # Get captured data
                signature_data = self.page.evaluate("() => window.signatureData || []")
                entropy_data = self.page.evaluate("() => window.entropyData || []")
                
                # Check for new signatures
                if len(signature_data) > last_signature_count:
                    new_signatures = signature_data[last_signature_count:]
                    
                    for i, sig_data in enumerate(new_signatures):
                        sig_num = last_signature_count + i + 1
                        print(f"\n🔥 SIGNATURE #{sig_num}: {sig_data['signature']}")
                        print(f"   Recent entropy: {len(sig_data['recentEntropy'])} values")
                        
                        # Analyze this signature
                        if self.crack_signature_with_entropy(sig_data):
                            print(f"🎉 SIGNATURE CRACKED!")
                            return True
                    
                    last_signature_count = len(signature_data)
                
                # Show progress
                elapsed = int(time.time() - start_time)
                if elapsed % 20 == 0 and elapsed > 0:
                    print(f"⏱️  Analyzing... ({elapsed}s, {len(signature_data)} sigs, {len(entropy_data)} entropy)")
                
                time.sleep(2)
                
            except Exception as e:
                print(f"⚠️  Error: {e}")
                time.sleep(2)
        
        print(f"\n⏰ Analysis timeout")
        return False
    
    def crack_signature_with_entropy(self, sig_data):
        """Try to crack signature using entropy data"""
        
        signature = sig_data['signature']
        recent_entropy = sig_data.get('recentEntropy', [])
        
        print(f"   🧪 Testing {len(recent_entropy)} entropy values...")
        
        # Test each entropy value
        for entropy in recent_entropy:
            if entropy['type'] == 'crypto_random':
                entropy_hex = entropy['hex']
                
                # Test various combinations with this entropy
                test_patterns = [
                    # Just entropy
                    entropy_hex,
                    
                    # Entropy + auth
                    entropy_hex + self.auth,
                    self.auth + entropy_hex,
                    
                    # Entropy + nonce
                    entropy_hex + "1754929178532",
                    "1754929178532" + entropy_hex,
                    
                    # Entropy + auth + nonce
                    entropy_hex + self.auth + "1754929178532",
                    self.auth + entropy_hex + "1754929178532",
                    "1754929178532" + self.auth + entropy_hex,
                    
                    # Entropy + order data
                    entropy_hex + "TRU_USDT10.021",
                    "TRU_USDT10.021" + entropy_hex,
                    
                    # Complex combinations
                    self.auth + entropy_hex + "TRU_USDT10.021",
                    entropy_hex + self.auth + "TRU_USDT10.021",
                ]
                
                for pattern in test_patterns:
                    # Test MD5
                    test_sig = hashlib.md5(pattern.encode()).hexdigest()
                    if test_sig == signature:
                        print(f"🎉🎉🎉 SIGNATURE CRACKED! 🎉🎉🎉")
                        print(f"   Algorithm: MD5")
                        print(f"   Pattern: {pattern}")
                        print(f"   Entropy: {entropy_hex}")
                        return True
                    
                    # Test SHA256 (first 32 chars)
                    test_sig = hashlib.sha256(pattern.encode()).hexdigest()[:32]
                    if test_sig == signature:
                        print(f"🎉🎉🎉 SIGNATURE CRACKED! 🎉🎉🎉")
                        print(f"   Algorithm: SHA256[:32]")
                        print(f"   Pattern: {pattern}")
                        print(f"   Entropy: {entropy_hex}")
                        return True
                    
                    # Test HMAC-MD5
                    try:
                        test_sig = hmac.new(self.auth.encode(), pattern.encode(), hashlib.md5).hexdigest()
                        if test_sig == signature:
                            print(f"🎉🎉🎉 SIGNATURE CRACKED! 🎉🎉🎉")
                            print(f"   Algorithm: HMAC-MD5")
                            print(f"   Key: {self.auth}")
                            print(f"   Message: {pattern}")
                            print(f"   Entropy: {entropy_hex}")
                            return True
                    except:
                        pass
            
            elif entropy['type'] == 'math_random':
                random_val = str(entropy['value'])
                
                # Test with Math.random value
                test_patterns = [
                    self.auth + random_val,
                    random_val + self.auth,
                    random_val + "1754929178532",
                    self.auth + random_val + "1754929178532",
                ]
                
                for pattern in test_patterns:
                    test_sig = hashlib.md5(pattern.encode()).hexdigest()
                    if test_sig == signature:
                        print(f"🎉🎉🎉 MATH.RANDOM SIGNATURE CRACKED! 🎉🎉🎉")
                        print(f"   Algorithm: MD5({pattern})")
                        print(f"   Random: {random_val}")
                        return True
        
        return False
    
    def run_simple_analysis(self):
        """Run the simple analysis"""
        
        print("="*60)
        print("🎲 SIMPLE ENTROPY ANALYSIS")
        print("="*60)
        
        # Setup hooks
        if not self.setup_simple_hooks():
            return False
        
        try:
            print("\n🎯 PLACE ORDERS NOW!")
            print("   - Simple hooks are monitoring")
            print("   - No alerts will interrupt")
            print("   - Analysis happens in real-time")
            print()
            
            # Analyze captured data
            if self.analyze_captured_data():
                print("\n🎉 SIGNATURE ALGORITHM CRACKED!")
                return True
            else:
                print("\n🔍 Continue analysis or try different approaches")
                return False
            
        finally:
            # Cleanup
            try:
                if hasattr(self, 'browser'):
                    self.browser.close()
                if hasattr(self, 'playwright'):
                    self.playwright.stop()
            except:
                pass

def main():
    """Main function"""
    
    analyzer = SimpleEntropyAnalyzer()
    analyzer.run_simple_analysis()

if __name__ == '__main__':
    main()
