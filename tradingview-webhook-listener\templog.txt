OK] ✅ Panel State Monitor: Already on Open panel
[WEBHOOK] ✅ Field Cleanup Monitor: No popups found
[WEBHOOK] ✅ Field Cleanup Monitor: Quantity field is clean
[WEBHOOK] 💰 Balance updated: 2.2548 USDT
[WEBHOOK] 🗑️ Memory optimized (safe mode - no cookie clearing)
✅ Background maintenance completed - panel ready for next trade
[WEBHOOK] 🧹 Background maintenance (between trades only)...
[WEBHOOK] ✅ Panel State Monitor: Already on Open panel
[WEBHOOK] ✅ Field Cleanup Monitor: No popups found
[WEBHOOK] ✅ Field Cleanup Monitor: Quantity field is clean
[WEBHOOK] 💰 Balance updated: 2.2548 USDT
🗑️ Memory optimized (safe mode - no cookie clearing)
✅ Background maintenance completed - panel ready for next trade
[WEBHOOK] info: POST /webhook {"ip":"::ffff:************","service":"tradingview-webhook-listener","timestamp":"2025-08-17T17:33:04.057Z","userAgent":"Go-http-client/1.1"}
[WEBHOOK] info: Webhook received {"payload":{"last_price":"0.03407","symbol":"TRUUSDT","trade":"sell"},"service":"tradingview-webhook-listener","signalNumber":1,"timestamp":"2025-08-17T17:33:04.058Z"}
[WEBHOOK] 🧪 TESTING MODE: Using mock balance of 2.29 USDT
[WEBHOOK] 💰 Money Management using balance: 2.29 USDT (source: frontend)
info: Checking for existing positions to force close before opening new position {"service":"tradingview-webhook-listener","timestamp":"2025-08-17T17:33:04.400Z"}
info: No active positions to force close {"service":"tradingview-webhook-listener","timestamp":"2025-08-17T17:33:04.401Z"}
🎯 Starting trade execution (attempt 1/3): Open Short
Executing trade: Open Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Open Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🚀 Pre-execution preparation...
[WEBHOOK] ✅ Pre-execution preparation complete
[WEBHOOK] 🎯 EXECUTING OPEN SHORT...
⚡ PERFORMANCE MODE: No monitoring during execution
🔢 Filling quantity...
[WEBHOOK] ✅ Quantity filled: 1.145
📊 Clicking Open Short button...
[WEBHOOK] ✅ Open Short clicked - Trade considered DONE
⚡ Open Short completed in 1063ms
[WEBHOOK] 🏁 Trade execution completed, balance checks re-enabled
[WEBHOOK] info: Position added for monitoring {"atr":0.00004428740947489989,"entryPrice":0.03408,"positionId":"pos_1755451985742_nemhpmcfl","service":"tradingview-webhook-listener","stopLoss":0.03415971733705482,"symbol":"TRUUSDT","takeProfits":1,"timestamp":"2025-08-17T17:33:05.742Z"}
[WEBHOOK] info: Position monitoring started {"service":"tradingview-webhook-listener","timestamp":"2025-08-17T17:33:05.742Z"}
info: Position added to SL/TP monitoring {"entryPrice":0.03408,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","symbol":"TRUUSDT","timestamp":"2025-08-17T17:33:05.742Z"}
info: Trade executed {"service":"tradingview-webhook-listener","timestamp":"2025-08-17T17:33:05.743Z","totalExecutionTime":1417,"tradeRecord":{"executionTime":1417,"forceCloseResult":{"closedPositions":[],"failedPositions":[],"success":true},"id":"trade_1755451985743_5onir1mgv","positionId":"pos_1755451985742_nemhpmcfl","positionSize":1.145,"processedSignal":{"action":"open","direction":"short","leverage":1,"orderType":"Open Short","originalSignal":{"last_price":"0.03407","symbol":"TRUUSDT","trade":"sell"},"price":0.03407,"symbol":"TRUUSDT"},"signal":{"last_price":"0.03407","symbol":"TRUUSDT","trade":"sell"},"slTpEnabled":true,"success":true,"timestamp":"2025-08-17T17:33:04.058Z","tradeResult":{"attempt":1,"executionTime":1063,"maxAttempts":3,"orderType":"Open Short","port":9223,"quantity":"1.145","serviceResponse":{"executionTime":1063,"message":"Trade executed successfully via browser automation","monitoringActive":true,"orderType":"Open Short","port":9223,"quantity":"1.145","success":true,"targetAchieved":true,"timestamp":"2025-08-17T17:33:05.474Z","totalTime":1073,"verified":true},"success":true,"targetAchieved":true,"timestamp":"2025-08-17T17:33:05.474Z","totalServiceTime":1073,"tradeRequest":{"action":"open","direction":"short","leverage":1,"orderType":"Open Short","originalSignal":{"last_price":"0.03407","symbol":"TRUUSDT","trade":"sell"},"price":0.03407,"quantity":"1.145","symbol":"TRUUSDT"},"verified":true}}}
[WEBHOOK] info: Trailing stop loss activated {"currentPrice":0.03408,"positionId":"pos_1755451985742_nemhpmcfl","service":"tradingview-webhook-listener","timestamp":"2025-08-17T17:33:06.745Z","trailingStart":0.0340357125905251,"trailingStopLoss":0.03405785629526255}
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:06.747Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:07.745Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
[WEBHOOK] 🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:08.746Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:10.015Z"}
🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
[WEBHOOK] 🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:10.747Z"}
🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
[WEBHOOK] 🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:11.747Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
[WEBHOOK] 🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:12.747Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:13.748Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
[WEBHOOK] 🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:14.750Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:16.057Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:16.749Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
[WEBHOOK] 🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:17.749Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:18.750Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:19.750Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
[WEBHOOK] 🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:20.752Z"}
🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:22.117Z"}
🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
[WEBHOOK] 🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:22.753Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:23.753Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:24.753Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:25.755Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
[WEBHOOK] Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
[WEBHOOK] 🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:26.756Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:28.144Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:28.830Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:29.831Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:30.830Z"}
🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
[WEBHOOK] 🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
[WEBHOOK] 🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:31.834Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:32.834Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
[WEBHOOK] 🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:34.180Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:34.834Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
[WEBHOOK] 🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:35.834Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:36.834Z"}
🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
[WEBHOOK] 🔢 Filling quantity...
[WEBHOOK ERROR] ❌ Close Short failed: locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator('text=Quantity(USDT)').locator('xpath=following::input[1]').first()
    - locator resolved to <input value="" type="text" class="ant-input" autocomplete="off"/>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
      - waiting 100ms
    49 × waiting for element to be visible, enabled and stable
       - element is not visible
     - retrying click action
       - waiting 500ms
[WEBHOOK] ❌ Trade failed on attempt 1, retrying...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:38.014Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK ERROR] ❌ Close Short failed: locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator('text=Quantity(USDT)').locator('xpath=following::input[1]').first()
    - locator resolved to <input value="" type="text" class="ant-input" autocomplete="off"/>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
      - waiting 100ms
    49 × waiting for element to be visible, enabled and stable
       - element is not visible
     - retrying click action
       - waiting 500ms
[WEBHOOK] ❌ Trade failed on attempt 1, retrying...
[WEBHOOK] 🏁 Trade execution completed, balance checks re-enabled
[WEBHOOK] 🎯 Starting trade execution (attempt 2/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
[WEBHOOK] 🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:39.023Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
[WEBHOOK] 🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] 🏁 Trade execution completed, balance checks re-enabled
[WEBHOOK] 🎯 Starting trade execution (attempt 2/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK ERROR] ❌ Close Short failed: locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator('text=Quantity(USDT)').locator('xpath=following::input[1]').first()
    - locator resolved to <input value="" type="text" class="ant-input" autocomplete="off"/>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
      - waiting 100ms
    48 × waiting for element to be visible, enabled and stable
       - element is not visible
     - retrying click action
       - waiting 500ms
[WEBHOOK] ❌ Trade failed on attempt 1, retrying...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] 🏁 Trade execution completed, balance checks re-enabled
[WEBHOOK] 🎯 Starting trade execution (attempt 2/3): Close Short
Executing trade: Close Short with quantity 1.145
[WEBHOOK] 🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK ERROR] ❌ Close Short failed: locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator('text=Quantity(USDT)').locator('xpath=following::input[1]').first()
    - locator resolved to <input value="" type="text" class="ant-input" autocomplete="off"/>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
      - waiting 100ms
    48 × waiting for element to be visible, enabled and stable
       - element is not visible
     - retrying click action
       - waiting 500ms
[WEBHOOK] ❌ Trade failed on attempt 1, retrying...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:40.811Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:41.024Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK ERROR] ❌ Close Short failed: locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator('text=Quantity(USDT)').locator('xpath=following::input[1]').first()
    - locator resolved to <input value="" type="text" class="ant-input" autocomplete="off"/>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
      - waiting 100ms
    49 × waiting for element to be visible, enabled and stable
       - element is not visible
     - retrying click action
       - waiting 500ms
[WEBHOOK] ❌ Trade failed on attempt 1, retrying...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] 🏁 Trade execution completed, balance checks re-enabled
[WEBHOOK] 🎯 Starting trade execution (attempt 2/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:42.025Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] 🏁 Trade execution completed, balance checks re-enabled
[WEBHOOK] 🎯 Starting trade execution (attempt 2/3): Close Short
[WEBHOOK] Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK ERROR] ❌ Close Short failed: locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator('text=Quantity(USDT)').locator('xpath=following::input[1]').first()
    - locator resolved to <input value="" type="text" class="ant-input" autocomplete="off"/>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
      - waiting 100ms
    48 × waiting for element to be visible, enabled and stable
       - element is not visible
     - retrying click action
       - waiting 500ms
[WEBHOOK] ❌ Trade failed on attempt 1, retrying...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:43.025Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK ERROR] ❌ Close Short failed: locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator('text=Quantity(USDT)').locator('xpath=following::input[1]').first()
    - locator resolved to <input value="" type="text" class="ant-input" autocomplete="off"/>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
      - waiting 100ms
    48 × waiting for element to be visible, enabled and stable
       - element is not visible
     - retrying click action
       - waiting 500ms
[WEBHOOK] ❌ Trade failed on attempt 1, retrying...
[WEBHOOK] 🏁 Trade execution completed, balance checks re-enabled
[WEBHOOK] 🎯 Starting trade execution (attempt 2/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:44.026Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] 🏁 Trade execution completed, balance checks re-enabled
[WEBHOOK] 🎯 Starting trade execution (attempt 2/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK ERROR] ❌ Close Short failed: locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator('text=Quantity(USDT)').locator('xpath=following::input[1]').first()
    - locator resolved to <input value="" type="text" class="ant-input" autocomplete="off"/>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
      - waiting 100ms
    48 × waiting for element to be visible, enabled and stable
       - element is not visible
     - retrying click action
       - waiting 500ms
[WEBHOOK] ❌ Trade failed on attempt 1, retrying...
[WEBHOOK] ✅ Switched to Close panel
[WEBHOOK] 🔢 Filling quantity...
[WEBHOOK] 🧹 Background maintenance (between trades only)...
[WEBHOOK] 🔄 Panel State Monitor: Switching to Open panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:45.026Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Quantity filled: 1.145
📊 Clicking Close Short button...
[WEBHOOK] ✅ Panel State Monitor: Switched to Open panel
[WEBHOOK ERROR] ❌ Close Short failed: locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator('text=Quantity(USDT)').locator('xpath=following::input[1]').first()
    - locator resolved to <input value="" type="text" class="ant-input" autocomplete="off"/>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
      - waiting 100ms
    48 × waiting for element to be visible, enabled and stable
       - element is not visible
     - retrying click action
       - waiting 500ms
[WEBHOOK] ❌ Trade failed on attempt 1, retrying...
[WEBHOOK] 🏁 Trade execution completed, balance checks re-enabled
🎯 Starting trade execution (attempt 2/3): Close Short
Executing trade: Close Short with quantity 1.145
[WEBHOOK] 🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] ✅ Field Cleanup Monitor: No popups found
[WEBHOOK] ✅ Field Cleanup Monitor: Quantity field is clean
[WEBHOOK] 🗑️ Memory optimized (safe mode - no cookie clearing)
✅ Background maintenance completed - panel ready for next trade
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:46.345Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] 🏁 Trade execution completed, balance checks re-enabled
🎯 Starting trade execution (attempt 2/3): Close Short
[WEBHOOK] Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK ERROR] ❌ Close Short failed: locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator('text=Quantity(USDT)').locator('xpath=following::input[1]').first()
    - locator resolved to <input value="" type="text" class="ant-input" autocomplete="off"/>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
      - waiting 100ms
    48 × waiting for element to be visible, enabled and stable
       - element is not visible
     - retrying click action
       - waiting 500ms
[WEBHOOK] ❌ Trade failed on attempt 1, retrying...
[WEBHOOK] ✅ Switched to Close panel
[WEBHOOK] 🔢 Filling quantity...
[WEBHOOK] ✅ Switched to Close panel
[WEBHOOK] 🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:47.029Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK ERROR] ❌ Close Short failed: locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator('text=Quantity(USDT)').locator('xpath=following::input[1]').first()
    - locator resolved to <input value="" type="text" class="ant-input" autocomplete="off"/>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
      - waiting 100ms
    48 × waiting for element to be visible, enabled and stable
       - element is not visible
     - retrying click action
       - waiting 500ms
[WEBHOOK] ❌ Trade failed on attempt 1, retrying...
[WEBHOOK] 🏁 Trade execution completed, balance checks re-enabled
🎯 Starting trade execution (attempt 2/3): Close Short
[WEBHOOK] Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:48.030Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK ERROR] ❌ Close Short failed: locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator('text=Quantity(USDT)').locator('xpath=following::input[1]').first()
    - locator resolved to <input value="" type="text" class="ant-input" autocomplete="off"/>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
      - waiting 100ms
    48 × waiting for element to be visible, enabled and stable
       - element is not visible
     - retrying click action
       - waiting 500ms
[WEBHOOK] ❌ Trade failed on attempt 1, retrying...
[WEBHOOK] 🏁 Trade execution completed, balance checks re-enabled
🎯 Starting trade execution (attempt 2/3): Close Short
[WEBHOOK] Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
[WEBHOOK] 🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:49.031Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
[WEBHOOK] 🔢 Filling quantity...
[WEBHOOK] 🏁 Trade execution completed, balance checks re-enabled
🎯 Starting trade execution (attempt 2/3): Close Short
[WEBHOOK] Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK ERROR] ❌ Close Short failed: locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator('text=Quantity(USDT)').locator('xpath=following::input[1]').first()
    - locator resolved to <input value="" type="text" class="ant-input" autocomplete="off"/>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
      - waiting 100ms
    48 × waiting for element to be visible, enabled and stable
       - element is not visible
     - retrying click action
       - waiting 500ms
[WEBHOOK] ❌ Trade failed on attempt 1, retrying...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:50.032Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] 🏁 Trade execution completed, balance checks re-enabled
🎯 Starting trade execution (attempt 2/3): Close Short
[WEBHOOK ERROR] ❌ Close Short failed: locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator('text=Quantity(USDT)').locator('xpath=following::input[1]').first()
    - locator resolved to <input value="" type="text" class="ant-input" autocomplete="off"/>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
      - waiting 100ms
    48 × waiting for element to be visible, enabled and stable
       - element is not visible
     - retrying click action
       - waiting 500ms
[WEBHOOK] Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
❌ Trade failed on attempt 1, retrying...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:51.032Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] 🏁 Trade execution completed, balance checks re-enabled
🎯 Starting trade execution (attempt 2/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
❌ Trade failed on attempt 1, retrying...
[WEBHOOK ERROR] ❌ Close Short failed: locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator('text=Quantity(USDT)').locator('xpath=following::input[1]').first()
    - locator resolved to <input value="" type="text" class="ant-input" autocomplete="off"/>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
      - waiting 100ms
    48 × waiting for element to be visible, enabled and stable
       - element is not visible
     - retrying click action
       - waiting 500ms
    - waiting for element to be visible, enabled and stable
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] 🏁 Trade execution completed, balance checks re-enabled
[WEBHOOK] 🎯 Starting trade execution (attempt 2/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:52.346Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK ERROR] ❌ Close Short failed: locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator('text=Quantity(USDT)').locator('xpath=following::input[1]').first()
    - locator resolved to <input value="" type="text" class="ant-input" autocomplete="off"/>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
      - waiting 100ms
    49 × waiting for element to be visible, enabled and stable
       - element is not visible
     - retrying click action
       - waiting 500ms
[WEBHOOK] ❌ Trade failed on attempt 1, retrying...
[WEBHOOK] ✅ Switched to Close panel
[WEBHOOK] 🔢 Filling quantity...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:53.033Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK ERROR] ❌ Close Short failed: locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator('text=Quantity(USDT)').locator('xpath=following::input[1]').first()
    - locator resolved to <input value="" type="text" class="ant-input" autocomplete="off"/>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
      - waiting 100ms
    48 × waiting for element to be visible, enabled and stable
       - element is not visible
     - retrying click action
       - waiting 500ms
[WEBHOOK] ❌ Trade failed on attempt 1, retrying...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] 🏁 Trade execution completed, balance checks re-enabled
🎯 Starting trade execution (attempt 2/3): Close Short
[WEBHOOK] Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:54.032Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] 🏁 Trade execution completed, balance checks re-enabled
🎯 Starting trade execution (attempt 2/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK ERROR] ❌ Close Short failed: locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator('text=Quantity(USDT)').locator('xpath=following::input[1]').first()
    - locator resolved to <input value="" type="text" class="ant-input" autocomplete="off"/>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
      - waiting 100ms
    49 × waiting for element to be visible, enabled and stable
       - element is not visible
     - retrying click action
       - waiting 500ms
[WEBHOOK] ❌ Trade failed on attempt 1, retrying...
[WEBHOOK] 🧹 Background maintenance (between trades only)...
[WEBHOOK] 🔄 Panel State Monitor: Switching to Open panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:55.034Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Panel State Monitor: Switched to Open panel
[WEBHOOK ERROR] ❌ Close Short failed: locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator('text=Quantity(USDT)').locator('xpath=following::input[1]').first()
    - locator resolved to <input value="" type="text" class="ant-input" autocomplete="off"/>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not visible
    - retrying click action
      - waiting 100ms
    47 × waiting for element to be visible, enabled and stable
       - element is not visible
     - retrying click action
       - waiting 500ms
    - waiting for element to be visible, enabled and stable
[WEBHOOK] ❌ Trade failed on attempt 1, retrying...
[WEBHOOK] 🏁 Trade execution completed, balance checks re-enabled
[WEBHOOK] 🎯 Starting trade execution (attempt 2/3): Close Short
[WEBHOOK] Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
[WEBHOOK] 🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Quantity filled: 1.145
[WEBHOOK] 📊 Clicking Close Short button...
[WEBHOOK] ✅ Quantity filled: 1.145
📊 Clicking Close Short button...
[WEBHOOK] ✅ Quantity filled: 1.145
📊 Clicking Close Short button...
[WEBHOOK] ✅ Quantity filled: 1.145
📊 Clicking Close Short button...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:56.033Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] 🏁 Trade execution completed, balance checks re-enabled
🎯 Starting trade execution (attempt 2/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:57.034Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:58.359Z"}
🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] ✅ Field Cleanup Monitor: No popups found
[WEBHOOK] ✅ Field Cleanup Monitor: Quantity field is clean
[WEBHOOK] 🗑️ Memory optimized (safe mode - no cookie clearing)
✅ Background maintenance completed - panel ready for next trade
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:33:59.034Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
[WEBHOOK] 🔢 Filling quantity...
[WEBHOOK] ✅ Switched to Close panel
[WEBHOOK] 🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:34:00.034Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:34:01.036Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
[WEBHOOK] 🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:34:02.036Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
🔢 Filling quantity...
[WEBHOOK] info: Executing stop loss {"currentPrice":0.03408,"failureCount":0,"positionId":"pos_1755451985742_nemhpmcfl","quantity":1.145,"service":"tradingview-webhook-listener","stopLoss":0.03405785629526255,"timestamp":"2025-08-17T17:34:03.035Z"}
[WEBHOOK] 🎯 Starting trade execution (attempt 1/3): Close Short
Executing trade: Close Short with quantity 1.145
🚀 Using browser automation for trade execution
🎯 Browser automation: Close Short on port 9223
🚀 Using persistent trader with background monitoring
🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel
🎯 EXECUTING CLOSE SHORT...
🔄 Switching to Close panel...
[WEBHOOK] ✅ Switched to Close panel
[WEBHOOK] 🔢 Filling quantity...





















