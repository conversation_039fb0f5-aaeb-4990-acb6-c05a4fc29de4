#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Enhanced Features
=====================

Test the new enhanced features including:
- MEXC API integration
- Configuration management
- Money management settings
- Dashboard functionality
"""

import asyncio
import sys
import os
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.config import settings
from src.integrations.mexc_api import MEXCAPIClient, test_mexc_api


class EnhancedFeaturesTest:
    """Test enhanced features"""
    
    def __init__(self):
        self.test_results = {
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "test_details": [],
            "start_time": datetime.now()
        }
        
        print("Enhanced Features Test")
        print("=" * 30)
        print("Testing new enhanced features...")
        print()
    
    def log_test_result(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        self.test_results["total_tests"] += 1
        
        if success:
            self.test_results["passed_tests"] += 1
            status = "PASS"
            symbol = "✅"
        else:
            self.test_results["failed_tests"] += 1
            status = "FAIL"
            symbol = "❌"
        
        result = {
            "test_name": test_name,
            "status": status,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        
        self.test_results["test_details"].append(result)
        
        print(f"{symbol} {test_name}: {status}")
        if details:
            print(f"   Details: {details}")
        print()
    
    def test_configuration_loading(self) -> bool:
        """Test configuration loading"""
        try:
            # Test basic configuration
            success = (
                hasattr(settings, 'BOT_ENABLED') and
                hasattr(settings, 'TRADING_SYMBOL') and
                hasattr(settings, 'LEVERAGE') and
                hasattr(settings, 'POSITION_SIZE_TYPE') and
                hasattr(settings, 'MEXC_API_KEY') and
                hasattr(settings, 'MEXC_API_SECRET')
            )
            
            details = f"Bot enabled: {settings.BOT_ENABLED}, Trading symbol: {settings.TRADING_SYMBOL}"
            
            self.log_test_result(
                "Configuration Loading",
                success,
                details
            )
            
            return success
            
        except Exception as e:
            self.log_test_result(
                "Configuration Loading",
                False,
                f"Error: {str(e)}"
            )
            return False
    
    def test_money_management_settings(self) -> bool:
        """Test money management settings"""
        try:
            # Test money management configuration
            success = (
                settings.POSITION_SIZE_TYPE in ["percentage", "fixed"] and
                0 <= settings.POSITION_SIZE_PERCENTAGE <= 100 and
                settings.POSITION_SIZE_FIXED >= 0 and
                settings.MAX_POSITION_AMOUNT >= 0 and
                1 <= settings.LEVERAGE <= 100
            )
            
            details = (
                f"Position size type: {settings.POSITION_SIZE_TYPE}, "
                f"Percentage: {settings.POSITION_SIZE_PERCENTAGE}%, "
                f"Fixed: {settings.POSITION_SIZE_FIXED} USDT, "
                f"Max: {settings.MAX_POSITION_AMOUNT} USDT, "
                f"Leverage: {settings.LEVERAGE}x"
            )
            
            self.log_test_result(
                "Money Management Settings",
                success,
                details
            )
            
            return success
            
        except Exception as e:
            self.log_test_result(
                "Money Management Settings",
                False,
                f"Error: {str(e)}"
            )
            return False
    
    async def test_mexc_api_client_creation(self) -> bool:
        """Test MEXC API client creation"""
        try:
            # Test creating MEXC API client
            client = MEXCAPIClient()
            
            success = client is not None
            
            # Check if credentials are configured
            credentials_configured = bool(client.api_key and client.api_secret)
            
            details = f"Client created: {success}, Credentials configured: {credentials_configured}"
            
            self.log_test_result(
                "MEXC API Client Creation",
                success,
                details
            )
            
            return success
            
        except Exception as e:
            self.log_test_result(
                "MEXC API Client Creation",
                False,
                f"Error: {str(e)}"
            )
            return False
    
    async def test_mexc_api_connectivity(self) -> bool:
        """Test MEXC API connectivity (if credentials available)"""
        try:
            if not settings.MEXC_API_KEY or not settings.MEXC_API_SECRET:
                self.log_test_result(
                    "MEXC API Connectivity",
                    True,
                    "Skipped - No API credentials configured"
                )
                return True
            
            # Test API connectivity
            result = await test_mexc_api()
            
            success = result.get('status') == 'connected'
            
            details = f"Status: {result.get('status')}, Public API: {result.get('public_api')}, Private API: {result.get('private_api')}"
            
            self.log_test_result(
                "MEXC API Connectivity",
                success,
                details
            )
            
            return success
            
        except Exception as e:
            self.log_test_result(
                "MEXC API Connectivity",
                False,
                f"Error: {str(e)}"
            )
            return False
    
    def test_bot_control_settings(self) -> bool:
        """Test bot control settings"""
        try:
            # Test bot control
            success = hasattr(settings, 'BOT_ENABLED')
            
            details = f"Bot enabled: {settings.BOT_ENABLED}"
            
            self.log_test_result(
                "Bot Control Settings",
                success,
                details
            )
            
            return success
            
        except Exception as e:
            self.log_test_result(
                "Bot Control Settings",
                False,
                f"Error: {str(e)}"
            )
            return False
    
    def test_supported_symbols(self) -> bool:
        """Test supported symbols configuration"""
        try:
            # Test supported symbols
            success = (
                hasattr(settings, 'SUPPORTED_SYMBOLS') and
                isinstance(settings.SUPPORTED_SYMBOLS, list) and
                len(settings.SUPPORTED_SYMBOLS) > 0 and
                settings.TRADING_SYMBOL in settings.SUPPORTED_SYMBOLS
            )
            
            details = f"Supported symbols: {settings.SUPPORTED_SYMBOLS}, Trading symbol: {settings.TRADING_SYMBOL}"
            
            self.log_test_result(
                "Supported Symbols Configuration",
                success,
                details
            )
            
            return success
            
        except Exception as e:
            self.log_test_result(
                "Supported Symbols Configuration",
                False,
                f"Error: {str(e)}"
            )
            return False
    
    async def run_all_tests(self):
        """Run all enhanced feature tests"""
        print("Starting Enhanced Features Tests...")
        print()
        
        # Test 1: Configuration Loading
        config_success = self.test_configuration_loading()
        
        # Test 2: Money Management Settings
        mm_success = self.test_money_management_settings()
        
        # Test 3: MEXC API Client Creation
        api_client_success = await self.test_mexc_api_client_creation()
        
        # Test 4: MEXC API Connectivity
        api_connectivity_success = await self.test_mexc_api_connectivity()
        
        # Test 5: Bot Control Settings
        bot_control_success = self.test_bot_control_settings()
        
        # Test 6: Supported Symbols
        symbols_success = self.test_supported_symbols()
        
        # Calculate results
        self.test_results["end_time"] = datetime.now()
        duration = (self.test_results["end_time"] - self.test_results["start_time"]).total_seconds()
        
        success_rate = 0
        if self.test_results["total_tests"] > 0:
            success_rate = (self.test_results["passed_tests"] / self.test_results["total_tests"]) * 100
        
        # Print summary
        print("=" * 30)
        print("ENHANCED FEATURES TEST RESULTS")
        print("=" * 30)
        print(f"Total Tests: {self.test_results['total_tests']}")
        print(f"Passed: {self.test_results['passed_tests']}")
        print(f"Failed: {self.test_results['failed_tests']}")
        print(f"Success Rate: {success_rate:.1f}%")
        print(f"Duration: {duration:.2f}s")
        print()
        
        if success_rate == 100:
            print("🎉 ALL ENHANCED FEATURES WORKING!")
            print("✅ Configuration management ready")
            print("✅ Money management settings configured")
            print("✅ MEXC API integration ready")
            print("✅ Bot control functional")
        elif success_rate >= 80:
            print("⚠️  Most features working, minor issues detected")
        else:
            print("❌ Multiple feature issues detected")
        
        return self.test_results


async def main():
    """Main test function"""
    test_runner = EnhancedFeaturesTest()
    results = await test_runner.run_all_tests()
    
    # Return appropriate exit code
    if results["failed_tests"] == 0:
        return 0  # Success
    else:
        return 1  # Failure


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
