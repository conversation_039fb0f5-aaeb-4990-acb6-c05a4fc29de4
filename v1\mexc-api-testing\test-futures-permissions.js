const ccxt = require('ccxt');
const axios = require('axios');
const crypto = require('crypto');
require('dotenv').config();

class MexcFuturesAPI {
    constructor(apiKey, apiSecret) {
        this.apiKey = apiKey;
        this.apiSecret = apiSecret;
        this.baseURL = 'https://contract.mexc.com';
    }

    generateSignature(queryString) {
        return crypto
            .createHmac('sha256', this.apiSecret)
            .update(queryString)
            .digest('hex');
    }

    async makeRequest(method, endpoint, params = {}) {
        const timestamp = Date.now();
        params.timestamp = timestamp;
        
        const queryString = Object.keys(params)
            .sort()
            .map(key => `${key}=${params[key]}`)
            .join('&');
        
        const signature = this.generateSignature(queryString);
        params.signature = signature;
        
        const config = {
            method,
            url: `${this.baseURL}${endpoint}`,
            headers: {
                'X-MEXC-APIKEY': this.apiKey,
                'Content-Type': 'application/json'
            }
        };

        if (method === 'GET') {
            config.params = params;
        } else {
            config.data = params;
        }

        return axios(config);
    }

    async testEndpoint(endpoint, description) {
        try {
            const response = await this.makeRequest('GET', endpoint);
            return { success: true, endpoint, description, data: response.data };
        } catch (error) {
            return { 
                success: false, 
                endpoint, 
                description, 
                error: error.response?.data || error.message 
            };
        }
    }
}

async function testFuturesPermissions() {
    console.log('🔍 Testing MEXC Futures API Permissions');
    console.log('=====================================');
    console.log(`API Key: ${process.env.MEXC_API_KEY?.substring(0, 10)}...`);
    console.log('');

    const client = new MexcFuturesAPI(
        process.env.MEXC_API_KEY,
        process.env.MEXC_API_SECRET
    );

    const tests = [
        {
            endpoint: '/api/v1/private/account/assets',
            description: 'Account Assets (Futures Balance)'
        },
        {
            endpoint: '/api/v1/private/position/open_positions',
            description: 'Open Positions'
        },
        {
            endpoint: '/api/v1/private/position/list/history_positions',
            description: 'Position History'
        },
        {
            endpoint: '/api/v1/private/order/list/open_orders',
            description: 'Open Orders'
        },
        {
            endpoint: '/api/v1/private/order/list/history_orders',
            description: 'Order History'
        },
        {
            endpoint: '/api/v1/private/account/risk_limit',
            description: 'Risk Limits'
        }
    ];

    const results = [];

    for (const test of tests) {
        console.log(`Testing: ${test.description}...`);
        const result = await client.testEndpoint(test.endpoint, test.description);
        results.push(result);
        
        if (result.success) {
            console.log(`✅ ${test.description} - SUCCESS`);
            if (result.data) {
                console.log(`   Data: ${JSON.stringify(result.data).substring(0, 100)}...`);
            }
        } else {
            console.log(`❌ ${test.description} - FAILED`);
            console.log(`   Error: ${JSON.stringify(result.error)}`);
        }
        
        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Test CCXT futures capabilities
    console.log('\n🔍 Testing CCXT Futures Capabilities');
    console.log('====================================');

    try {
        const exchange = new ccxt.mexc({
            apiKey: process.env.MEXC_API_KEY,
            secret: process.env.MEXC_API_SECRET,
            sandbox: false,
            enableRateLimit: true,
        });

        await exchange.loadMarkets();
        console.log('✅ CCXT markets loaded');

        // Test different market types
        const spotMarkets = Object.values(exchange.markets).filter(m => m.spot);
        const futuresMarkets = Object.values(exchange.markets).filter(m => m.swap || m.future);
        
        console.log(`   Spot markets: ${spotMarkets.length}`);
        console.log(`   Futures markets: ${futuresMarkets.length}`);

        // Test balance with different options
        try {
            const balance = await exchange.fetchBalance();
            console.log('✅ Default balance fetch - SUCCESS');
            console.log(`   Currencies: ${Object.keys(balance.total).length}`);
        } catch (error) {
            console.log('❌ Default balance fetch - FAILED:', error.message);
        }

        // Test futures-specific balance
        try {
            const futuresBalance = await exchange.fetchBalance({ type: 'swap' });
            console.log('✅ Futures balance fetch - SUCCESS');
            console.log(`   Futures currencies: ${Object.keys(futuresBalance.total).length}`);
        } catch (error) {
            console.log('❌ Futures balance fetch - FAILED:', error.message);
        }

        // Test positions
        try {
            const positions = await exchange.fetchPositions();
            console.log('✅ Positions fetch - SUCCESS');
            console.log(`   Positions: ${positions.length}`);
        } catch (error) {
            console.log('❌ Positions fetch - FAILED:', error.message);
        }

    } catch (error) {
        console.log('❌ CCXT initialization failed:', error.message);
    }

    // Generate summary
    console.log('\n📊 PERMISSION TEST SUMMARY');
    console.log('==========================');
    
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    
    console.log(`✅ Successful endpoints: ${successful.length}/${results.length}`);
    console.log(`❌ Failed endpoints: ${failed.length}/${results.length}`);
    
    if (successful.length > 0) {
        console.log('\n✅ WORKING ENDPOINTS:');
        successful.forEach(result => {
            console.log(`   • ${result.description}`);
        });
    }
    
    if (failed.length > 0) {
        console.log('\n❌ FAILED ENDPOINTS:');
        failed.forEach(result => {
            console.log(`   • ${result.description}`);
            if (result.error.code) {
                console.log(`     Code: ${result.error.code}, Message: ${result.error.message}`);
            }
        });
    }

    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    
    if (successful.length === 0) {
        console.log('   🚨 No futures endpoints accessible');
        console.log('   📝 Check if:');
        console.log('      - Futures trading is enabled on your MEXC account');
        console.log('      - API key has futures trading permissions');
        console.log('      - Account has completed futures trading verification');
        console.log('      - IP restrictions are properly configured');
    } else if (successful.length < results.length) {
        console.log('   ⚠️  Partial futures access detected');
        console.log('   📝 Some endpoints work, others don\'t');
        console.log('   🔧 You may be able to place orders with working endpoints');
    } else {
        console.log('   🎉 Full futures API access confirmed!');
        console.log('   ✅ You should be able to place futures orders');
    }

    // Save report
    const report = {
        timestamp: new Date().toISOString(),
        summary: {
            total: results.length,
            successful: successful.length,
            failed: failed.length
        },
        results: results,
        recommendations: successful.length > 0 ? 'futures_possible' : 'futures_blocked'
    };

    require('fs').writeFileSync(
        'futures-permissions-report.json',
        JSON.stringify(report, null, 2)
    );

    console.log('\n📄 Detailed report saved to: futures-permissions-report.json');
    
    return report;
}

if (require.main === module) {
    testFuturesPermissions()
        .then(report => {
            process.exit(report.summary.successful > 0 ? 0 : 1);
        })
        .catch(error => {
            console.error('Permission test failed:', error);
            process.exit(1);
        });
}

module.exports = testFuturesPermissions;
