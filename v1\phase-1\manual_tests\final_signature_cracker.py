#!/usr/bin/env python3
"""
FINAL SIGNATURE CRACKER
Based on analysis: p0 and k0 are encrypted/signed data that's part of signature generation
"""

import json
import hashlib
import hmac
import base64
import time
from curl_cffi import requests
from dotenv import dotenv_values

class FinalSignatureCracker:
    """Final approach - use the captured p0/k0 data to crack the signature"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        self.session = requests.Session(impersonate='chrome124')
        
        print("🎯 FINAL SIGNATURE CRACKER")
        print("="*35)
        print("🔥 USING CAPTURED P0/K0 DATA")
    
    def test_with_real_captured_data(self):
        """Test signature generation with real captured p0/k0 data"""
        
        print("\n🧪 TESTING WITH REAL CAPTURED DATA")
        print("="*45)
        
        # Real captured data from our advanced capture
        captured_data = [
            {
                "signature": "1ed499f829cd58b0473709cbb4b44619",
                "nonce": "1754927012577",
                "auth": self.auth,
                "symbol": "TRU_USDT",
                "side": 1,
                "price": "0.02",
                "vol": 1,
                "ts": 1754927013963,
                "chash": "d6c64d28e362f314071b3f9d78ff7494d9cd7177ae0465e772d1840e9f7905d8",
                "mhash": "85723e9fb269ff0e1e19525050842a3c",
                "mtoken": "b03MOmeXoiZid75ogtwP",
                "p0": "AfP1RrE1NFB0xVGUVu4dE3EswEH+qf4/xUt8zYUiTyq04zSDWVpLDa7JgyZednLiCWt757ygSTT2cCzuY8KYcomQwg3FjT+4nFEP4tA9N0UCTUrZgknvkt1el9gEh55cK5HmBXiG8G+kX20octO8t/YYN8zhElhbfnkI+TfJ8JSBOtP93aF73gAWIyVWca1nhWKM56VGBtLTNYB9+A/n4dGvgQvUJF+eD0ajjQDyfp1xvC5vvaxf4AaY2w1kOF45+MN/xGbl8G9b+8vW/eDphB/+t4H+oYIpTO8Y5kUPdXVdPU+BYWlASP1nQoCr8lWJZuzdyFwDhYzYZI=",
                "k0": "bd8w0vzDjkwcrqu0/WWrvB4Ce2dBc1V7Ct/jbwwH7yfuj1tdamKljm/j6m8yclru510t1gNrL/Cn59vUQMBi//UtWZ5WS9CMJeCt8auua0Qf1ANVmYFFcUJ1xdrvN8qCvMFAsRJ+hH98asX7k7E3COa2JJezLt9Y11VC3YisWtoUMOn2V/Z8NhKSUwnt7rrDD5h0I9McniEqDyqHN/KMuuOG8x2X5f4rq3hLB9gt509ilGEUJlYmWtV2ulC6W0JGBrlK6i83TqaweLWXm4ch463PAlMpc+Iyhq9fCdC1a+mRKlpltn7tIYFUsMNe+fqMtzYuD/HB2lBmOO6fRp/uQyexw=="
            },
            {
                "signature": "99aa050ac9852cf2bae033964204ec23",
                "nonce": "1754927019615",
                "auth": self.auth,
                "symbol": "TRU_USDT",
                "side": 1,
                "price": "0.02",
                "vol": 1,
                "ts": 1754927020959,
                "chash": "d6c64d28e362f314071b3f9d78ff7494d9cd7177ae0465e772d1840e9f7905d8",
                "mhash": "85723e9fb269ff0e1e19525050842a3c",
                "mtoken": "b03MOmeXoiZid75ogtwP",
                "p0": "AfP1RrE1NFB0xVGUVu4dE3EswEH+qf4/xUt8zYUiTyq04zSDWVpLDa7JgyZednLiCWt757ygSTT2cCzuY8KYcomQwg3FjT+4nFEP4tA9N0UCTUrZgknvkt1el9gEh55cK5HmBXiG8G+kX20octO8t/YYN8zhElhbfnkI+TfJ8JSBOtP93aF73gAWIyVWca1nhWKM56VGBtLTNYB9+A/n4dGvgQvUJF+eD0ajjQDyfp1xvC5vvaxf4AaY2w1kOF45+MN/xGbl8G9b+8vW/eDphB/+t4H+oYIpTO8Y5kUPdXVdPU+BYWlASP1nQoCr8lWJZuzdyFwDhYzYZI=",
                "k0": "bd8w0vzDjkwcrqu0/WWrvB4Ce2dBc1V7Ct/jbwwH7yfuj1tdamKljm/j6m8yclru510t1gNrL/Cn59vUQMBi//UtWZ5WS9CMJeCt8auua0Qf1ANVmYFFcUJ1xdrvN8qCvMFAsRJ+hH98asX7k7E3COa2JJezLt9Y11VC3YisWtoUMOn2V/Z8NhKSUwnt7rrDD5h0I9McniEqDyqHN/KMuuOG8x2X5f4rq3hLB9gt509ilGEUJlYmWtV2ulC6W0JGBrlK6i83TqaweLWXm4ch463PAlMpc+Iyhq9fCdC1a+mRKlpltn7tIYFUsMNe+fqMtzYuD/HB2lBmOO6fRp/uQyexw=="
            }
        ]
        
        for i, data in enumerate(captured_data):
            print(f"\n🔍 Testing capture #{i+1}: {data['signature']}")
            
            # Create the complete order data as it was sent
            complete_order = {
                "symbol": data["symbol"],
                "side": data["side"],
                "openType": 1,
                "type": "2",
                "vol": data["vol"],
                "leverage": 1,
                "marketCeiling": False,
                "price": data["price"],
                "priceProtect": "0",
                "p0": data["p0"],
                "k0": data["k0"],
                "chash": data["chash"],
                "mtoken": data["mtoken"],
                "ts": data["ts"],
                "mhash": data["mhash"]
            }
            
            # Test signature generation with complete data
            if self.test_signature_with_complete_data(data, complete_order):
                return True
        
        return False
    
    def test_signature_with_complete_data(self, data, complete_order):
        """Test signature generation with complete order data including p0/k0"""
        
        target_sig = data['signature']
        nonce = data['nonce']
        auth = data['auth']
        
        print(f"   Target: {target_sig}")
        print(f"   Nonce: {nonce}")
        
        # Test different JSON serializations of complete order
        json_variants = [
            json.dumps(complete_order, separators=(',', ':')),
            json.dumps(complete_order, separators=(',', ':'), sort_keys=True),
            json.dumps(complete_order),
            json.dumps(complete_order, sort_keys=True),
        ]
        
        print(f"   Testing {len(json_variants)} JSON variants with complete data...")
        
        for j, json_str in enumerate(json_variants):
            print(f"      JSON variant {j+1}: {len(json_str)} chars")
            
            # Test different input combinations
            test_combinations = [
                # Basic combinations
                f"{auth}{nonce}{json_str}",
                f"{nonce}{json_str}{auth}",
                f"{json_str}{nonce}{auth}",
                f"{auth}{json_str}{nonce}",
                f"{nonce}{auth}{json_str}",
                f"{json_str}{auth}{nonce}",
                
                # Without p0/k0 (maybe they're added after signature)
                f"{auth}{nonce}",
                f"{nonce}{auth}",
                
                # With specific components
                f"{auth}{nonce}{data['chash']}",
                f"{nonce}{data['chash']}{auth}",
                f"{auth}{nonce}{data['mhash']}",
                f"{nonce}{data['mhash']}{auth}",
                f"{auth}{nonce}{data['mtoken']}",
                f"{nonce}{data['mtoken']}{auth}",
                
                # Complex combinations
                f"{auth}{nonce}{data['symbol']}{data['side']}{data['price']}{data['vol']}",
                f"{nonce}{data['symbol']}{data['side']}{data['price']}{data['vol']}{auth}",
                f"{auth}{nonce}{data['ts']}",
                f"{nonce}{data['ts']}{auth}",
                
                # URL-like format
                f"symbol={data['symbol']}&side={data['side']}&price={data['price']}&vol={data['vol']}&nonce={nonce}&auth={auth}",
                
                # With separators
                f"{auth}|{nonce}|{json_str}",
                f"{nonce}|{json_str}|{auth}",
                f"{auth}&{nonce}&{json_str}",
                f"{nonce}&{json_str}&{auth}",
            ]
            
            for k, test_input in enumerate(test_combinations):
                # Test MD5
                result = hashlib.md5(test_input.encode()).hexdigest()
                if result == target_sig:
                    print(f"🎉 SIGNATURE CRACKED! MD5")
                    print(f"   JSON variant: {j+1}")
                    print(f"   Combination: {k+1}")
                    print(f"   Input: {test_input[:100]}...")
                    print(f"   Full input: {test_input}")
                    return True
                
                # Test SHA1 (first 32 chars)
                result = hashlib.sha1(test_input.encode()).hexdigest()[:32]
                if result == target_sig:
                    print(f"🎉 SIGNATURE CRACKED! SHA1[:32]")
                    print(f"   JSON variant: {j+1}")
                    print(f"   Combination: {k+1}")
                    print(f"   Input: {test_input[:100]}...")
                    print(f"   Full input: {test_input}")
                    return True
                
                # Test SHA256 (first 32 chars)
                result = hashlib.sha256(test_input.encode()).hexdigest()[:32]
                if result == target_sig:
                    print(f"🎉 SIGNATURE CRACKED! SHA256[:32]")
                    print(f"   JSON variant: {j+1}")
                    print(f"   Combination: {k+1}")
                    print(f"   Input: {test_input[:100]}...")
                    print(f"   Full input: {test_input}")
                    return True
                
                # Test HMAC variants
                hmac_keys = [auth, nonce, data['mtoken'], data['chash']]
                for hmac_key in hmac_keys:
                    if not hmac_key:
                        continue
                    
                    try:
                        # HMAC-MD5
                        result = hmac.new(hmac_key.encode(), test_input.encode(), hashlib.md5).hexdigest()
                        if result == target_sig:
                            print(f"🎉 SIGNATURE CRACKED! HMAC-MD5")
                            print(f"   Key: {hmac_key[:20]}...")
                            print(f"   Message: {test_input[:100]}...")
                            print(f"   Full input: {test_input}")
                            return True
                        
                        # HMAC-SHA1 (first 32 chars)
                        result = hmac.new(hmac_key.encode(), test_input.encode(), hashlib.sha1).hexdigest()[:32]
                        if result == target_sig:
                            print(f"🎉 SIGNATURE CRACKED! HMAC-SHA1[:32]")
                            print(f"   Key: {hmac_key[:20]}...")
                            print(f"   Message: {test_input[:100]}...")
                            print(f"   Full input: {test_input}")
                            return True
                        
                        # HMAC-SHA256 (first 32 chars)
                        result = hmac.new(hmac_key.encode(), test_input.encode(), hashlib.sha256).hexdigest()[:32]
                        if result == target_sig:
                            print(f"🎉 SIGNATURE CRACKED! HMAC-SHA256[:32]")
                            print(f"   Key: {hmac_key[:20]}...")
                            print(f"   Message: {test_input[:100]}...")
                            print(f"   Full input: {test_input}")
                            return True
                    except:
                        continue
        
        print(f"   ❌ Failed to crack this signature")
        return False
    
    def test_order_with_captured_signature(self):
        """Test placing an order with a captured signature"""
        
        print("\n🚀 TESTING ORDER WITH CAPTURED SIGNATURE")
        print("="*50)
        
        # Use the first captured signature
        captured = {
            "signature": "1ed499f829cd58b0473709cbb4b44619",
            "nonce": "1754927012577",
            "mtoken": "b03MOmeXoiZid75ogtwP",
            "order_data": {
                "symbol": "BTC_USDT",  # Change to BTC for testing
                "side": 1,
                "openType": 1,
                "type": "2",
                "vol": 1,
                "leverage": 1,
                "marketCeiling": False,
                "price": "1000.0",  # Very low price
                "priceProtect": "0",
                "p0": "AfP1RrE1NFB0xVGUVu4dE3EswEH+qf4/xUt8zYUiTyq04zSDWVpLDa7JgyZednLiCWt757ygSTT2cCzuY8KYcomQwg3FjT+4nFEP4tA9N0UCTUrZgknvkt1el9gEh55cK5HmBXiG8G+kX20octO8t/YYN8zhElhbfnkI+TfJ8JSBOtP93aF73gAWIyVWca1nhWKM56VGBtLTNYB9+A/n4dGvgQvUJF+eD0ajjQDyfp1xvC5vvaxf4AaY2w1kOF45+MN/xGbl8G9b+8vW/eDphB/+t4H+oYIpTO8Y5kUPdXVdPU+BYWlASP1nQoCr8lWJZuzdyFwDhYzYZI=",
                "k0": "bd8w0vzDjkwcrqu0/WWrvB4Ce2dBc1V7Ct/jbwwH7yfuj1tdamKljm/j6m8yclru510t1gNrL/Cn59vUQMBi//UtWZ5WS9CMJeCt8auua0Qf1ANVmYFFcUJ1xdrvN8qCvMFAsRJ+hH98asX7k7E3COa2JJezLt9Y11VC3YisWtoUMOn2V/Z8NhKSUwnt7rrDD5h0I9McniEqDyqHN/KMuuOG8x2X5f4rq3hLB9gt509ilGEUJlYmWtV2ulC6W0JGBrlK6i83TqaweLWXm4ch463PAlMpc+Iyhq9fCdC1a+mRKlpltn7tIYFUsMNe+fqMtzYuD/HB2lBmOO6fRp/uQyexw==",
                "chash": "d6c64d28e362f314071b3f9d78ff7494d9cd7177ae0465e772d1840e9f7905d8",
                "mtoken": "b03MOmeXoiZid75ogtwP",
                "ts": int(time.time() * 1000),
                "mhash": "85723e9fb269ff0e1e19525050842a3c"
            }
        }
        
        # Prepare headers
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Origin': 'https://futures.mexc.com',
            'Referer': 'https://futures.mexc.com/exchange/BTC_USDT',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Authorization': self.auth,
            'mtoken': captured['mtoken'],
            'x-mxc-sign': captured['signature'],
            'x-mxc-nonce': captured['nonce'],
            'x-language': 'en_US',
        }
        
        try:
            url = f'https://futures.mexc.com/api/v1/private/order/create?mhash={captured["order_data"]["mhash"]}'
            
            r = self.session.post(url, json=captured['order_data'], headers=headers)
            
            print(f"Response status: {r.status_code}")
            
            if r.status_code == 200:
                result = r.json()
                print(f"Response: {json.dumps(result, indent=2)}")
                
                if result.get('success') and result.get('code') == 0:
                    print("🎉 ORDER PLACED WITH CAPTURED SIGNATURE!")
                    return True
                else:
                    error_code = result.get('code')
                    error_msg = result.get('message', '')
                    print(f"❌ Order failed: {error_code} - {error_msg}")
                    
                    if error_code == 602:
                        print("🔍 Signature verification still failing")
                    
                    return False
            else:
                print(f"❌ HTTP error: {r.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Request error: {e}")
            return False
    
    def run_final_crack(self):
        """Run the final cracking attempt"""
        
        print("="*60)
        print("🔥 FINAL SIGNATURE CRACKING ATTEMPT 🔥")
        print("="*60)
        
        # Test with real captured data
        if self.test_with_real_captured_data():
            print("\n🎉 SIGNATURE ALGORITHM CRACKED!")
            
            # Test placing an order
            if self.test_order_with_captured_signature():
                print("\n🚀 ORDER EXECUTION SUCCESSFUL!")
                return True
            else:
                print("\n🔧 Signature cracked but order execution needs work")
                return True
        else:
            print("\n❌ Signature algorithm still not cracked")
            print("🔍 The algorithm may involve:")
            print("   - Server-side components")
            print("   - Hardware-based signing")
            print("   - Complex multi-step encryption")
            print("   - Time-based tokens")
            return False

def main():
    """Main function"""
    
    cracker = FinalSignatureCracker()
    success = cracker.run_final_crack()
    
    if success:
        print("\n🎯 MISSION ACCOMPLISHED!")
        print("Signature algorithm understanding achieved!")
    else:
        print("\n⚔️ BATTLE CONTINUES...")
        print("More advanced techniques needed!")

if __name__ == '__main__':
    main()
