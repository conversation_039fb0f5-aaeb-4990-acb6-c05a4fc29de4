const axios = require('axios');

async function debugBalanceIssue() {
    console.log('🔍 DEBUGGING BALANCE ISSUE');
    console.log('===========================');
    console.log('Expected: 2.29 USDT balance');
    console.log('Current: Panel shows 0 USDT');
    console.log('');

    try {
        // Test 1: Check service health
        console.log('1️⃣ SERVICE HEALTH CHECK');
        console.log('========================');
        
        try {
            const mexcHealth = await axios.get('http://localhost:3000/health');
            console.log(`✅ MEXC Trader: ${mexcHealth.data.status}`);
        } catch (error) {
            console.log(`❌ MEXC Trader: ${error.message}`);
            return;
        }

        try {
            const webhookHealth = await axios.get('http://localhost:4000/health');
            console.log(`✅ Webhook Listener: ${webhookHealth.data.status}`);
        } catch (error) {
            console.log(`❌ Webhook Listener: ${error.message}`);
            return;
        }

        // Test 2: Test MEXC Trader balance endpoint directly
        console.log('\n2️⃣ MEXC TRADER BALANCE TEST');
        console.log('============================');
        
        try {
            const mexcBalance = await axios.get('http://localhost:3000/balance');
            console.log(`MEXC Trader Balance Response:`);
            console.log(`   Success: ${mexcBalance.data.success}`);
            console.log(`   Balance: ${mexcBalance.data.balance} ${mexcBalance.data.currency}`);
            console.log(`   Raw Text: "${mexcBalance.data.raw}"`);
            console.log(`   Selector: ${mexcBalance.data.selector}`);
            console.log(`   Cached: ${mexcBalance.data.cached}`);
            
            if (!mexcBalance.data.success) {
                console.log(`   ❌ Error: ${mexcBalance.data.error}`);
            }
        } catch (error) {
            console.log(`❌ MEXC Trader balance failed: ${error.message}`);
        }

        // Test 3: Test webhook listener balance endpoint
        console.log('\n3️⃣ WEBHOOK LISTENER BALANCE TEST');
        console.log('=================================');
        
        try {
            const webhookBalance = await axios.get('http://localhost:4000/api/balance');
            console.log(`Webhook Listener Balance Response:`);
            console.log(`   Success: ${webhookBalance.data.success}`);
            console.log(`   Balance: ${webhookBalance.data.balance.total} ${webhookBalance.data.balance.asset}`);
            console.log(`   Source: ${webhookBalance.data.source}`);
            console.log(`   Raw Text: "${webhookBalance.data.balance.raw}"`);
            console.log(`   Cached: ${webhookBalance.data.balance.cached}`);
            
            if (webhookBalance.data.source === 'api') {
                console.log('   ⚠️ Using API balance (may be 0)');
            } else if (webhookBalance.data.source === 'frontend') {
                console.log('   ✅ Using frontend balance (should be accurate)');
            }
        } catch (error) {
            console.log(`❌ Webhook listener balance failed: ${error.message}`);
        }

        // Test 4: Force refresh balance
        console.log('\n4️⃣ FORCE REFRESH BALANCE TEST');
        console.log('==============================');
        
        try {
            const refreshBalance = await axios.get('http://localhost:4000/api/balance?refresh=true');
            console.log(`Force Refresh Balance Response:`);
            console.log(`   Success: ${refreshBalance.data.success}`);
            console.log(`   Balance: ${refreshBalance.data.balance.total} ${refreshBalance.data.balance.asset}`);
            console.log(`   Source: ${refreshBalance.data.source}`);
            console.log(`   Raw Text: "${refreshBalance.data.balance.raw}"`);
            console.log(`   Cached: ${refreshBalance.data.balance.cached}`);
            
            if (refreshBalance.data.balance.total > 0) {
                console.log('   ✅ NON-ZERO BALANCE FOUND!');
            } else {
                console.log('   ❌ Still showing 0 balance');
            }
        } catch (error) {
            console.log(`❌ Force refresh failed: ${error.message}`);
        }

        // Test 5: Check system status
        console.log('\n5️⃣ SYSTEM STATUS CHECK');
        console.log('=======================');
        
        try {
            const status = await axios.get('http://localhost:4000/api/status');
            console.log(`System Status:`);
            console.log(`   Bot Active: ${status.data.botActive}`);
            console.log(`   MEXC Connected: ${status.data.mexcConnected}`);
            console.log(`   Balance: ${status.data.balance.total} ${status.data.balance.asset}`);
            console.log(`   Balance Source: ${status.data.balance.source}`);
            console.log(`   Balance Raw: "${status.data.balance.raw}"`);
            
            if (status.data.balance.source === 'frontend' && status.data.balance.total > 0) {
                console.log('   ✅ Frontend balance working in status!');
            } else if (status.data.balance.source === 'api' && status.data.balance.total === 0) {
                console.log('   ⚠️ Status using API balance (0), frontend fallback may not be working');
            }
        } catch (error) {
            console.log(`❌ Status check failed: ${error.message}`);
        }

        // Test 6: Direct MEXC trader balance with force refresh
        console.log('\n6️⃣ MEXC TRADER FORCE REFRESH TEST');
        console.log('==================================');
        
        try {
            const mexcRefresh = await axios.get('http://localhost:3000/balance?refresh=true');
            console.log(`MEXC Trader Force Refresh:`);
            console.log(`   Success: ${mexcRefresh.data.success}`);
            console.log(`   Balance: ${mexcRefresh.data.balance} ${mexcRefresh.data.currency}`);
            console.log(`   Raw Text: "${mexcRefresh.data.raw}"`);
            console.log(`   Selector: ${mexcRefresh.data.selector}`);
            
            if (mexcRefresh.data.success && mexcRefresh.data.balance > 0) {
                console.log('   ✅ MEXC Trader can fetch balance from frontend!');
            } else {
                console.log('   ❌ MEXC Trader cannot fetch balance from frontend');
                console.log(`   Error: ${mexcRefresh.data.error}`);
            }
        } catch (error) {
            console.log(`❌ MEXC Trader force refresh failed: ${error.message}`);
        }

        console.log('\n🔧 DIAGNOSIS & RECOMMENDATIONS');
        console.log('===============================');
        console.log('Based on the test results above:');
        console.log('');
        console.log('If MEXC Trader balance endpoint shows 0:');
        console.log('  - Browser may not be connected to MEXC page');
        console.log('  - Balance selector may have changed');
        console.log('  - Page may not be fully loaded');
        console.log('');
        console.log('If Webhook Listener shows API source with 0:');
        console.log('  - API fallback logic is working but API returns 0');
        console.log('  - Frontend fallback should activate when API = 0');
        console.log('');
        console.log('Expected behavior:');
        console.log('  1. API returns 0 balance');
        console.log('  2. System detects 0 and tries frontend');
        console.log('  3. Frontend returns 2.29 USDT');
        console.log('  4. Panel shows 2.29 USDT with source: frontend');

    } catch (error) {
        console.error('❌ Debug failed:', error.message);
    }
}

// Run debug
debugBalanceIssue().catch(console.error);
