"""
Dashboard routes for web interface
"""

from fastapi import APIRouter, Request, Depends
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from datetime import datetime

from src.core.session_manager import SessionManager
from src.core.trading_engine import TradingEngine
from src.config import settings
from src.utils.logger import get_logger
from src.integrations.mexc_api import get_mexc_client

logger = get_logger(__name__)
router = APIRouter()
templates = Jinja2Templates(directory="templates")

# Global instances (will be injected)
session_manager = None
trading_engine = None


async def get_session_manager() -> SessionManager:
    """Get session manager instance"""
    global session_manager
    return session_manager


async def get_trading_engine() -> TradingEngine:
    """Get trading engine instance"""
    global trading_engine
    return trading_engine


@router.get("/", response_class=HTMLResponse)
async def dashboard_home(
    request: Request,
    sm: SessionManager = Depends(get_session_manager),
    te: TradingEngine = Depends(get_trading_engine)
):
    """Main dashboard page"""
    try:
        # Get system status
        session_stats = sm.get_session_stats() if sm else {}
        trading_stats = te.get_performance_stats() if te else {}
        
        context = {
            "request": request,
            "title": "MEXC Trading System Dashboard",
            "system_status": {
                "status": "running" if (sm and te) else "starting",
                "version": settings.VERSION,
                "environment": settings.ENVIRONMENT
            },
            "session_stats": session_stats,
            "trading_stats": trading_stats,
            "settings": {
                "max_concurrent_trades": settings.MAX_CONCURRENT_TRADES,
                "session_pool_size": settings.SESSION_POOL_SIZE,
                "supported_symbols": settings.SUPPORTED_SYMBOLS
            }
        }
        
        return templates.TemplateResponse("dashboard.html", context)
        
    except Exception as e:
        logger.error(f"Dashboard error: {e}")
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )


@router.get("/sessions", response_class=HTMLResponse)
async def sessions_dashboard(
    request: Request,
    sm: SessionManager = Depends(get_session_manager)
):
    """Sessions management dashboard"""
    try:
        sessions_data = []
        
        if sm:
            for session in sm.sessions:
                sessions_data.append({
                    "id": session.session_id[:8],
                    "full_id": session.session_id,
                    "status": "healthy" if session.is_healthy() else "unhealthy",
                    "health_score": round(session.health_score, 2),
                    "authenticated": session.is_authenticated,
                    "created_at": session.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    "last_used": session.last_used_at.strftime("%Y-%m-%d %H:%M:%S"),
                    "expires_at": session.expires_at.strftime("%Y-%m-%d %H:%M:%S") if session.expires_at else "N/A",
                    "total_trades": session.total_trades,
                    "successful_trades": session.successful_trades,
                    "success_rate": round(session.successful_trades / max(1, session.total_trades) * 100, 1),
                    "time_until_expiry": session.time_until_expiry()
                })
        
        context = {
            "request": request,
            "title": "Session Management",
            "sessions": sessions_data,
            "pool_size": settings.SESSION_POOL_SIZE
        }
        
        return templates.TemplateResponse("sessions.html", context)
        
    except Exception as e:
        logger.error(f"Sessions dashboard error: {e}")
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )


@router.get("/trades", response_class=HTMLResponse)
async def trades_dashboard(
    request: Request,
    te: TradingEngine = Depends(get_trading_engine)
):
    """Trades monitoring dashboard"""
    try:
        # Get recent trades from database
        from src.models.database import get_recent_trades
        recent_trades = await get_recent_trades(100)
        
        trades_data = []
        for trade in recent_trades:
            trades_data.append({
                "id": trade.id,
                "symbol": trade.symbol,
                "action": trade.action.upper(),
                "side": trade.side.upper(),
                "quantity": trade.quantity,
                "price": trade.price,
                "executed_price": trade.executed_price,
                "status": trade.status.upper(),
                "created_at": trade.created_at.strftime("%Y-%m-%d %H:%M:%S") if trade.created_at else "N/A",
                "execution_time": trade.execution_time.strftime("%Y-%m-%d %H:%M:%S") if trade.execution_time else "N/A",
                "order_id": trade.order_id,
                "pnl": trade.pnl,
                "error_message": trade.error_message
            })
        
        # Get active trades
        active_trades = []
        if te:
            for trade_id, trade_info in te.active_trades.items():
                trade_request = trade_info["trade_request"]
                active_trades.append({
                    "id": trade_id,
                    "symbol": trade_request.symbol,
                    "action": trade_request.action.upper(),
                    "side": trade_request.side.upper(),
                    "quantity": trade_request.quantity,
                    "price": trade_request.price,
                    "start_time": trade_info["start_time"].strftime("%Y-%m-%d %H:%M:%S")
                })
        
        context = {
            "request": request,
            "title": "Trade Monitoring",
            "recent_trades": trades_data,
            "active_trades": active_trades,
            "trading_stats": te.get_performance_stats() if te else {}
        }
        
        return templates.TemplateResponse("trades.html", context)
        
    except Exception as e:
        logger.error(f"Trades dashboard error: {e}")
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )


@router.get("/config", response_class=HTMLResponse)
async def config_dashboard(request: Request):
    """Configuration dashboard"""
    try:
        context = {
            "request": request,
            "title": "System Configuration",
            "config": {
                "trading": {
                    "max_concurrent_trades": settings.MAX_CONCURRENT_TRADES,
                    "default_leverage": settings.DEFAULT_LEVERAGE,
                    "risk_management_enabled": settings.RISK_MANAGEMENT_ENABLED,
                    "max_position_size": settings.MAX_POSITION_SIZE,
                    "supported_symbols": settings.SUPPORTED_SYMBOLS,
                    "stop_loss_enabled": settings.STOP_LOSS_ENABLED,
                    "take_profit_enabled": settings.TAKE_PROFIT_ENABLED
                },
                "sessions": {
                    "pool_size": settings.SESSION_POOL_SIZE,
                    "health_check_interval": settings.SESSION_HEALTH_CHECK_INTERVAL,
                    "expiry_warning_hours": settings.SESSION_EXPIRY_WARNING_HOURS,
                    "session_timeout": settings.MEXC_SESSION_TIMEOUT,
                    "auto_refresh": settings.AUTO_SESSION_REFRESH
                },
                "browser": {
                    "headless_mode": settings.HEADLESS_MODE,
                    "timeout": settings.BROWSER_TIMEOUT,
                    "network_interception": settings.ENABLE_NETWORK_INTERCEPTION,
                    "stealth_mode": settings.ENABLE_STEALTH_MODE
                },
                "notifications": {
                    "telegram_enabled": settings.TELEGRAM_ENABLED,
                    "email_enabled": settings.EMAIL_NOTIFICATIONS_ENABLED
                },
                "system": {
                    "version": settings.VERSION,
                    "environment": settings.ENVIRONMENT,
                    "log_level": settings.LOG_LEVEL,
                    "debug_mode": settings.DEBUG_MODE,
                    "database_url": settings.DATABASE_URL,
                    "structured_logging": settings.STRUCTURED_LOGGING,
                    "telegram_enabled": settings.TELEGRAM_ENABLED
                }
            }
        }
        
        return templates.TemplateResponse("config.html", context)
        
    except Exception as e:
        logger.error(f"Config dashboard error: {e}")
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )


@router.get("/api/account-info")
async def get_account_info():
    """Get MEXC account information"""
    try:
        if not settings.MEXC_API_KEY or not settings.MEXC_API_SECRET:
            return {
                "success": False,
                "error": "MEXC API credentials not configured",
                "spot_account": None,
                "futures_account": None
            }

        async with await get_mexc_client() as client:
            # Get spot account info
            spot_account = await client.get_account_info()

            # Get futures account info (limited support)
            futures_account = await client.get_futures_account_info()

            # Get 24hr ticker for supported symbols
            tickers = {}
            for symbol in settings.SUPPORTED_SYMBOLS:
                ticker = await client.get_24hr_ticker(symbol.replace('_', ''))
                if 'error' not in ticker:
                    tickers[symbol] = ticker

            return {
                "success": True,
                "spot_account": spot_account,
                "futures_account": futures_account,
                "tickers": tickers,
                "timestamp": datetime.now().isoformat()
            }

    except Exception as e:
        logger.error(f"Failed to get account info: {e}")
        return {
            "success": False,
            "error": str(e),
            "spot_account": None,
            "futures_account": None
        }


@router.get("/api/test-mexc-connection")
async def test_mexc_connection():
    """Test MEXC API connection"""
    try:
        async with await get_mexc_client() as client:
            result = await client.test_connectivity()
            return {
                "success": True,
                "connection_test": result
            }
    except Exception as e:
        logger.error(f"MEXC connection test failed: {e}")
        return {
            "success": False,
            "error": str(e)
        }


@router.get("/api/system-config")
async def get_system_config():
    """Get current system configuration"""
    try:
        return {
            "success": True,
            "config": {
                # Bot Control
                "bot_enabled": settings.BOT_ENABLED,

                # MEXC API
                "mexc_api_enabled": settings.MEXC_API_ENABLED,
                "mexc_api_key_configured": bool(settings.MEXC_API_KEY),
                "mexc_api_secret_configured": bool(settings.MEXC_API_SECRET),

                # Money Management
                "trading_symbol": settings.TRADING_SYMBOL,
                "leverage": settings.LEVERAGE,
                "position_size_type": settings.POSITION_SIZE_TYPE,
                "position_size_percentage": settings.POSITION_SIZE_PERCENTAGE,
                "position_size_fixed": settings.POSITION_SIZE_FIXED,
                "max_position_amount": settings.MAX_POSITION_AMOUNT,
                "use_max_position_limit": settings.USE_MAX_POSITION_LIMIT,

                # Trading Settings
                "max_concurrent_trades": settings.MAX_CONCURRENT_TRADES,
                "default_leverage": settings.DEFAULT_LEVERAGE,
                "risk_management_enabled": settings.RISK_MANAGEMENT_ENABLED,
                "max_position_size": settings.MAX_POSITION_SIZE,
                "supported_symbols": settings.SUPPORTED_SYMBOLS,

                # Notifications
                "telegram_enabled": settings.TELEGRAM_ENABLED,
                "telegram_configured": bool(settings.TELEGRAM_BOT_TOKEN and settings.TELEGRAM_CHAT_ID),

                # System
                "version": settings.VERSION,
                "environment": settings.ENVIRONMENT,
                "debug_mode": settings.DEBUG_MODE
            }
        }
    except Exception as e:
        logger.error(f"Failed to get system config: {e}")
        return {
            "success": False,
            "error": str(e)
        }


def set_managers(sm: SessionManager, te: TradingEngine):
    """Set the global manager instances"""
    global session_manager, trading_engine
    session_manager = sm
    trading_engine = te
