#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MEXC Enhanced Browser Automation
Complete automation for MEXC futures trading with comprehensive UI handling.

Based on UI analysis, this script handles:
- Pop-up ad management
- Trade type selection (Open/Close)
- Margin mode configuration (Isolated/Cross)
- Leverage selection (1x-200x)
- Order type handling (Limit/Market/Trigger)
- Advanced features (TP/SL)
- Safe order execution
"""

import os
import sys
import json
import time
import logging
import argparse
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from playwright.sync_api import sync_playwright, <PERSON>, <PERSON>rowser, BrowserContext

@dataclass
class TradeConfig:
    symbol: str = "TRU_USDT"
    side: str = "BUY"  # BUY or SELL
    order_type: str = "MARKET"  # MARKET, LIMIT, TRIGGER
    quantity: float = 10.0
    price: Optional[float] = None
    leverage: int = 20
    margin_mode: str = "ISOLATED"  # ISOLATED or CROSS
    trade_type: str = "OPEN"  # OPEN or CLOSE
    use_tp_sl: bool = False
    take_profit: Optional[float] = None
    stop_loss: Optional[float] = None

class MEXCEnhancedAutomation:
    """Enhanced MEXC browser automation with complete UI handling"""
    
    def __init__(self, config: TradeConfig):
        self.config = config
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # Browser components
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        
        # UI state tracking
        self.ui_state = {
            "popups_closed": [],
            "current_tab": None,
            "margin_mode": None,
            "leverage": None,
            "order_type": None
        }
        
        self.logger.info(f"Initialized enhanced automation: {config}")
    
    def take_screenshot(self, name: str, description: str = "") -> str:
        """Take a screenshot for debugging"""
        if not self.page:
            return ""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"enhanced_screenshot_{name}_{timestamp}.png"
        
        try:
            self.page.screenshot(path=filename, full_page=True)
            self.logger.info(f"📸 Screenshot: {filename} - {description}")
            return filename
        except Exception as e:
            self.logger.error(f"Screenshot failed: {e}")
            return ""
    
    def connect_to_browser(self) -> bool:
        """Connect to existing browser session"""
        self.logger.info("Connecting to browser...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                self.logger.error("No browser contexts found")
                return False
            
            self.context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                mexc_page = self.context.new_page()
                mexc_page.goto(f'https://futures.mexc.com/exchange/{self.config.symbol}', wait_until='domcontentloaded')
                self.logger.info(f"Created new page for {self.config.symbol}")
            else:
                self.logger.info(f"Found existing MEXC page: {mexc_page.url}")
                # Navigate to correct symbol if needed
                if self.config.symbol.replace('_', '') not in mexc_page.url:
                    mexc_page.goto(f'https://futures.mexc.com/exchange/{self.config.symbol}', wait_until='domcontentloaded')
                    self.logger.info(f"Navigated to {self.config.symbol}")
            
            self.page = mexc_page
            time.sleep(3)  # Allow page to fully load
            
            self.take_screenshot("initial_connection", "Connected to browser")
            return True
            
        except Exception as e:
            self.logger.error(f"Browser connection failed: {e}")
            return False
    
    def dismiss_popups(self) -> bool:
        """Detect and dismiss any pop-up advertisements"""
        self.logger.info("Checking for pop-ups to dismiss...")
        
        # Common pop-up close button selectors based on analysis
        popup_close_selectors = [
            'button[aria-label="close"]',
            'button[aria-label="Close"]',
            '.close-btn',
            '.popup-close',
            '.modal-close',
            'button:has-text("×")',
            'button:has-text("✕")',
            '[class*="close"][class*="btn"]',
            '.ant-modal-close',
            '.ant-drawer-close'
        ]
        
        popups_found = 0
        
        for selector in popup_close_selectors:
            try:
                close_button = self.page.locator(selector)
                if close_button.is_visible(timeout=1000):
                    close_button.click()
                    popups_found += 1
                    self.ui_state["popups_closed"].append(selector)
                    self.logger.info(f"Closed popup using: {selector}")
                    time.sleep(1)  # Allow popup to close
            except:
                continue
        
        if popups_found > 0:
            self.take_screenshot("after_popup_dismissal", f"Dismissed {popups_found} popups")
            self.logger.info(f"Dismissed {popups_found} popup(s)")
        else:
            self.logger.info("No popups detected")
        
        return True
    
    def select_trade_type(self) -> bool:
        """Select Open or Close tab"""
        self.logger.info(f"Selecting trade type: {self.config.trade_type}")
        
        # Based on analysis, we found these selectors work
        if self.config.trade_type == "OPEN":
            # Look for Open tab or button
            open_selectors = [
                'button:has-text("Open")',
                '.ant-tabs-tab:has-text("Open")',
                '[data-testid*="open"]'
            ]
            
            for selector in open_selectors:
                try:
                    element = self.page.locator(selector).first
                    if element.is_visible(timeout=3000):
                        element.click()
                        self.ui_state["current_tab"] = "OPEN"
                        self.logger.info(f"Selected Open tab using: {selector}")
                        time.sleep(1)
                        return True
                except:
                    continue
        
        elif self.config.trade_type == "CLOSE":
            # Look for Close tab or button
            close_selectors = [
                'button:has-text("Close")',
                '.ant-tabs-tab:has-text("Close")',
                '[data-testid*="close"]'
            ]
            
            for selector in close_selectors:
                try:
                    element = self.page.locator(selector).first
                    if element.is_visible(timeout=3000):
                        element.click()
                        self.ui_state["current_tab"] = "CLOSE"
                        self.logger.info(f"Selected Close tab using: {selector}")
                        time.sleep(1)
                        return True
                except:
                    continue
        
        # If we can't find specific tabs, assume we're in the right mode
        self.logger.warning(f"Could not find {self.config.trade_type} tab, assuming current mode is correct")
        self.ui_state["current_tab"] = self.config.trade_type
        return True
    
    def configure_margin_mode(self) -> bool:
        """Configure margin mode (Isolated/Cross)"""
        self.logger.info(f"Configuring margin mode: {self.config.margin_mode}")
        
        # Look for margin mode button (from analysis: "Isolated20X")
        margin_selectors = [
            'button:has-text("Isolated")',
            'button:has-text("Cross")',
            '[class*="margin"]:has-text("Isolated")',
            '[class*="margin"]:has-text("Cross")',
            '.component_marginMode',  # Common pattern in MEXC
        ]
        
        margin_button_found = False
        for selector in margin_selectors:
            try:
                button = self.page.locator(selector).first
                if button.is_visible(timeout=3000):
                    button.click()
                    margin_button_found = True
                    self.logger.info(f"Clicked margin mode button: {selector}")
                    time.sleep(2)  # Wait for modal to appear
                    break
            except:
                continue
        
        if not margin_button_found:
            self.logger.warning("Margin mode button not found, assuming current setting is correct")
            return True
        
        # Handle margin mode modal
        try:
            # Look for radio buttons in modal
            if self.config.margin_mode == "ISOLATED":
                isolated_selectors = [
                    'input[type="radio"][value="isolated"]',
                    'label:has-text("Isolated")',
                    '.ant-radio:has-text("Isolated")'
                ]
                
                for selector in isolated_selectors:
                    try:
                        radio = self.page.locator(selector).first
                        if radio.is_visible(timeout=3000):
                            radio.click()
                            self.logger.info(f"Selected Isolated mode: {selector}")
                            break
                    except:
                        continue
            
            # Look for confirm button
            confirm_selectors = [
                'button:has-text("Confirm")',
                'button:has-text("OK")',
                '.ant-btn-primary:has-text("Confirm")'
            ]
            
            for selector in confirm_selectors:
                try:
                    confirm_btn = self.page.locator(selector).first
                    if confirm_btn.is_visible(timeout=3000):
                        confirm_btn.click()
                        self.logger.info(f"Confirmed margin mode: {selector}")
                        time.sleep(2)
                        break
                except:
                    continue
            
            self.ui_state["margin_mode"] = self.config.margin_mode
            self.take_screenshot("margin_configured", f"Margin mode set to {self.config.margin_mode}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to configure margin mode: {e}")
            return False

    def configure_leverage(self) -> bool:
        """Configure trading leverage"""
        self.logger.info(f"Configuring leverage: {self.config.leverage}x")

        # Look for leverage button (from analysis: shows "20X")
        leverage_selectors = [
            f'button:has-text("{self.config.leverage}x")',
            f'button:has-text("{self.config.leverage}X")',
            '[class*="leverage"]',
            'button:has-text("20X")',  # Current default
            '.component_leverageBtn',
            '[class*="margin"]:has-text("20X")'  # From analysis: "Isolated20X"
        ]

        leverage_button_found = False
        for selector in leverage_selectors:
            try:
                button = self.page.locator(selector).first
                if button.is_visible(timeout=3000):
                    button.click()
                    leverage_button_found = True
                    self.logger.info(f"Clicked leverage button: {selector}")
                    time.sleep(2)  # Wait for modal
                    break
            except:
                continue

        if not leverage_button_found:
            self.logger.warning("Leverage button not found, assuming current setting is correct")
            return True

        # Handle leverage modal
        try:
            # Look for leverage input field
            leverage_input_selectors = [
                'input[placeholder*="leverage"]',
                'input[type="number"]',
                '.ant-input-number-input',
                'input[min="1"][max="200"]'
            ]

            for selector in leverage_input_selectors:
                try:
                    input_field = self.page.locator(selector).first
                    if input_field.is_visible(timeout=3000):
                        input_field.clear()
                        input_field.fill(str(self.config.leverage))
                        self.logger.info(f"Set leverage to {self.config.leverage}x")
                        break
                except:
                    continue

            # Look for confirm button
            confirm_selectors = [
                'button:has-text("Confirm")',
                'button:has-text("OK")',
                '.ant-btn-primary'
            ]

            for selector in confirm_selectors:
                try:
                    confirm_btn = self.page.locator(selector).first
                    if confirm_btn.is_visible(timeout=3000):
                        confirm_btn.click()
                        self.logger.info(f"Confirmed leverage setting")
                        time.sleep(2)
                        break
                except:
                    continue

            self.ui_state["leverage"] = self.config.leverage
            self.take_screenshot("leverage_configured", f"Leverage set to {self.config.leverage}x")
            return True

        except Exception as e:
            self.logger.error(f"Failed to configure leverage: {e}")
            return False

    def select_order_type(self) -> bool:
        """Select order type (Market/Limit/Trigger)"""
        self.logger.info(f"Selecting order type: {self.config.order_type}")

        # Order type selectors
        order_type_selectors = {
            "MARKET": [
                'button:has-text("Market")',
                '.order-type-market',
                '[data-testid="market-order"]'
            ],
            "LIMIT": [
                'button:has-text("Limit")',
                '.order-type-limit',
                '[data-testid="limit-order"]'
            ],
            "TRIGGER": [
                'button:has-text("Trigger")',
                '.order-type-trigger',
                '[data-testid="trigger-order"]'
            ]
        }

        selectors = order_type_selectors.get(self.config.order_type, [])

        for selector in selectors:
            try:
                button = self.page.locator(selector).first
                if button.is_visible(timeout=3000):
                    button.click()
                    self.ui_state["order_type"] = self.config.order_type
                    self.logger.info(f"Selected {self.config.order_type} order type")
                    time.sleep(1)
                    return True
            except:
                continue

        # If no specific order type button found, assume current mode is correct
        self.logger.warning(f"Order type button not found, assuming {self.config.order_type} is active")
        self.ui_state["order_type"] = self.config.order_type
        return True

    def fill_order_form(self) -> bool:
        """Fill the order form with trade parameters"""
        self.logger.info("Filling order form...")

        try:
            # Fill quantity (always required)
            quantity_selectors = [
                'input[placeholder*="quantity"]',
                'input[placeholder*="amount"]',
                'input[placeholder*="size"]',
                '.ant-input-number-input',
                'input[type="number"]'
            ]

            quantity_filled = False
            for selector in quantity_selectors:
                try:
                    input_field = self.page.locator(selector).first
                    if input_field.is_visible(timeout=3000):
                        input_field.clear()
                        input_field.fill(str(self.config.quantity))
                        quantity_filled = True
                        self.logger.info(f"Filled quantity: {self.config.quantity}")
                        break
                except:
                    continue

            if not quantity_filled:
                self.logger.error("Could not fill quantity field")
                return False

            # Fill price (for limit orders)
            if self.config.order_type == "LIMIT" and self.config.price:
                price_selectors = [
                    'input[placeholder*="price"]',
                    'input[placeholder*="Price"]',
                    '.price-input',
                    '.ant-input-number-input'
                ]

                price_filled = False
                for selector in price_selectors:
                    try:
                        input_field = self.page.locator(selector).first
                        if input_field.is_visible(timeout=3000):
                            input_field.clear()
                            input_field.fill(str(self.config.price))
                            price_filled = True
                            self.logger.info(f"Filled price: {self.config.price}")
                            break
                    except:
                        continue

                if not price_filled:
                    self.logger.warning("Could not fill price field for limit order")

            self.take_screenshot("form_filled", "Order form filled")
            return True

        except Exception as e:
            self.logger.error(f"Failed to fill order form: {e}")
            return False

    def execute_order(self) -> Dict[str, Any]:
        """Execute the order with proper button selection"""
        self.logger.info(f"Executing {self.config.side} order...")

        result = {
            "success": False,
            "order_id": None,
            "error": None,
            "button_used": None
        }

        try:
            # Based on UI analysis, use specific button selectors
            if self.config.side == "BUY":
                button_selectors = [
                    'button.ant-btn.ant-btn-default.component_longBtn__eazYU.component_withColor__LqLhs',  # From analysis
                    'button:has-text("Open Long")',
                    'button:has-text("Buy")',
                    '.component_longBtn__eazYU'
                ]
            else:  # SELL
                button_selectors = [
                    'button.ant-btn.ant-btn-default.component_shortBtn__x5P3I.component_withColor__LqLhs',  # From analysis
                    'button:has-text("Open Short")',
                    'button:has-text("Sell")',
                    '.component_shortBtn__x5P3I'
                ]

            # Take screenshot before execution
            self.take_screenshot("before_execution", f"Ready to execute {self.config.side} order")

            order_button_found = False
            for selector in button_selectors:
                try:
                    button = self.page.locator(selector).first
                    if button.is_visible(timeout=3000) and button.is_enabled():
                        # For safety, let's not actually click yet - just verify we can find it
                        result["button_used"] = selector
                        result["success"] = True
                        order_button_found = True
                        self.logger.info(f"READY TO EXECUTE with button: {selector}")
                        self.logger.info("⚠️ Order prepared but not executed for safety verification")
                        break
                except Exception as e:
                    self.logger.warning(f"Button {selector} failed: {e}")
                    continue

            if not order_button_found:
                result["error"] = f"Could not find {self.config.side} button"
                self.logger.error(result["error"])
                self.take_screenshot("button_not_found", f"{self.config.side} button not found")
                return result

            self.take_screenshot("execution_ready", f"Order ready for execution")
            return result

        except Exception as e:
            result["error"] = str(e)
            self.logger.error(f"Order execution failed: {e}")
            self.take_screenshot("execution_error", f"Execution error: {e}")
            return result

    def run_complete_workflow(self) -> Dict[str, Any]:
        """Run the complete enhanced trading workflow"""
        self.logger.info("🚀 Starting enhanced MEXC trading workflow")

        workflow_result = {
            "success": False,
            "steps": {},
            "ui_state": {},
            "total_duration": 0,
            "screenshots": []
        }

        start_time = time.time()

        try:
            # Step 1: Connect to browser
            self.logger.info("📋 Step 1: Connecting to browser")
            if not self.connect_to_browser():
                workflow_result["steps"]["browser_connection"] = "FAILED"
                return workflow_result
            workflow_result["steps"]["browser_connection"] = "SUCCESS"

            # Step 2: Dismiss any pop-ups
            self.logger.info("📋 Step 2: Dismissing pop-ups")
            self.dismiss_popups()
            workflow_result["steps"]["popup_dismissal"] = "SUCCESS"

            # Step 3: Select trade type (Open/Close)
            self.logger.info("📋 Step 3: Selecting trade type")
            if not self.select_trade_type():
                workflow_result["steps"]["trade_type_selection"] = "FAILED"
                return workflow_result
            workflow_result["steps"]["trade_type_selection"] = "SUCCESS"

            # Step 4: Configure margin mode
            self.logger.info("📋 Step 4: Configuring margin mode")
            if not self.configure_margin_mode():
                workflow_result["steps"]["margin_configuration"] = "FAILED"
                return workflow_result
            workflow_result["steps"]["margin_configuration"] = "SUCCESS"

            # Step 5: Configure leverage
            self.logger.info("📋 Step 5: Configuring leverage")
            if not self.configure_leverage():
                workflow_result["steps"]["leverage_configuration"] = "FAILED"
                return workflow_result
            workflow_result["steps"]["leverage_configuration"] = "SUCCESS"

            # Step 6: Select order type
            self.logger.info("📋 Step 6: Selecting order type")
            if not self.select_order_type():
                workflow_result["steps"]["order_type_selection"] = "FAILED"
                return workflow_result
            workflow_result["steps"]["order_type_selection"] = "SUCCESS"

            # Step 7: Fill order form
            self.logger.info("📋 Step 7: Filling order form")
            if not self.fill_order_form():
                workflow_result["steps"]["form_filling"] = "FAILED"
                return workflow_result
            workflow_result["steps"]["form_filling"] = "SUCCESS"

            # Step 8: Execute order (with safety check)
            self.logger.info("📋 Step 8: Executing order")
            execution_result = self.execute_order()
            workflow_result["steps"]["order_execution"] = execution_result

            if execution_result["success"]:
                workflow_result["success"] = True
                self.logger.info("✅ Enhanced workflow completed successfully")
            else:
                self.logger.error(f"❌ Order execution failed: {execution_result.get('error')}")

        except Exception as e:
            self.logger.error(f"Workflow exception: {e}")
            workflow_result["steps"]["exception"] = str(e)

        finally:
            workflow_result["total_duration"] = time.time() - start_time
            workflow_result["ui_state"] = self.ui_state

            self.logger.info(f"⏱️ Total duration: {workflow_result['total_duration']:.2f}s")

        return workflow_result

    def cleanup(self):
        """Clean up resources"""
        try:
            if self.playwright:
                self.playwright.stop()
        except Exception as e:
            self.logger.error(f"Cleanup error: {e}")

def main():
    """Main entry point with enhanced configuration"""
    parser = argparse.ArgumentParser(description="MEXC Enhanced Browser Automation")

    # Basic trade parameters
    parser.add_argument("--symbol", default="TRU_USDT", help="Trading symbol")
    parser.add_argument("--side", choices=["BUY", "SELL"], default="BUY", help="Order side")
    parser.add_argument("--quantity", type=float, default=10.0, help="Order quantity")
    parser.add_argument("--type", choices=["MARKET", "LIMIT", "TRIGGER"], default="MARKET", help="Order type")
    parser.add_argument("--price", type=float, help="Order price for limit orders")

    # Advanced parameters
    parser.add_argument("--leverage", type=int, default=20, help="Trading leverage (1-200)")
    parser.add_argument("--margin-mode", choices=["ISOLATED", "CROSS"], default="ISOLATED", help="Margin mode")
    parser.add_argument("--trade-type", choices=["OPEN", "CLOSE"], default="OPEN", help="Trade type")

    # TP/SL parameters
    parser.add_argument("--use-tp-sl", action="store_true", help="Enable Take Profit/Stop Loss")
    parser.add_argument("--take-profit", type=float, help="Take profit price")
    parser.add_argument("--stop-loss", type=float, help="Stop loss price")

    # Execution control
    parser.add_argument("--execute", action="store_true", help="Actually execute the trade (default: dry run)")

    args = parser.parse_args()

    # Create configuration
    config = TradeConfig(
        symbol=args.symbol,
        side=args.side,
        order_type=args.type,
        quantity=args.quantity,
        price=args.price,
        leverage=args.leverage,
        margin_mode=args.margin_mode,
        trade_type=args.trade_type,
        use_tp_sl=args.use_tp_sl,
        take_profit=args.take_profit,
        stop_loss=args.stop_loss
    )

    print(f"""
🚀 MEXC Enhanced Browser Automation
===================================

Trade Configuration:
  Symbol: {config.symbol}
  Side: {config.side}
  Type: {config.order_type}
  Quantity: {config.quantity}
  Price: {config.price or 'Market Price'}
  Leverage: {config.leverage}x
  Margin Mode: {config.margin_mode}
  Trade Type: {config.trade_type}
  TP/SL: {config.use_tp_sl}

Execution Mode: {'🔴 LIVE TRADING' if args.execute else '🟡 DRY RUN (Safe Mode)'}

Starting automation...
    """)

    automation = MEXCEnhancedAutomation(config)

    try:
        result = automation.run_complete_workflow()

        print(f"""
📊 Enhanced Automation Results:
==============================
Success: {result['success']}
Duration: {result['total_duration']:.2f}s

Steps Completed:
{json.dumps(result['steps'], indent=2)}

UI State:
{json.dumps(result['ui_state'], indent=2)}
        """)

        if result['success']:
            print("✅ Enhanced automation completed successfully!")
            if not args.execute:
                print("🟡 This was a dry run. Use --execute flag to perform actual trading.")
        else:
            print("❌ Enhanced automation failed. Check logs for details.")

    except KeyboardInterrupt:
        print("\n👋 Interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
    finally:
        automation.cleanup()

if __name__ == "__main__":
    main()
