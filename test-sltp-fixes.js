#!/usr/bin/env node

/**
 * Test Script for SL/TP Mechanism Fixes
 * Tests the price data source improvements and duplicate execution prevention
 */

const axios = require('axios');

class SLTPTester {
    constructor() {
        this.baseURL = 'http://localhost:80';
        this.testResults = [];
    }

    async runTests() {
        console.log('🧪 Starting SL/TP Mechanism Tests...\n');

        try {
            // Test 1: Price Data Source Validation
            await this.testPriceDataSources();
            
            // Test 2: Market Data API Endpoints
            await this.testMarketDataEndpoints();
            
            // Test 3: ATR Calculation with Different Data Sources
            await this.testATRCalculation();
            
            // Test 4: Position Management Status
            await this.testPositionManagement();
            
            // Test 5: Configuration Validation
            await this.testConfiguration();

            // Print Results
            this.printResults();

        } catch (error) {
            console.error('❌ Test suite failed:', error.message);
        }
    }

    async testPriceDataSources() {
        console.log('📊 Testing Price Data Sources...');
        
        try {
            const response = await axios.get(`${this.baseURL}/api/market-data?symbol=TRUUSDT`);
            const data = response.data;

            if (data.success) {
                console.log(`✅ Current Price: ${data.currentPrice.price} (Source: ${data.currentPrice.source || 'unknown'})`);
                console.log(`✅ ATR Value: ${data.atr}`);
                
                this.testResults.push({
                    test: 'Price Data Sources',
                    status: 'PASS',
                    details: `Price: ${data.currentPrice.price}, ATR: ${data.atr}, Source: ${data.currentPrice.source || 'unknown'}`
                });
            } else {
                throw new Error('Market data API returned unsuccessful response');
            }
        } catch (error) {
            console.log(`❌ Price data test failed: ${error.message}`);
            this.testResults.push({
                test: 'Price Data Sources',
                status: 'FAIL',
                details: error.message
            });
        }
    }

    async testMarketDataEndpoints() {
        console.log('🔗 Testing Market Data Endpoints...');
        
        try {
            // Test kline data endpoint
            const klineResponse = await axios.get(`${this.baseURL}/api/market-data/klines?symbol=TRUUSDT&interval=1m&limit=20`);
            
            if (klineResponse.data.success && klineResponse.data.klines.length > 0) {
                const latestKline = klineResponse.data.klines[klineResponse.data.klines.length - 1];
                console.log(`✅ Kline Data: Latest close price: ${latestKline.close} (Source: ${latestKline.source || 'unknown'})`);
                
                this.testResults.push({
                    test: 'Market Data Endpoints',
                    status: 'PASS',
                    details: `Retrieved ${klineResponse.data.klines.length} klines, latest close: ${latestKline.close}`
                });
            } else {
                throw new Error('Kline data endpoint returned no data');
            }
        } catch (error) {
            console.log(`❌ Market data endpoints test failed: ${error.message}`);
            this.testResults.push({
                test: 'Market Data Endpoints',
                status: 'FAIL',
                details: error.message
            });
        }
    }

    async testATRCalculation() {
        console.log('📈 Testing ATR Calculation...');
        
        try {
            const response = await axios.get(`${this.baseURL}/api/market-data?symbol=TRUUSDT`);
            const data = response.data;

            if (data.success && data.atr > 0) {
                // Simulate SL/TP calculation
                const entryPrice = data.currentPrice.price;
                const atr = data.atr;
                const slMultiplier = 1.8; // From config
                const tpMultiplier = 3; // TP1 reward from config

                const longSL = entryPrice - (atr * slMultiplier);
                const longTP = entryPrice + (atr * tpMultiplier);
                const shortSL = entryPrice + (atr * slMultiplier);
                const shortTP = entryPrice - (atr * tpMultiplier);

                console.log(`✅ ATR-based calculations:`);
                console.log(`   Entry Price: ${entryPrice}`);
                console.log(`   ATR: ${atr}`);
                console.log(`   Long SL: ${longSL.toFixed(6)} | Long TP: ${longTP.toFixed(6)}`);
                console.log(`   Short SL: ${shortSL.toFixed(6)} | Short TP: ${shortTP.toFixed(6)}`);

                this.testResults.push({
                    test: 'ATR Calculation',
                    status: 'PASS',
                    details: `ATR: ${atr}, Long SL: ${longSL.toFixed(6)}, Long TP: ${longTP.toFixed(6)}`
                });
            } else {
                throw new Error('ATR calculation returned invalid data');
            }
        } catch (error) {
            console.log(`❌ ATR calculation test failed: ${error.message}`);
            this.testResults.push({
                test: 'ATR Calculation',
                status: 'FAIL',
                details: error.message
            });
        }
    }

    async testPositionManagement() {
        console.log('📋 Testing Position Management...');
        
        try {
            const response = await axios.get(`${this.baseURL}/api/positions`);
            const data = response.data;

            if (data.success) {
                console.log(`✅ Active Positions: ${data.positions.length}`);
                console.log(`✅ Monitoring Status: ${data.statistics.isMonitoring ? 'Active' : 'Inactive'}`);
                
                if (data.positions.length > 0) {
                    data.positions.forEach((pos, index) => {
                        console.log(`   Position ${index + 1}: ${pos.symbol} ${pos.direction} - Entry: ${pos.entryPrice}, Qty: ${pos.quantity}`);
                    });
                }

                this.testResults.push({
                    test: 'Position Management',
                    status: 'PASS',
                    details: `${data.positions.length} active positions, monitoring: ${data.statistics.isMonitoring}`
                });
            } else {
                throw new Error('Position management API returned unsuccessful response');
            }
        } catch (error) {
            console.log(`❌ Position management test failed: ${error.message}`);
            this.testResults.push({
                test: 'Position Management',
                status: 'FAIL',
                details: error.message
            });
        }
    }

    async testConfiguration() {
        console.log('⚙️ Testing Configuration...');
        
        try {
            const response = await axios.get(`${this.baseURL}/api/config`);
            const config = response.data;

            console.log(`✅ SL/TP Enabled: ${config.slTpEnabled}`);
            console.log(`✅ ATR Length: ${config.atrLength}`);
            console.log(`✅ ATR Smoothing: ${config.atrSmoothing}`);
            console.log(`✅ SL Multiplier: ${config.slMultiplier}`);
            console.log(`✅ TP1 Enabled: ${config.tp1Enabled} (Reward: ${config.tp1Reward})`);
            console.log(`✅ TP2 Enabled: ${config.tp2Enabled} (Reward: ${config.tp2Reward})`);
            console.log(`✅ TP3 Enabled: ${config.tp3Enabled} (Reward: ${config.tp3Reward})`);
            console.log(`✅ SL Type: ${config.slType}`);
            console.log(`✅ Trailing Start: ${config.startTrailingAtProfit} ATR`);
            console.log(`✅ Trailing Value: ${config.trailingValue} ATR`);

            this.testResults.push({
                test: 'Configuration',
                status: 'PASS',
                details: `SL/TP: ${config.slTpEnabled}, ATR: ${config.atrLength}/${config.atrSmoothing}, SL: ${config.slMultiplier}x`
            });
        } catch (error) {
            console.log(`❌ Configuration test failed: ${error.message}`);
            this.testResults.push({
                test: 'Configuration',
                status: 'FAIL',
                details: error.message
            });
        }
    }

    printResults() {
        console.log('\n📊 Test Results Summary:');
        console.log('=' .repeat(60));
        
        let passed = 0;
        let failed = 0;

        this.testResults.forEach(result => {
            const status = result.status === 'PASS' ? '✅' : '❌';
            console.log(`${status} ${result.test}: ${result.status}`);
            console.log(`   Details: ${result.details}`);
            
            if (result.status === 'PASS') passed++;
            else failed++;
        });

        console.log('=' .repeat(60));
        console.log(`Total Tests: ${this.testResults.length} | Passed: ${passed} | Failed: ${failed}`);
        
        if (failed === 0) {
            console.log('🎉 All tests passed! SL/TP mechanism fixes are working correctly.');
        } else {
            console.log('⚠️ Some tests failed. Please review the issues above.');
        }
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Run tests if called directly
if (require.main === module) {
    const tester = new SLTPTester();
    tester.runTests().catch(error => {
        console.error('❌ Test execution failed:', error);
        process.exit(1);
    });
}

module.exports = SLTPTester;
