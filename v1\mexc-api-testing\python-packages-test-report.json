{"timestamp": "2025-08-13T07:52:58.445113", "summary": {"total": 3, "successful": 0, "failed": 3}, "results": [{"success": false, "error": "Import failed", "library": "pymexc"}, {"success": false, "library": "ccxt-python", "error": "mexc POST https://contract.mexc.com/api/v1/private/order/submit"}, {"success": false, "library": "custom-python-api", "error": "HTTPSConnectionPool(host='contract.mexc.com', port=443): Read timed out. (read timeout=30)"}]}