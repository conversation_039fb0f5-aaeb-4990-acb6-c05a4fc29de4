const { chromium } = require('playwright');

async function debugClosePanel() {
    console.log('🔍 DEBUGGING CLOSE PANEL');
    console.log('=========================');
    
    try {
        const browser = await chromium.connectOverCDP('http://localhost:9223');
        const contexts = browser.contexts();
        const pages = contexts[0].pages();
        const page = pages[0];
        
        console.log('✅ Connected to browser');
        
        // Switch to Close panel
        console.log('🔄 Switching to Close panel...');
        const closeTab = page.locator('span[data-testid="contract-trade-order-form-tab-close"]').first();
        await closeTab.click();
        await page.waitForTimeout(1000);
        console.log('✅ Switched to Close panel');
        
        // Debug all input fields
        console.log('\n🔍 ANALYZING INPUT FIELDS IN CLOSE PANEL:');
        console.log('==========================================');
        
        const inputs = await page.locator('input').all();
        console.log(`Found ${inputs.length} input elements`);
        
        for (let i = 0; i < inputs.length; i++) {
            try {
                const input = inputs[i];
                const isVisible = await input.isVisible();
                
                if (isVisible) {
                    const type = await input.getAttribute('type');
                    const placeholder = await input.getAttribute('placeholder');
                    const className = await input.getAttribute('class');
                    const value = await input.inputValue();
                    
                    console.log(`\nInput ${i + 1}:`);
                    console.log(`  Type: ${type || 'none'}`);
                    console.log(`  Placeholder: ${placeholder || 'none'}`);
                    console.log(`  Class: ${className || 'none'}`);
                    console.log(`  Value: "${value}"`);
                    console.log(`  Visible: ${isVisible}`);
                }
            } catch (error) {
                console.log(`Input ${i + 1}: Error - ${error.message}`);
            }
        }
        
        // Look for Quantity text
        console.log('\n🔍 LOOKING FOR QUANTITY TEXT:');
        console.log('==============================');
        
        const quantityTexts = await page.locator('text=Quantity').all();
        console.log(`Found ${quantityTexts.length} "Quantity" text elements`);
        
        for (let i = 0; i < quantityTexts.length; i++) {
            try {
                const element = quantityTexts[i];
                const text = await element.textContent();
                const isVisible = await element.isVisible();
                console.log(`Quantity text ${i + 1}: "${text}" (visible: ${isVisible})`);
            } catch (error) {
                console.log(`Quantity text ${i + 1}: Error - ${error.message}`);
            }
        }
        
        // Test specific selectors
        console.log('\n🧪 TESTING QUANTITY SELECTORS:');
        console.log('===============================');
        
        const testSelectors = [
            'text=Quantity(USDT) >> xpath=following::input[1]',
            'text=Quantity >> xpath=following::input[1]',
            'input[type="text"]',
            'input[placeholder*="quantity"]',
            'input[placeholder*="amount"]',
            '.ant-input',
            'input.ant-input'
        ];
        
        for (const selector of testSelectors) {
            try {
                const element = page.locator(selector).first();
                const isVisible = await element.isVisible({ timeout: 500 });
                
                if (isVisible) {
                    const type = await element.getAttribute('type');
                    const placeholder = await element.getAttribute('placeholder');
                    const className = await element.getAttribute('class');
                    console.log(`✅ WORKING: ${selector}`);
                    console.log(`   Type: ${type}, Placeholder: ${placeholder}`);
                    console.log(`   Class: ${className}`);
                    
                    // Try to fill it
                    try {
                        await element.click();
                        await element.fill('0.3600');
                        const value = await element.inputValue();
                        console.log(`   ✅ Successfully filled with: "${value}"`);
                        
                        // Clear it
                        await element.fill('');
                        console.log(`   🧹 Cleared field`);
                        
                        console.log(`\n🎉 FOUND WORKING SELECTOR: ${selector}`);
                        break;
                    } catch (fillError) {
                        console.log(`   ❌ Fill failed: ${fillError.message}`);
                    }
                } else {
                    console.log(`❌ NOT VISIBLE: ${selector}`);
                }
            } catch (error) {
                console.log(`❌ ERROR: ${selector} - ${error.message}`);
            }
        }
        
        // Look for Close buttons
        console.log('\n🔍 LOOKING FOR CLOSE BUTTONS:');
        console.log('==============================');
        
        const closeButtonSelectors = [
            'button:has-text("Close Long")',
            'button:has-text("Close Short")',
            'text=Close Long',
            'text=Close Short'
        ];
        
        for (const selector of closeButtonSelectors) {
            try {
                const element = page.locator(selector).first();
                const isVisible = await element.isVisible({ timeout: 500 });
                
                if (isVisible) {
                    const text = await element.textContent();
                    console.log(`✅ FOUND: ${selector} - "${text}"`);
                } else {
                    console.log(`❌ NOT VISIBLE: ${selector}`);
                }
            } catch (error) {
                console.log(`❌ ERROR: ${selector} - ${error.message}`);
            }
        }
        
        console.log('\n🏁 DEBUG COMPLETED');
        
    } catch (error) {
        console.error('💥 Debug failed:', error.message);
    }
}

if (require.main === module) {
    debugClosePanel();
}

module.exports = debugClosePanel;
