const { chromium } = require('playwright');
const fs = require('fs');
require('dotenv').config();

class FastMEXCFuturesBot {
    constructor() {
        this.browser = null;
        this.page = null;
        this.isLoggedIn = false;
        this.sessionFile = 'mexc-session.json';
        
        // Configuration
        this.config = {
            email: process.env.MEXC_EMAIL,
            password: process.env.MEXC_PASSWORD,
            headless: true, // Set to false for debugging
            timeout: 2000, // 2 second timeout
            fastMode: true
        };
    }

    async initialize() {
        console.log('🚀 Initializing Fast MEXC Futures Bot...');
        
        // Launch browser with optimized settings for speed
        this.browser = await chromium.launch({
            headless: this.config.headless,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ]
        });

        this.page = await this.browser.newPage();
        
        // Optimize page for speed
        await this.page.setViewportSize({ width: 1920, height: 1080 });
        
        // Block unnecessary resources to speed up loading
        await this.page.route('**/*', (route) => {
            const resourceType = route.request().resourceType();
            if (['image', 'stylesheet', 'font', 'media'].includes(resourceType)) {
                route.abort();
            } else {
                route.continue();
            }
        });

        console.log('✅ Browser initialized');
    }

    async loadSession() {
        try {
            if (fs.existsSync(this.sessionFile)) {
                const sessionData = JSON.parse(fs.readFileSync(this.sessionFile, 'utf8'));
                await this.page.context().addCookies(sessionData.cookies);
                console.log('✅ Session loaded from file');
                return true;
            }
        } catch (error) {
            console.log('⚠️ Could not load session:', error.message);
        }
        return false;
    }

    async saveSession() {
        try {
            const cookies = await this.page.context().cookies();
            const sessionData = { cookies, timestamp: Date.now() };
            fs.writeFileSync(this.sessionFile, JSON.stringify(sessionData, null, 2));
            console.log('✅ Session saved');
        } catch (error) {
            console.log('⚠️ Could not save session:', error.message);
        }
    }

    async login() {
        console.log('🔐 Attempting login...');
        
        try {
            // Try to load existing session first
            await this.loadSession();
            
            // Go to futures page to check if logged in
            await this.page.goto('https://futures.mexc.com/exchange/BTC_USDT', { 
                waitUntil: 'domcontentloaded',
                timeout: this.config.timeout 
            });

            // Check if already logged in
            try {
                await this.page.waitForSelector('.user-info', { timeout: 1000 });
                console.log('✅ Already logged in');
                this.isLoggedIn = true;
                return true;
            } catch {
                console.log('🔄 Need to login...');
            }

            // Go to login page
            await this.page.goto('https://www.mexc.com/login', { 
                waitUntil: 'domcontentloaded',
                timeout: this.config.timeout 
            });

            // Fill login form
            await this.page.fill('input[type="email"], input[name="email"]', this.config.email);
            await this.page.fill('input[type="password"], input[name="password"]', this.config.password);
            
            // Click login button
            await this.page.click('button[type="submit"], .login-btn, .btn-login');
            
            // Wait for login to complete
            await this.page.waitForURL('**/futures/**', { timeout: 10000 });
            
            console.log('✅ Login successful');
            this.isLoggedIn = true;
            
            // Save session for future use
            await this.saveSession();
            
            return true;
        } catch (error) {
            console.error('❌ Login failed:', error.message);
            return false;
        }
    }

    async navigateToFutures(symbol = 'BTC_USDT') {
        const startTime = Date.now();
        
        try {
            const url = `https://futures.mexc.com/exchange/${symbol}`;
            await this.page.goto(url, { 
                waitUntil: 'domcontentloaded',
                timeout: this.config.timeout 
            });

            // Wait for trading interface to load
            await this.page.waitForSelector('.trading-panel, .order-form, .trade-form', { 
                timeout: this.config.timeout 
            });

            const loadTime = Date.now() - startTime;
            console.log(`✅ Futures page loaded in ${loadTime}ms`);
            return true;
        } catch (error) {
            console.error('❌ Failed to navigate to futures:', error.message);
            return false;
        }
    }

    async placeFuturesOrder(orderConfig) {
        const startTime = Date.now();
        console.log(`🎯 Placing ${orderConfig.side} order for ${orderConfig.symbol}...`);

        try {
            // Default order configuration
            const config = {
                symbol: 'BTC_USDT',
                side: 'buy', // 'buy' or 'sell'
                type: 'market', // 'market' or 'limit'
                quantity: '0.001',
                price: null, // Only for limit orders
                leverage: '1',
                ...orderConfig
            };

            // Navigate to the correct symbol if needed
            if (!this.page.url().includes(config.symbol)) {
                await this.navigateToFutures(config.symbol);
            }

            // Set leverage first (if needed)
            try {
                await this.page.click('.leverage-selector, .leverage-btn', { timeout: 500 });
                await this.page.fill('.leverage-input', config.leverage);
                await this.page.press('.leverage-input', 'Enter');
            } catch {
                console.log('⚠️ Could not set leverage, using current setting');
            }

            // Select order side (Buy/Sell)
            const sideSelector = config.side === 'buy' ? 
                '.buy-btn, .long-btn, [data-side="buy"]' : 
                '.sell-btn, .short-btn, [data-side="sell"]';
            
            await this.page.click(sideSelector, { timeout: 1000 });

            // Select order type (Market/Limit)
            if (config.type === 'market') {
                await this.page.click('.market-order, [data-type="market"]', { timeout: 1000 });
            } else {
                await this.page.click('.limit-order, [data-type="limit"]', { timeout: 1000 });
                // Fill price for limit orders
                if (config.price) {
                    await this.page.fill('.price-input, input[name="price"]', config.price);
                }
            }

            // Fill quantity
            const quantitySelectors = [
                '.quantity-input',
                'input[name="quantity"]',
                'input[name="amount"]',
                '.amount-input',
                '.size-input'
            ];

            let quantityFilled = false;
            for (const selector of quantitySelectors) {
                try {
                    await this.page.fill(selector, config.quantity, { timeout: 500 });
                    quantityFilled = true;
                    break;
                } catch {
                    continue;
                }
            }

            if (!quantityFilled) {
                throw new Error('Could not find quantity input field');
            }

            // Submit order
            const submitSelectors = [
                '.submit-order',
                '.place-order',
                '.order-submit',
                '.trade-btn',
                `button:has-text("${config.side === 'buy' ? 'Buy' : 'Sell'}")`,
                `button:has-text("${config.side === 'buy' ? 'Long' : 'Short'}")`
            ];

            let orderSubmitted = false;
            for (const selector of submitSelectors) {
                try {
                    await this.page.click(selector, { timeout: 500 });
                    orderSubmitted = true;
                    break;
                } catch {
                    continue;
                }
            }

            if (!orderSubmitted) {
                throw new Error('Could not find submit button');
            }

            // Wait for order confirmation
            try {
                await this.page.waitForSelector('.success-message, .order-success, .toast-success', { 
                    timeout: 1000 
                });
                console.log('✅ Order confirmation received');
            } catch {
                console.log('⚠️ No confirmation message, but order may have been placed');
            }

            const executionTime = Date.now() - startTime;
            console.log(`🎉 Order placed in ${executionTime}ms`);

            return {
                success: true,
                executionTime,
                config
            };

        } catch (error) {
            const executionTime = Date.now() - startTime;
            console.error(`❌ Order failed after ${executionTime}ms:`, error.message);
            
            return {
                success: false,
                executionTime,
                error: error.message,
                config: orderConfig
            };
        }
    }

    async getAccountInfo() {
        try {
            // Try to get balance information
            const balanceElements = await this.page.$$('.balance, .available-balance, .wallet-balance');
            const balances = {};
            
            for (const element of balanceElements) {
                const text = await element.textContent();
                if (text && text.includes('USDT')) {
                    balances.USDT = text.match(/[\d,]+\.?\d*/)?.[0];
                }
            }

            return { balances };
        } catch (error) {
            console.log('⚠️ Could not get account info:', error.message);
            return null;
        }
    }

    async close() {
        if (this.browser) {
            await this.browser.close();
            console.log('✅ Browser closed');
        }
    }
}

// Usage example and test function
async function testFastTrading() {
    const bot = new FastMEXCFuturesBot();
    
    try {
        await bot.initialize();
        
        const loginSuccess = await bot.login();
        if (!loginSuccess) {
            throw new Error('Login failed');
        }

        // Get account info
        const accountInfo = await bot.getAccountInfo();
        console.log('💰 Account Info:', accountInfo);

        // Test order configuration
        const orderConfig = {
            symbol: 'TRU_USDT',
            side: 'buy',
            type: 'market',
            quantity: '40', // Small quantity for testing
            leverage: '1'
        };

        console.log('⚠️ WARNING: About to place REAL order!');
        console.log('Order config:', orderConfig);
        console.log('Waiting 3 seconds... Press Ctrl+C to cancel');
        
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Place the order
        const result = await bot.placeFuturesOrder(orderConfig);
        
        console.log('\n📊 EXECUTION RESULTS:');
        console.log('Success:', result.success);
        console.log('Execution Time:', result.executionTime + 'ms');
        if (result.error) {
            console.log('Error:', result.error);
        }

        // Save results
        fs.writeFileSync('fast-trading-result.json', JSON.stringify(result, null, 2));
        
        return result;

    } catch (error) {
        console.error('💥 Test failed:', error.message);
        return { success: false, error: error.message };
    } finally {
        await bot.close();
    }
}

// Export for use as module
module.exports = FastMEXCFuturesBot;

// Run test if called directly
if (require.main === module) {
    testFastTrading()
        .then(result => {
            console.log('\n🏁 Test completed');
            process.exit(result.success ? 0 : 1);
        })
        .catch(error => {
            console.error('💥 Test crashed:', error);
            process.exit(1);
        });
}
