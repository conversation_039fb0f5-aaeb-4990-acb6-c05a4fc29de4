const axios = require('axios');

async function quickTest() {
    console.log('🧪 Quick System Test');
    console.log('===================');

    try {
        // Test basic open signal
        console.log('📡 Testing basic open signal...');
        const openSignal = {
            symbol: "TRUUSDT",
            trade: "open",
            last_price: "0.03295",
            leverage: "2"
        };

        const response = await axios.post('http://localhost:4000/webhook', openSignal);
        
        if (response.data.success) {
            console.log('✅ Open signal successful!');
            console.log(`   Trade ID: ${response.data.tradeId}`);
            console.log(`   Execution Time: ${response.data.executionTime}ms`);
            console.log(`   Position Size: ${response.data.positionSize}`);
        } else {
            console.log('❌ Open signal failed:', response.data.error);
        }

        // Wait and check positions
        console.log('\n⏳ Waiting 3 seconds...');
        await new Promise(resolve => setTimeout(resolve, 3000));

        console.log('📍 Checking positions...');
        const positionsResponse = await axios.get('http://localhost:4000/api/positions');
        console.log(`   Active Positions: ${positionsResponse.data.statistics.activePositions}`);

        if (positionsResponse.data.positions.length > 0) {
            const position = positionsResponse.data.positions[0];
            console.log(`   Position Details:`);
            console.log(`     Entry Price: $${position.entryPrice}`);
            console.log(`     Stop Loss: $${position.stopLoss}`);
            console.log(`     SL Type: ${position.slType}`);
            console.log(`     Take Profits: ${position.takeProfits.length}`);
        }

        // Test close signal
        console.log('\n📡 Testing close signal...');
        const closeSignal = {
            symbol: "TRUUSDT",
            trade: "close",
            last_price: "0.03295",
            leverage: "2"
        };

        const closeResponse = await axios.post('http://localhost:4000/webhook', closeSignal);
        
        if (closeResponse.data.success) {
            console.log('✅ Close signal successful!');
            console.log(`   Trade ID: ${closeResponse.data.tradeId}`);
            console.log(`   Execution Time: ${closeResponse.data.executionTime}ms`);
        } else {
            console.log('❌ Close signal failed:', closeResponse.data.error);
        }

    } catch (error) {
        console.log('❌ Test failed:', error.response?.data?.error || error.message);
    }

    console.log('\n🏁 Quick test completed');
}

quickTest();
