#!/usr/bin/env python3
"""
Fast MEXC Futures Trading Bot using <PERSON><PERSON>
Optimized for sub-2 second order execution
"""

import asyncio
import json
import os
import time
from pathlib import Path
from playwright.async_api import async_playwright
from dotenv import load_dotenv

load_dotenv()

class FastMEXCFuturesBot:
    def __init__(self):
        self.browser = None
        self.page = None
        self.is_logged_in = False
        self.session_file = 'mexc-session.json'
        
        # Configuration
        self.config = {
            'email': os.getenv('MEXC_EMAIL'),
            'password': os.getenv('MEXC_PASSWORD'),
            'headless': True,  # Set to False for debugging
            'timeout': 2000,   # 2 second timeout
            'fast_mode': True
        }

    async def initialize(self):
        print('🚀 Initializing Fast MEXC Futures Bot...')
        
        self.playwright = await async_playwright().start()
        
        # Launch browser with optimized settings for speed
        self.browser = await self.playwright.chromium.launch(
            headless=self.config['headless'],
            args=[
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ]
        )

        self.page = await self.browser.new_page()
        
        # Optimize page for speed
        await self.page.set_viewport_size({"width": 1920, "height": 1080})
        
        # Block unnecessary resources to speed up loading
        async def handle_route(route):
            resource_type = route.request.resource_type
            if resource_type in ['image', 'stylesheet', 'font', 'media']:
                await route.abort()
            else:
                await route.continue_()
        
        await self.page.route('**/*', handle_route)
        
        print('✅ Browser initialized')

    async def load_session(self):
        try:
            if Path(self.session_file).exists():
                with open(self.session_file, 'r') as f:
                    session_data = json.load(f)
                await self.page.context.add_cookies(session_data['cookies'])
                print('✅ Session loaded from file')
                return True
        except Exception as error:
            print(f'⚠️ Could not load session: {error}')
        return False

    async def save_session(self):
        try:
            cookies = await self.page.context.cookies()
            session_data = {'cookies': cookies, 'timestamp': int(time.time() * 1000)}
            with open(self.session_file, 'w') as f:
                json.dump(session_data, f, indent=2)
            print('✅ Session saved')
        except Exception as error:
            print(f'⚠️ Could not save session: {error}')

    async def login(self):
        print('🔐 Attempting login...')
        
        try:
            # Try to load existing session first
            await self.load_session()
            
            # Go to futures page to check if logged in
            await self.page.goto('https://futures.mexc.com/exchange/BTC_USDT', 
                                wait_until='domcontentloaded',
                                timeout=self.config['timeout'])

            # Check if already logged in
            try:
                await self.page.wait_for_selector('.user-info', timeout=1000)
                print('✅ Already logged in')
                self.is_logged_in = True
                return True
            except:
                print('🔄 Need to login...')

            # Go to login page
            await self.page.goto('https://www.mexc.com/login', 
                                wait_until='domcontentloaded',
                                timeout=self.config['timeout'])

            # Fill login form
            await self.page.fill('input[type="email"], input[name="email"]', self.config['email'])
            await self.page.fill('input[type="password"], input[name="password"]', self.config['password'])
            
            # Click login button
            await self.page.click('button[type="submit"], .login-btn, .btn-login')
            
            # Wait for login to complete
            await self.page.wait_for_url('**/futures/**', timeout=10000)
            
            print('✅ Login successful')
            self.is_logged_in = True
            
            # Save session for future use
            await self.save_session()
            
            return True
        except Exception as error:
            print(f'❌ Login failed: {error}')
            return False

    async def navigate_to_futures(self, symbol='BTC_USDT'):
        start_time = time.time()
        
        try:
            url = f'https://futures.mexc.com/exchange/{symbol}'
            await self.page.goto(url, 
                                wait_until='domcontentloaded',
                                timeout=self.config['timeout'])

            # Wait for trading interface to load
            await self.page.wait_for_selector('.trading-panel, .order-form, .trade-form', 
                                            timeout=self.config['timeout'])

            load_time = int((time.time() - start_time) * 1000)
            print(f'✅ Futures page loaded in {load_time}ms')
            return True
        except Exception as error:
            print(f'❌ Failed to navigate to futures: {error}')
            return False

    async def place_futures_order(self, order_config):
        start_time = time.time()
        print(f'🎯 Placing {order_config.get("side", "buy")} order for {order_config.get("symbol", "BTC_USDT")}...')

        try:
            # Default order configuration
            config = {
                'symbol': 'BTC_USDT',
                'side': 'buy',  # 'buy' or 'sell'
                'type': 'market',  # 'market' or 'limit'
                'quantity': '0.001',
                'price': None,  # Only for limit orders
                'leverage': '1',
                **order_config
            }

            # Navigate to the correct symbol if needed
            if config['symbol'] not in self.page.url:
                await self.navigate_to_futures(config['symbol'])

            # Set leverage first (if needed)
            try:
                await self.page.click('.leverage-selector, .leverage-btn', timeout=500)
                await self.page.fill('.leverage-input', config['leverage'])
                await self.page.press('.leverage-input', 'Enter')
            except:
                print('⚠️ Could not set leverage, using current setting')

            # Select order side (Buy/Sell)
            side_selector = ('.buy-btn, .long-btn, [data-side="buy"]' if config['side'] == 'buy' 
                           else '.sell-btn, .short-btn, [data-side="sell"]')
            
            await self.page.click(side_selector, timeout=1000)

            # Select order type (Market/Limit)
            if config['type'] == 'market':
                await self.page.click('.market-order, [data-type="market"]', timeout=1000)
            else:
                await self.page.click('.limit-order, [data-type="limit"]', timeout=1000)
                # Fill price for limit orders
                if config['price']:
                    await self.page.fill('.price-input, input[name="price"]', config['price'])

            # Fill quantity
            quantity_selectors = [
                '.quantity-input',
                'input[name="quantity"]',
                'input[name="amount"]',
                '.amount-input',
                '.size-input'
            ]

            quantity_filled = False
            for selector in quantity_selectors:
                try:
                    await self.page.fill(selector, config['quantity'], timeout=500)
                    quantity_filled = True
                    break
                except:
                    continue

            if not quantity_filled:
                raise Exception('Could not find quantity input field')

            # Submit order
            submit_selectors = [
                '.submit-order',
                '.place-order',
                '.order-submit',
                '.trade-btn',
                f'button:has-text("{config["side"].title()}")',
                f'button:has-text("{"Long" if config["side"] == "buy" else "Short"}")'
            ]

            order_submitted = False
            for selector in submit_selectors:
                try:
                    await self.page.click(selector, timeout=500)
                    order_submitted = True
                    break
                except:
                    continue

            if not order_submitted:
                raise Exception('Could not find submit button')

            # Wait for order confirmation
            try:
                await self.page.wait_for_selector('.success-message, .order-success, .toast-success', 
                                                timeout=1000)
                print('✅ Order confirmation received')
            except:
                print('⚠️ No confirmation message, but order may have been placed')

            execution_time = int((time.time() - start_time) * 1000)
            print(f'🎉 Order placed in {execution_time}ms')

            return {
                'success': True,
                'execution_time': execution_time,
                'config': config
            }

        except Exception as error:
            execution_time = int((time.time() - start_time) * 1000)
            print(f'❌ Order failed after {execution_time}ms: {error}')
            
            return {
                'success': False,
                'execution_time': execution_time,
                'error': str(error),
                'config': order_config
            }

    async def get_account_info(self):
        try:
            # Try to get balance information
            balance_elements = await self.page.query_selector_all('.balance, .available-balance, .wallet-balance')
            balances = {}
            
            for element in balance_elements:
                text = await element.text_content()
                if text and 'USDT' in text:
                    import re
                    match = re.search(r'[\d,]+\.?\d*', text)
                    if match:
                        balances['USDT'] = match.group(0)

            return {'balances': balances}
        except Exception as error:
            print(f'⚠️ Could not get account info: {error}')
            return None

    async def close(self):
        if self.browser:
            await self.browser.close()
            await self.playwright.stop()
            print('✅ Browser closed')

# Test function
async def test_fast_trading():
    bot = FastMEXCFuturesBot()
    
    try:
        await bot.initialize()
        
        login_success = await bot.login()
        if not login_success:
            raise Exception('Login failed')

        # Get account info
        account_info = await bot.get_account_info()
        print('💰 Account Info:', account_info)

        # Test order configuration
        order_config = {
            'symbol': 'TRU_USDT',
            'side': 'buy',
            'type': 'market',
            'quantity': '40',  # Small quantity for testing
            'leverage': '1'
        }

        print('⚠️ WARNING: About to place REAL order!')
        print('Order config:', order_config)
        print('Waiting 3 seconds... Press Ctrl+C to cancel')
        
        await asyncio.sleep(3)

        # Place the order
        result = await bot.place_futures_order(order_config)
        
        print('\n📊 EXECUTION RESULTS:')
        print('Success:', result['success'])
        print('Execution Time:', str(result['execution_time']) + 'ms')
        if 'error' in result:
            print('Error:', result['error'])

        # Save results
        with open('fast-trading-result-python.json', 'w') as f:
            json.dump(result, f, indent=2)
        
        return result

    except Exception as error:
        print(f'💥 Test failed: {error}')
        return {'success': False, 'error': str(error)}
    finally:
        await bot.close()

# Run test if called directly
if __name__ == '__main__':
    try:
        result = asyncio.run(test_fast_trading())
        print('\n🏁 Test completed')
        exit(0 if result['success'] else 1)
    except KeyboardInterrupt:
        print('\n❌ Test cancelled by user')
        exit(1)
    except Exception as error:
        print(f'💥 Test crashed: {error}')
        exit(1)
