#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

class BrowserManager {
    constructor() {
        this.browsers = [];
        this.isWindows = process.platform === 'win32';
        this.chromePath = this.findChromePath();
    }

    findChromePath() {
        // For Windows, try to find Chrome in common locations
        if (this.isWindows) {
            const possiblePaths = [
                'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
                'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe'
            ];

            for (const chromePath of possiblePaths) {
                if (fs.existsSync(chromePath)) {
                    return `"${chromePath}"`; // Quote the path for Windows
                }
            }
        }

        return 'chrome'; // Fallback to PATH
    }

    createBrowserDataDir(dirName) {
        const dir = path.join(process.cwd(), dirName);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
            console.log(`✅ Created directory: ${dir}`);
        } else {
            console.log(`📁 Directory exists: ${dir}`);
        }
        return dir;
    }

    async startBrowser(port, userDataDir, description) {
        return new Promise((resolve, reject) => {
            console.log(`🚀 Starting ${description} on port ${port}...`);
            
            const args = [
                `--remote-debugging-port=${port}`,
                `--user-data-dir=${userDataDir}`,
                '--disable-features=VizDisplayCompositor'
            ];

            const browser = spawn(this.chromePath, args, {
                detached: true,
                stdio: 'ignore'
            });

            browser.on('error', (error) => {
                console.error(`❌ Failed to start ${description}:`, error.message);
                reject(error);
            });

            // Give browser time to start
            setTimeout(() => {
                console.log(`✅ ${description} started successfully`);
                console.log(`   Port: ${port}`);
                console.log(`   Data Dir: ${userDataDir}`);
                console.log(`   PID: ${browser.pid}`);
                
                this.browsers.push({
                    port,
                    description,
                    process: browser,
                    pid: browser.pid
                });
                
                resolve(browser);
            }, 2000);
        });
    }

    async startAllBrowsers() {
        console.log('🌐 MEXC FUTURES TRADING - BROWSER MANAGER');
        console.log('==========================================');
        console.log(`Chrome Path: ${this.chromePath}`);
        console.log('');

        try {
            // Create browser data directory for single browser
            const closeDataDir = this.createBrowserDataDir('browser_data_close');

            console.log('');

            // Start single browser on port 9223
            await this.startBrowser(9223, closeDataDir, 'Single Browser (All Orders)');

            console.log('');
            console.log('🎯 SETUP INSTRUCTIONS:');
            console.log('======================');
            console.log('1. Open both browser windows that just launched');
            console.log('2. Navigate to: https://www.mexc.com/futures/TRU_USDT');
            console.log('3. Login to your MEXC account in BOTH browsers');
            console.log('4. Ensure you are on the futures trading page');
            console.log('5. Keep both browsers open and logged in');
            console.log('');
            console.log('📊 BROWSER PORT:');
            console.log('- Port 9223: All orders (Open/Close Long/Short)');
            console.log('');
            console.log('⚠️  IMPORTANT: Do NOT close these browser windows!');
            console.log('   The trading system depends on these browsers.');
            console.log('');
            console.log('✅ Browsers are ready for trading!');

        } catch (error) {
            console.error('❌ Failed to start browsers:', error.message);
            process.exit(1);
        }
    }

    async stopAllBrowsers() {
        console.log('🛑 Stopping all browsers...');
        
        for (const browser of this.browsers) {
            try {
                if (browser.process && !browser.process.killed) {
                    browser.process.kill();
                    console.log(`✅ Stopped ${browser.description} (PID: ${browser.pid})`);
                }
            } catch (error) {
                console.log(`⚠️ Error stopping ${browser.description}:`, error.message);
            }
        }
        
        this.browsers = [];
        console.log('✅ All browsers stopped');
    }

    showStatus() {
        console.log('📊 BROWSER STATUS:');
        console.log('==================');
        
        if (this.browsers.length === 0) {
            console.log('❌ No browsers running');
            return;
        }

        for (const browser of this.browsers) {
            const status = browser.process && !browser.process.killed ? '✅ Running' : '❌ Stopped';
            console.log(`${status} ${browser.description} (Port: ${browser.port}, PID: ${browser.pid})`);
        }
    }
}

// Handle command line arguments
async function main() {
    const browserManager = new BrowserManager();
    const command = process.argv[2];

    // Handle process termination
    process.on('SIGINT', async () => {
        console.log('\n🛑 Received interrupt signal...');
        await browserManager.stopAllBrowsers();
        process.exit(0);
    });

    process.on('SIGTERM', async () => {
        console.log('\n🛑 Received termination signal...');
        await browserManager.stopAllBrowsers();
        process.exit(0);
    });

    switch (command) {
        case 'start':
            await browserManager.startAllBrowsers();
            break;
        case 'stop':
            await browserManager.stopAllBrowsers();
            break;
        case 'status':
            browserManager.showStatus();
            break;
        case 'restart':
            await browserManager.stopAllBrowsers();
            setTimeout(async () => {
                await browserManager.startAllBrowsers();
            }, 3000);
            break;
        default:
            console.log('🌐 MEXC Browser Manager');
            console.log('=======================');
            console.log('Usage: node start-browsers.js [command]');
            console.log('');
            console.log('Commands:');
            console.log('  start   - Start both browsers (ports 9222 & 9223)');
            console.log('  stop    - Stop all browsers');
            console.log('  status  - Show browser status');
            console.log('  restart - Restart all browsers');
            console.log('');
            console.log('Example: node start-browsers.js start');
            break;
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = BrowserManager;
