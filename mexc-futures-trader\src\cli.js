#!/usr/bin/env node

const MexcFuturesTrader = require('./trader');

async function executeCommand(orderType, quantity = '0.3600') {
    // Determine port based on order type
    const port = orderType.includes('Close') ? 9223 : 9222;
    const trader = new MexcFuturesTrader(port);
    
    try {
        console.log('🎯 MEXC FUTURES TRADER');
        console.log('======================');
        console.log(`📊 Order: ${orderType}`);
        console.log(`💰 Quantity: ${quantity} USDT`);
        console.log(`🌐 Port: ${port}`);
        console.log(`⚡ Target: <2 seconds`);
        console.log('🧹 Enhanced cleanup & recovery');
        console.log('');

        const connected = await trader.connectToBrowser();
        if (!connected) {
            throw new Error(`Failed to connect to port ${port}`);
        }

        const result = await trader.executeOrder(orderType, quantity);

        if (result.success) {
            if (result.targetAchieved) {
                console.log('\n🏆 TARGET ACHIEVED!');
                console.log(`⚡ ${result.executionTime}ms (<2 seconds)`);
            } else {
                console.log('\n✅ SUCCESS!');
                console.log(`⏱️ ${result.executionTime}ms`);
            }
            
            if (result.emergencyRecovery) {
                console.log('🚨 Via emergency recovery!');
            }
        } else {
            console.log('\n❌ FAILED');
            console.log(`Error: ${result.error}`);
        }

        // Save result to file
        const fs = require('fs');
        const filename = `result-${orderType.replace(' ', '')}-${port}-${Date.now()}.json`;
        fs.writeFileSync(filename, JSON.stringify(result, null, 2));
        console.log(`\n💾 Result saved to: ${filename}`);

        process.exit(result.success ? 0 : 1);
        
    } catch (error) {
        console.error('💥 Failed:', error.message);
        process.exit(1);
    }
}

function showUsage() {
    console.log('🎯 MEXC FUTURES TRADER CLI');
    console.log('===========================');
    console.log('');
    console.log('📋 USAGE:');
    console.log('node src/cli.js <ORDER_TYPE> [QUANTITY]');
    console.log('');
    console.log('📊 ORDER TYPES:');
    console.log('  "Open Long"   - Open a long position');
    console.log('  "Open Short"  - Open a short position');
    console.log('  "Close Long"  - Close a long position');
    console.log('  "Close Short" - Close a short position');
    console.log('');
    console.log('💰 QUANTITY (optional):');
    console.log('  Default: 0.3600 USDT');
    console.log('  Example: 1.5000');
    console.log('');
    console.log('🌐 BROWSER PORTS:');
    console.log('  Open orders  → Port 9222');
    console.log('  Close orders → Port 9223');
    console.log('');
    console.log('📝 EXAMPLES:');
    console.log('  node src/cli.js "Open Long"');
    console.log('  node src/cli.js "Open Long" 1.5000');
    console.log('  node src/cli.js "Close Short" 0.5000');
    console.log('');
    console.log('🚀 BROWSER SETUP:');
    console.log('  chrome.exe --remote-debugging-port=9222 --user-data-dir="./browser_data_open"');
    console.log('  chrome.exe --remote-debugging-port=9223 --user-data-dir="./browser_data_close"');
    console.log('');
    console.log('⚡ Features:');
    console.log('  • Sub-2 second execution target');
    console.log('  • Smart quantity field handling');
    console.log('  • Automatic popup management');
    console.log('  • Error recovery system');
    console.log('  • Position validation for close orders');
}

if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length === 0 || args[0] === '--help' || args[0] === '-h') {
        showUsage();
        process.exit(0);
    }
    
    const orderType = args[0];
    const quantity = args[1] || '0.3600';
    
    const validOrders = ['Open Long', 'Open Short', 'Close Long', 'Close Short'];
    
    if (!validOrders.includes(orderType)) {
        console.error(`❌ Invalid order type: "${orderType}"`);
        console.error(`✅ Valid order types: ${validOrders.join(', ')}`);
        console.error('');
        showUsage();
        process.exit(1);
    }
    
    // Validate quantity
    const quantityNum = parseFloat(quantity);
    if (isNaN(quantityNum) || quantityNum <= 0) {
        console.error(`❌ Invalid quantity: "${quantity}"`);
        console.error('✅ Quantity must be a positive number (e.g., 0.3600)');
        process.exit(1);
    }
    
    executeCommand(orderType, quantity);
}

module.exports = { executeCommand, showUsage };
