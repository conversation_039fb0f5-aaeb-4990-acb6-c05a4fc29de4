#!/usr/bin/env node

/**
 * Test the optimized trader fix for Close trades
 */

const OptimizedMexcTrader = require('./mexc-api-testing/optimized-mexc-trader.js');

async function testOptimizedTraderFix() {
    console.log('🧪 Testing Optimized MEXC Trader Close Fix...\n');
    
    try {
        const trader = new OptimizedMexcTrader(9223);
        
        console.log('🔗 Connecting to browser...');
        const connected = await trader.connectToBrowser();
        
        if (!connected) {
            throw new Error('Failed to connect to browser');
        }
        
        console.log('✅ Connected to browser successfully');
        
        // Test Close Short execution (the one that was failing)
        console.log('\n🧪 Testing Close Short execution...');
        
        try {
            const result = await trader.executeCloseTrade('Close Short', '1.1291');
            
            if (result.success) {
                console.log('🎉 SUCCESS! Close Short executed successfully');
                console.log(`⚡ Execution time: ${result.executionTime}ms`);
                console.log(`✅ Verified: ${result.verified}`);
                
                return { 
                    success: true, 
                    message: 'Close Short fix verified and working',
                    executionTime: result.executionTime
                };
            } else {
                console.log('❌ FAILED: Close Short execution failed');
                console.log(`Error: ${result.error}`);
                
                return { 
                    success: false, 
                    message: `Close Short failed: ${result.error}`,
                    executionTime: result.executionTime || 0
                };
            }
        } catch (error) {
            console.log(`❌ Close Short test failed: ${error.message}`);
            return { 
                success: false, 
                message: error.message,
                executionTime: 0
            };
        }
        
    } catch (error) {
        console.error('❌ Test setup failed:', error.message);
        return { success: false, message: error.message, executionTime: 0 };
    }
}

// Run test if called directly
if (require.main === module) {
    testOptimizedTraderFix().then(result => {
        console.log('\n📊 Test Result:');
        console.log('================');
        console.log(`Status: ${result.success ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`Message: ${result.message}`);
        console.log(`Execution Time: ${result.executionTime}ms`);
        
        if (result.success) {
            console.log('\n🎯 The optimized trader fix is working!');
            console.log('Your SL/TP system should now execute close trades successfully.');
            console.log('\n📋 Next Steps:');
            console.log('1. Your system should now handle Close Long/Short trades properly');
            console.log('2. SL/TP executions should complete without timeout errors');
            console.log('3. Monitor the next few trades to confirm the fix is working');
        } else {
            console.log('\n⚠️ The fix needs more investigation.');
            console.log('Please check the browser state and MEXC interface.');
        }
        
        process.exit(result.success ? 0 : 1);
    }).catch(error => {
        console.error('❌ Test execution failed:', error);
        process.exit(1);
    });
}

module.exports = testOptimizedTraderFix;
