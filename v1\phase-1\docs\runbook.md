# Runbook: Place and Cancel via Edge UI (CDP)

## Prereqs
- Windows + Edge
- Python venv ready with Playwright
- Start Edge with remote debugging and your profile:
  "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe" --remote-debugging-port=9222 --user-data-dir="C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Edge\\User Data"
- In that Edge, log into MEXC and open the Futures trading page

## Sniff the live order flow (manual place/cancel)
- python webtrader_edge_cdp_sniffer.py
- In Edge: place a Post‑Only order far from market; then cancel
- Terminal will print the exact submit and cancel endpoints and responses

## Automate place+cancel (UI)
- (To be added) python webtrader_edge_cdp_place_cancel_ui.py --symbol TRU_USDT --side long --price 0.02 --vol 1 --post-only
- It will attach to your Edge, fill the form, submit, read orderId, and cancel. Outputs JSON.

## Troubleshooting
- Could not connect to Edge CDP
  - Ensure Edge started with --remote-debugging-port=9222
  - Visit http://127.0.0.1:9222/json/version to verify WebSocketDebuggerUrl
- Login blocked / session expired
  - Log in inside Edge window; complete 2FA; keep that window open
- Order unexpectedly filled
  - Use Post‑Only + price far from market; verify the Post‑Only toggle is on
- 401/602 from raw XHR
  - Expected when not using UI clicks; stick to the UI‑automation script

## Notes
- Avoid recording or persisting sensitive headers (authorization, x‑mxc‑sign, etc.)
- Logs are for local debugging only; do not commit raw header/body dumps

