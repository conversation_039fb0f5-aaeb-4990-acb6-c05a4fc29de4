const { spawn } = require('child_process');
const axios = require('axios');
const path = require('path');

class ServiceManager {
    constructor() {
        this.services = [];
        this.webhookListenerProcess = null;
        this.mexcTraderProcess = null;
    }

    log(message) {
        console.log(`[${new Date().toISOString()}] ${message}`);
    }

    async startWebhookListener() {
        this.log('🚀 Starting TradingView Webhook Listener...');
        
        return new Promise((resolve, reject) => {
            const webhookPath = path.join(__dirname, 'tradingview-webhook-listener');
            
            this.webhookListenerProcess = spawn('node', ['src/server.js'], {
                cwd: webhookPath,
                stdio: ['pipe', 'pipe', 'pipe']
            });

            this.webhookListenerProcess.stdout.on('data', (data) => {
                console.log(`[Webhook] ${data.toString().trim()}`);
            });

            this.webhookListenerProcess.stderr.on('data', (data) => {
                console.error(`[Webhook Error] ${data.toString().trim()}`);
            });

            this.webhookListenerProcess.on('error', (error) => {
                this.log(`❌ Webhook Listener failed to start: ${error.message}`);
                reject(error);
            });

            // Wait for service to start
            setTimeout(async () => {
                try {
                    const response = await axios.get('http://localhost:4000/api/status', { timeout: 5000 });
                    if (response.data.status === 'healthy') {
                        this.log('✅ Webhook Listener started successfully');
                        resolve();
                    } else {
                        reject(new Error('Webhook Listener not healthy'));
                    }
                } catch (error) {
                    this.log('⚠️ Webhook Listener may still be starting...');
                    resolve(); // Continue anyway
                }
            }, 5000);
        });
    }

    async startMexcTrader() {
        this.log('🚀 Starting MEXC Futures Trader...');
        
        return new Promise((resolve, reject) => {
            const traderPath = path.join(__dirname, 'mexc-futures-trader');
            
            this.mexcTraderProcess = spawn('node', ['single-browser-trader.js'], {
                cwd: traderPath,
                stdio: ['pipe', 'pipe', 'pipe']
            });

            this.mexcTraderProcess.stdout.on('data', (data) => {
                console.log(`[MEXC Trader] ${data.toString().trim()}`);
            });

            this.mexcTraderProcess.stderr.on('data', (data) => {
                console.error(`[MEXC Trader Error] ${data.toString().trim()}`);
            });

            this.mexcTraderProcess.on('error', (error) => {
                this.log(`❌ MEXC Trader failed to start: ${error.message}`);
                reject(error);
            });

            // Wait for service to start
            setTimeout(async () => {
                try {
                    const response = await axios.get('http://localhost:3000/health', { timeout: 5000 });
                    if (response.data.status === 'healthy') {
                        this.log('✅ MEXC Trader started successfully');
                        resolve();
                    } else {
                        reject(new Error('MEXC Trader not healthy'));
                    }
                } catch (error) {
                    this.log('⚠️ MEXC Trader may still be starting...');
                    resolve(); // Continue anyway
                }
            }, 8000);
        });
    }

    async checkServiceHealth() {
        this.log('🔍 Checking service health...');
        
        const services = [
            { name: 'Webhook Listener', url: 'http://localhost:4000/api/status' },
            { name: 'MEXC Trader', url: 'http://localhost:3000/health' }
        ];

        for (const service of services) {
            try {
                const response = await axios.get(service.url, { timeout: 5000 });
                const status = response.data.status === 'healthy' ? '✅' : '⚠️';
                this.log(`${status} ${service.name}: ${response.data.status}`);
            } catch (error) {
                this.log(`❌ ${service.name}: Not responding (${error.message})`);
            }
        }
    }

    async startAllServices() {
        this.log('🎬 Starting all services for testing...');
        
        try {
            // Start services in parallel
            await Promise.all([
                this.startWebhookListener(),
                this.startMexcTrader()
            ]);

            this.log('✅ All services started successfully!');
            
            // Check health after startup
            await this.sleep(3000);
            await this.checkServiceHealth();
            
            return true;
            
        } catch (error) {
            this.log(`❌ Failed to start services: ${error.message}`);
            return false;
        }
    }

    async stopAllServices() {
        this.log('🛑 Stopping all services...');
        
        if (this.webhookListenerProcess) {
            this.webhookListenerProcess.kill();
            this.log('✅ Webhook Listener stopped');
        }
        
        if (this.mexcTraderProcess) {
            this.mexcTraderProcess.kill();
            this.log('✅ MEXC Trader stopped');
        }
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    setupGracefulShutdown() {
        process.on('SIGINT', async () => {
            this.log('🛑 Received SIGINT, shutting down gracefully...');
            await this.stopAllServices();
            process.exit(0);
        });

        process.on('SIGTERM', async () => {
            this.log('🛑 Received SIGTERM, shutting down gracefully...');
            await this.stopAllServices();
            process.exit(0);
        });
    }
}

// Main execution
if (require.main === module) {
    const serviceManager = new ServiceManager();
    serviceManager.setupGracefulShutdown();
    
    serviceManager.startAllServices()
        .then(success => {
            if (success) {
                console.log('\n🎉 All services are running and ready for testing!');
                console.log('📡 Webhook Listener: http://localhost:4000');
                console.log('🤖 MEXC Trader: http://localhost:3000');
                console.log('\n💡 You can now run the comprehensive test with:');
                console.log('   node comprehensive-system-test.js');
                console.log('\n⏹️ Press Ctrl+C to stop all services');
                
                // Keep the process running
                setInterval(() => {
                    // Keep alive
                }, 60000);
            } else {
                console.log('\n❌ Failed to start services. Check the logs above.');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('\n💥 Startup failed:', error.message);
            process.exit(1);
        });
}

module.exports = ServiceManager;
