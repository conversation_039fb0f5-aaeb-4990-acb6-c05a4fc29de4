const { chromium } = require('playwright');

async function startRemoteBrowser() {
    console.log('🚀 Starting Chrome with remote debugging...');
    
    const browser = await chromium.launch({
        headless: false,
        args: [
            '--remote-debugging-port=9222',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--no-sandbox',
            '--disable-setuid-sandbox'
        ]
    });

    const context = await browser.newContext();
    const page = await context.newPage();
    
    // Navigate to MEXC futures
    await page.goto('https://futures.mexc.com/exchange/TRU_USDT');
    
    console.log('✅ Browser started with remote debugging on port 9222');
    console.log('🔗 You can now connect to: http://localhost:9222');
    console.log('📊 MEXC Futures page loaded: https://futures.mexc.com/exchange/TRU_USDT');
    console.log('');
    console.log('⚠️  Please login manually if needed, then I will take control');
    console.log('🎯 Target: Place TRUUSDT futures order under 2 seconds');
    console.log('');
    console.log('Press Ctrl+C to stop the browser');
    
    // Keep the browser running
    process.on('SIGINT', async () => {
        console.log('\n🛑 Closing browser...');
        await browser.close();
        process.exit(0);
    });
    
    // Keep alive
    setInterval(() => {
        console.log(`⏰ Browser still running... ${new Date().toLocaleTimeString()}`);
    }, 30000);
}

startRemoteBrowser().catch(console.error);
