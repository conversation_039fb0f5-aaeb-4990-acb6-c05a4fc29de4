#!/usr/bin/env python3
"""
Signature Reuse Executor
Test if we can reuse captured signatures with new nonces
"""

import json
import time
import random
import string
import hashlib
from curl_cffi import requests
from dotenv import dotenv_values

class SignatureReuseExecutor:
    """Test signature reuse with different approaches"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        self.uc_token = vals.get('uc_token')
        self.session = requests.Session(impersonate='chrome124')
        
        print("🔄 Signature Reuse Executor")
        print("="*30)
    
    def test_signature_approaches(self):
        """Test different approaches with captured signature"""
        
        # Use the captured signature from your test
        captured_signature = "08e144808f7b6544ed2644207b605666"
        captured_nonce = "1754924669553"
        
        print(f"🔍 Testing signature: {captured_signature}")
        print(f"🔢 Original nonce: {captured_nonce}")
        
        # Test order data
        order_data = {
            'symbol': 'TRU_USDT',
            'side': 1,
            'openType': 1,
            'type': '2',
            'vol': 1,
            'leverage': 1,
            'marketCeiling': False,
            'price': '0.03',
            'priceProtect': '0'
        }
        
        # Add opaque parameters
        new_nonce = str(int(time.time() * 1000))
        p0 = hashlib.md5(f"{new_nonce}{json.dumps(order_data)}{self.auth}".encode()).hexdigest()
        k0 = hashlib.md5(f"{time.time()}{random.random()}".encode()).hexdigest()[:16]
        
        order_data['p0'] = p0
        order_data['k0'] = k0
        
        print(f"📋 Order data: {json.dumps(order_data, indent=2)}")
        
        # Test different approaches
        approaches = [
            ("Same signature + Same nonce", captured_signature, captured_nonce),
            ("Same signature + New nonce", captured_signature, new_nonce),
            ("Same signature + Modified nonce", captured_signature, str(int(captured_nonce) + 1)),
            ("Same signature + Current timestamp", captured_signature, str(int(time.time() * 1000))),
        ]
        
        for approach_name, signature, nonce in approaches:
            print(f"\n🧪 Testing: {approach_name}")
            print(f"   Signature: {signature}")
            print(f"   Nonce: {nonce}")
            
            result = self._test_order_execution(signature, nonce, order_data)
            
            if result.get('success'):
                print(f"   ✅ SUCCESS! Order ID: {result.get('order_id')}")
                
                # Cancel the order
                if result.get('order_id'):
                    time.sleep(1)
                    self._cancel_order(str(result['order_id']))
                
                return True
            else:
                error_code = result.get('error_code')
                error_msg = result.get('error_msg', 'Unknown')
                print(f"   ❌ Failed: {error_code} - {error_msg}")
                
                if error_code == 602:
                    print(f"      → Signature verification failed")
                elif error_code == 401:
                    print(f"      → Authentication failed")
                elif error_code == 403:
                    print(f"      → Forbidden (possibly nonce reuse)")
        
        return False
    
    def _test_order_execution(self, signature: str, nonce: str, order_data: dict) -> dict:
        """Test order execution with given signature and nonce"""
        
        # Prepare headers
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Origin': 'https://futures.mexc.com',
            'Referer': 'https://futures.mexc.com/exchange',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'authorization': self.auth,
            'x-mxc-nonce': nonce,
            'x-mxc-sign': signature,
            'x-language': 'en_US',
        }
        
        if self.uc_token:
            headers['mtoken'] = self.uc_token
        
        try:
            # Generate mhash
            mhash = ''.join(random.choices(string.ascii_lowercase + string.digits, k=32))
            url = f'https://futures.mexc.com/api/v1/private/order/create?mhash={mhash}'
            
            r = self.session.post(url, json=order_data, headers=headers)
            
            print(f"      📡 Response: {r.status_code}")
            
            if r.status_code == 200:
                result = r.json()
                
                if result.get('success') and result.get('code') == 0:
                    order_id = result.get('data', {}).get('orderId')
                    return {
                        'success': True,
                        'order_id': order_id,
                        'result': result
                    }
                else:
                    return {
                        'success': False,
                        'error_code': result.get('code'),
                        'error_msg': result.get('message', 'Unknown')
                    }
            else:
                return {
                    'success': False,
                    'error_code': r.status_code,
                    'error_msg': f'HTTP {r.status_code}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error_code': 'exception',
                'error_msg': str(e)
            }
    
    def _cancel_order(self, order_id: str):
        """Cancel order"""
        
        print(f"      🔄 Canceling order {order_id}...")
        
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'authorization': self.auth,
            'x-mxc-nonce': str(int(time.time() * 1000)),
        }
        
        if self.uc_token:
            headers['mtoken'] = self.uc_token
        
        try:
            r = self.session.post('https://futures.mexc.com/api/v1/private/order/cancel',
                                json=[order_id], headers=headers)
            
            if r.status_code == 200:
                result = r.json()
                if result.get('success') and result.get('code') == 0:
                    print(f"      ✅ Order canceled")
                else:
                    print(f"      ⚠️ Cancel failed: {result.get('message', 'Unknown')}")
            else:
                print(f"      ⚠️ Cancel HTTP error: {r.status_code}")
                
        except Exception as e:
            print(f"      ❌ Cancel error: {e}")
    
    def test_signature_generation_patterns(self):
        """Test if we can generate working signatures"""
        
        print(f"\n🔬 Testing signature generation patterns...")
        
        # Test order data
        order_data = {
            'symbol': 'TRU_USDT',
            'side': 1,
            'openType': 1,
            'type': '2',
            'vol': 1,
            'leverage': 1,
            'marketCeiling': False,
            'price': '0.03',
            'priceProtect': '0'
        }
        
        nonce = str(int(time.time() * 1000))
        
        # Add opaque parameters
        p0 = hashlib.md5(f"{nonce}{json.dumps(order_data)}{self.auth}".encode()).hexdigest()
        k0 = hashlib.md5(f"{time.time()}{random.random()}".encode()).hexdigest()[:16]
        
        order_data['p0'] = p0
        order_data['k0'] = k0
        
        # Test different signature generation methods
        signature_methods = [
            ("MD5(auth+nonce+json)", lambda: hashlib.md5((self.auth + nonce + json.dumps(order_data, sort_keys=True)).encode()).hexdigest()),
            ("SHA256(auth+nonce+json)[:32]", lambda: hashlib.sha256((self.auth + nonce + json.dumps(order_data, sort_keys=True)).encode()).hexdigest()[:32]),
            ("MD5(nonce+auth+json)", lambda: hashlib.md5((nonce + self.auth + json.dumps(order_data, sort_keys=True)).encode()).hexdigest()),
            ("MD5(json+nonce+auth)", lambda: hashlib.md5((json.dumps(order_data, sort_keys=True) + nonce + self.auth).encode()).hexdigest()),
        ]
        
        for method_name, method_func in signature_methods:
            try:
                signature = method_func()
                print(f"\n🧪 Testing: {method_name}")
                print(f"   Generated: {signature}")
                
                result = self._test_order_execution(signature, nonce, order_data)
                
                if result.get('success'):
                    print(f"   🎉 SUCCESS! Found working algorithm!")
                    print(f"   Order ID: {result.get('order_id')}")
                    
                    # Cancel the order
                    if result.get('order_id'):
                        time.sleep(1)
                        self._cancel_order(str(result['order_id']))
                    
                    return True
                else:
                    error_code = result.get('error_code')
                    print(f"   ❌ Failed: {error_code}")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        return False

def main():
    """Main signature reuse test"""
    
    executor = SignatureReuseExecutor()
    
    print("🔄 SIGNATURE REUSE TESTING")
    print("="*40)
    
    # Test 1: Signature reuse approaches
    print("\n🧪 TEST 1: Signature Reuse Approaches")
    success1 = executor.test_signature_approaches()
    
    # Test 2: Signature generation patterns
    print("\n🧪 TEST 2: Signature Generation Patterns")
    success2 = executor.test_signature_generation_patterns()
    
    # Results
    print(f"\n{'='*40}")
    print("SIGNATURE TESTING RESULTS")
    print("="*40)
    
    if success1:
        print("✅ Signature reuse: SUCCESSFUL")
        print("🚀 Can reuse captured signatures!")
    else:
        print("❌ Signature reuse: FAILED")
    
    if success2:
        print("✅ Signature generation: SUCCESSFUL")
        print("🚀 Found working signature algorithm!")
    else:
        print("❌ Signature generation: FAILED")
    
    if success1 or success2:
        print(f"\n🎉 BREAKTHROUGH ACHIEVED!")
        print("Ready for production implementation!")
    else:
        print(f"\n🔧 Need more analysis")
        print("Consider browser automation as fallback")

if __name__ == '__main__':
    main()
