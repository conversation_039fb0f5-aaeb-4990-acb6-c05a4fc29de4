const { chromium } = require('playwright');
require('dotenv').config();

class HumanLikeMEXCTrader {
    constructor() {
        this.browser = null;
        this.page = null;
        this.startTime = null;
    }

    async initialize() {
        console.log('🚀 Starting human-like MEXC trader...');
        
        // Launch browser with human-like settings
        this.browser = await chromium.launch({
            headless: false,
            slowMo: 50, // Small delay to appear more human
            args: [
                '--disable-blink-features=AutomationControlled',
                '--disable-dev-shm-usage',
                '--no-first-run',
                '--disable-default-apps',
                '--disable-extensions-file-access-check',
                '--disable-extensions-http-throttling',
                '--disable-extensions-https-throttling'
            ]
        });

        const context = await this.browser.newContext({
            viewport: { width: 1920, height: 1080 },
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            locale: 'en-US',
            timezoneId: 'America/New_York'
        });

        this.page = await context.newPage();

        // Add human-like properties to avoid detection
        await this.page.addInitScript(() => {
            // Remove webdriver property
            delete navigator.__proto__.webdriver;
            
            // Override plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5]
            });
            
            // Override languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en']
            });
        });

        console.log('✅ Browser initialized with human-like settings');
    }

    async navigateToMEXC() {
        console.log('🌐 Navigating to MEXC futures...');

        try {
            await this.page.goto('https://futures.mexc.com/exchange/TRU_USDT', {
                waitUntil: 'domcontentloaded',
                timeout: 10000
            });
        } catch (error) {
            console.log('⚠️ First attempt failed, trying alternative approach...');
            await this.page.goto('https://futures.mexc.com/', {
                waitUntil: 'domcontentloaded',
                timeout: 10000
            });
            await this.page.waitForTimeout(2000);
            // Navigate to TRU_USDT manually
            await this.page.goto('https://futures.mexc.com/exchange/TRU_USDT', {
                waitUntil: 'domcontentloaded',
                timeout: 10000
            });
        }

        // Wait for page to load
        await this.page.waitForTimeout(3000);
        console.log('✅ MEXC futures page loaded');
    }

    async humanTouch(selector, options = {}) {
        const element = await this.page.locator(selector).first();
        
        // Get element position
        const box = await element.boundingBox();
        if (!box) {
            throw new Error(`Element not found: ${selector}`);
        }

        // Calculate random position within element
        const x = box.x + box.width * (0.3 + Math.random() * 0.4);
        const y = box.y + box.height * (0.3 + Math.random() * 0.4);

        // Simulate human-like touch sequence
        await this.page.mouse.move(x, y, { steps: 3 });
        await this.page.waitForTimeout(50 + Math.random() * 100);
        
        // Use touch events instead of click
        await this.page.touchscreen.tap(x, y);
        
        console.log(`👆 Touched element: ${selector} at (${Math.round(x)}, ${Math.round(y)})`);
        
        // Small delay after touch
        await this.page.waitForTimeout(100 + Math.random() * 200);
    }

    async humanType(selector, text, options = {}) {
        await this.page.locator(selector).first().focus();
        await this.page.waitForTimeout(100);
        
        // Clear existing text
        await this.page.keyboard.press('Control+a');
        await this.page.waitForTimeout(50);
        
        // Type with human-like delays
        for (const char of text) {
            await this.page.keyboard.type(char);
            await this.page.waitForTimeout(50 + Math.random() * 100);
        }
        
        console.log(`⌨️ Typed: ${text} into ${selector}`);
    }

    async analyzePageElements() {
        console.log('🔍 Analyzing MEXC page elements...');
        
        // Get page content
        const content = await this.page.content();
        
        // Look for common trading elements
        const selectors = [
            // Buy/Sell buttons
            'button[class*="buy"]',
            'button[class*="sell"]',
            'button[class*="long"]',
            'button[class*="short"]',
            '.buy-btn',
            '.sell-btn',
            '.long-btn',
            '.short-btn',
            
            // Order type buttons
            'button[class*="market"]',
            'button[class*="limit"]',
            '.market-btn',
            '.limit-btn',
            
            // Input fields
            'input[class*="amount"]',
            'input[class*="quantity"]',
            'input[class*="size"]',
            'input[placeholder*="amount"]',
            'input[placeholder*="quantity"]',
            
            // Submit buttons
            'button[class*="submit"]',
            'button[class*="place"]',
            'button[class*="confirm"]',
            '.submit-btn',
            '.place-order-btn'
        ];

        const foundElements = {};
        
        for (const selector of selectors) {
            try {
                const elements = await this.page.locator(selector).all();
                if (elements.length > 0) {
                    foundElements[selector] = elements.length;
                    console.log(`✅ Found ${elements.length} elements for: ${selector}`);
                }
            } catch (error) {
                // Element not found, continue
            }
        }

        // Also check for text-based elements
        const textElements = [
            'Buy', 'Sell', 'Long', 'Short', 'Market', 'Limit', 'Place Order', 'Submit'
        ];

        for (const text of textElements) {
            try {
                const elements = await this.page.getByText(text, { exact: false }).all();
                if (elements.length > 0) {
                    console.log(`✅ Found ${elements.length} elements with text: "${text}"`);
                }
            } catch (error) {
                // Continue
            }
        }

        return foundElements;
    }

    async placeFuturesOrder() {
        this.startTime = Date.now();
        console.log('🎯 Starting futures order placement...');
        
        try {
            // Step 1: Analyze current page
            await this.analyzePageElements();
            
            // Step 2: Look for and click Buy button
            console.log('📈 Looking for Buy button...');
            
            const buySelectors = [
                'button:has-text("Buy")',
                'button:has-text("Long")',
                '.buy-btn',
                '.long-btn',
                'button[class*="buy"]',
                'button[class*="long"]'
            ];

            let buyClicked = false;
            for (const selector of buySelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    if (await element.isVisible({ timeout: 1000 })) {
                        await this.humanTouch(selector);
                        buyClicked = true;
                        console.log('✅ Buy button clicked');
                        break;
                    }
                } catch (error) {
                    continue;
                }
            }

            if (!buyClicked) {
                throw new Error('Could not find Buy button');
            }

            await this.page.waitForTimeout(500);

            // Step 3: Select Market order
            console.log('📊 Looking for Market order option...');
            
            const marketSelectors = [
                'button:has-text("Market")',
                '.market-btn',
                'button[class*="market"]',
                '[data-type="market"]'
            ];

            let marketClicked = false;
            for (const selector of marketSelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    if (await element.isVisible({ timeout: 1000 })) {
                        await this.humanTouch(selector);
                        marketClicked = true;
                        console.log('✅ Market order selected');
                        break;
                    }
                } catch (error) {
                    continue;
                }
            }

            await this.page.waitForTimeout(300);

            // Step 4: Enter quantity
            console.log('🔢 Looking for quantity input...');
            
            const quantitySelectors = [
                'input[placeholder*="amount"]',
                'input[placeholder*="quantity"]',
                'input[placeholder*="size"]',
                'input[class*="amount"]',
                'input[class*="quantity"]',
                'input[class*="size"]',
                '.amount-input',
                '.quantity-input'
            ];

            let quantityEntered = false;
            for (const selector of quantitySelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    if (await element.isVisible({ timeout: 1000 })) {
                        await this.humanType(selector, '40');
                        quantityEntered = true;
                        console.log('✅ Quantity entered: 40');
                        break;
                    }
                } catch (error) {
                    continue;
                }
            }

            if (!quantityEntered) {
                console.log('⚠️ Could not find quantity input, trying alternative approach...');
                
                // Try clicking on percentage buttons or preset amounts
                const presetSelectors = [
                    'button:has-text("25%")',
                    'button:has-text("50%")',
                    'button:has-text("75%")',
                    'button:has-text("100%")'
                ];

                for (const selector of presetSelectors) {
                    try {
                        const element = this.page.locator(selector).first();
                        if (await element.isVisible({ timeout: 500 })) {
                            await this.humanTouch(selector);
                            console.log('✅ Used preset amount');
                            quantityEntered = true;
                            break;
                        }
                    } catch (error) {
                        continue;
                    }
                }
            }

            await this.page.waitForTimeout(500);

            // Step 5: Submit order
            console.log('🚀 Looking for submit button...');
            
            const submitSelectors = [
                'button:has-text("Buy")',
                'button:has-text("Long")',
                'button:has-text("Place Order")',
                'button:has-text("Submit")',
                'button:has-text("Confirm")',
                '.submit-btn',
                '.place-order-btn',
                '.confirm-btn',
                'button[class*="submit"]',
                'button[class*="place"]',
                'button[class*="confirm"]'
            ];

            let orderSubmitted = false;
            for (const selector of submitSelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    if (await element.isVisible({ timeout: 1000 })) {
                        await this.humanTouch(selector);
                        orderSubmitted = true;
                        console.log('✅ Order submitted!');
                        break;
                    }
                } catch (error) {
                    continue;
                }
            }

            if (!orderSubmitted) {
                throw new Error('Could not find submit button');
            }

            // Step 6: Wait for confirmation
            await this.page.waitForTimeout(1000);
            
            // Look for success messages
            const successSelectors = [
                '.success',
                '.toast-success',
                '.notification-success',
                '[class*="success"]',
                'text=success',
                'text=Success',
                'text=placed',
                'text=Placed'
            ];

            let confirmationFound = false;
            for (const selector of successSelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    if (await element.isVisible({ timeout: 2000 })) {
                        confirmationFound = true;
                        console.log('🎉 Order confirmation detected!');
                        break;
                    }
                } catch (error) {
                    continue;
                }
            }

            const executionTime = Date.now() - this.startTime;
            
            console.log('\n📊 EXECUTION RESULTS:');
            console.log('====================');
            console.log(`⏱️ Total execution time: ${executionTime}ms`);
            console.log(`🎯 Target achieved: ${executionTime < 2000 ? '✅ YES' : '❌ NO'} (target: <2000ms)`);
            console.log(`📋 Order submitted: ${orderSubmitted ? '✅ YES' : '❌ NO'}`);
            console.log(`🎉 Confirmation: ${confirmationFound ? '✅ DETECTED' : '⚠️ NOT DETECTED'}`);

            return {
                success: orderSubmitted,
                executionTime,
                targetAchieved: executionTime < 2000,
                confirmationDetected: confirmationFound
            };

        } catch (error) {
            const executionTime = Date.now() - this.startTime;
            console.error(`❌ Order placement failed after ${executionTime}ms:`, error.message);
            
            return {
                success: false,
                executionTime,
                error: error.message
            };
        }
    }

    async close() {
        if (this.browser) {
            await this.browser.close();
            console.log('✅ Browser closed');
        }
    }
}

async function runTrader() {
    const trader = new HumanLikeMEXCTrader();
    
    try {
        await trader.initialize();
        await trader.navigateToMEXC();
        
        console.log('\n⚠️ WARNING: About to place REAL futures order!');
        console.log('🎯 Symbol: TRUUSDT');
        console.log('📈 Side: Buy/Long');
        console.log('🔢 Quantity: 40');
        console.log('⏱️ Target: <2 seconds');
        console.log('\nStarting in 3 seconds...');
        
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        const result = await trader.placeFuturesOrder();
        
        // Save results
        require('fs').writeFileSync(
            'human-trader-results.json',
            JSON.stringify(result, null, 2)
        );
        
        if (result.success && result.targetAchieved) {
            console.log('\n🏆 MISSION ACCOMPLISHED!');
            console.log('✅ Order placed successfully under 2 seconds!');
        } else if (result.success) {
            console.log('\n✅ Order placed successfully!');
            console.log('⚠️ But execution time exceeded 2 seconds');
        } else {
            console.log('\n❌ Order placement failed');
            console.log('🔧 Check the error details and page elements');
        }
        
        return result;
        
    } catch (error) {
        console.error('💥 Trader crashed:', error.message);
        return { success: false, error: error.message };
    } finally {
        // Keep browser open for 10 seconds to see results
        console.log('\n⏳ Keeping browser open for 10 seconds to verify results...');
        await new Promise(resolve => setTimeout(resolve, 10000));
        await trader.close();
    }
}

// Run the trader
if (require.main === module) {
    runTrader()
        .then(result => {
            console.log('\n🏁 Trading session completed');
            process.exit(result.success ? 0 : 1);
        })
        .catch(error => {
            console.error('💥 Session crashed:', error);
            process.exit(1);
        });
}

module.exports = HumanLikeMEXCTrader;
