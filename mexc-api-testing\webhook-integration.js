const express = require('express');
const { spawn } = require('child_process');
const path = require('path');

class WebhookIntegration {
    constructor() {
        this.app = express();
        this.port = process.env.WEBHOOK_PORT || 3000;
        this.setupMiddleware();
        this.setupRoutes();
    }

    setupMiddleware() {
        this.app.use(express.json());
        this.app.use(express.urlencoded({ extended: true }));
        
        // CORS
        this.app.use((req, res, next) => {
            res.header('Access-Control-Allow-Origin', '*');
            res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
            res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            next();
        });

        // Logging
        this.app.use((req, res, next) => {
            console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
            next();
        });
    }

    setupRoutes() {
        // Health check
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'ok',
                service: 'mexc-webhook-integration',
                timestamp: new Date().toISOString()
            });
        });

        // TradingView webhook endpoint
        this.app.post('/webhook', async (req, res) => {
            const startTime = Date.now();
            
            try {
                console.log('📡 Webhook received:', JSON.stringify(req.body, null, 2));
                
                const signal = this.parseSignal(req.body);
                console.log('🎯 Parsed signal:', signal);

                if (!this.validateSignal(signal)) {
                    return res.status(400).json({
                        success: false,
                        error: 'Invalid signal format',
                        received: req.body
                    });
                }

                // Execute trade using our working trader
                const result = await this.executeTrade(signal);
                const totalTime = Date.now() - startTime;

                const response = {
                    success: result.success,
                    executionTime: result.executionTime,
                    totalTime: totalTime,
                    signal: signal,
                    result: result,
                    timestamp: new Date().toISOString()
                };

                if (result.success) {
                    console.log(`✅ Trade executed successfully in ${totalTime}ms`);
                } else {
                    console.log(`❌ Trade failed in ${totalTime}ms: ${result.error}`);
                }

                res.json(response);

            } catch (error) {
                const totalTime = Date.now() - startTime;
                console.error('💥 Webhook error:', error.message);
                
                res.status(500).json({
                    success: false,
                    error: error.message,
                    totalTime: totalTime,
                    timestamp: new Date().toISOString()
                });
            }
        });

        // Manual trade endpoint
        this.app.post('/trade', async (req, res) => {
            const startTime = Date.now();
            
            try {
                const { orderType, port } = req.body;
                
                if (!orderType) {
                    return res.status(400).json({
                        success: false,
                        error: 'orderType is required'
                    });
                }

                console.log(`🎯 Manual trade: ${orderType} on port ${port || 'auto'}`);

                const result = await this.executeTradeCommand(orderType, port);
                const totalTime = Date.now() - startTime;

                res.json({
                    success: result.success,
                    executionTime: result.executionTime,
                    totalTime: totalTime,
                    orderType: orderType,
                    result: result,
                    timestamp: new Date().toISOString()
                });

            } catch (error) {
                const totalTime = Date.now() - startTime;
                res.status(500).json({
                    success: false,
                    error: error.message,
                    totalTime: totalTime,
                    timestamp: new Date().toISOString()
                });
            }
        });
    }

    parseSignal(payload) {
        // Handle different webhook formats
        if (typeof payload === 'string') {
            try {
                payload = JSON.parse(payload);
            } catch {
                return null;
            }
        }

        // Extract signal data
        const signal = {
            symbol: payload.symbol || payload.pair || 'TRUUSDT',
            trade: payload.trade || payload.action || payload.side,
            price: payload.last_price || payload.price,
            leverage: payload.leverage || '1'
        };

        // Normalize trade action
        if (signal.trade) {
            signal.trade = signal.trade.toLowerCase();
        }

        return signal;
    }

    validateSignal(signal) {
        return signal && 
               signal.symbol && 
               signal.trade && 
               ['buy', 'sell', 'close'].includes(signal.trade);
    }

    async executeTrade(signal) {
        try {
            // Map signal to order type
            let orderType;
            let port;

            switch (signal.trade) {
                case 'buy':
                    orderType = 'Open Long';
                    port = 9222;
                    break;
                case 'sell':
                    orderType = 'Open Short';
                    port = 9222;
                    break;
                case 'close':
                    // For close, we need to determine if it's closing long or short
                    // For now, default to Close Long
                    orderType = 'Close Long';
                    port = 9223;
                    break;
                default:
                    throw new Error(`Unknown trade action: ${signal.trade}`);
            }

            console.log(`🚀 Executing: ${orderType} on port ${port}`);

            return await this.executeTradeCommand(orderType, port);

        } catch (error) {
            return {
                success: false,
                error: error.message,
                executionTime: 0
            };
        }
    }

    async executeTradeCommand(orderType, port) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            // Use our working final-ultimate-trader
            const traderPath = path.join(__dirname, 'final-ultimate-trader.js');
            const args = [traderPath, orderType];
            
            if (port) {
                args.push(port.toString());
            }

            console.log(`🔧 Spawning: node ${args.join(' ')}`);

            const child = spawn('node', args, {
                cwd: __dirname,
                stdio: ['pipe', 'pipe', 'pipe']
            });

            let stdout = '';
            let stderr = '';

            child.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            child.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            child.on('close', (code) => {
                const executionTime = Date.now() - startTime;
                
                const result = {
                    success: code === 0,
                    executionTime: executionTime,
                    exitCode: code,
                    stdout: stdout,
                    stderr: stderr
                };

                if (code === 0) {
                    console.log(`✅ Trade completed successfully in ${executionTime}ms`);
                } else {
                    console.log(`❌ Trade failed with exit code ${code} in ${executionTime}ms`);
                    result.error = `Process exited with code ${code}`;
                }

                resolve(result);
            });

            child.on('error', (error) => {
                const executionTime = Date.now() - startTime;
                console.error(`💥 Process error: ${error.message}`);
                
                resolve({
                    success: false,
                    executionTime: executionTime,
                    error: error.message
                });
            });

            // Timeout after 10 seconds
            setTimeout(() => {
                if (!child.killed) {
                    child.kill();
                    const executionTime = Date.now() - startTime;
                    
                    resolve({
                        success: false,
                        executionTime: executionTime,
                        error: 'Execution timeout (10s)'
                    });
                }
            }, 10000);
        });
    }

    start() {
        this.app.listen(this.port, () => {
            console.log('🚀 MEXC WEBHOOK INTEGRATION SERVER');
            console.log('===================================');
            console.log(`📡 Server running on port ${this.port}`);
            console.log(`🔗 Webhook URL: http://localhost:${this.port}/webhook`);
            console.log(`💊 Health check: http://localhost:${this.port}/health`);
            console.log('');
            console.log('📋 Endpoints:');
            console.log('  POST /webhook     - TradingView webhook');
            console.log('  POST /trade       - Manual trade');
            console.log('  GET  /health      - Health check');
            console.log('');
            console.log('📊 Signal Format:');
            console.log('  {"symbol": "TRUUSDT", "trade": "buy", "last_price": "0.03295", "leverage": "2"}');
            console.log('');
            console.log('🎯 Trade Mapping:');
            console.log('  buy  → Open Long  (port 9222)');
            console.log('  sell → Open Short (port 9222)');
            console.log('  close → Close Long (port 9223)');
            console.log('');
            console.log('⚡ Using final-ultimate-trader.js for execution');
        });
    }
}

// Start server if run directly
if (require.main === module) {
    const server = new WebhookIntegration();
    server.start();

    // Graceful shutdown
    process.on('SIGINT', () => {
        console.log('\n🛑 Shutting down webhook server...');
        process.exit(0);
    });
}

module.exports = WebhookIntegration;
