{"name": "tradingview-webhook-listener", "version": "1.0.0", "description": "TradingView webhook listener with MEXC API integration and money management", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "node tests/test-webhook.js", "setup": "npm install && echo 'Setup complete! Configure your API keys in the web interface.'"}, "keywords": ["tradingview", "webhook", "mexc", "trading", "money-management", "futures"], "author": "MEXC Trading System", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "winston": "^3.11.0", "axios": "^1.6.0", "crypto": "^1.0.1", "body-parser": "^1.20.2", "express-rate-limit": "^7.1.5", "node-cron": "^3.0.3", "sqlite3": "^5.1.6"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}