<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MEXC Trading System - Debug Viewer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .status {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .status-item {
            text-align: center;
        }
        .status-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .sessions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .session-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
        }
        .session-card.active {
            border-color: #28a745;
            background-color: #f8fff9;
        }
        .session-card.inactive {
            border-color: #dc3545;
            background-color: #fff8f8;
        }
        .session-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .session-info {
            margin: 5px 0;
            font-size: 14px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: black;
        }
        .refresh-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <button class="btn btn-primary refresh-btn" onclick="loadData()">🔄 Refresh</button>
    
    <div class="container">
        <div class="header">
            <h1>🌐 MEXC Trading System - Debug Viewer</h1>
            <p>Real-time browser session monitoring</p>
        </div>

        <div class="status">
            <div class="status-item">
                <div class="status-value" id="totalSessions">-</div>
                <div>Total Sessions</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="activeSessions">-</div>
                <div>Active Sessions</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="debugPort">9222</div>
                <div>Debug Port</div>
            </div>
        </div>

        <div id="content">
            <div class="loading">Loading browser sessions...</div>
        </div>
    </div>

    <script>
        async function loadData() {
            const content = document.getElementById('content');
            const totalSessions = document.getElementById('totalSessions');
            const activeSessions = document.getElementById('activeSessions');
            
            try {
                // Try to fetch from Chrome debug port
                const response = await fetch('http://localhost:9222/json');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const sessions = await response.json();
                
                totalSessions.textContent = sessions.length;
                let activeCount = 0;
                
                if (sessions.length === 0) {
                    content.innerHTML = `
                        <div class="error">
                            <h3>❌ No Browser Sessions Found</h3>
                            <p>The Chrome debug port is accessible but no sessions are active.</p>
                            <p><strong>Possible reasons:</strong></p>
                            <ul>
                                <li>Browser sessions failed to initialize</li>
                                <li>Sessions timed out and were closed</li>
                                <li>MEXC website is not loading properly</li>
                            </ul>
                            <p><strong>Check the server logs for:</strong></p>
                            <ul>
                                <li>"Session pool created: 0/3 sessions" (indicates failure)</li>
                                <li>"Timeout 30000ms exceeded" errors</li>
                                <li>Network connectivity issues</li>
                            </ul>
                        </div>
                    `;
                    return;
                }
                
                let html = '<div class="sessions-grid">';
                
                sessions.forEach((session, index) => {
                    const isActive = session.type === 'page' && session.url && !session.url.includes('chrome://');
                    if (isActive) activeCount++;
                    
                    html += `
                        <div class="session-card ${isActive ? 'active' : 'inactive'}">
                            <div class="session-title">
                                ${isActive ? '✅' : '❌'} Session ${index + 1}
                            </div>
                            <div class="session-info"><strong>Title:</strong> ${session.title || 'No title'}</div>
                            <div class="session-info"><strong>URL:</strong> ${session.url || 'No URL'}</div>
                            <div class="session-info"><strong>Type:</strong> ${session.type}</div>
                            <div class="session-info"><strong>ID:</strong> ${session.id.substring(0, 8)}...</div>
                            ${session.devtoolsFrontendUrl ? 
                                `<a href="http://localhost:9222${session.devtoolsFrontendUrl}" target="_blank" class="btn btn-primary">🔧 Inspect</a>` : 
                                ''
                            }
                            ${session.webSocketDebuggerUrl ? 
                                `<button class="btn btn-success" onclick="copyToClipboard('${session.webSocketDebuggerUrl}')">📋 Copy WebSocket</button>` : 
                                ''
                            }
                        </div>
                    `;
                });
                
                html += '</div>';
                content.innerHTML = html;
                activeSessions.textContent = activeCount;
                
            } catch (error) {
                content.innerHTML = `
                    <div class="error">
                        <h3>❌ Cannot Connect to Debug Port</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p><strong>Troubleshooting:</strong></p>
                        <ul>
                            <li>Make sure the MEXC Trading System is running</li>
                            <li>Check if USE_PERSISTENT_BROWSER=true in .env</li>
                            <li>Verify port 9222 is not blocked by firewall</li>
                            <li>Look for "Persistent browser launched successfully" in logs</li>
                        </ul>
                        <p><strong>Expected log messages:</strong></p>
                        <ul>
                            <li>"Browser launched successfully"</li>
                            <li>"Browser debug port accessible"</li>
                            <li>"Session pool created: 3/3 sessions"</li>
                        </ul>
                    </div>
                `;
                totalSessions.textContent = '❌';
                activeSessions.textContent = '❌';
            }
        }
        
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                alert('WebSocket URL copied to clipboard!');
            });
        }
        
        // Load data on page load
        loadData();
        
        // Auto-refresh every 10 seconds
        setInterval(loadData, 10000);
    </script>
</body>
</html>
