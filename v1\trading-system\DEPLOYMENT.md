# MEXC Trading System - Deployment Guide

This guide covers deployment options for the MEXC High-Speed Futures Trading System.

## Prerequisites

- Linux server (Ubuntu 20.04+ recommended)
- Python 3.11+
- Docker and Docker Compose (for containerized deployment)
- Minimum 2GB RAM, 2 CPU cores
- Stable internet connection

## Deployment Options

### Option 1: Docker Deployment (Recommended)

#### 1. <PERSON><PERSON> and Setup

```bash
# Clone the repository
git clone <repository-url>
cd trading-system

# Copy environment template
cp .env.template .env

# Edit configuration
nano .env
```

#### 2. Configure Environment

Edit `.env` file with your settings:

```bash
# Telegram Bot (Required for notifications)
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here

# Trading Configuration
MAX_CONCURRENT_TRADES=5
DEFAULT_LEVERAGE=1
SUPPORTED_SYMBOLS=["BTC_USDT", "ETH_USDT", "BNB_USDT"]

# Session Management
SESSION_POOL_SIZE=3
SESSION_HEALTH_CHECK_INTERVAL=1800

# Security
WEBHOOK_SECRET=your_webhook_secret_here
```

#### 3. Deploy with Docker Compose

```bash
# Build and start services
docker-compose up -d

# Check logs
docker-compose logs -f mexc-trading-system

# Check status
docker-compose ps
```

#### 4. Access Dashboard

- Dashboard: http://your-server:8000
- API Documentation: http://your-server:8000/docs
- Health Check: http://your-server:8000/health

### Option 2: Manual Installation

#### 1. System Dependencies

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and dependencies
sudo apt install -y python3.11 python3.11-pip python3.11-venv
sudo apt install -y chromium-browser xvfb

# Create user
sudo useradd -m -s /bin/bash mexc
sudo usermod -aG sudo mexc
```

#### 2. Application Setup

```bash
# Switch to mexc user
sudo su - mexc

# Create application directory
sudo mkdir -p /opt/mexc-trading-system
sudo chown mexc:mexc /opt/mexc-trading-system
cd /opt/mexc-trading-system

# Clone repository
git clone <repository-url> .

# Create virtual environment
python3.11 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
playwright install chromium
playwright install-deps chromium
```

#### 3. Configuration

```bash
# Copy and edit environment file
cp .env.template .env
nano .env

# Make startup script executable
chmod +x start.sh

# Create directories
mkdir -p logs data browser_data backups
```

#### 4. Systemd Service

```bash
# Copy service file
sudo cp mexc-trader.service /etc/systemd/system/

# Reload systemd and enable service
sudo systemctl daemon-reload
sudo systemctl enable mexc-trader

# Start service
sudo systemctl start mexc-trader

# Check status
sudo systemctl status mexc-trader
```

## Configuration

### Telegram Bot Setup

1. Create a bot with @BotFather on Telegram
2. Get your bot token
3. Get your chat ID by messaging @userinfobot
4. Update `.env` with your credentials

### TradingView Webhook Setup

Configure your TradingView alerts to send webhooks to:
```
POST http://your-server:8000/webhook/tradingview
```

Example payload:
```json
{
  "action": "buy",
  "symbol": "BTC_USDT",
  "side": "long",
  "quantity": 0.1,
  "price": 45000,
  "leverage": 10
}
```

### MEXC Session Management

1. Access the dashboard at http://your-server:8000
2. Navigate to Sessions tab
3. When prompted, manually log into MEXC in the browser instance
4. Complete 2FA if required
5. The system will automatically manage sessions afterward

## Monitoring

### Health Checks

- System health: `GET /health`
- Detailed status: `GET /api/status`
- Metrics: `GET /api/metrics`

### Logs

```bash
# Docker deployment
docker-compose logs -f mexc-trading-system

# Manual deployment
sudo journalctl -u mexc-trader -f

# Application logs
tail -f /opt/mexc-trading-system/logs/trading_system.log
```

### Telegram Notifications

The system sends automatic notifications for:
- Trade executions
- Session expiry warnings
- System errors
- Health check failures

## Security

### Firewall Configuration

```bash
# Allow SSH, HTTP, and HTTPS
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443

# Allow webhook port (if not using nginx)
sudo ufw allow 8000

# Enable firewall
sudo ufw enable
```

### SSL/TLS Setup

For production, configure SSL certificates:

1. Obtain certificates (Let's Encrypt recommended)
2. Update nginx.conf with SSL configuration
3. Restart nginx service

### Webhook Security

Configure webhook signature verification:
```bash
# Generate webhook secret
WEBHOOK_SECRET=$(openssl rand -hex 32)
echo "WEBHOOK_SECRET=$WEBHOOK_SECRET" >> .env
```

## Backup and Recovery

### Automated Backups

The system automatically backs up:
- Database (trades, sessions)
- Configuration files
- Session data (encrypted)

Backup location: `./backups/`

### Manual Backup

```bash
# Create backup
tar -czf mexc-backup-$(date +%Y%m%d).tar.gz \
  data/ logs/ .env browser_data/

# Restore backup
tar -xzf mexc-backup-YYYYMMDD.tar.gz
```

## Troubleshooting

### Common Issues

1. **Sessions not authenticating**
   - Check browser_data directory permissions
   - Manually refresh sessions via dashboard
   - Verify MEXC login credentials

2. **Trades failing**
   - Check session health in dashboard
   - Verify symbol support
   - Review error logs

3. **High memory usage**
   - Reduce SESSION_POOL_SIZE
   - Enable HEADLESS_MODE
   - Monitor browser instances

### Debug Mode

Enable debug logging:
```bash
# Update .env
LOG_LEVEL=DEBUG

# Restart service
sudo systemctl restart mexc-trader
```

### Performance Optimization

1. **Reduce resource usage**
   ```bash
   HEADLESS_MODE=true
   SESSION_POOL_SIZE=2
   BROWSER_TIMEOUT=15000
   ```

2. **Increase performance**
   ```bash
   PARALLEL_PROCESSING_ENABLED=true
   ENABLE_CONNECTION_POOLING=true
   ENABLE_SELECTOR_CACHING=true
   ```

## Scaling

### Horizontal Scaling

Deploy multiple instances with load balancing:

1. Use external database (PostgreSQL)
2. Configure Redis for session sharing
3. Deploy behind load balancer
4. Coordinate session management

### Vertical Scaling

Increase server resources:
- 4+ CPU cores for high-frequency trading
- 8+ GB RAM for multiple sessions
- SSD storage for better I/O performance

## Support

For issues and questions:
1. Check logs for error messages
2. Review dashboard for system status
3. Test webhook endpoints manually
4. Verify Telegram bot connectivity

## Updates

To update the system:

```bash
# Docker deployment
git pull
docker-compose build --no-cache
docker-compose up -d

# Manual deployment
git pull
source venv/bin/activate
pip install -r requirements.txt
sudo systemctl restart mexc-trader
```
