const axios = require('axios');
const fs = require('fs');

class MoneyManagementTester {
    constructor() {
        this.webhookUrl = 'http://localhost:4000/webhook';
        this.configUrl = 'http://localhost:4000/api/config';
        this.balanceUrl = 'http://localhost:4000/api/balance';
        this.testResults = [];
        this.mockBalance = 2.29; // Test balance in USDT
        this.currentTRUPrice = 0.03225;
    }

    log(message, data = null) {
        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] ${message}`);
        if (data && typeof data === 'object') {
            console.log(JSON.stringify(data, null, 2));
        }
    }

    addTestResult(testName, success, details = null) {
        const result = {
            testName,
            success,
            details,
            timestamp: new Date().toISOString()
        };
        this.testResults.push(result);
        
        const status = success ? '✅' : '❌';
        this.log(`${status} ${testName}`);
        if (details) {
            console.log('   Details:', details);
        }
    }

    async getCurrentConfig() {
        try {
            const response = await axios.get(this.configUrl, { timeout: 5000 });
            return response.data;
        } catch (error) {
            this.log('Failed to get config:', error.message);
            return null;
        }
    }

    async updateConfig(newConfig) {
        try {
            // Get current config first
            const currentConfig = await this.getCurrentConfig();
            if (!currentConfig) {
                throw new Error('Could not get current config');
            }

            // Merge with new config
            const mergedConfig = { ...currentConfig, ...newConfig };
            
            // Write to config file directly since API might have issues
            const configPath = 'tradingview-webhook-listener/config.json';
            fs.writeFileSync(configPath, JSON.stringify(mergedConfig, null, 2));
            
            this.log('✅ Configuration updated successfully');
            await this.sleep(1000); // Wait for config to reload
            
            return true;
        } catch (error) {
            this.log('Failed to update config:', error.message);
            return false;
        }
    }

    async testBalanceFetching() {
        this.log('💰 Testing Balance Fetching with Mock Balance...');
        
        try {
            const response = await axios.get(this.balanceUrl, { timeout: 10000 });
            
            const balanceWorking = response.data && response.data.balance !== undefined;
            const isMockBalance = response.data.balance === this.mockBalance || response.data.mockMode;
            
            this.addTestResult('Balance Fetching', balanceWorking, {
                balance: response.data.balance,
                currency: response.data.currency,
                mockMode: response.data.mockMode,
                isMockBalance: isMockBalance,
                fullResponse: response.data
            });
            
            return response.data;
        } catch (error) {
            this.addTestResult('Balance Fetching', false, { error: error.message });
            return null;
        }
    }

    async testPercentageBasedMoneyManagement() {
        this.log('📊 Testing Percentage-Based Money Management...');

        const percentageTests = [
            { percentage: 25, expectedQuantity: this.mockBalance * 0.25 },
            { percentage: 50, expectedQuantity: this.mockBalance * 0.50 },
            { percentage: 75, expectedQuantity: this.mockBalance * 0.75 },
            { percentage: 100, expectedQuantity: this.mockBalance * 1.0 }
        ];

        for (const test of percentageTests) {
            await this.testPercentageConfiguration(test);
            await this.sleep(2000);
        }
    }

    async testPercentageConfiguration(test) {
        this.log(`📈 Testing ${test.percentage}% of balance (Expected: ${test.expectedQuantity.toFixed(4)} USDT)`);

        try {
            // Update configuration
            const config = {
                moneyManagementEnabled: true,
                moneyManagementMode: 'percentage',
                positionSizePercentage: test.percentage,
                maxPriceDifference: 100 // Allow large price differences for testing
            };

            const configUpdated = await this.updateConfig(config);
            if (!configUpdated) {
                throw new Error('Failed to update configuration');
            }

            // Send test signal
            const testSignal = {
                symbol: "TRUUSDT",
                trade: "buy",
                last_price: this.currentTRUPrice.toString(),
                leverege: "2"
            };

            const response = await axios.post(this.webhookUrl, testSignal, { timeout: 15000 });
            
            // Check if quantity calculation is correct
            const calculatedQuantity = parseFloat(response.data.processedSignal?.quantity || 0);
            const quantityMatch = Math.abs(calculatedQuantity - test.expectedQuantity) < 0.01;
            
            this.addTestResult(`${test.percentage}% Percentage Mode`, quantityMatch, {
                expectedQuantity: test.expectedQuantity,
                calculatedQuantity: calculatedQuantity,
                quantityDifference: Math.abs(calculatedQuantity - test.expectedQuantity),
                quantityMatch: quantityMatch,
                success: response.data.success,
                message: response.data.message,
                skipped: response.data.skipped
            });

        } catch (error) {
            this.addTestResult(`${test.percentage}% Percentage Mode`, false, { error: error.message });
        }
    }

    async testFixedAmountMoneyManagement() {
        this.log('💵 Testing Fixed Amount Money Management...');

        const fixedAmountTests = [
            { amount: 0.5, expectedQuantity: 0.5 },
            { amount: 1.0, expectedQuantity: 1.0 },
            { amount: 2.0, expectedQuantity: 2.0 }, // Should be limited by available balance
            { amount: 5.0, expectedQuantity: this.mockBalance } // Should use entire balance
        ];

        for (const test of fixedAmountTests) {
            await this.testFixedAmountConfiguration(test);
            await this.sleep(2000);
        }
    }

    async testFixedAmountConfiguration(test) {
        this.log(`💰 Testing Fixed Amount ${test.amount} USDT (Expected: ${test.expectedQuantity.toFixed(4)} USDT)`);

        try {
            // Update configuration
            const config = {
                moneyManagementEnabled: true,
                moneyManagementMode: 'fixed',
                fixedTradeAmount: test.amount,
                maxPriceDifference: 100
            };

            const configUpdated = await this.updateConfig(config);
            if (!configUpdated) {
                throw new Error('Failed to update configuration');
            }

            // Send test signal
            const testSignal = {
                symbol: "TRUUSDT",
                trade: "buy",
                last_price: this.currentTRUPrice.toString(),
                leverege: "2"
            };

            const response = await axios.post(this.webhookUrl, testSignal, { timeout: 15000 });
            
            // Check if quantity calculation is correct
            const calculatedQuantity = parseFloat(response.data.processedSignal?.quantity || 0);
            const quantityMatch = Math.abs(calculatedQuantity - test.expectedQuantity) < 0.01;
            
            this.addTestResult(`Fixed Amount ${test.amount} USDT`, quantityMatch, {
                requestedAmount: test.amount,
                expectedQuantity: test.expectedQuantity,
                calculatedQuantity: calculatedQuantity,
                quantityDifference: Math.abs(calculatedQuantity - test.expectedQuantity),
                quantityMatch: quantityMatch,
                success: response.data.success,
                message: response.data.message,
                skipped: response.data.skipped
            });

        } catch (error) {
            this.addTestResult(`Fixed Amount ${test.amount} USDT`, false, { error: error.message });
        }
    }

    async testMoneyManagementDisabled() {
        this.log('🚫 Testing Money Management Disabled...');

        try {
            // Update configuration to disable money management
            const config = {
                moneyManagementEnabled: false,
                defaultQuantity: '0.5000',
                maxPriceDifference: 100
            };

            const configUpdated = await this.updateConfig(config);
            if (!configUpdated) {
                throw new Error('Failed to update configuration');
            }

            // Send test signal
            const testSignal = {
                symbol: "TRUUSDT",
                trade: "buy",
                last_price: this.currentTRUPrice.toString(),
                leverege: "2"
            };

            const response = await axios.post(this.webhookUrl, testSignal, { timeout: 15000 });
            
            // Should use default quantity
            const calculatedQuantity = parseFloat(response.data.processedSignal?.quantity || 0);
            const expectedQuantity = 0.5;
            const quantityMatch = Math.abs(calculatedQuantity - expectedQuantity) < 0.01;
            
            this.addTestResult('Money Management Disabled', quantityMatch, {
                expectedQuantity: expectedQuantity,
                calculatedQuantity: calculatedQuantity,
                quantityMatch: quantityMatch,
                success: response.data.success,
                message: response.data.message,
                skipped: response.data.skipped
            });

        } catch (error) {
            this.addTestResult('Money Management Disabled', false, { error: error.message });
        }
    }

    async testTradeLimits() {
        this.log('🚧 Testing Trade Limits...');

        try {
            // Test with very small amount (below minimum)
            const config = {
                moneyManagementEnabled: true,
                moneyManagementMode: 'fixed',
                fixedTradeAmount: 0.05, // Below typical minimum
                minTradeAmount: 0.1,
                maxPriceDifference: 100
            };

            const configUpdated = await this.updateConfig(config);
            if (!configUpdated) {
                throw new Error('Failed to update configuration');
            }

            const testSignal = {
                symbol: "TRUUSDT",
                trade: "buy",
                last_price: this.currentTRUPrice.toString(),
                leverege: "2"
            };

            const response = await axios.post(this.webhookUrl, testSignal, { timeout: 15000 });
            
            // Should either use minimum amount or fail gracefully
            const calculatedQuantity = parseFloat(response.data.processedSignal?.quantity || 0);
            const handledCorrectly = calculatedQuantity >= 0.1 || response.data.skipped || !response.data.success;
            
            this.addTestResult('Trade Limits - Minimum Amount', handledCorrectly, {
                requestedAmount: 0.05,
                minTradeAmount: 0.1,
                calculatedQuantity: calculatedQuantity,
                success: response.data.success,
                message: response.data.message,
                skipped: response.data.skipped
            });

        } catch (error) {
            this.addTestResult('Trade Limits - Minimum Amount', false, { error: error.message });
        }
    }

    async generateReport() {
        this.log('📊 Generating Money Management Test Report...');

        const report = {
            summary: {
                totalTests: this.testResults.length,
                passed: this.testResults.filter(r => r.success).length,
                failed: this.testResults.filter(r => !r.success).length,
                successRate: `${Math.round((this.testResults.filter(r => r.success).length / this.testResults.length) * 100)}%`
            },
            testEnvironment: {
                mockBalance: this.mockBalance,
                currentTRUPrice: this.currentTRUPrice,
                testDate: new Date().toISOString()
            },
            testResults: this.testResults,
            timestamp: new Date().toISOString()
        };

        fs.writeFileSync('money-management-test-report.json', JSON.stringify(report, null, 2));
        
        this.log('📋 Money Management Test Summary:');
        this.log(`   Total Tests: ${report.summary.totalTests}`);
        this.log(`   Passed: ${report.summary.passed}`);
        this.log(`   Failed: ${report.summary.failed}`);
        this.log(`   Success Rate: ${report.summary.successRate}`);
        
        // Show failed tests
        const failedTests = this.testResults.filter(r => !r.success);
        if (failedTests.length > 0) {
            this.log('\n❌ Failed Tests:');
            failedTests.forEach(test => {
                this.log(`   - ${test.testName}: ${test.details?.error || 'Unknown error'}`);
            });
        }
        
        return report;
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async runMoneyManagementTest() {
        this.log('🚀 Starting Money Management Comprehensive Test...');
        
        try {
            await this.testBalanceFetching();
            await this.testPercentageBasedMoneyManagement();
            await this.testFixedAmountMoneyManagement();
            await this.testMoneyManagementDisabled();
            await this.testTradeLimits();
            
            const report = await this.generateReport();
            
            this.log('\n✅ Money Management testing completed!');
            this.log('📄 Report saved to: money-management-test-report.json');
            
            return report;
            
        } catch (error) {
            this.log('❌ Testing failed:', error.message);
            throw error;
        }
    }
}

// Run the test
if (require.main === module) {
    const tester = new MoneyManagementTester();
    tester.runMoneyManagementTest()
        .then(report => {
            console.log('\n🎉 Money Management testing completed successfully!');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n💥 Testing failed:', error.message);
            process.exit(1);
        });
}

module.exports = MoneyManagementTester;
