#!/usr/bin/env python3
"""
Deep Signature Analysis
Attempts to find and implement the real MEXC signing algorithm
"""

import json
import time
import hashlib
import hmac
import base64
from curl_cffi import requests
from playwright.sync_api import sync_playwright
from dotenv import dotenv_values

class DeepSignatureAnalysis:
    """Deep analysis of MEXC signing algorithm"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        self.uc_token = vals.get('uc_token')
        self.session = requests.Session(impersonate='chrome124')
        
        print("🔬 Deep MEXC Signature Analysis")
        print("="*40)
    
    def extract_signing_code_from_scripts(self):
        """Extract actual signing code from JavaScript"""
        
        print("\n🔍 Extracting signing code from scripts...")
        
        try:
            with sync_playwright() as p:
                browser = p.chromium.connect_over_cdp('http://127.0.0.1:9222')
                context = browser.contexts[0]
                
                mexc_page = None
                for page in context.pages:
                    if 'mexc.com' in (page.url or ''):
                        mexc_page = page
                        break
                
                if not mexc_page:
                    print("❌ No MEXC page found")
                    return None
                
                # Extract detailed JavaScript analysis
                detailed_analysis = mexc_page.evaluate("""
                    () => {
                        const results = {
                            cryptoMethods: [],
                            signatureMethods: [],
                            requestInterceptors: [],
                            possibleAlgorithms: []
                        };
                        
                        // 1. Check for crypto libraries
                        if (window.crypto) {
                            results.cryptoMethods.push('window.crypto available');
                        }
                        
                        // 2. Look for common crypto libraries
                        ['CryptoJS', 'crypto', 'sjcl', 'forge'].forEach(lib => {
                            if (window[lib]) {
                                results.cryptoMethods.push(`${lib} library found`);
                            }
                        });
                        
                        // 3. Scan for signature-related code patterns
                        const scripts = Array.from(document.querySelectorAll('script'));
                        scripts.forEach((script, index) => {
                            if (script.textContent) {
                                const content = script.textContent;
                                
                                // Look for x-mxc-sign patterns
                                if (content.includes('x-mxc-sign')) {
                                    const lines = content.split('\\n');
                                    lines.forEach((line, lineNum) => {
                                        if (line.includes('x-mxc-sign')) {
                                            results.signatureMethods.push({
                                                script: index,
                                                line: lineNum,
                                                code: line.trim()
                                            });
                                        }
                                    });
                                }
                                
                                // Look for signing algorithms
                                const patterns = [
                                    /sign[A-Za-z]*\\s*[=:]\\s*function/g,
                                    /signature\\s*[=:]\\s*[^;]+/g,
                                    /hmac[^(]*\\([^)]*\\)/gi,
                                    /sha256[^(]*\\([^)]*\\)/gi
                                ];
                                
                                patterns.forEach(pattern => {
                                    const matches = content.match(pattern);
                                    if (matches) {
                                        matches.forEach(match => {
                                            results.possibleAlgorithms.push({
                                                script: index,
                                                pattern: pattern.toString(),
                                                match: match
                                            });
                                        });
                                    }
                                });
                            }
                        });
                        
                        // 4. Try to find request interceptors
                        const originalFetch = window.fetch;
                        if (originalFetch.toString().includes('native code')) {
                            results.requestInterceptors.push('fetch is native');
                        } else {
                            results.requestInterceptors.push('fetch may be overridden');
                        }
                        
                        return results;
                    }
                """)
                
                browser.close()
                return detailed_analysis
                
        except Exception as e:
            print(f"❌ Script extraction failed: {e}")
            return None
    
    def test_advanced_signature_algorithms(self):
        """Test advanced signature algorithms based on analysis"""
        
        print("\n🧪 Testing advanced signature algorithms...")
        
        test_order = {
            'symbol': 'BTC_USDT',
            'side': 1,
            'openType': 1,
            'type': '2',
            'vol': 1,
            'leverage': 1,
            'price': '50000',
            'priceProtect': '0'
        }
        
        nonce = str(int(time.time() * 1000))
        
        # Advanced algorithms to test
        algorithms = [
            self._mexc_style_v1,
            self._mexc_style_v2,
            self._mexc_style_v3,
            self._mexc_style_v4,
            self._mexc_style_v5
        ]
        
        results = []
        
        for algo in algorithms:
            try:
                signature = algo(test_order, nonce)
                results.append({
                    'algorithm': algo.__name__,
                    'signature': signature,
                    'test_result': None
                })
                print(f"✅ {algo.__name__}: {signature[:16]}...")
            except Exception as e:
                print(f"❌ {algo.__name__}: {e}")
        
        return results
    
    def _mexc_style_v1(self, order_data, nonce):
        """MEXC Style Algorithm V1: Query string + HMAC"""
        # Create query string
        params = []
        for key, value in sorted(order_data.items()):
            params.append(f"{key}={value}")
        query_string = '&'.join(params)
        
        # Message format: auth + nonce + query_string
        message = f"{self.auth}{nonce}{query_string}"
        
        # HMAC-SHA256 with auth as secret
        signature = hmac.new(
            self.auth.encode(),
            message.encode(),
            hashlib.sha256
        ).hexdigest()
        
        return signature[:32]
    
    def _mexc_style_v2(self, order_data, nonce):
        """MEXC Style Algorithm V2: JSON + Double Hash"""
        # JSON string
        json_str = json.dumps(order_data, separators=(',', ':'), sort_keys=True)
        
        # Message: nonce + json + auth
        message = f"{nonce}{json_str}{self.auth}"
        
        # Double hash: SHA256 then MD5
        first_hash = hashlib.sha256(message.encode()).hexdigest()
        second_hash = hashlib.md5(first_hash.encode()).hexdigest()
        
        return second_hash
    
    def _mexc_style_v3(self, order_data, nonce):
        """MEXC Style Algorithm V3: Base64 + HMAC"""
        # Create message
        json_str = json.dumps(order_data, separators=(',', ':'), sort_keys=True)
        message = f"{nonce}{json_str}"
        
        # Base64 encode message
        b64_message = base64.b64encode(message.encode()).decode()
        
        # HMAC with uc_token as secret
        secret = self.uc_token or self.auth
        signature = hmac.new(
            secret.encode(),
            b64_message.encode(),
            hashlib.sha256
        ).hexdigest()
        
        return signature[:32]
    
    def _mexc_style_v4(self, order_data, nonce):
        """MEXC Style Algorithm V4: Multi-step with salt"""
        # Step 1: Create base string
        params = []
        for key, value in sorted(order_data.items()):
            params.append(f"{key}={value}")
        query_string = '&'.join(params)
        
        # Step 2: Add salt (common in trading platforms)
        salt = "mexc_futures_api_v1"
        base_string = f"{salt}{nonce}{query_string}{self.auth}"
        
        # Step 3: Multiple hashing
        hash1 = hashlib.sha256(base_string.encode()).hexdigest()
        hash2 = hashlib.md5((hash1 + nonce).encode()).hexdigest()
        
        return hash2
    
    def _mexc_style_v5(self, order_data, nonce):
        """MEXC Style Algorithm V5: Timestamp-based with XOR"""
        # Create message with timestamp
        timestamp = str(int(time.time()))
        json_str = json.dumps(order_data, separators=(',', ':'), sort_keys=True)
        message = f"{timestamp}{nonce}{json_str}{self.auth}"
        
        # Hash and XOR with nonce
        hash_bytes = hashlib.sha256(message.encode()).digest()
        nonce_bytes = nonce.encode().ljust(32, b'0')[:32]
        
        # XOR operation
        xor_result = bytes(a ^ b for a, b in zip(hash_bytes, nonce_bytes))
        
        # Final hash
        final_hash = hashlib.md5(xor_result).hexdigest()
        
        return final_hash
    
    def test_signatures_against_api(self, signatures):
        """Test generated signatures against real API"""
        
        print("\n🎯 Testing signatures against MEXC API...")
        
        test_order = {
            'symbol': 'BTC_USDT',
            'side': 1,
            'openType': 1,
            'type': '2',
            'vol': 1,
            'leverage': 1,
            'marketCeiling': False,
            'price': '30000',  # Very low price to avoid fill
            'priceProtect': '0'
        }
        
        for sig_data in signatures:
            algorithm = sig_data['algorithm']
            signature = sig_data['signature']
            
            print(f"\n🧪 Testing {algorithm}...")
            
            # Generate fresh nonce for this test
            nonce = str(int(time.time() * 1000))
            
            # Regenerate signature with fresh nonce
            try:
                if algorithm == '_mexc_style_v1':
                    fresh_signature = self._mexc_style_v1(test_order, nonce)
                elif algorithm == '_mexc_style_v2':
                    fresh_signature = self._mexc_style_v2(test_order, nonce)
                elif algorithm == '_mexc_style_v3':
                    fresh_signature = self._mexc_style_v3(test_order, nonce)
                elif algorithm == '_mexc_style_v4':
                    fresh_signature = self._mexc_style_v4(test_order, nonce)
                elif algorithm == '_mexc_style_v5':
                    fresh_signature = self._mexc_style_v5(test_order, nonce)
                else:
                    continue
                
                # Test with API
                headers = {
                    'Accept': 'application/json, text/plain, */*',
                    'Content-Type': 'application/json',
                    'authorization': self.auth,
                    'x-mxc-nonce': nonce,
                    'x-mxc-sign': fresh_signature,
                    'x-language': 'en_US',
                }
                
                if self.uc_token:
                    headers['mtoken'] = self.uc_token
                
                # Add opaque parameters
                test_order_with_params = test_order.copy()
                test_order_with_params['p0'] = hashlib.md5(f"{nonce}{self.auth}".encode()).hexdigest()
                test_order_with_params['k0'] = hashlib.md5(f"{time.time()}".encode()).hexdigest()[:16]
                
                # Try create endpoint
                r = self.session.post(
                    'https://futures.mexc.com/api/v1/private/order/create',
                    json=test_order_with_params,
                    headers=headers
                )
                
                if r.status_code == 200:
                    result = r.json()
                    error_code = result.get('code', -1)
                    
                    if error_code == 0:
                        print(f"🎉 {algorithm}: SUCCESS! Order would be placed!")
                        sig_data['test_result'] = 'SUCCESS'
                        return algorithm, fresh_signature  # Found the correct algorithm!
                    elif error_code == 602:
                        print(f"❌ {algorithm}: Signature verification failed")
                        sig_data['test_result'] = 'SIGNATURE_FAILED'
                    else:
                        print(f"⚠️ {algorithm}: Other error - {error_code}")
                        sig_data['test_result'] = f'ERROR_{error_code}'
                else:
                    print(f"❌ {algorithm}: HTTP {r.status_code}")
                    sig_data['test_result'] = f'HTTP_{r.status_code}'
                
            except Exception as e:
                print(f"❌ {algorithm}: Exception - {e}")
                sig_data['test_result'] = f'EXCEPTION'
        
        return None, None
    
    def run_deep_analysis(self):
        """Run complete deep analysis"""
        
        print("🚀 Starting Deep Signature Analysis...")
        print("="*50)
        
        # Step 1: Extract signing code
        script_analysis = self.extract_signing_code_from_scripts()
        if script_analysis:
            print(f"✅ Found {len(script_analysis.get('signatureMethods', []))} signature methods")
            print(f"✅ Found {len(script_analysis.get('possibleAlgorithms', []))} possible algorithms")
        
        # Step 2: Test advanced algorithms
        signature_results = self.test_advanced_signature_algorithms()
        print(f"✅ Generated {len(signature_results)} signature candidates")
        
        # Step 3: Test against API
        working_algorithm, working_signature = self.test_signatures_against_api(signature_results)
        
        # Results
        print("\n" + "="*50)
        print("DEEP ANALYSIS RESULTS")
        print("="*50)
        
        if working_algorithm:
            print(f"🎉 FOUND WORKING ALGORITHM: {working_algorithm}")
            print(f"🔑 Working signature: {working_signature[:16]}...")
            print("\n✅ REVERSE ENGINEERING SUCCESSFUL!")
            print("The signature algorithm has been cracked!")
            return True, working_algorithm
        else:
            print("❌ NO WORKING ALGORITHM FOUND")
            print("\nTested algorithms:")
            for result in signature_results:
                status = result.get('test_result', 'NOT_TESTED')
                print(f"   - {result['algorithm']}: {status}")
            
            print(f"\n💡 CONCLUSION:")
            print("The signature algorithm is more complex than tested patterns.")
            print("Recommendation: Switch to Option B (Real-time Browser Integration)")
            return False, None

def main():
    """Main deep analysis function"""
    
    analyzer = DeepSignatureAnalysis()
    success, algorithm = analyzer.run_deep_analysis()
    
    if success:
        print(f"\n🚀 READY FOR PRODUCTION!")
        print(f"Use algorithm: {algorithm}")
    else:
        print(f"\n🔄 SWITCHING TO OPTION B...")
        print("Implementing Real-time Browser Integration...")

if __name__ == '__main__':
    main()
