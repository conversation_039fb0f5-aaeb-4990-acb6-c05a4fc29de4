const { chromium } = require('playwright');

class LightningMEXCTrader {
    constructor() {
        this.browser = null;
        this.page = null;
        this.startTime = null;
    }

    async connectToRemoteBrowser() {
        console.log('⚡ Lightning connection...');
        
        try {
            this.browser = await chromium.connectOverCDP('http://localhost:9222');
            const contexts = this.browser.contexts();
            
            if (contexts.length > 0) {
                const context = contexts[0];
                const pages = context.pages();
                this.page = pages.length > 0 ? pages[0] : await context.newPage();
            } else {
                const context = await this.browser.newContext();
                this.page = await context.newPage();
            }
            
            console.log('✅ Connected');
            return true;
        } catch (error) {
            console.error('❌ Connection failed:', error.message);
            return false;
        }
    }

    async lightningOrder() {
        this.startTime = Date.now();
        console.log('⚡ LIGHTNING ORDER EXECUTION...');
        
        try {
            // Ensure we're on the right page (minimal check)
            const url = this.page.url();
            if (!url.includes('mexc.com') || !url.includes('TRU')) {
                await this.page.goto('https://www.mexc.com/futures/TRU_USDT', { 
                    waitUntil: 'domcontentloaded',
                    timeout: 3000 
                });
            }

            // LIGHTNING EXECUTION - NO DELAYS, PARALLEL OPERATIONS
            
            // Execute all operations in parallel
            const operations = await Promise.allSettled([
                // Operation 1: Click Buy
                (async () => {
                    const buySelectors = ['button:has-text("Buy")', '.buy-btn', '[class*="buy"]:not(input)'];
                    for (const selector of buySelectors) {
                        try {
                            await this.page.locator(selector).first().click({ timeout: 200 });
                            console.log('⚡ Buy clicked');
                            return true;
                        } catch (error) {
                            continue;
                        }
                    }
                    throw new Error('Buy not found');
                })(),

                // Operation 2: Market order (after tiny delay)
                (async () => {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    const marketSelectors = ['button:has-text("Market")', '.market-btn'];
                    for (const selector of marketSelectors) {
                        try {
                            await this.page.locator(selector).first().click({ timeout: 150 });
                            console.log('⚡ Market selected');
                            return true;
                        } catch (error) {
                            continue;
                        }
                    }
                    return false; // Non-critical
                })(),

                // Operation 3: Quantity (after tiny delay)
                (async () => {
                    await new Promise(resolve => setTimeout(resolve, 150));
                    
                    const quantitySelectors = [
                        'input[placeholder*="amount"]',
                        'input[placeholder*="quantity"]',
                        'input[type="number"]'
                    ];

                    for (const selector of quantitySelectors) {
                        try {
                            const input = this.page.locator(selector).first();
                            await input.click({ timeout: 100 });
                            await input.fill('40');
                            console.log('⚡ Quantity: 40');
                            return true;
                        } catch (error) {
                            continue;
                        }
                    }

                    // Fallback: percentage
                    try {
                        await this.page.locator('button:has-text("25%")').first().click({ timeout: 100 });
                        console.log('⚡ 25% used');
                        return true;
                    } catch (error) {
                        return false;
                    }
                })()
            ]);

            // Check if Buy operation succeeded
            const buySuccess = operations[0].status === 'fulfilled' && operations[0].value;
            if (!buySuccess) {
                throw new Error('Buy operation failed');
            }

            // Minimal delay for UI update, then submit
            await new Promise(resolve => setTimeout(resolve, 50));

            // SUBMIT - Try multiple selectors simultaneously
            const submitPromises = [
                'button:has-text("Buy")',
                'button:has-text("Long")', 
                'button:has-text("Place")',
                '.submit-btn',
                '.place-order-btn'
            ].map(selector => 
                this.page.locator(selector).first().click({ timeout: 200 }).catch(() => false)
            );

            const submitResults = await Promise.allSettled(submitPromises);
            const submitSuccess = submitResults.some(result => result.status === 'fulfilled' && result.value !== false);

            if (submitSuccess) {
                console.log('⚡ Order submitted');
            }

            const executionTime = Date.now() - this.startTime;

            // Immediate confirmation check (non-blocking)
            let confirmationFound = false;
            try {
                const confirmationPromises = [
                    'text=successfully',
                    'text=Success', 
                    'text=Purchased',
                    '.success'
                ].map(selector =>
                    this.page.locator(selector).first().isVisible({ timeout: 100 }).catch(() => false)
                );

                const confirmationResults = await Promise.allSettled(confirmationPromises);
                confirmationFound = confirmationResults.some(result => result.status === 'fulfilled' && result.value);

                if (confirmationFound) {
                    console.log('⚡ Instant confirmation detected');
                }
            } catch (error) {
                // Non-blocking
            }

            console.log('\n⚡ LIGHTNING RESULTS:');
            console.log('====================');
            console.log(`⏱️ Execution time: ${executionTime}ms`);
            console.log(`🎯 Under 2 seconds: ${executionTime < 2000 ? '🏆 YES!' : '❌ NO'}`);
            console.log(`🎯 Under 1.5 seconds: ${executionTime < 1500 ? '🏆 EXCELLENT!' : '⚠️ NO'}`);
            console.log(`📋 Order submitted: ${submitSuccess ? '✅ YES' : '❌ NO'}`);
            console.log(`🎉 Instant confirmation: ${confirmationFound ? '✅ YES' : '⚠️ CHECKING...'}`);

            const result = {
                success: submitSuccess,
                executionTime,
                targetAchieved: executionTime < 2000,
                excellentTime: executionTime < 1500,
                confirmationDetected: confirmationFound,
                timestamp: new Date().toISOString()
            };

            require('fs').writeFileSync('lightning-results.json', JSON.stringify(result, null, 2));

            return result;

        } catch (error) {
            const executionTime = Date.now() - this.startTime;
            console.error(`❌ Lightning order failed after ${executionTime}ms:`, error.message);
            
            return {
                success: false,
                executionTime,
                error: error.message
            };
        }
    }

    async delayedConfirmationCheck() {
        console.log('🔍 Delayed confirmation check...');
        
        try {
            // Wait for any delayed UI updates
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const successIndicators = [
                'text=Purchased successfully',
                'text=Order placed',
                'text=Success',
                'text=Filled',
                '.order-success',
                '.success-message',
                '.toast-success'
            ];

            for (const indicator of successIndicators) {
                try {
                    const element = this.page.locator(indicator).first();
                    const isVisible = await element.isVisible({ timeout: 500 });
                    if (isVisible) {
                        const text = await element.textContent();
                        console.log(`✅ Delayed confirmation: ${text}`);
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }

            console.log('⚠️ No delayed confirmation found');
            return false;
        } catch (error) {
            console.log('❌ Delayed check error:', error.message);
            return false;
        }
    }
}

async function runLightningTrader() {
    const trader = new LightningMEXCTrader();
    
    try {
        const connected = await trader.connectToRemoteBrowser();
        if (!connected) {
            throw new Error('Could not connect to remote browser');
        }

        console.log('\n⚡ LIGHTNING MODE ACTIVATED');
        console.log('==========================');
        console.log('🎯 Target: Sub-2 second execution');
        console.log('🏆 Stretch goal: Sub-1.5 second execution');
        console.log('📊 Symbol: TRUUSDT');
        console.log('📈 Side: Buy/Long');
        console.log('🔢 Quantity: 40');
        console.log('\nExecuting NOW...');
        
        const result = await trader.lightningOrder();
        
        // Delayed confirmation check
        const delayedConfirmation = await trader.delayedConfirmationCheck();
        
        if (result.success && result.excellentTime) {
            console.log('\n🏆 LIGHTNING SUCCESS!');
            console.log('⚡ ORDER PLACED UNDER 1.5 SECONDS!');
        } else if (result.success && result.targetAchieved) {
            console.log('\n🎯 TARGET ACHIEVED!');
            console.log('⚡ ORDER PLACED UNDER 2 SECONDS!');
        } else if (result.success) {
            console.log('\n✅ Order placed successfully!');
            console.log(`⏱️ Time: ${result.executionTime}ms`);
        } else {
            console.log('\n❌ Order placement failed');
        }

        if (delayedConfirmation) {
            console.log('🎉 Order placement CONFIRMED!');
        }
        
        return result;
        
    } catch (error) {
        console.error('💥 Lightning trader failed:', error.message);
        return { success: false, error: error.message };
    }
}

if (require.main === module) {
    console.log('⚡ MEXC LIGHTNING TRADER');
    console.log('========================');
    console.log('🚀 Maximum speed optimization');
    console.log('🎯 Target: Sub-2 second execution');
    console.log('🏆 Stretch: Sub-1.5 second execution');
    console.log('📋 Requires: Chrome with --remote-debugging-port=9222');
    console.log('');
    
    runLightningTrader()
        .then(result => {
            console.log('\n⚡ Lightning session completed');
            process.exit(result.success ? 0 : 1);
        })
        .catch(error => {
            console.error('💥 Session crashed:', error);
            process.exit(1);
        });
}

module.exports = LightningMEXCTrader;
