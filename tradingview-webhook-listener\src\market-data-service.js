const axios = require('axios');

class MarketDataService {
    constructor(configManager) {
        this.configManager = configManager;
        this.baseURL = 'https://api.mexc.com';
        this.futuresBaseURL = 'https://contract.mexc.com'; // Add futures API endpoint
        this.priceCache = new Map();
        this.klineCache = new Map();
        this.cacheTimeout = 5000; // 5 seconds cache
    }

    updateConfig(configManager) {
        this.configManager = configManager;
    }

    async getCurrentPrice(symbol = 'TRUUSDT') {
        const cacheKey = `price_${symbol}`;
        const cached = this.priceCache.get(cacheKey);

        if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
            return cached.data;
        }

        try {
            // Try futures API first for more accurate pricing
            let priceData;
            try {
                const futuresResponse = await axios.get(`${this.futuresBaseURL}/api/v1/contract/ticker`, {
                    params: { symbol: symbol.replace('USDT', '_USDT') }, // Convert TRUUSDT to TRU_USDT
                    timeout: 5000
                });

                if (futuresResponse.data && futuresResponse.data.code === 0) {
                    const tickerData = Array.isArray(futuresResponse.data.data)
                        ? futuresResponse.data.data[0]
                        : futuresResponse.data.data;

                    priceData = {
                        symbol: symbol,
                        price: parseFloat(tickerData.lastPrice),
                        timestamp: new Date().toISOString(),
                        source: 'futures'
                    };
                } else {
                    throw new Error('Futures API returned invalid data');
                }
            } catch (futuresError) {
                console.warn(`Futures API failed for ${symbol}, falling back to spot:`, futuresError.message);

                // Fallback to spot API
                const spotResponse = await axios.get(`${this.baseURL}/api/v3/ticker/price`, {
                    params: { symbol },
                    timeout: 5000
                });

                priceData = {
                    symbol: spotResponse.data.symbol,
                    price: parseFloat(spotResponse.data.price),
                    timestamp: new Date().toISOString(),
                    source: 'spot'
                };
            }

            this.priceCache.set(cacheKey, {
                data: priceData,
                timestamp: Date.now()
            });

            return priceData;
        } catch (error) {
            throw new Error(`Failed to get current price: ${error.message}`);
        }
    }

    async getKlineData(symbol = 'TRUUSDT', interval = '1m', limit = 100) {
        const cacheKey = `kline_${symbol}_${interval}_${limit}`;
        const cached = this.klineCache.get(cacheKey);

        if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
            return cached.data;
        }

        try {
            // Try futures API first for more accurate kline data
            let klineData;
            try {
                const futuresSymbol = symbol.replace('USDT', '_USDT'); // Convert TRUUSDT to TRU_USDT
                const futuresResponse = await axios.get(`${this.futuresBaseURL}/api/v1/contract/kline/${futuresSymbol}`, {
                    params: { interval, limit },
                    timeout: 10000
                });

                if (futuresResponse.data && futuresResponse.data.code === 0 && futuresResponse.data.data) {
                    klineData = futuresResponse.data.data.map(kline => ({
                        openTime: parseInt(kline.time),
                        open: parseFloat(kline.open),
                        high: parseFloat(kline.high),
                        low: parseFloat(kline.low),
                        close: parseFloat(kline.close),
                        volume: parseFloat(kline.vol),
                        closeTime: parseInt(kline.time) + 60000, // Add 1 minute for close time
                        source: 'futures'
                    }));
                } else {
                    throw new Error('Futures kline API returned invalid data');
                }
            } catch (futuresError) {
                console.warn(`Futures kline API failed for ${symbol}, falling back to spot:`, futuresError.message);

                // Fallback to spot API
                const spotResponse = await axios.get(`${this.baseURL}/api/v3/klines`, {
                    params: { symbol, interval, limit },
                    timeout: 10000
                });

                klineData = spotResponse.data.map(kline => ({
                    openTime: parseInt(kline[0]),
                    open: parseFloat(kline[1]),
                    high: parseFloat(kline[2]),
                    low: parseFloat(kline[3]),
                    close: parseFloat(kline[4]),
                    volume: parseFloat(kline[5]),
                    closeTime: parseInt(kline[6]),
                    source: 'spot'
                }));
            }

            this.klineCache.set(cacheKey, {
                data: klineData,
                timestamp: Date.now()
            });

            return klineData;
        } catch (error) {
            throw new Error(`Failed to get kline data: ${error.message}`);
        }
    }

    calculateATR(klineData, length = 14, smoothing = 'RMA') {
        if (klineData.length < length + 1) {
            throw new Error(`Insufficient data for ATR calculation. Need at least ${length + 1} candles`);
        }

        const trueRanges = [];
        
        // Calculate True Range for each candle
        for (let i = 1; i < klineData.length; i++) {
            const current = klineData[i];
            const previous = klineData[i - 1];
            
            const tr1 = current.high - current.low;
            const tr2 = Math.abs(current.high - previous.close);
            const tr3 = Math.abs(current.low - previous.close);
            
            const trueRange = Math.max(tr1, tr2, tr3);
            trueRanges.push(trueRange);
        }

        // Calculate ATR based on smoothing method
        let atr;
        switch (smoothing) {
            case 'SMA':
                atr = this.calculateSMA(trueRanges, length);
                break;
            case 'EMA':
                atr = this.calculateEMA(trueRanges, length);
                break;
            case 'RMA':
            default:
                atr = this.calculateRMA(trueRanges, length);
                break;
        }

        return atr;
    }

    calculateSMA(data, length) {
        if (data.length < length) return 0;
        const slice = data.slice(-length);
        return slice.reduce((sum, val) => sum + val, 0) / length;
    }

    calculateEMA(data, length) {
        if (data.length < length) return 0;
        
        const multiplier = 2 / (length + 1);
        let ema = data[0];
        
        for (let i = 1; i < data.length; i++) {
            ema = (data[i] * multiplier) + (ema * (1 - multiplier));
        }
        
        return ema;
    }

    calculateRMA(data, length) {
        if (data.length < length) return 0;
        
        let rma = data.slice(0, length).reduce((sum, val) => sum + val, 0) / length;
        
        for (let i = length; i < data.length; i++) {
            rma = (rma * (length - 1) + data[i]) / length;
        }
        
        return rma;
    }

    async getATR(symbol = 'TRUUSDT') {
        const config = this.configManager.getConfig();
        const klineData = await this.getKlineData(symbol, '1m', config.atrLength + 20); // Extra data for accuracy
        
        return this.calculateATR(klineData, config.atrLength, config.atrSmoothing);
    }

    validatePriceDifference(currentPrice, expectedPrice, maxDifferencePercent) {
        const difference = Math.abs(currentPrice - expectedPrice);
        const percentDifference = (difference / expectedPrice) * 100;
        
        return {
            valid: percentDifference <= maxDifferencePercent,
            difference: percentDifference,
            currentPrice,
            expectedPrice
        };
    }

    calculateSLTP(entryPrice, atr, config, direction = 'long') {
        const slDistance = atr * config.slMultiplier;
        const results = {
            entryPrice,
            atr,
            direction,
            stopLoss: null,
            takeProfits: [],
            trailingStart: null,
            slType: config.slType || 'Normal'
        };

        if (direction === 'long') {
            // Long position
            results.stopLoss = entryPrice - slDistance;

            // Calculate take profits
            if (config.tp1Enabled) {
                const tp1Distance = atr * config.tp1Reward;
                results.takeProfits.push({
                    level: 1,
                    price: entryPrice + tp1Distance,
                    percent: config.tp1Percent,
                    enabled: true
                });
            }

            if (config.tp2Enabled) {
                const tp2Distance = atr * config.tp2Reward;
                results.takeProfits.push({
                    level: 2,
                    price: entryPrice + tp2Distance,
                    percent: config.tp2Percent,
                    enabled: true
                });
            }

            if (config.tp3Enabled) {
                const tp3Distance = atr * config.tp3Reward;
                results.takeProfits.push({
                    level: 3,
                    price: entryPrice + tp3Distance,
                    percent: config.tp3Percent,
                    enabled: true
                });
            }

            // Trailing start level
            if (config.startTrailingAtProfit > 0) {
                const trailingDistance = atr * config.startTrailingAtProfit;
                results.trailingStart = entryPrice + trailingDistance;
            }
        } else if (direction === 'short') {
            // Short position
            results.stopLoss = entryPrice + slDistance;

            // Calculate take profits for short
            if (config.tp1Enabled) {
                const tp1Distance = atr * config.tp1Reward;
                results.takeProfits.push({
                    level: 1,
                    price: entryPrice - tp1Distance,
                    percent: config.tp1Percent,
                    enabled: true
                });
            }

            if (config.tp2Enabled) {
                const tp2Distance = atr * config.tp2Reward;
                results.takeProfits.push({
                    level: 2,
                    price: entryPrice - tp2Distance,
                    percent: config.tp2Percent,
                    enabled: true
                });
            }

            if (config.tp3Enabled) {
                const tp3Distance = atr * config.tp3Reward;
                results.takeProfits.push({
                    level: 3,
                    price: entryPrice - tp3Distance,
                    percent: config.tp3Percent,
                    enabled: true
                });
            }

            // Trailing start level for short
            if (config.startTrailingAtProfit > 0) {
                const trailingDistance = atr * config.startTrailingAtProfit;
                results.trailingStart = entryPrice - trailingDistance;
            }
        }

        return results;
    }

    // Get price based on source configuration
    getPriceFromSource(klineData, source, sourceAdd = 0) {
        if (!klineData || klineData.length === 0) return null;

        const latest = klineData[klineData.length - 1];
        let price;

        switch (source) {
            case 'Open':
                price = latest.open;
                break;
            case 'High':
                price = latest.high;
                break;
            case 'Low':
                price = latest.low;
                break;
            case 'HLCO4':
                price = (latest.high + latest.low + latest.close + latest.open) / 4;
                break;
            case 'Close':
            default:
                price = latest.close;
                break;
        }

        // Apply source add percentage
        if (sourceAdd > 0) {
            price = price * (1 + sourceAdd / 100);
        }

        return price;
    }

    // Clear caches
    clearCache() {
        this.priceCache.clear();
        this.klineCache.clear();
    }

    // Get cache statistics
    getCacheStats() {
        return {
            priceCache: this.priceCache.size,
            klineCache: this.klineCache.size,
            cacheTimeout: this.cacheTimeout
        };
    }
}

module.exports = MarketDataService;
