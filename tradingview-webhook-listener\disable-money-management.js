const axios = require('axios');

async function disableMoneyManagement() {
    const config = {
        moneyManagementEnabled: false,
        defaultQuantity: '0.0001'
    };

    try {
        const response = await axios.post('http://localhost:4000/api/config', config);
        console.log('✅ Money management disabled:', response.data);
        
        // Now test a webhook
        const testSignal = {
            symbol: "TRUUSDT",
            trade: "open",
            last_price: "0.000012064",
            leverage: "2"
        };

        console.log('\n🚀 Testing webhook with disabled money management...');
        const webhookResponse = await axios.post('http://localhost:4000/webhook', testSignal);
        console.log('✅ Webhook Response:', webhookResponse.data);
        
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

disableMoneyManagement();
