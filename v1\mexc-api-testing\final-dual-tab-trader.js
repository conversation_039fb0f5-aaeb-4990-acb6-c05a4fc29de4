const { chromium } = require('playwright');

class FinalDualTabTrader {
    constructor() {
        this.browser = null;
        this.openTab = null;
        this.closeTab = null;
    }

    async connectToRemoteBrowser() {
        try {
            this.browser = await chromium.connectOverCDP('http://localhost:9222');
            const contexts = this.browser.contexts();
            
            if (contexts.length > 0) {
                const context = contexts[0];
                const pages = context.pages();
                
                if (pages.length >= 2) {
                    this.openTab = pages[0];
                    this.closeTab = pages[1];
                    console.log('✅ Connected - Using existing 2 tabs');
                } else {
                    this.openTab = pages[0];
                    this.closeTab = await context.newPage();
                    await this.closeTab.goto('https://www.mexc.com/futures/TRU_USDT');
                    console.log('✅ Connected - Created second tab');
                }
            }
            
            return true;
        } catch (error) {
            console.error('❌ Connection failed:', error.message);
            return false;
        }
    }

    async executeOpenTrade() {
        const startTime = Date.now();
        console.log('📈 EXECUTING OPEN TRADE...');
        
        try {
            // Quick workflow execution
            await this.quickFillAndClick(this.openTab, 'Open Long', 'OPEN');
            
            const executionTime = Date.now() - startTime;
            
            // Quick verification
            const verified = await this.quickVerify(this.openTab);
            
            console.log(`⚡ OPEN completed in ${executionTime}ms`);
            console.log(`🎉 OPEN verified: ${verified ? '✅ YES' : '❌ NO'}`);
            
            return {
                success: verified, // Use verification as success indicator
                executionTime,
                verified,
                type: 'OPEN'
            };
            
        } catch (error) {
            const executionTime = Date.now() - startTime;
            console.error(`❌ OPEN failed: ${error.message}`);
            return { success: false, executionTime, error: error.message, type: 'OPEN' };
        }
    }

    async executeCloseTrade() {
        const startTime = Date.now();
        console.log('📉 EXECUTING CLOSE TRADE...');
        
        try {
            // Quick workflow execution
            await this.quickFillAndClick(this.closeTab, 'Open Short', 'CLOSE');
            
            const executionTime = Date.now() - startTime;
            
            // Quick verification
            const verified = await this.quickVerify(this.closeTab);
            
            console.log(`⚡ CLOSE completed in ${executionTime}ms`);
            console.log(`🎉 CLOSE verified: ${verified ? '✅ YES' : '❌ NO'}`);
            
            return {
                success: verified, // Use verification as success indicator
                executionTime,
                verified,
                type: 'CLOSE'
            };
            
        } catch (error) {
            const executionTime = Date.now() - startTime;
            console.error(`❌ CLOSE failed: ${error.message}`);
            return { success: false, executionTime, error: error.message, type: 'CLOSE' };
        }
    }

    async quickFillAndClick(page, buttonText, tradeType) {
        // Step 1: Fill quantity (parallel with button preparation)
        const operations = await Promise.allSettled([
            // Fill quantity
            (async () => {
                const selectors = [
                    'text=Quantity(USDT) >> xpath=following::input[1]',
                    'input[placeholder*="quantity"]',
                    'input[type="number"]'
                ];
                
                for (const selector of selectors) {
                    try {
                        const element = page.locator(selector).first();
                        if (await element.isVisible({ timeout: 200 })) {
                            await element.click();
                            await element.fill('0.3600');
                            console.log(`⚡ ${tradeType}: Quantity filled`);
                            return true;
                        }
                    } catch (error) {
                        continue;
                    }
                }
                return false;
            })(),
            
            // Prepare button
            (async () => {
                await new Promise(resolve => setTimeout(resolve, 100));
                const element = page.locator(`button:has-text("${buttonText}")`).first();
                if (await element.isVisible({ timeout: 200 })) {
                    return element;
                }
                return null;
            })()
        ]);

        const quantityFilled = operations[0].status === 'fulfilled' && operations[0].value;
        const button = operations[1].status === 'fulfilled' ? operations[1].value : null;

        // Step 2: Click button
        if (button) {
            await button.click();
            console.log(`⚡ ${tradeType}: ${buttonText} clicked`);
        }

        // Step 3: Handle popup
        await this.quickConfirm(page, tradeType);
        
        return quantityFilled && button;
    }

    async quickConfirm(page, tradeType) {
        // Aggressive popup handling
        for (let i = 0; i < 20; i++) {
            try {
                const confirmBtn = page.locator('button:has-text("Confirm")').first();
                if (await confirmBtn.isVisible({ timeout: 50 })) {
                    await confirmBtn.click();
                    console.log(`⚡ ${tradeType}: Confirm clicked`);
                    return true;
                }
            } catch (error) {
                // Continue trying
            }
            await new Promise(resolve => setTimeout(resolve, 25));
        }
        return false;
    }

    async quickVerify(page) {
        try {
            const successSelectors = [
                'text=Purchased successfully',
                'text=Success',
                'text=success',
                '.success'
            ];

            for (const selector of successSelectors) {
                try {
                    if (await page.locator(selector).first().isVisible({ timeout: 300 })) {
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }
            return false;
        } catch (error) {
            return false;
        }
    }
}

async function executeCommand(command) {
    const trader = new FinalDualTabTrader();
    
    try {
        console.log('🎯 MEXC ULTRA-FAST DUAL TAB TRADER');
        console.log('===================================');
        console.log('⚡ Target: <2 seconds per trade');
        console.log('📈 OPEN: Tab 1 - Open Long');
        console.log('📉 CLOSE: Tab 2 - Open Short');
        console.log('');

        const connected = await trader.connectToRemoteBrowser();
        if (!connected) {
            throw new Error('Connection failed');
        }

        let result;
        
        switch (command) {
            case 'open':
                result = await trader.executeOpenTrade();
                break;
                
            case 'close':
                result = await trader.executeCloseTrade();
                break;
                
            case 'both':
                console.log('🔄 EXECUTING BOTH TRADES...');
                const openResult = await trader.executeOpenTrade();
                await new Promise(resolve => setTimeout(resolve, 500));
                const closeResult = await trader.executeCloseTrade();
                
                const totalTime = openResult.executionTime + closeResult.executionTime;
                
                console.log('\n🏆 BOTH TRADES SUMMARY:');
                console.log('=======================');
                console.log(`📈 OPEN: ${openResult.success ? '✅' : '❌'} (${openResult.executionTime}ms)`);
                console.log(`📉 CLOSE: ${closeResult.success ? '✅' : '❌'} (${closeResult.executionTime}ms)`);
                console.log(`⏱️ Total: ${totalTime}ms`);
                console.log(`🎯 Both <2s: ${openResult.executionTime < 2000 && closeResult.executionTime < 2000 ? '✅' : '❌'}`);
                
                result = { 
                    success: openResult.success && closeResult.success,
                    openResult, 
                    closeResult, 
                    totalTime 
                };
                break;
                
            default:
                console.log('📋 USAGE:');
                console.log('node final-dual-tab-trader.js open   - Execute OPEN trade');
                console.log('node final-dual-tab-trader.js close  - Execute CLOSE trade');
                console.log('node final-dual-tab-trader.js both   - Execute BOTH trades');
                return;
        }

        // Final results
        if (result.success) {
            if (result.executionTime && result.executionTime < 2000) {
                console.log('\n🏆 TARGET ACHIEVED!');
                console.log(`⚡ Executed in ${result.executionTime}ms (<2 seconds)`);
            } else {
                console.log('\n✅ Trade completed successfully!');
            }
        } else {
            console.log('\n❌ Trade failed');
        }

        // Save results
        require('fs').writeFileSync('final-dual-results.json', JSON.stringify(result, null, 2));
        
        process.exit(result.success ? 0 : 1);
        
    } catch (error) {
        console.error('💥 Trader failed:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    const command = process.argv[2];
    executeCommand(command);
}

module.exports = FinalDualTabTrader;
