const crypto = require('crypto');
const axios = require('axios');

class MexcApiTester {
    constructor(api<PERSON>ey, secretKey) {
        this.apiKey = apiKey;
        this.secretKey = secretKey;
        this.baseURL = 'https://api.mexc.com';
    }

    generateSignature(queryString, secretKey) {
        return crypto
            .createHmac('sha256', secretKey)
            .update(queryString)
            .digest('hex');
    }

    async makeRequest(method, endpoint, params = {}, isPrivate = false) {
        console.log(`\n🔍 Testing ${method} ${endpoint}`);
        console.log(`   Private: ${isPrivate}`);
        
        const timestamp = Date.now();
        let url = `${this.baseURL}${endpoint}`;
        let headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'MEXC-API-Test/1.0.0'
        };

        if (isPrivate) {
            // Add API key to headers
            headers['X-MEXC-APIKEY'] = this.apiKey;
            
            // Create query string with timestamp
            const queryParams = {
                ...params,
                timestamp: timestamp
            };
            
            const queryString = Object.keys(queryParams)
                .sort()
                .map(key => `${key}=${queryParams[key]}`)
                .join('&');
            
            console.log(`   Query String: ${queryString}`);
            
            // Generate signature
            const signature = this.generateSignature(queryString, this.secretKey);
            console.log(`   Signature: ${signature.substring(0, 16)}...`);
            
            // Add signature to query string
            url += `?${queryString}&signature=${signature}`;
            
            headers['signature'] = signature;
        } else if (Object.keys(params).length > 0) {
            const queryString = Object.keys(params)
                .map(key => `${key}=${params[key]}`)
                .join('&');
            url += `?${queryString}`;
        }

        console.log(`   URL: ${url.substring(0, 80)}...`);
        console.log(`   Headers: ${JSON.stringify(headers, null, 2)}`);

        try {
            const response = await axios({
                method,
                url,
                headers,
                timeout: 10000
            });

            console.log(`   ✅ Status: ${response.status}`);
            console.log(`   📊 Response:`, JSON.stringify(response.data, null, 2));
            
            return response.data;
        } catch (error) {
            console.log(`   ❌ Error: ${error.response?.status || 'Network Error'}`);
            console.log(`   📋 Error Data:`, JSON.stringify(error.response?.data || error.message, null, 2));
            throw error;
        }
    }

    async testServerTime() {
        console.log('\n🕐 Testing Server Time...');
        try {
            const result = await this.makeRequest('GET', '/api/v3/time');
            const serverTime = new Date(result.serverTime);
            const localTime = new Date();
            const timeDiff = Math.abs(serverTime.getTime() - localTime.getTime());
            
            console.log(`   Server Time: ${serverTime.toISOString()}`);
            console.log(`   Local Time: ${localTime.toISOString()}`);
            console.log(`   Time Difference: ${timeDiff}ms`);
            
            if (timeDiff > 5000) {
                console.log('   ⚠️ WARNING: Time difference > 5 seconds');
            }
            
            return result;
        } catch (error) {
            console.log('   ❌ Server time test failed');
            throw error;
        }
    }

    async testExchangeInfo() {
        console.log('\n📋 Testing Exchange Info...');
        try {
            const result = await this.makeRequest('GET', '/api/v3/exchangeInfo');
            console.log(`   Symbols Count: ${result.symbols?.length || 0}`);
            
            // Find TRUUSDT symbol
            const truSymbol = result.symbols?.find(s => s.symbol === 'TRUUSDT');
            if (truSymbol) {
                console.log(`   ✅ TRUUSDT found: ${truSymbol.status}`);
            } else {
                console.log(`   ⚠️ TRUUSDT not found`);
            }
            
            return result;
        } catch (error) {
            console.log('   ❌ Exchange info test failed');
            throw error;
        }
    }

    async testAccountInfo() {
        console.log('\n👤 Testing Account Info...');
        try {
            const result = await this.makeRequest('GET', '/api/v3/account', {}, true);
            console.log(`   Can Trade: ${result.canTrade}`);
            console.log(`   Can Withdraw: ${result.canWithdraw}`);
            console.log(`   Can Deposit: ${result.canDeposit}`);
            console.log(`   Account Type: ${result.accountType}`);
            console.log(`   Balances Count: ${result.balances?.length || 0}`);
            
            // Find USDT balance
            const usdtBalance = result.balances?.find(b => b.asset === 'USDT');
            if (usdtBalance) {
                console.log(`   💰 USDT Balance:`);
                console.log(`      Free: ${usdtBalance.free}`);
                console.log(`      Locked: ${usdtBalance.locked}`);
                console.log(`      Total: ${parseFloat(usdtBalance.free) + parseFloat(usdtBalance.locked)}`);
            } else {
                console.log(`   ⚠️ USDT balance not found`);
            }
            
            return result;
        } catch (error) {
            console.log('   ❌ Account info test failed');
            throw error;
        }
    }

    async testFuturesAccount() {
        console.log('\n🔮 Testing Futures Account...');
        try {
            // Try different futures endpoints
            const endpoints = [
                '/api/v3/account',
                '/fapi/v1/account',
                '/dapi/v1/account',
                '/api/v3/capital/config/getall'
            ];

            for (const endpoint of endpoints) {
                try {
                    console.log(`\n   Trying endpoint: ${endpoint}`);
                    const result = await this.makeRequest('GET', endpoint, {}, true);
                    
                    if (result.totalWalletBalance !== undefined) {
                        console.log(`   💰 Futures Balance: ${result.totalWalletBalance} USDT`);
                        console.log(`   💰 Available Balance: ${result.availableBalance} USDT`);
                        return result;
                    }
                    
                    if (result.balances) {
                        const usdtBalance = result.balances.find(b => b.asset === 'USDT');
                        if (usdtBalance) {
                            console.log(`   💰 USDT Balance: ${usdtBalance.free} (free) + ${usdtBalance.locked} (locked)`);
                        }
                    }
                    
                } catch (endpointError) {
                    console.log(`   ❌ ${endpoint} failed: ${endpointError.response?.status || endpointError.message}`);
                }
            }
            
        } catch (error) {
            console.log('   ❌ Futures account test failed');
            throw error;
        }
    }

    async runCompleteTest() {
        console.log('🧪 MEXC API Credentials Test');
        console.log('============================');
        console.log(`API Key: ${this.apiKey}`);
        console.log(`Secret: ${this.secretKey.substring(0, 8)}...`);
        console.log(`Base URL: ${this.baseURL}`);

        try {
            // Test 1: Server Time
            await this.testServerTime();

            // Test 2: Exchange Info (public)
            await this.testExchangeInfo();

            // Test 3: Account Info (private)
            await this.testAccountInfo();

            // Test 4: Futures Account (private)
            await this.testFuturesAccount();

            console.log('\n✅ All tests completed successfully!');
            
        } catch (error) {
            console.log('\n❌ Test failed:', error.message);
        }
    }
}

// Test with provided credentials
async function main() {
    const apiKey = 'mx0vgl6bPv2Frz96j0';
    const secretKey = 'ac416e95c9b34ec0ba6d3210c18a1e76';
    
    const tester = new MexcApiTester(apiKey, secretKey);
    await tester.runCompleteTest();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = MexcApiTester;
