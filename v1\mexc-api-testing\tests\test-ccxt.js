const ccxt = require('ccxt');
require('dotenv').config();

async function testCCXT() {
    console.log('\n=== Testing CCXT Library ===');
    
    try {
        const exchange = new ccxt.mexc({
            apiKey: process.env.MEXC_API_KEY,
            secret: process.env.MEXC_API_SECRET,
            sandbox: false, // Set to true for testnet
            enableRateLimit: true,
        });

        console.log('✓ CCXT MEXC exchange initialized');

        // Test connection
        console.log('\n1. Testing connection...');
        const markets = await exchange.loadMarkets();
        console.log(`✓ Connected! Found ${Object.keys(markets).length} markets`);

        // Test account info
        console.log('\n2. Testing account info...');
        const balance = await exchange.fetchBalance();
        console.log('✓ Account balance fetched:', Object.keys(balance.total).length, 'currencies');

        // Test futures-specific functionality
        console.log('\n3. Testing futures functionality...');
        
        // Check if futures are supported
        if (exchange.has['fetchPositions']) {
            try {
                const positions = await exchange.fetchPositions();
                console.log('✓ Futures positions fetched:', positions.length, 'positions');
            } catch (error) {
                console.log('⚠ Futures positions error:', error.message);
            }
        } else {
            console.log('⚠ Futures positions not supported in this version');
        }

        // Test order placement (dry run)
        console.log('\n4. Testing order creation (dry run)...');
        const symbol = process.env.TEST_SYMBOL || 'BTC/USDT';
        const side = process.env.TEST_SIDE || 'buy';
        const amount = parseFloat(process.env.TEST_QUANTITY || '0.001');
        
        try {
            // Get current price for limit order
            const ticker = await exchange.fetchTicker(symbol);
            const price = ticker.last * 0.99; // 1% below current price for buy
            
            console.log(`Symbol: ${symbol}, Side: ${side}, Amount: ${amount}, Price: ${price}`);
            
            // Note: Uncomment the line below to actually place an order
            // const order = await exchange.createLimitOrder(symbol, side, amount, price);
            console.log('✓ Order parameters validated (order not placed)');
            
        } catch (error) {
            console.log('⚠ Order creation test failed:', error.message);
        }

        console.log('\n✅ CCXT test completed successfully');
        return { success: true, library: 'ccxt', features: ['spot', 'futures_partial'] };

    } catch (error) {
        console.error('❌ CCXT test failed:', error.message);
        return { success: false, library: 'ccxt', error: error.message };
    }
}

if (require.main === module) {
    testCCXT().then(result => {
        console.log('\nResult:', result);
        process.exit(result.success ? 0 : 1);
    });
}

module.exports = testCCXT;
