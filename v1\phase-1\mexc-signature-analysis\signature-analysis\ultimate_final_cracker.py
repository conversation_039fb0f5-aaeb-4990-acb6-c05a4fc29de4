#!/usr/bin/env python3
"""
ULTIMATE FINAL CRACKER
Based on our analysis: nonce ≈ timestamp, test all possible variations
"""

import json
import hashlib
import hmac
import base64

class UltimateFinalCracker:
    """Final attempt to crack the signature algorithm"""
    
    def __init__(self):
        print("🎯 ULTIMATE FINAL CRACKER")
        print("="*30)
        print("🔥 TESTING ALL POSSIBLE VARIATIONS")
        
        # Load captured data
        with open('captured_data.json', 'r') as f:
            self.data = json.load(f)
        
        self.auth = "WEBd98cd3a6eecf378454e859e5b4b680aeae16c4bd38492179d5a4f4afa81f43b6"
        
        # Get first order creation signature for testing
        self.test_signature = None
        self.test_nonce = None
        self.test_timestamp = None
        
        for sig in self.data['signatures']:
            if 'order/create' in sig.get('url', ''):
                self.test_signature = sig['signature']
                self.test_nonce = sig['headers'].get('x-mxc-nonce', 0)
                self.test_timestamp = sig['timestamp']
                break
        
        print(f"🎯 Test signature: {self.test_signature}")
        print(f"🎯 Test nonce: {self.test_nonce}")
        print(f"🎯 Test timestamp: {self.test_timestamp}")
    
    def test_all_variations(self):
        """Test all possible signature variations"""
        
        print(f"\n🧪 TESTING ALL VARIATIONS")
        print("="*30)
        
        # Auth token variations
        auth_variations = [
            self.auth,                    # Full auth
            self.auth[3:],               # Without WEB prefix
            self.auth[3:67],             # Just the main token part
            self.auth[3:35],             # First 32 chars after WEB
            self.auth[35:67],            # Last 32 chars of token
        ]
        
        # Nonce/timestamp variations
        time_variations = [
            str(self.test_nonce),
            str(self.test_timestamp),
            str(self.test_nonce)[:10],    # First 10 digits
            str(self.test_timestamp)[:10], # First 10 digits
            str(int(self.test_nonce/1000)), # Seconds instead of milliseconds
            str(int(self.test_timestamp/1000)),
        ]
        
        # Additional components
        additional_components = [
            "",
            "POST",
            "/api/v1/private/order/create",
            "order/create",
            "85723e9fb269ff0e1e19525050842a3c",  # mhash
            "b03MOmeXoiZid75ogtwP",              # mtoken
        ]
        
        test_count = 0
        
        for auth_var in auth_variations:
            for time_var in time_variations:
                for additional in additional_components:
                    # Test different orderings
                    patterns = [
                        auth_var + time_var + additional,
                        time_var + auth_var + additional,
                        auth_var + additional + time_var,
                        time_var + additional + auth_var,
                        additional + auth_var + time_var,
                        additional + time_var + auth_var,
                    ]
                    
                    for pattern in patterns:
                        if pattern:  # Skip empty patterns
                            test_count += 1
                            
                            # Test MD5
                            test_sig = hashlib.md5(pattern.encode()).hexdigest()
                            if test_sig == self.test_signature:
                                print(f"🎉🎉🎉 SIGNATURE CRACKED! 🎉🎉🎉")
                                print(f"   Algorithm: MD5")
                                print(f"   Pattern: {pattern}")
                                print(f"   Auth: {auth_var[:20]}...")
                                print(f"   Time: {time_var}")
                                print(f"   Additional: {additional}")
                                return True
                            
                            # Test SHA1 (first 32 chars)
                            test_sig = hashlib.sha1(pattern.encode()).hexdigest()[:32]
                            if test_sig == self.test_signature:
                                print(f"🎉🎉🎉 SIGNATURE CRACKED! 🎉🎉🎉")
                                print(f"   Algorithm: SHA1[:32]")
                                print(f"   Pattern: {pattern}")
                                return True
                            
                            # Test SHA256 (first 32 chars)
                            test_sig = hashlib.sha256(pattern.encode()).hexdigest()[:32]
                            if test_sig == self.test_signature:
                                print(f"🎉🎉🎉 SIGNATURE CRACKED! 🎉🎉🎉")
                                print(f"   Algorithm: SHA256[:32]")
                                print(f"   Pattern: {pattern}")
                                return True
                            
                            # Test HMAC-MD5 with auth as key
                            try:
                                test_sig = hmac.new(auth_var.encode(), (time_var + additional).encode(), hashlib.md5).hexdigest()
                                if test_sig == self.test_signature:
                                    print(f"🎉🎉🎉 SIGNATURE CRACKED! 🎉🎉🎉")
                                    print(f"   Algorithm: HMAC-MD5")
                                    print(f"   Key: {auth_var[:20]}...")
                                    print(f"   Message: {time_var + additional}")
                                    return True
                            except:
                                pass
                            
                            # Test HMAC-SHA256 (first 32 chars)
                            try:
                                test_sig = hmac.new(auth_var.encode(), (time_var + additional).encode(), hashlib.sha256).hexdigest()[:32]
                                if test_sig == self.test_signature:
                                    print(f"🎉🎉🎉 SIGNATURE CRACKED! 🎉🎉🎉")
                                    print(f"   Algorithm: HMAC-SHA256[:32]")
                                    print(f"   Key: {auth_var[:20]}...")
                                    print(f"   Message: {time_var + additional}")
                                    return True
                            except:
                                pass
        
        print(f"❌ Tested {test_count} patterns, no matches found")
        return False
    
    def test_multiple_signatures(self):
        """Test the algorithm against multiple signatures to verify"""
        
        print(f"\n🔍 TESTING MULTIPLE SIGNATURES")
        print("="*40)
        
        # Get multiple order creation signatures
        order_signatures = []
        for sig in self.data['signatures']:
            if 'order/create' in sig.get('url', ''):
                order_signatures.append(sig)
        
        print(f"📊 Testing against {len(order_signatures)} order signatures")
        
        # Test likely patterns against multiple signatures
        likely_patterns = [
            # Pattern: auth_without_web + nonce
            lambda sig: self.auth[3:] + str(sig['headers'].get('x-mxc-nonce', 0)),
            
            # Pattern: nonce + auth_without_web
            lambda sig: str(sig['headers'].get('x-mxc-nonce', 0)) + self.auth[3:],
            
            # Pattern: auth_token_part + nonce
            lambda sig: self.auth[3:67] + str(sig['headers'].get('x-mxc-nonce', 0)),
            
            # Pattern: nonce + auth_token_part
            lambda sig: str(sig['headers'].get('x-mxc-nonce', 0)) + self.auth[3:67],
        ]
        
        for i, pattern_func in enumerate(likely_patterns):
            matches = 0
            
            for sig in order_signatures[:5]:  # Test first 5
                pattern = pattern_func(sig)
                test_sig = hashlib.md5(pattern.encode()).hexdigest()
                
                if test_sig == sig['signature']:
                    matches += 1
            
            if matches > 0:
                print(f"🎯 Pattern {i+1} matched {matches}/5 signatures!")
                print(f"   Example pattern: {pattern_func(order_signatures[0])}")
                
                if matches >= 3:  # If 3+ matches, likely correct
                    print(f"🎉🎉🎉 ALGORITHM FOUND! 🎉🎉🎉")
                    return True
        
        return False
    
    def test_entropy_combinations(self):
        """Test combinations with captured entropy"""
        
        print(f"\n🧪 TESTING ENTROPY COMBINATIONS")
        print("="*40)
        
        entropy_values = self.data.get('entropy', [])
        
        # Find entropy values near our test signature timestamp
        nearby_entropy = [
            e for e in entropy_values
            if abs(e['timestamp'] - self.test_timestamp) < 30000  # Within 30 seconds
        ]
        
        print(f"📊 Found {len(nearby_entropy)} nearby entropy values")
        
        for i, entropy in enumerate(nearby_entropy[:10]):  # Test first 10
            if entropy['type'] == 'crypto_random':
                entropy_hex = entropy['hex']
                
                print(f"   Testing entropy #{i+1}: {entropy_hex[:16]}...")
                
                # Test various combinations
                patterns = [
                    # Entropy + auth + nonce
                    entropy_hex + self.auth + str(self.test_nonce),
                    self.auth + entropy_hex + str(self.test_nonce),
                    str(self.test_nonce) + entropy_hex + self.auth,
                    
                    # Just entropy + nonce
                    entropy_hex + str(self.test_nonce),
                    str(self.test_nonce) + entropy_hex,
                    
                    # Entropy + auth (without WEB)
                    entropy_hex + self.auth[3:],
                    self.auth[3:] + entropy_hex,
                ]
                
                for pattern in patterns:
                    # Test MD5
                    test_sig = hashlib.md5(pattern.encode()).hexdigest()
                    if test_sig == self.test_signature:
                        print(f"🎉🎉🎉 ENTROPY SIGNATURE CRACKED! 🎉🎉🎉")
                        print(f"   Algorithm: MD5")
                        print(f"   Pattern: {pattern}")
                        print(f"   Entropy: {entropy_hex}")
                        return True
        
        return False
    
    def run_ultimate_crack(self):
        """Run the ultimate cracking attempt"""
        
        print("="*60)
        print("🎯 ULTIMATE FINAL CRACKING ATTEMPT")
        print("="*60)
        
        if not self.test_signature:
            print("❌ No test signature found")
            return False
        
        # Test all variations
        if self.test_all_variations():
            return True
        
        # Test multiple signatures
        if self.test_multiple_signatures():
            return True
        
        # Test entropy combinations
        if self.test_entropy_combinations():
            return True
        
        print(f"\n📋 ULTIMATE ANALYSIS:")
        print("- Tested thousands of signature combinations")
        print("- Standard algorithms (MD5, SHA1, SHA256, HMAC) don't match")
        print("- Algorithm likely uses:")
        print("  * Custom hash function")
        print("  * WebAssembly implementation")
        print("  * Hardware-based signing")
        print("  * Obfuscated JavaScript")
        print("  * Server-side component")
        
        return False

def main():
    """Main function"""
    
    cracker = UltimateFinalCracker()
    if cracker.run_ultimate_crack():
        print("\n🎉 MEXC SIGNATURE ALGORITHM CRACKED!")
    else:
        print("\n🔍 Algorithm requires specialized tools or server-side analysis")

if __name__ == '__main__':
    main()
