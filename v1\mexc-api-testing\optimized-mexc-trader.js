const { chromium } = require('playwright');

class OptimizedMEXCTrader {
    constructor() {
        this.browser = null;
        this.page = null;
        this.startTime = null;
    }

    async connectToRemoteBrowser() {
        try {
            this.browser = await chromium.connectOverCDP('http://localhost:9222');
            const contexts = this.browser.contexts();
            
            if (contexts.length > 0) {
                const context = contexts[0];
                const pages = context.pages();
                this.page = pages.length > 0 ? pages[0] : await context.newPage();
            } else {
                const context = await this.browser.newContext();
                this.page = await context.newPage();
            }
            
            return true;
        } catch (error) {
            console.error('❌ Connection failed:', error.message);
            return false;
        }
    }

    async optimizedWorkflow() {
        this.startTime = Date.now();
        console.log('⚡ OPTIMIZED WORKFLOW - TARGET: <2 SECONDS');
        
        try {
            // OPTIMIZATION 1: Skip navigation if already on correct page
            const currentUrl = this.page.url();
            if (!currentUrl.includes('mexc.com/futures/TRU_USDT')) {
                console.log('🌐 Quick navigation...');
                await this.page.goto('https://www.mexc.com/futures/TRU_USDT', {
                    waitUntil: 'domcontentloaded',
                    timeout: 3000
                });
                // Minimal wait for critical elements
                await this.page.waitForTimeout(800);
            } else {
                console.log('✅ Already on correct page');
            }

            // OPTIMIZATION 2: Parallel element detection and interaction
            console.log('⚡ Executing parallel operations...');
            
            const parallelOperations = await Promise.allSettled([
                // Operation 1: Find and fill quantity field
                (async () => {
                    const quantitySelectors = [
                        'text=Quantity(USDT) >> xpath=following::input[1]', // This worked before
                        'text=Quantity >> xpath=following::input[1]',
                        'input[placeholder*="quantity"]',
                        'input[placeholder*="USDT"]'
                    ];

                    for (const selector of quantitySelectors) {
                        try {
                            const element = this.page.locator(selector).first();
                            const isVisible = await element.isVisible({ timeout: 300 });
                            
                            if (isVisible) {
                                await element.click({ timeout: 200 });
                                await element.fill('0.3600');
                                console.log('⚡ Quantity filled: 0.3600');
                                return true;
                            }
                        } catch (error) {
                            continue;
                        }
                    }
                    throw new Error('Quantity field not found');
                })(),

                // Operation 2: Prepare Open Long button (with small delay)
                (async () => {
                    await new Promise(resolve => setTimeout(resolve, 200)); // Small delay for UI update
                    
                    const openLongSelectors = [
                        'button:has-text("Open Long")', // This worked before
                        'text=Open Long',
                        '.open-long',
                        'button[class*="long"]'
                    ];

                    for (const selector of openLongSelectors) {
                        try {
                            const element = this.page.locator(selector).first();
                            const isVisible = await element.isVisible({ timeout: 300 });
                            
                            if (isVisible) {
                                console.log('⚡ Open Long button ready');
                                return { element, selector };
                            }
                        } catch (error) {
                            continue;
                        }
                    }
                    throw new Error('Open Long button not found');
                })()
            ]);

            // Check if quantity operation succeeded
            const quantitySuccess = parallelOperations[0].status === 'fulfilled';
            const openLongReady = parallelOperations[1].status === 'fulfilled' ? parallelOperations[1].value : null;

            if (!quantitySuccess) {
                throw new Error('Failed to fill quantity field');
            }

            if (!openLongReady) {
                throw new Error('Open Long button not ready');
            }

            // OPTIMIZATION 3: Immediate Open Long click
            console.log('⚡ Clicking Open Long...');
            await openLongReady.element.click({ timeout: 300 });

            // OPTIMIZATION 4: Aggressive popup handling
            console.log('⚡ Handling popup...');
            
            // Use Promise.race to click Confirm as soon as it appears
            const confirmPromise = (async () => {
                const confirmSelectors = [
                    'button:has-text("Confirm")', // This worked before
                    'text=Confirm',
                    '.confirm-btn'
                ];

                // Try multiple times with short intervals
                for (let attempt = 0; attempt < 10; attempt++) {
                    for (const selector of confirmSelectors) {
                        try {
                            const element = this.page.locator(selector).first();
                            const isVisible = await element.isVisible({ timeout: 100 });
                            
                            if (isVisible) {
                                await element.click({ timeout: 200 });
                                console.log('⚡ Confirm clicked');
                                return true;
                            }
                        } catch (error) {
                            continue;
                        }
                    }
                    await new Promise(resolve => setTimeout(resolve, 50)); // Very short wait
                }
                return false;
            })();

            // Wait for confirm with timeout
            const confirmClicked = await Promise.race([
                confirmPromise,
                new Promise(resolve => setTimeout(() => resolve(false), 2000)) // 2 second timeout
            ]);

            const executionTime = Date.now() - this.startTime;

            // OPTIMIZATION 5: Quick success detection
            let successDetected = false;
            try {
                const successPromises = [
                    this.page.locator('text=Purchased successfully').first().isVisible({ timeout: 100 }),
                    this.page.locator('text=success').first().isVisible({ timeout: 100 }),
                    this.page.locator('.success').first().isVisible({ timeout: 100 })
                ];
                
                const successResults = await Promise.allSettled(successPromises);
                successDetected = successResults.some(r => r.status === 'fulfilled' && r.value);
                
                if (successDetected) {
                    console.log('⚡ Success detected instantly');
                }
            } catch (error) {
                // Non-blocking
            }

            console.log('\n⚡ OPTIMIZED RESULTS:');
            console.log('====================');
            console.log(`⏱️ Execution time: ${executionTime}ms`);
            console.log(`🎯 Under 2 seconds: ${executionTime < 2000 ? '🏆 SUCCESS!' : '❌ NO'}`);
            console.log(`🎯 Under 3 seconds: ${executionTime < 3000 ? '✅ SUCCESS!' : '❌ NO'}`);
            console.log(`📊 Quantity filled: ${quantitySuccess ? '✅ YES' : '❌ NO'}`);
            console.log(`📈 Open Long clicked: ${openLongReady ? '✅ YES' : '❌ NO'}`);
            console.log(`✅ Confirm clicked: ${confirmClicked ? '✅ YES' : '❌ NO'}`);
            console.log(`🎉 Success detected: ${successDetected ? '✅ YES' : '⚠️ CHECKING...'}`);

            const result = {
                success: quantitySuccess && openLongReady && confirmClicked,
                executionTime,
                quantityFilled: quantitySuccess,
                openLongClicked: !!openLongReady,
                confirmClicked,
                successDetected,
                targetAchieved: executionTime < 2000,
                acceptableTime: executionTime < 3000,
                timestamp: new Date().toISOString()
            };

            require('fs').writeFileSync('optimized-mexc-results.json', JSON.stringify(result, null, 2));

            return result;

        } catch (error) {
            const executionTime = Date.now() - this.startTime;
            console.error(`❌ Optimized workflow failed after ${executionTime}ms:`, error.message);
            
            return {
                success: false,
                executionTime,
                error: error.message
            };
        }
    }

    async delayedVerification() {
        console.log('🔍 Quick verification...');
        
        try {
            // Short wait for any delayed confirmations
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const successIndicators = [
                'text=Purchased successfully',
                'text=Success',
                'text=completed',
                '.success',
                '.toast-success'
            ];

            for (const indicator of successIndicators) {
                try {
                    const element = this.page.locator(indicator).first();
                    const isVisible = await element.isVisible({ timeout: 300 });
                    if (isVisible) {
                        const text = await element.textContent();
                        console.log(`✅ Verified: ${text}`);
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }

            return false;
        } catch (error) {
            return false;
        }
    }
}

async function runOptimizedMEXCTrader() {
    const trader = new OptimizedMEXCTrader();
    
    try {
        console.log('⚡ OPTIMIZED MEXC TRADER');
        console.log('========================');
        console.log('🎯 Target: <2 seconds');
        console.log('✅ Acceptable: <3 seconds');
        console.log('🚀 Optimizations:');
        console.log('  - Skip navigation if already on page');
        console.log('  - Parallel element detection');
        console.log('  - Aggressive popup handling');
        console.log('  - Minimal delays');
        console.log('  - Instant success detection');
        console.log('');

        const connected = await trader.connectToRemoteBrowser();
        if (!connected) {
            throw new Error('Could not connect to remote browser');
        }

        console.log('⚡ EXECUTING OPTIMIZED WORKFLOW...');
        
        const result = await trader.optimizedWorkflow();
        
        // Quick verification
        const verified = await trader.delayedVerification();
        
        if (result.success && result.targetAchieved) {
            console.log('\n🏆 TARGET ACHIEVED!');
            console.log('⚡ WORKFLOW COMPLETED UNDER 2 SECONDS!');
        } else if (result.success && result.acceptableTime) {
            console.log('\n✅ ACCEPTABLE PERFORMANCE!');
            console.log('⚡ WORKFLOW COMPLETED UNDER 3 SECONDS!');
        } else if (result.success) {
            console.log('\n✅ Workflow completed successfully!');
            console.log(`⏱️ Time: ${result.executionTime}ms`);
        } else {
            console.log('\n❌ Workflow failed');
        }

        if (verified) {
            console.log('🎉 SUCCESS VERIFIED!');
        }
        
        return result;
        
    } catch (error) {
        console.error('💥 Optimized trader failed:', error.message);
        return { success: false, error: error.message };
    }
}

if (require.main === module) {
    runOptimizedMEXCTrader()
        .then(result => {
            console.log('\n⚡ Optimized workflow completed');
            process.exit(result.success ? 0 : 1);
        })
        .catch(error => {
            console.error('💥 Session crashed:', error);
            process.exit(1);
        });
}

module.exports = OptimizedMEXCTrader;
