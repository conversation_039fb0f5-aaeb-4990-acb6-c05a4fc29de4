#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MEXC Simple Working Click System
Simplified approach focusing only on the click events that work, avoiding complex touch events.

ANALYSIS RESULTS:
✅ <PERSON><PERSON> found: "Open Long" at position (659, 792)
✅ Button enabled: Not disabled
✅ Event handlers: click=True (This is what we'll use!)
❌ TouchEvent constructor failed (Skip complex touch events)

SIMPLE SOLUTION: Use direct click events and native clicks that the button responds to.
"""

import os
import sys
import json
import time
import logging
import argparse
from datetime import datetime
from typing import Dict, Any, Optional
from dataclasses import dataclass
from playwright.sync_api import sync_playwright

@dataclass
class TradeConfig:
    symbol: str = "TRU_USDT"
    side: str = "BUY"  # BUY or SELL
    quantity: float = 10.0
    execute_real_trade: bool = False

class MEXCSimpleWorkingClick:
    """Simple working click using only the methods that work"""
    
    def __init__(self, config: TradeConfig):
        self.config = config
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # Browser components
        self.playwright = None
        self.browser = None
        self.page = None
        
        # Interaction tracking
        self.screenshot_counter = 0
        
        self.logger.info(f"🎯 Simple working click initialized: {config}")
    
    def take_screenshot(self, name: str, description: str = "") -> str:
        """Take a screenshot for verification"""
        self.screenshot_counter += 1
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"simple_{self.screenshot_counter:03d}_{name}_{timestamp}.png"
        
        try:
            self.page.screenshot(path=filename, full_page=True)
            self.logger.info(f"📸 {filename} - {description}")
            return filename
        except Exception as e:
            self.logger.error(f"Screenshot failed: {e}")
            return ""
    
    def connect_to_browser(self) -> bool:
        """Connect to browser"""
        self.logger.info("🔌 Connecting to browser...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                self.logger.error("No browser contexts found")
                return False
            
            context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                self.logger.error("No MEXC page found")
                return False
            
            self.page = mexc_page
            self.logger.info(f"✅ Connected to MEXC page: {self.page.url}")
            
            # Take initial screenshot
            self.take_screenshot("connected", "Connected to MEXC")
            return True
            
        except Exception as e:
            self.logger.error(f"Browser connection failed: {e}")
            return False
    
    def execute_simple_working_click(self) -> bool:
        """Execute simple working click using only proven methods"""
        self.logger.info(f"🎯 Simple working {self.config.side} button click")
        
        # Take before screenshot
        before_screenshot = self.take_screenshot("before_simple_click", f"Before simple {self.config.side} click")
        
        # Determine button class
        if self.config.side == "BUY":
            button_class = "component_longBtn__eazYU"
            expected_text = "Open Long"
        else:
            button_class = "component_shortBtn__x5P3I"
            expected_text = "Open Short"
        
        # Simple working click script
        click_script = f"""
        () => {{
            console.log('🎯 Starting SIMPLE WORKING click...');
            console.log('Using only the methods that work: focus + click');
            
            const button = document.querySelector('button.{button_class}');
            
            if (!button) {{
                return {{ success: false, error: 'Button not found' }};
            }}
            
            console.log(`✅ Found button: "${{button.textContent}}" at (${{Math.round(button.getBoundingClientRect().x)}}, ${{Math.round(button.getBoundingClientRect().y)}})`);
            
            if (!{str(self.config.execute_real_trade).lower()}) {{
                console.log('🟡 SAFETY MODE: Button found and ready for simple click');
                return {{
                    success: true,
                    mode: 'safety',
                    text: button.textContent,
                    position: {{
                        x: Math.round(button.getBoundingClientRect().x),
                        y: Math.round(button.getBoundingClientRect().y)
                    }}
                }};
            }}
            
            console.log('🔴 LIVE MODE: Executing SIMPLE WORKING click...');
            
            let results = [];
            
            try {{
                // Step 1: Focus the button (this worked in previous test)
                console.log('👁️ Focusing button...');
                button.focus();
                results.push({{ step: 'focus', result: 'success' }});
                
                // Step 2: Simple click event (button has click=True)
                console.log('👆 Creating simple click event...');
                
                const clickEvent = new Event('click', {{
                    bubbles: true,
                    cancelable: true
                }});
                
                const dispatched = button.dispatchEvent(clickEvent);
                results.push({{ step: 'click_event', result: dispatched ? 'dispatched' : 'blocked' }});
                
                // Step 3: Native click (most reliable)
                console.log('🖱️ Executing native click...');
                button.click();
                results.push({{ step: 'native_click', result: 'executed' }});
                
                // Step 4: Force click if button seems unresponsive
                console.log('💪 Force click attempt...');
                
                // Remove any potential blocking
                button.style.pointerEvents = 'auto';
                button.disabled = false;
                
                // Try clicking again
                button.click();
                results.push({{ step: 'force_click', result: 'executed' }});
                
                // Step 5: Check for immediate response
                console.log('🔍 Checking for immediate response...');
                
                // Wait a moment for any immediate UI changes
                setTimeout(() => {{
                    const modal = document.querySelector('.ant-modal:not([style*="display: none"])');
                    const notification = document.querySelector('.ant-notification, .ant-message');
                    const loading = document.querySelector('.ant-spin, .loading');
                    
                    if (modal) {{
                        results.push({{ step: 'ui_response', result: 'modal_appeared' }});
                        console.log('✅ Modal appeared - click likely successful');
                    }} else if (notification) {{
                        results.push({{ step: 'ui_response', result: 'notification_appeared' }});
                        console.log('✅ Notification appeared - click likely successful');
                    }} else if (loading) {{
                        results.push({{ step: 'ui_response', result: 'loading_appeared' }});
                        console.log('✅ Loading state - click likely successful');
                    }} else {{
                        results.push({{ step: 'ui_response', result: 'no_immediate_response' }});
                        console.log('ℹ️ No immediate UI response detected');
                    }}
                }}, 500);
                
                console.log('✅ All simple click methods executed');
                
                return {{
                    success: true,
                    mode: 'live',
                    text: button.textContent,
                    results: results,
                    position: {{
                        x: Math.round(button.getBoundingClientRect().x),
                        y: Math.round(button.getBoundingClientRect().y)
                    }}
                }};
                
            }} catch (error) {{
                console.log(`❌ Simple click failed: ${{error.message}}`);
                return {{
                    success: false,
                    error: error.message,
                    results: results
                }};
            }}
        }}
        """
        
        try:
            result = self.page.evaluate(click_script)
            
            # Wait for UI changes
            time.sleep(3)
            
            # Take after screenshot
            after_screenshot = self.take_screenshot("after_simple_click", f"After simple {self.config.side} click")
            
            if result.get('success'):
                mode = result.get('mode')
                text = result.get('text', '')
                position = result.get('position', {})
                
                if mode == 'safety':
                    self.logger.info(f"✅ SIMPLE {self.config.side} button ready!")
                    self.logger.info(f"   Text: '{text}'")
                    self.logger.info(f"   Position: {position}")
                    self.logger.info("🟡 SAFETY MODE: Ready for simple live execution")
                    return True
                else:
                    results = result.get('results', [])
                    
                    self.logger.info(f"🔴 SIMPLE {self.config.side} button click executed!")
                    self.logger.info(f"   Text: '{text}'")
                    self.logger.info(f"   Position: {position}")
                    self.logger.info(f"   Execution steps:")
                    
                    for step_result in results:
                        step = step_result.get('step', 'unknown')
                        result_type = step_result.get('result', 'unknown')
                        self.logger.info(f"     ✅ {step}: {result_type}")
                    
                    # Additional UI check
                    self.check_for_ui_changes()
                    
                    return True
            else:
                error = result.get('error', 'Unknown error')
                results = result.get('results', [])
                self.logger.error(f"❌ Simple button click failed: {error}")
                
                if results:
                    self.logger.info("Partial results:")
                    for step_result in results:
                        step = step_result.get('step', 'unknown')
                        result_type = step_result.get('result', 'unknown')
                        self.logger.info(f"     - {step}: {result_type}")
                
        except Exception as e:
            self.logger.error(f"❌ Simple click script failed: {e}")
        
        self.take_screenshot("simple_click_failed", f"Simple {self.config.side} click failed")
        return False
    
    def check_for_ui_changes(self):
        """Check for UI changes after button click"""
        self.logger.info("🔍 Final UI change check...")
        
        ui_check_script = """
        () => {
            const changes = [];
            
            // Check for any visible modals
            const modals = document.querySelectorAll('.ant-modal, .modal, [role="dialog"]');
            const visibleModals = Array.from(modals).filter(m => {
                const style = window.getComputedStyle(m);
                return style.display !== 'none' && style.visibility !== 'hidden';
            });
            
            if (visibleModals.length > 0) {
                changes.push({
                    type: 'modal',
                    count: visibleModals.length,
                    content: visibleModals.map(m => m.textContent?.substring(0, 100) || '').join('; ')
                });
            }
            
            // Check for notifications
            const notifications = document.querySelectorAll('.ant-notification, .ant-message, .notification');
            if (notifications.length > 0) {
                changes.push({
                    type: 'notification',
                    count: notifications.length,
                    content: Array.from(notifications).map(n => n.textContent?.substring(0, 100) || '').join('; ')
                });
            }
            
            // Check for loading states
            const loading = document.querySelectorAll('.ant-spin, .loading, [class*="loading"]');
            if (loading.length > 0) {
                changes.push({
                    type: 'loading',
                    count: loading.length
                });
            }
            
            return changes;
        }
        """
        
        try:
            ui_changes = self.page.evaluate(ui_check_script)
            
            if ui_changes:
                self.logger.info("📊 FINAL UI CHANGES:")
                for change in ui_changes:
                    change_type = change.get('type', 'unknown')
                    count = change.get('count', 0)
                    content = change.get('content', '')
                    
                    self.logger.info(f"   {change_type.upper()}: {count} elements")
                    if content:
                        self.logger.info(f"   Content: {content[:100]}...")
            else:
                self.logger.info("ℹ️ No final UI changes detected")
                
        except Exception as e:
            self.logger.error(f"Final UI change check failed: {e}")
    
    def execute_simple_test(self) -> Dict[str, Any]:
        """Execute simple working click test"""
        self.logger.info("🎯 Starting simple working click test")
        
        result = {
            "success": False,
            "steps_completed": [],
            "errors": [],
            "total_duration": 0
        }
        
        start_time = time.time()
        
        try:
            # Step 1: Connect to browser
            self.logger.info("📋 Step 1: Browser connection")
            if not self.connect_to_browser():
                result["errors"].append("Browser connection failed")
                return result
            result["steps_completed"].append("browser_connected")
            
            # Step 2: Execute simple working click
            self.logger.info("📋 Step 2: Simple working click")
            if not self.execute_simple_working_click():
                result["errors"].append("Simple working click failed")
                return result
            result["steps_completed"].append("simple_working_click_executed")
            
            # Success!
            result["success"] = True
            self.logger.info("✅ Simple working click test completed!")
            
        except Exception as e:
            self.logger.error(f"Test exception: {e}")
            result["errors"].append(str(e))
        
        finally:
            result["total_duration"] = time.time() - start_time
        
        return result
    
    def cleanup(self):
        """Clean up resources"""
        try:
            if self.playwright:
                self.playwright.stop()
        except Exception as e:
            self.logger.error(f"Cleanup error: {e}")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="MEXC Simple Working Click System")
    
    parser.add_argument("--symbol", default="TRU_USDT", help="Trading symbol")
    parser.add_argument("--side", choices=["BUY", "SELL"], default="BUY", help="Order side")
    parser.add_argument("--quantity", type=float, default=10.0, help="Order quantity")
    parser.add_argument("--execute", action="store_true", help="🔴 EXECUTE REAL BUTTON CLICK")
    parser.add_argument("--confirm", action="store_true", help="Confirm real button execution")
    
    args = parser.parse_args()
    
    if args.execute and not args.confirm:
        print("❌ ERROR: For live button clicks, use both --execute AND --confirm flags")
        return
    
    config = TradeConfig(
        symbol=args.symbol,
        side=args.side,
        quantity=args.quantity,
        execute_real_trade=args.execute
    )
    
    print(f"""
🎯 MEXC Simple Working Click System
===================================

SIMPLE APPROACH:
✅ Focus button (proven to work)
✅ Simple click event (button has click=True)
✅ Native click (most reliable)
✅ Force click (remove any blocking)
❌ Skip complex touch events (caused errors)

Target Configuration:
  Symbol: {config.symbol}
  Side: {config.side} (Button: {"Open Long" if config.side == "BUY" else "Open Short"})
  Quantity: {config.quantity}

Execution Mode: {'🔴 LIVE SIMPLE CLICKS' if args.execute else '🟡 SAFE MODE'}
    """)
    
    if args.execute:
        print("⚠️  WARNING: LIVE SIMPLE BUTTON CLICK MODE")
        print("⚠️  This will use simple, proven click methods")
        print("⚠️  Focus + Click events + Native click")
        
        confirmation = input("\nType 'EXECUTE' to proceed with simple button click: ")
        if confirmation != 'EXECUTE':
            print("❌ Simple button click cancelled")
            return
    
    print("\nStarting simple working click system...")
    
    # Initialize simple click system
    click_system = MEXCSimpleWorkingClick(config)
    
    try:
        result = click_system.execute_simple_test()
        
        print(f"""
📊 Simple Click Results:
=======================
Success: {'✅' if result['success'] else '❌'}
Duration: {result['total_duration']:.2f}s
Steps: {', '.join(result['steps_completed'])}
        """)
        
        if result['errors']:
            print(f"Errors: {', '.join(result['errors'])}")
        
        if result['success']:
            if args.execute:
                print("🎉 SIMPLE BUTTON CLICK EXECUTED!")
                print("🎯 Used proven simple methods")
                print("🔍 Check screenshots for results")
            else:
                print("✅ SIMPLE CLICK SYSTEM READY!")
                print("🎯 Will use focus + click + native click")
        else:
            print("❌ Simple click failed")
    
    except KeyboardInterrupt:
        print("\n👋 Interrupted")
    finally:
        click_system.cleanup()

if __name__ == "__main__":
    main()
