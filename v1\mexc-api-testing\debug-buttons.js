const { chromium } = require('playwright');

async function debugButtons() {
    console.log('🔍 DEBUGGING AVAILABLE BUTTONS');
    console.log('===============================');
    
    const ports = [9222, 9223];
    
    for (const port of ports) {
        console.log(`\n🌐 Checking port ${port}...`);
        
        try {
            const browser = await chromium.connectOverCDP(`http://localhost:${port}`);
            const contexts = browser.contexts();
            
            if (contexts.length > 0) {
                const pages = contexts[0].pages();
                const page = pages.length > 0 ? pages[0] : await contexts[0].newPage();
                
                // Navigate to the page
                const url = page.url();
                if (!url.includes('mexc.com/futures/TRU_USDT')) {
                    console.log('🌐 Navigating to TRU_USDT...');
                    await page.goto('https://www.mexc.com/futures/TRU_USDT');
                    await page.waitForTimeout(3000);
                }
                
                // Check for Open/Close mode buttons
                console.log('\n📋 Mode buttons:');
                const modeButtons = await page.locator('button').all();
                for (const btn of modeButtons) {
                    try {
                        if (await btn.isVisible({ timeout: 100 })) {
                            const text = await btn.textContent();
                            if (text && (text.includes('Open') || text.includes('Close'))) {
                                console.log(`  - "${text}"`);
                            }
                        }
                    } catch (error) {
                        continue;
                    }
                }
                
                // Try setting Close mode
                console.log('\n🔄 Trying to set Close mode...');
                try {
                    const closeBtn = page.locator('button:has-text("Close")').first();
                    if (await closeBtn.isVisible({ timeout: 1000 })) {
                        await closeBtn.click();
                        await page.waitForTimeout(500);
                        console.log('✅ Close mode activated');
                    } else {
                        console.log('⚠️ Close mode button not found');
                    }
                } catch (error) {
                    console.log('❌ Error setting Close mode:', error.message);
                }
                
                // Check for trading buttons
                console.log('\n📊 Trading buttons:');
                const tradingButtons = await page.locator('button').all();
                for (const btn of tradingButtons) {
                    try {
                        if (await btn.isVisible({ timeout: 100 })) {
                            const text = await btn.textContent();
                            if (text && (text.includes('Long') || text.includes('Short') || text.includes('Buy') || text.includes('Sell'))) {
                                console.log(`  - "${text}"`);
                            }
                        }
                    } catch (error) {
                        continue;
                    }
                }
                
                // Check for any buttons with specific colors (green/red)
                console.log('\n🎨 Colored buttons:');
                const coloredButtons = await page.locator('button[style*="background"], button[class*="green"], button[class*="red"], button[class*="buy"], button[class*="sell"]').all();
                for (const btn of coloredButtons) {
                    try {
                        if (await btn.isVisible({ timeout: 100 })) {
                            const text = await btn.textContent();
                            if (text && text.trim()) {
                                console.log(`  - "${text}"`);
                            }
                        }
                    } catch (error) {
                        continue;
                    }
                }
                
            }
        } catch (error) {
            console.error(`❌ Error checking port ${port}:`, error.message);
        }
    }
    
    console.log('\n🏁 Debug completed');
}

if (require.main === module) {
    debugButtons().catch(console.error);
}

module.exports = debugButtons;
