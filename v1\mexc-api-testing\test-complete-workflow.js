const FinalUltimateTrader = require('./final-ultimate-trader.js');

async function testCompleteWorkflow() {
    console.log('🎯 COMPLETE WORKFLOW TEST: OPEN → CLOSE');
    console.log('========================================');
    console.log('This will test the complete trading cycle');
    console.log('');

    // Step 1: Open a position first
    console.log('📈 STEP 1: Opening Long Position');
    console.log('─'.repeat(40));
    
    const openTrader = new FinalUltimateTrader(9222);
    
    try {
        const connected = await openTrader.connectToBrowser();
        if (!connected) {
            throw new Error('Failed to connect to port 9222');
        }

        console.log('🎯 Executing Open Long...');
        const openResult = await openTrader.executeOrder('Open Long');
        
        if (openResult.success) {
            console.log('✅ OPEN LONG SUCCESS!');
            console.log(`⏱️  Time: ${openResult.executionTime}ms`);
            console.log(`🎯 Target achieved: ${openResult.targetAchieved ? 'YES' : 'NO'}`);
            
            // Wait for position to be registered in the system
            console.log('\n⏳ Waiting for position to be registered...');
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // Step 2: Now test close with an actual position
            console.log('\n📉 STEP 2: Closing Long Position');
            console.log('─'.repeat(40));
            
            const closeTrader = new FinalUltimateTrader(9223);
            const closeConnected = await closeTrader.connectToBrowser();
            
            if (!closeConnected) {
                throw new Error('Failed to connect to port 9223');
            }

            // First, let's check if position is visible on close interface
            console.log('🔍 Checking position status on close interface...');
            
            // Navigate to ensure we're on the right page
            const url = closeTrader.page.url();
            if (!url.includes('mexc.com/futures/TRU_USDT')) {
                await closeTrader.page.goto('https://www.mexc.com/futures/TRU_USDT');
                await closeTrader.page.waitForTimeout(2000);
            }
            
            // Set close mode
            try {
                const closeModeBtn = closeTrader.page.locator('button:has-text("Close")').first();
                if (await closeModeBtn.isVisible({ timeout: 1000 })) {
                    await closeModeBtn.click();
                    await closeTrader.page.waitForTimeout(1000);
                    console.log('✅ Close mode activated');
                }
            } catch (error) {
                console.log('⚠️ Close mode activation issue');
            }
            
            // Check for position indicators
            const positionChecks = [
                'text=Open Position',
                'text=Position',
                '.position-row',
                '[class*="position"]'
            ];
            
            let positionFound = false;
            for (const selector of positionChecks) {
                try {
                    const element = closeTrader.page.locator(selector).first();
                    if (await element.isVisible({ timeout: 1000 })) {
                        const text = await element.textContent();
                        console.log(`✅ Position indicator: "${text}"`);
                        if (text && !text.includes('(0)') && !text.includes('0.0000')) {
                            positionFound = true;
                        }
                    }
                } catch (error) {
                    continue;
                }
            }
            
            if (positionFound) {
                console.log('✅ Position detected! Proceeding with close...');
                
                // Now try to close
                console.log('🎯 Executing Close Long...');
                const closeResult = await closeTrader.executeOrder('Close Long');
                
                if (closeResult.success) {
                    console.log('✅ CLOSE LONG SUCCESS!');
                    console.log(`⏱️  Time: ${closeResult.executionTime}ms`);
                    console.log(`🎯 Target achieved: ${closeResult.targetAchieved ? 'YES' : 'NO'}`);
                    
                    console.log('\n🎉 COMPLETE WORKFLOW SUCCESS!');
                    console.log('Both open and close executed successfully!');
                    
                } else {
                    console.log('❌ CLOSE LONG FAILED');
                    console.log(`Error: ${closeResult.error}`);
                    
                    // Debug the close interface when position exists
                    console.log('\n🔍 DEBUGGING CLOSE INTERFACE WITH POSITION:');
                    await debugCloseWithPosition(closeTrader.page);
                }
                
            } else {
                console.log('❌ No position detected on close interface');
                console.log('This might be a timing issue or interface sync problem');
                
                // Try refreshing and checking again
                console.log('🔄 Refreshing and checking again...');
                await closeTrader.page.reload();
                await closeTrader.page.waitForTimeout(3000);
                
                // Check again
                const refreshCheck = closeTrader.page.locator('text=Open Position').first();
                if (await refreshCheck.isVisible({ timeout: 2000 })) {
                    const refreshText = await refreshCheck.textContent();
                    console.log(`After refresh: "${refreshText}"`);
                }
            }
            
        } else {
            console.log('❌ OPEN LONG FAILED');
            console.log(`Error: ${openResult.error}`);
            console.log('Cannot proceed to close step without open position');
        }
        
    } catch (error) {
        console.error('❌ Workflow failed:', error.message);
    }
}

async function debugCloseWithPosition(page) {
    console.log('🔬 Debugging close interface with active position...');
    
    try {
        // Look for all inputs again
        const inputs = await page.locator('input[type="text"], input[type="number"], input:not([type])').all();
        console.log(`Found ${inputs.length} potential quantity inputs`);
        
        for (let i = 0; i < inputs.length; i++) {
            const input = inputs[i];
            if (await input.isVisible({ timeout: 100 })) {
                const placeholder = await input.getAttribute('placeholder');
                const value = await input.inputValue();
                const className = await input.getAttribute('class');
                console.log(`  Input ${i + 1}: placeholder="${placeholder}", value="${value}", class="${className}"`);
                
                // Try to interact with each input
                try {
                    await input.click();
                    await input.fill('0.3600');
                    const newValue = await input.inputValue();
                    if (newValue === '0.3600') {
                        console.log(`  ✅ Input ${i + 1} accepts quantity!`);
                    } else {
                        console.log(`  ❌ Input ${i + 1} rejected quantity`);
                    }
                } catch (error) {
                    console.log(`  ❌ Input ${i + 1} interaction failed`);
                }
            }
        }
        
        // Look for close buttons
        const closeButtons = await page.locator('button:has-text("Close Long"), button:has-text("Close Short"), button:has-text("Close")').all();
        console.log(`Found ${closeButtons.length} close buttons`);
        
        for (let i = 0; i < closeButtons.length; i++) {
            const btn = closeButtons[i];
            if (await btn.isVisible({ timeout: 100 })) {
                const text = await btn.textContent();
                console.log(`  Close button ${i + 1}: "${text}"`);
            }
        }
        
    } catch (error) {
        console.log('❌ Debug failed:', error.message);
    }
}

if (require.main === module) {
    testCompleteWorkflow().catch(console.error);
}

module.exports = testCompleteWorkflow;
