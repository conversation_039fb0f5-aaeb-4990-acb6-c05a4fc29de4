const axios = require('axios');

async function testCloseOrder() {
    console.log('🧪 Testing Close Order (Insufficient Quantity Handling)');
    console.log('========================================================');

    try {
        const signal = {
            symbol: "TRUUSDT",
            trade: "close_long",
            last_price: "0.03295",
            leverage: "2"
        };

        console.log('📡 Sending close long signal...');
        console.log(`Signal: ${JSON.stringify(signal)}`);

        const startTime = Date.now();
        const response = await axios.post('http://localhost:4000/webhook', signal);
        const executionTime = Date.now() - startTime;

        console.log('\n📊 RESULT:');
        console.log(`Success: ${response.data.success}`);
        console.log(`Skipped: ${response.data.skipped || false}`);
        console.log(`Message: ${response.data.message}`);
        console.log(`Execution Time: ${executionTime}ms`);

        if (response.data.skipped) {
            console.log(`Skip Reason: ${response.data.skipReason}`);
            console.log('\n✅ SUCCESS: Trade was correctly skipped!');
            console.log('The system detected insufficient closeable quantity');
            console.log('and skipped the trade instead of getting stuck.');
        } else if (response.data.success) {
            console.log('\n✅ SUCCESS: Trade executed successfully!');
        } else {
            console.log('\n❌ FAILED: Trade failed for other reasons');
            console.log(`Error: ${response.data.message}`);
        }

        console.log('\n🎯 ERROR HANDLING STATUS:');
        if (response.data.skipped && executionTime < 10000) {
            console.log('✅ Fast skip mechanism working (< 10 seconds)');
        } else if (response.data.skipped) {
            console.log('⚠️ Skip mechanism working but slow (> 10 seconds)');
        } else {
            console.log('ℹ️ No skip needed (trade executed or different error)');
        }

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response?.data) {
            console.log('Response data:', JSON.stringify(error.response.data, null, 2));
        }
    }
}

testCloseOrder().catch(console.error);
