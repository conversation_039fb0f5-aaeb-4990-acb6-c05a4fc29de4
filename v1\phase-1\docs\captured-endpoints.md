# Captured Endpoints (from live Edge session)

These were captured while placing and canceling orders on TRU_USDT in the live Edge session.

## Place order
- Method: POST
- URL: https://futures.mexc.com/api/v1/private/order/create?mhash=<hash>
- Headers (summary):
  - authorization: <present>
  - x-mxc-sign: <present>
  - x-mxc-nonce: <millisecond timestamp>
  - mtoken: <present>
  - content-type: application/json
  - x-language, referer, user-agent, others
- Body (example):
```
{
  "symbol": "TRU_USDT",
  "side": 1,
  "openType": 1,
  "type": "2",           // Post‑Only limit (via UI)
  "vol": 1,
  "leverage": 1,
  "marketCeiling": false,
  "price": "0.02",
  "priceProtect": "0",
  "p0": "<opaque>",
  "k0": "<opaque>"
}
```
- Response (example):
```
{"success":true,"code":0,"data":{"orderId":"709824909683574272","ts":1754905846384}}
```

## Cancel order
- Method: POST
- URL: https://futures.mexc.com/api/v1/private/order/cancel
- Headers: authorization, x-mxc-sign, x-mxc-nonce, content-type: application/json, ...
- Body (example):
```
["709824909683574272"]
```
- Response (example):
```
{"success":true,"code":0,"data":[{"orderId":709824909683574272,"errorCode":0,"errorMsg":"success"}]}
```

## Robot / Captcha adjuncts
- Repeated POST to an obfuscated path:
  - https://www.mexc.com/_7NZLg-YAp/quNQoP/a6Kc/GSJ7Q8fVmDrbSbX9//aioOJQUXBw/QhN8/HSZ3NyQB
  - Body: {"sensor_data": "<long string>"}
  - Response: 201 {"success": true}
- Captcha APIs that green‑light the action:
  - GET /ucgateway/captcha_api/captcha/robot/robot.future.openlong.TRU_USDT.1X
  - GET /ucgateway/captcha_api/captcha/robot/robot.future.order.cancel.long.TRU_USDT.1X
  - Response: code=0

## Pre‑submit calculations
- POST /api/v1/private/position/order/calc_liquidate_price and /v2 are called before/around submit
- GET /api/v1/private/account/tiered_fee_rate/v2

## Takeaway
- These artifacts (x-mxc-sign, x-mxc-nonce, mtoken, p0, k0, sensor/captcha) are generated by the front‑end path during real UI interactions. Reproducing them offline is fragile.
- Therefore, the reliable solution is to automate the UI submit/cancel, rather than hand‑crafting XHR.

