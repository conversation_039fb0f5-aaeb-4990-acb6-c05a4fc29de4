{"timestamp": "2025-08-12T11:09:29.499270", "summary": {"total_tests": 5, "successful_tests": 4, "failed_tests": 1, "success_rate": 0.8, "total_duration": 22.005388975143433, "total_screenshots": 20}, "test_results": [{"test_name": "tab_navigation", "success": true, "duration": 1.2182316780090332, "errors": [], "screenshots": ["test_002_before_tab_test_20250812_110907.png", "test_003_after_tab_test_20250812_110907.png"], "details": {"total_tabs": 20, "successful_navigations": 3, "tabs_found": [{"index": 0, "text": "Open Position(0)", "isActive": false, "className": "ant-tabs-tab", "position": {"x": 16, "y": 1013}}, {"index": 1, "text": "Open Order (0)", "isActive": true, "className": "ant-tabs-tab ant-tabs-tab-active", "position": {"x": 135, "y": 1013}}, {"index": 2, "text": "Position History", "isActive": false, "className": "ant-tabs-tab", "position": {"x": 245, "y": 1013}}, {"index": 3, "text": "Order & Trade History", "isActive": false, "className": "ant-tabs-tab", "position": {"x": 357, "y": 1013}}, {"index": 4, "text": "Capital Flow", "isActive": false, "className": "ant-tabs-tab", "position": {"x": 509, "y": 1013}}, {"index": 5, "text": "Wallet", "isActive": false, "className": "ant-tabs-tab", "position": {"x": 601, "y": 1013}}, {"index": 6, "text": "Limit Order(0)", "isActive": true, "className": "ant-tabs-tab ant-tabs-tab-active", "position": {"x": 16, "y": 1105}}, {"index": 7, "text": "TP/SL Order (0)", "isActive": false, "className": "ant-tabs-tab", "position": {"x": 122, "y": 1105}}, {"index": 8, "text": "Trigger Order(0)", "isActive": false, "className": "ant-tabs-tab", "position": {"x": 234, "y": 1105}}, {"index": 9, "text": "Trailing Stop(0)", "isActive": false, "className": "ant-tabs-tab", "position": {"x": 351, "y": 1105}}, {"index": 10, "text": "Chart", "isActive": true, "className": "ant-tabs-tab ant-tabs-tab-active", "position": {"x": 0, "y": 0}}, {"index": 11, "text": "Order Book", "isActive": false, "className": "ant-tabs-tab", "position": {"x": 0, "y": 0}}, {"index": 12, "text": "Market Trades", "isActive": false, "className": "ant-tabs-tab", "position": {"x": 0, "y": 0}}, {"index": 13, "text": "Info", "isActive": false, "className": "ant-tabs-tab", "position": {"x": 0, "y": 0}}, {"index": 14, "text": "Trading Rules", "isActive": false, "className": "ant-tabs-tab", "position": {"x": 0, "y": 0}}, {"index": 15, "text": "Risk Limit", "isActive": false, "className": "ant-tabs-tab", "position": {"x": 0, "y": 0}}, {"index": 16, "text": "Chart", "isActive": true, "className": "ant-tabs-tab ant-tabs-tab-active", "position": {"x": 16, "y": 323}}, {"index": 17, "text": "Info", "isActive": false, "className": "ant-tabs-tab", "position": {"x": 65, "y": 323}}, {"index": 18, "text": "Trading Rules", "isActive": false, "className": "ant-tabs-tab", "position": {"x": 106, "y": 323}}, {"index": 19, "text": "Risk Limit", "isActive": false, "className": "ant-tabs-tab", "position": {"x": 204, "y": 323}}], "current_tab": {"index": 16, "text": "Chart"}, "navigation_tests": [{"tab_index": 0, "tab_text": "Open Position(0)", "click_attempted": true, "became_active": false, "success": true}, {"tab_index": 1, "tab_text": "Open Order (0)", "click_attempted": true, "became_active": true, "success": true}, {"tab_index": 2, "tab_text": "Position History", "click_attempted": true, "became_active": false, "success": true}]}}, {"test_name": "trading_configuration", "success": true, "duration": 0.7153770923614502, "errors": [], "screenshots": ["test_004_before_config_test_20250812_110908.png", "test_005_after_config_test_20250812_110909.png"], "details": {"results": {"trigger_type": {"found": true, "tested": true, "success": true, "error": "Failed to execute 'querySelectorAll' on 'Document': 'button:has-text(\"Post Only\")' is not a valid selector.", "selector": "[class*=\"trigger\"]", "count": 7}, "take_profit": {"found": false, "tested": false, "success": false}, "stop_loss": {"found": false, "tested": false, "success": false}, "leverage": {"found": false, "tested": false, "success": false, "error": "Failed to execute 'querySelectorAll' on 'Document': 'button:has-text(\"10x\")' is not a valid selector."}, "margin_mode": {"found": false, "tested": false, "success": false, "error": "Failed to execute 'querySelectorAll' on 'Document': 'button:has-text(\"Isolated\")' is not a valid selector."}}, "summary": {"total_configs": 5, "found": 1, "tested": 1, "successful": 1}}}, {"test_name": "popup_management", "success": false, "duration": 0.32774829864501953, "errors": ["Page.evaluate: SyntaxError: Failed to execute 'querySelectorAll' on 'Element': '.ant-modal-close, .close, [aria-label=\"close\"], .ant-modal-close-x, button:has-text(\"×\")' is not a valid selector.\n    at eval (eval at evaluate (:291:30), <anonymous>:72:56)\n    at Array.forEach (<anonymous>)\n    at eval (eval at evaluate (:291:30), <anonymous>:54:23)\n    at UtilityScript.evaluate (<anonymous>:298:18)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)"], "screenshots": ["test_006_before_popup_test_20250812_110909.png"], "details": {}}, {"test_name": "comprehensive_fields", "success": true, "duration": 4.890273094177246, "errors": [], "screenshots": ["test_007_before_field_test_20250812_110909.png", "test_008_after_field_test_20250812_110914.png"], "details": {"results": {"quantity_fields": [], "price_fields": [], "leverage_fields": [], "other_fields": [{"index": 0, "type": "text", "name": "", "placeholder": "", "value": "", "className": "ant-input ant-input-sm", "position": {"x": 825, "y": 19, "width": 116, "height": 22}, "parent_text": "CYBER"}, {"index": 3, "type": "text", "name": "", "placeholder": "", "value": "0.03342", "className": "ant-input", "position": {"x": 668, "y": 523, "width": 224, "height": 40}, "parent_text": "Last"}, {"index": 4, "type": "text", "name": "", "placeholder": "", "value": "", "className": "ant-input", "position": {"x": 668, "y": 603, "width": 265, "height": 40}, "parent_text": ""}], "field_tests": [{"category": "other", "field_index": 0, "field_info": {"index": 0, "type": "text", "name": "", "placeholder": "", "value": "", "className": "ant-input ant-input-sm", "position": {"x": 825, "y": 19, "width": 116, "height": 22}, "parent_text": "CYBER"}, "test_value": "1.0", "original_value": "", "fill_attempted": true, "fill_successful": true, "value_persisted": false, "errors": []}, {"category": "other", "field_index": 3, "field_info": {"index": 3, "type": "text", "name": "", "placeholder": "", "value": "0.03342", "className": "ant-input", "position": {"x": 668, "y": 523, "width": 224, "height": 40}, "parent_text": "Last"}, "test_value": "1.0", "original_value": "0.03342", "fill_attempted": true, "fill_successful": true, "value_persisted": false, "errors": []}, {"category": "other", "field_index": 4, "field_info": {"index": 4, "type": "text", "name": "", "placeholder": "", "value": "", "className": "ant-input", "position": {"x": 668, "y": 603, "width": 265, "height": 40}, "parent_text": ""}, "test_value": "1.0", "original_value": "", "fill_attempted": true, "fill_successful": true, "value_persisted": false, "errors": []}]}, "summary": {"total_inputs": 10, "quantity_fields": 0, "price_fields": 0, "leverage_fields": 0, "other_fields": 3, "tests_attempted": 3, "successful_fills": 3, "persistent_values": 0}}}, {"test_name": "integration_workflow", "success": true, "duration": 14.853758811950684, "errors": [], "screenshots": ["test_009_before_integration_test_20250812_110914.png", "test_020_after_integration_test_20250812_110929.png"], "details": {"workflow_steps": [{"step": "tab_navigation", "success": true}, {"step": "popup_management", "success": true}, {"step": "trading_configuration", "success": true}, {"step": "field_filling", "success": true}, {"step": "final_popup_check", "success": true}], "successful_steps": 5, "total_steps": 5, "success_rate": 1.0}}], "recommendations": ["Popup management issues - review popup detection and handling logic", "Consider implementing retry mechanisms for failed interactions", "Add more robust element detection with multiple selector strategies"]}