#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MEXC Targeted Quantity Fix
Specifically target the actual trading form quantity field, not random fields.

ISSUE: Script fills random fields but not the actual quantity field in trading form
SOLUTION: Target specific trading form elements and verify they're the right ones
"""

import os
import sys
import time
import logging
from datetime import datetime
from playwright.sync_api import sync_playwright

class MEXCTargetedQuantityFix:
    """Fix quantity field population by targeting the actual trading form"""
    
    def __init__(self, symbol="TRU_USDT", side="BUY", quantity=2.5):
        self.symbol = symbol
        self.side = side
        self.quantity = quantity
        
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
        self.logger = logging.getLogger(__name__)
        
        self.playwright = None
        self.browser = None
        self.page = None
        
        self.logger.info(f"🎯 TARGETED QUANTITY FIX: {side} {quantity} {symbol}")
    
    def connect(self):
        """Connect to MEXC"""
        self.logger.info("🔌 Connecting...")
        
        self.playwright = sync_playwright().start()
        self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
        
        context = self.browser.contexts[0]
        for page in context.pages:
            if 'mexc.com' in (page.url or '') and 'testnet' not in (page.url or ''):
                self.page = page
                break
        
        if not self.page:
            self.logger.error("❌ MEXC page not found")
            return False
        
        self.logger.info(f"✅ Connected: {self.page.url}")
        return True
    
    def find_and_fill_quantity_field(self):
        """Find the ACTUAL quantity field in the trading form"""
        self.logger.info("🎯 FINDING ACTUAL QUANTITY FIELD...")
        
        quantity_fix_script = f"""
        () => {{
            console.log('🎯 TARGETED SEARCH for actual quantity field...');
            
            const testValue = '{self.quantity}';
            const results = {{
                search_methods: [],
                successful_fill: false,
                filled_field: null
            }};
            
            // METHOD 1: Look for quantity-specific selectors
            console.log('Method 1: Quantity-specific selectors...');
            const quantitySelectors = [
                'input[placeholder*="quantity" i]',
                'input[placeholder*="amount" i]',
                'input[placeholder*="size" i]',
                'input[name*="quantity" i]',
                'input[name*="amount" i]',
                'input[name*="size" i]',
                '.quantity input',
                '.amount input',
                '.size input'
            ];
            
            for (const selector of quantitySelectors) {{
                const inputs = document.querySelectorAll(selector);
                console.log(`Trying selector: ${{selector}} - found ${{inputs.length}} elements`);
                
                for (const input of inputs) {{
                    const rect = input.getBoundingClientRect();
                    if (rect.width > 0 && rect.height > 0 && !input.disabled) {{
                        console.log(`Testing input: placeholder="${{input.placeholder}}", name="${{input.name}}", class="${{input.className}}"`);
                        
                        try {{
                            // Clear and fill
                            input.focus();
                            input.value = '';
                            input.value = testValue;
                            
                            // Trigger events
                            input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                            input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                            input.dispatchEvent(new Event('blur', {{ bubbles: true }}));
                            
                            // Verify
                            if (input.value === testValue) {{
                                results.successful_fill = true;
                                results.filled_field = {{
                                    selector: selector,
                                    placeholder: input.placeholder,
                                    name: input.name,
                                    className: input.className,
                                    value: input.value,
                                    position: {{
                                        x: Math.round(rect.x),
                                        y: Math.round(rect.y)
                                    }}
                                }};
                                
                                console.log(`✅ SUCCESS with selector: ${{selector}}`);
                                console.log(`✅ Field filled: "${{input.value}}" at (${{rect.x}}, ${{rect.y}})`);
                                
                                results.search_methods.push({{
                                    method: 'quantity_selector',
                                    selector: selector,
                                    success: true,
                                    field_info: results.filled_field
                                }});
                                
                                return results; // Exit early on success
                            }}
                        }} catch (error) {{
                            console.log(`❌ Error with selector ${{selector}}: ${{error.message}}`);
                        }}
                    }}
                }}
            }}
            
            // METHOD 2: Look in trading form containers
            console.log('Method 2: Trading form containers...');
            const formSelectors = [
                '.trading-form',
                '.order-form',
                '.trade-form',
                '[class*="trading"]',
                '[class*="order"]',
                '[class*="trade"]'
            ];
            
            for (const formSelector of formSelectors) {{
                const forms = document.querySelectorAll(formSelector);
                console.log(`Checking form selector: ${{formSelector}} - found ${{forms.length}} forms`);
                
                for (const form of forms) {{
                    const inputs = form.querySelectorAll('input[type="text"], input[type="number"], input:not([type])');
                    console.log(`Found ${{inputs.length}} inputs in form`);
                    
                    for (const input of inputs) {{
                        const rect = input.getBoundingClientRect();
                        if (rect.width > 0 && rect.height > 0 && !input.disabled) {{
                            console.log(`Testing form input: placeholder="${{input.placeholder}}", value="${{input.value}}"`);
                            
                            try {{
                                input.focus();
                                input.value = '';
                                input.value = testValue;
                                
                                input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                                input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                input.dispatchEvent(new Event('blur', {{ bubbles: true }}));
                                
                                if (input.value === testValue) {{
                                    results.successful_fill = true;
                                    results.filled_field = {{
                                        selector: formSelector + ' input',
                                        placeholder: input.placeholder,
                                        name: input.name,
                                        className: input.className,
                                        value: input.value,
                                        position: {{
                                            x: Math.round(rect.x),
                                            y: Math.round(rect.y)
                                        }}
                                    }};
                                    
                                    console.log(`✅ SUCCESS in form: ${{formSelector}}`);
                                    
                                    results.search_methods.push({{
                                        method: 'form_container',
                                        selector: formSelector,
                                        success: true,
                                        field_info: results.filled_field
                                    }});
                                    
                                    return results;
                                }}
                            }} catch (error) {{
                                console.log(`❌ Error in form input: ${{error.message}}`);
                            }}
                        }}
                    }}
                }}
            }}
            
            // METHOD 3: Look near BUY/SELL buttons
            console.log('Method 3: Near trading buttons...');
            const buttons = document.querySelectorAll('button');
            for (const button of buttons) {{
                const buttonText = button.textContent?.toLowerCase() || '';
                if (buttonText.includes('long') || buttonText.includes('short') || buttonText.includes('buy') || buttonText.includes('sell')) {{
                    console.log(`Found trading button: "${{button.textContent}}" - looking for nearby inputs`);
                    
                    // Look for inputs in the same parent container
                    let parent = button.parentElement;
                    let depth = 0;
                    while (parent && depth < 5) {{
                        const inputs = parent.querySelectorAll('input[type="text"], input[type="number"], input:not([type])');
                        
                        for (const input of inputs) {{
                            const rect = input.getBoundingClientRect();
                            if (rect.width > 0 && rect.height > 0 && !input.disabled) {{
                                console.log(`Testing input near button: placeholder="${{input.placeholder}}"`);
                                
                                try {{
                                    input.focus();
                                    input.value = '';
                                    input.value = testValue;
                                    
                                    input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                                    input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                    input.dispatchEvent(new Event('blur', {{ bubbles: true }}));
                                    
                                    if (input.value === testValue) {{
                                        results.successful_fill = true;
                                        results.filled_field = {{
                                            selector: 'near_button',
                                            placeholder: input.placeholder,
                                            name: input.name,
                                            className: input.className,
                                            value: input.value,
                                            position: {{
                                                x: Math.round(rect.x),
                                                y: Math.round(rect.y)
                                            }}
                                        }};
                                        
                                        console.log(`✅ SUCCESS near button: "${{button.textContent}}"`);
                                        
                                        results.search_methods.push({{
                                            method: 'near_button',
                                            button_text: button.textContent,
                                            success: true,
                                            field_info: results.filled_field
                                        }});
                                        
                                        return results;
                                    }}
                                }} catch (error) {{
                                    console.log(`❌ Error near button: ${{error.message}}`);
                                }}
                            }}
                        }}
                        
                        parent = parent.parentElement;
                        depth++;
                    }}
                }}
            }}
            
            console.log('❌ No quantity field found with any method');
            return results;
        }}
        """
        
        try:
            result = self.page.evaluate(quantity_fix_script)
            
            if result.get('successful_fill'):
                filled_field = result.get('filled_field', {})
                
                self.logger.info("🎯 QUANTITY FIELD FOUND AND FILLED!")
                self.logger.info(f"   Selector: {filled_field.get('selector', 'unknown')}")
                self.logger.info(f"   Placeholder: '{filled_field.get('placeholder', '')}'")
                self.logger.info(f"   Name: '{filled_field.get('name', '')}'")
                self.logger.info(f"   Class: '{filled_field.get('className', '')}'")
                self.logger.info(f"   Value: '{filled_field.get('value', '')}'")
                self.logger.info(f"   Position: {filled_field.get('position', {})}")
                
                return True
            else:
                self.logger.error("❌ NO QUANTITY FIELD FOUND!")
                self.logger.info("Search methods tried:")
                for method in result.get('search_methods', []):
                    self.logger.info(f"   - {method.get('method', 'unknown')}: {method.get('selector', 'unknown')}")
                
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Quantity field search error: {e}")
            return False
    
    def verify_and_click_button(self):
        """Verify quantity is filled and click button"""
        self.logger.info("🔘 VERIFYING QUANTITY AND CLICKING BUTTON...")
        
        # Determine button class
        if self.side == "BUY":
            button_class = "component_longBtn__eazYU"
        else:
            button_class = "component_shortBtn__x5P3I"
        
        verify_click_script = f"""
        () => {{
            console.log('🔘 Verifying quantity and clicking button...');
            
            const results = {{
                quantity_verified: false,
                button_clicked: false,
                error_message: null,
                dom_changes: 0
            }};
            
            // First, verify quantity is actually in a field
            const allInputs = document.querySelectorAll('input');
            let quantityFound = false;
            
            for (const input of allInputs) {{
                if (input.value === '{self.quantity}') {{
                    const rect = input.getBoundingClientRect();
                    if (rect.width > 0 && rect.height > 0) {{
                        console.log(`✅ Quantity verified in field: placeholder="${{input.placeholder}}", class="${{input.className}}"`);
                        quantityFound = true;
                        break;
                    }}
                }}
            }}
            
            results.quantity_verified = quantityFound;
            
            if (!quantityFound) {{
                console.log('❌ Quantity not found in any visible field');
                results.error_message = 'Quantity not found in any field';
                return results;
            }}
            
            // Find and click button
            const button = document.querySelector('button.{button_class}');
            if (!button) {{
                console.log('❌ Button not found');
                results.error_message = 'Button not found';
                return results;
            }}
            
            console.log(`Found button: "${{button.textContent}}" at (${{button.getBoundingClientRect().x}}, ${{button.getBoundingClientRect().y}})`);
            
            // Record DOM state before click
            const beforeModals = document.querySelectorAll('.ant-modal, .modal, [role="dialog"]').length;
            const beforeNotifications = document.querySelectorAll('.ant-notification, .ant-message').length;
            
            try {{
                button.focus();
                button.click();
                button.dispatchEvent(new Event('click', {{ bubbles: true }}));
                
                results.button_clicked = true;
                console.log('✅ Button clicked');
                
                // Wait and check for changes
                setTimeout(() => {{
                    const afterModals = document.querySelectorAll('.ant-modal, .modal, [role="dialog"]').length;
                    const afterNotifications = document.querySelectorAll('.ant-notification, .ant-message').length;
                    
                    results.dom_changes = (afterModals - beforeModals) + (afterNotifications - beforeNotifications);
                    console.log(`DOM changes: ${{results.dom_changes}} (modals: ${{afterModals - beforeModals}}, notifications: ${{afterNotifications - beforeNotifications}})`);
                    
                    // Check for error messages
                    const errorElements = document.querySelectorAll('.ant-message-error, .error, [class*="error"]');
                    for (const errorEl of errorElements) {{
                        const errorText = errorEl.textContent?.toLowerCase() || '';
                        if (errorText.includes('quantity') || errorText.includes('amount')) {{
                            results.error_message = errorEl.textContent;
                            console.log(`❌ Error message found: ${{results.error_message}}`);
                            break;
                        }}
                    }}
                }}, 2000);
                
            }} catch (error) {{
                console.log(`❌ Button click error: ${{error.message}}`);
                results.error_message = error.message;
            }}
            
            return results;
        }}
        """
        
        try:
            result = self.page.evaluate(verify_click_script)
            
            # Wait for DOM changes
            time.sleep(3)
            
            quantity_verified = result.get('quantity_verified', False)
            button_clicked = result.get('button_clicked', False)
            dom_changes = result.get('dom_changes', 0)
            error_message = result.get('error_message')
            
            self.logger.info(f"🔘 VERIFICATION RESULTS:")
            self.logger.info(f"   Quantity verified: {quantity_verified}")
            self.logger.info(f"   Button clicked: {button_clicked}")
            self.logger.info(f"   DOM changes: {dom_changes}")
            
            if error_message:
                self.logger.error(f"   Error message: {error_message}")
            
            return quantity_verified and button_clicked and (dom_changes > 0 or not error_message)
            
        except Exception as e:
            self.logger.error(f"❌ Verify and click error: {e}")
            return False
    
    def execute_targeted_fix(self):
        """Execute targeted quantity fix"""
        self.logger.info("🎯 EXECUTING TARGETED QUANTITY FIX...")
        
        try:
            # Connect
            if not self.connect():
                return False
            
            # Find and fill actual quantity field
            if not self.find_and_fill_quantity_field():
                return False
            
            # Verify and click button
            if not self.verify_and_click_button():
                return False
            
            self.logger.info("🎉 TARGETED FIX SUCCESSFUL!")
            return True
            
        except Exception as e:
            self.logger.error(f"Targeted fix error: {e}")
            return False
    
    def cleanup(self):
        try:
            if self.playwright:
                self.playwright.stop()
        except:
            pass

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="MEXC Targeted Quantity Fix")
    parser.add_argument("--symbol", default="TRU_USDT", help="Trading symbol")
    parser.add_argument("--side", choices=["BUY", "SELL"], default="BUY", help="Order side")
    parser.add_argument("--quantity", type=float, default=2.5, help="Order quantity")
    
    args = parser.parse_args()
    
    print(f"""
🎯 MEXC TARGETED QUANTITY FIX
=============================
ISSUE: Fields filled but not the actual quantity field
SOLUTION: Target specific trading form elements

SEARCH METHODS:
1. Quantity-specific selectors (placeholder, name, class)
2. Trading form containers
3. Fields near BUY/SELL buttons

TARGET: {args.side} {args.quantity} {args.symbol}
    """)
    
    fix = MEXCTargetedQuantityFix(args.symbol, args.side, args.quantity)
    
    try:
        success = fix.execute_targeted_fix()
        
        if success:
            print("\n🎉 TARGETED FIX SUCCESSFUL!")
            print("Quantity field found and filled correctly.")
        else:
            print("\n❌ TARGETED FIX FAILED!")
            print("Could not find or fill the actual quantity field.")
            
    except KeyboardInterrupt:
        print("\n👋 Fix interrupted")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        fix.cleanup()

if __name__ == "__main__":
    main()
