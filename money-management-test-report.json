{"summary": {"totalTests": 11, "passed": 2, "failed": 9, "successRate": "18%"}, "testEnvironment": {"mockBalance": 2.29, "currentTRUPrice": 0.03225, "testDate": "2025-08-16T08:56:37.219Z"}, "testResults": [{"testName": "Balance Fetching", "success": true, "details": {"balance": {"asset": "USDT", "free": 0, "locked": 0, "total": 0, "updateTime": null, "timestamp": "2025-08-16T08:56:09.037Z"}, "fullResponse": {"success": true, "balance": {"asset": "USDT", "free": 0, "locked": 0, "total": 0, "updateTime": null, "timestamp": "2025-08-16T08:56:09.037Z"}, "source": "api", "timestamp": "2025-08-16T08:56:09.042Z"}}, "timestamp": "2025-08-16T08:56:09.047Z"}, {"testName": "25% Percentage Mode", "success": false, "details": {"expectedQuantity": 0.5725, "calculatedQuantity": 0, "quantityDifference": 0.5725, "quantityMatch": false, "success": false, "message": "Trade execution failed", "skipped": false}, "timestamp": "2025-08-16T08:56:10.377Z"}, {"testName": "50% Percentage Mode", "success": false, "details": {"expectedQuantity": 1.145, "calculatedQuantity": 0, "quantityDifference": 1.145, "quantityMatch": false, "success": false, "message": "Trade execution failed", "skipped": false}, "timestamp": "2025-08-16T08:56:13.616Z"}, {"testName": "75% Percentage Mode", "success": false, "details": {"expectedQuantity": 1.7175, "calculatedQuantity": 0, "quantityDifference": 1.7175, "quantityMatch": false, "success": false, "message": "Trade execution failed", "skipped": false}, "timestamp": "2025-08-16T08:56:16.995Z"}, {"testName": "100% Percentage Mode", "success": false, "details": {"expectedQuantity": 2.29, "calculatedQuantity": 0, "quantityDifference": 2.29, "quantityMatch": false, "success": false, "message": "Trade execution failed", "skipped": false}, "timestamp": "2025-08-16T08:56:20.033Z"}, {"testName": "Fixed Amount 0.5 USDT", "success": false, "details": {"requestedAmount": 0.5, "expectedQuantity": 0.5, "calculatedQuantity": 0, "quantityDifference": 0.5, "quantityMatch": false, "success": false, "message": "Trade execution failed", "skipped": false}, "timestamp": "2025-08-16T08:56:23.412Z"}, {"testName": "Fixed Amount 1 USDT", "success": false, "details": {"requestedAmount": 1, "expectedQuantity": 1, "calculatedQuantity": 0, "quantityDifference": 1, "quantityMatch": false, "success": false, "message": "Trade execution failed", "skipped": false}, "timestamp": "2025-08-16T08:56:26.445Z"}, {"testName": "Fixed Amount 2 USDT", "success": false, "details": {"requestedAmount": 2, "expectedQuantity": 2, "calculatedQuantity": 0, "quantityDifference": 2, "quantityMatch": false, "success": false, "message": "Trade execution failed", "skipped": false}, "timestamp": "2025-08-16T08:56:29.788Z"}, {"testName": "Fixed Amount 5 USDT", "success": false, "details": {"requestedAmount": 5, "expectedQuantity": 2.29, "calculatedQuantity": 0, "quantityDifference": 2.29, "quantityMatch": false, "success": false, "message": "Trade execution failed", "skipped": false}, "timestamp": "2025-08-16T08:56:32.809Z"}, {"testName": "Money Management Disabled", "success": false, "details": {"expectedQuantity": 0.5, "calculatedQuantity": 0, "quantityMatch": false, "success": false, "message": "Trade execution failed", "skipped": false}, "timestamp": "2025-08-16T08:56:36.203Z"}, {"testName": "Trade Limits - Minimum Amount", "success": true, "details": {"requestedAmount": 0.05, "minTradeAmount": 0.1, "calculatedQuantity": 0, "success": false, "message": "Trade execution failed", "skipped": false}, "timestamp": "2025-08-16T08:56:37.217Z"}], "timestamp": "2025-08-16T08:56:37.219Z"}