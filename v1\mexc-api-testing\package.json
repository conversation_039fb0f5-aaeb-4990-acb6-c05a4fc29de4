{"name": "mexc-api-testing", "version": "1.0.0", "description": "Testing various MEXC API packages for futures trading", "main": "index.js", "scripts": {"test": "node test-all-packages.js", "test-ccxt": "node tests/test-ccxt.js", "test-futures-sdk": "node tests/test-mexc-futures-sdk.js", "test-gotham-sdk": "node tests/test-gotham-sdk.js", "test-api-sdk": "node tests/test-mexc-api-sdk.js", "test-custom": "node tests/test-custom-api.js", "fast-bot": "node fast-futures-bot.js", "webhook-server": "node webhook-trading-server.js", "test-dry-run": "node test-bot-dry-run.js", "install-playwright": "npx playwright install chromium", "setup": "bash setup-fast-bot.sh"}, "dependencies": {"@theothergothamdev/mexc-sdk": "^1.3.0", "axios": "^1.7.7", "ccxt": "^4.4.29", "dotenv": "^16.4.5", "express": "^4.21.2", "mexc-api-sdk": "^1.0.3", "mexc-futures-sdk": "^1.5.1", "playwright": "^1.54.2"}, "devDependencies": {"colors": "^1.4.0"}, "keywords": ["mexc", "futures", "trading", "api", "testing"], "author": "", "license": "MIT"}