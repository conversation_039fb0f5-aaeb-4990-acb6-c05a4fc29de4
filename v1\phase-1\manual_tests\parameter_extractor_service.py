#!/usr/bin/env python3
"""
Parameter Extractor Service
Keeps browser open and extracts real-time trading parameters from MEXC frontend
"""

import json
import time
import threading
from typing import Dict, Optional
from playwright.sync_api import sync_playwright
from dotenv import dotenv_values
import http.server
import socketserver
from urllib.parse import urlparse, parse_qs

class MEXCParameterExtractorService:
    """Service that keeps browser open and extracts parameters on demand"""
    
    def __init__(self, port: int = 8888):
        self.port = port
        self.browser = None
        self.page = None
        self.context = None
        self.playwright = None
        self.running = False
        
        # Load session tokens
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        self.uc_token = vals.get('uc_token')
        self.session_uid = vals.get('mexc_session_uid')
        
        print(f"🔧 Parameter Extractor Service initialized on port {port}")
        print(f"🔑 Session tokens: AUTH={'✓' if self.auth else '✗'}, UC_TOKEN={'✓' if self.uc_token else '✗'}")
    
    def start_browser(self) -> bool:
        """Start and setup browser connection"""
        try:
            print("🌐 Connecting to browser...")
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                print("❌ No browser contexts found")
                return False
            
            self.context = self.browser.contexts[0]
            
            # Find or create MEXC page
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if mexc_page:
                self.page = mexc_page
                print(f"✅ Found existing MEXC page: {mexc_page.url}")
            else:
                self.page = self.context.new_page()
                print("🆕 Created new MEXC page")
            
            # Navigate to futures page
            print("🔄 Navigating to MEXC futures...")
            self.page.goto('https://futures.mexc.com/exchange/BTC_USDT', wait_until='domcontentloaded')
            
            # Inject session tokens
            self._inject_session_tokens()
            
            # Inject parameter extraction functions
            self._inject_extraction_functions()
            
            print("✅ Browser setup completed successfully")
            return True
            
        except Exception as e:
            print(f"❌ Browser setup failed: {e}")
            return False
    
    def _inject_session_tokens(self):
        """Inject session tokens into browser"""
        if self.auth:
            print("🔑 Injecting session tokens...")
            self.page.evaluate(f"""
                () => {{
                    localStorage.setItem('authorization', '{self.auth}');
                    localStorage.setItem('u_id', '{self.auth}');
                    {f"localStorage.setItem('uc_token', '{self.uc_token}');" if self.uc_token else ""}
                    {f"localStorage.setItem('mexc_session_uid', '{self.session_uid}');" if self.session_uid else ""}
                }}
            """)
            
            # Reload to apply session
            self.page.reload(wait_until='domcontentloaded')
            time.sleep(3)
            print("✅ Session tokens applied")
    
    def _inject_extraction_functions(self):
        """Inject JavaScript functions for parameter extraction"""
        print("🔧 Injecting parameter extraction functions...")
        
        extraction_script = """
            // Global parameter extraction functions
            window.mexcExtractor = {
                // Extract current market price
                async getMarketPrice(symbol) {
                    try {
                        const response = await fetch(`/api/v1/contract/ticker?symbol=${symbol}`, {
                            credentials: 'include'
                        });
                        const data = await response.json();
                        if (data.code === 0 && data.data) {
                            const ticker = Array.isArray(data.data) ? data.data[0] : data.data;
                            return parseFloat(ticker.lastPrice || 0);
                        }
                        return 0;
                    } catch (error) {
                        console.error('Market price error:', error);
                        return 0;
                    }
                },
                
                // Extract trading parameters
                async extractTradingParams(symbol, side, price, volume) {
                    try {
                        const orderData = {
                            symbol: symbol,
                            side: side,
                            openType: 1,
                            type: '2',
                            vol: volume,
                            leverage: 1,
                            marketCeiling: false,
                            price: price.toString(),
                            priceProtect: '0'
                        };
                        
                        const nonce = Date.now().toString();
                        
                        // Get auth tokens
                        const auth = localStorage.getItem('authorization') || localStorage.getItem('u_id');
                        const mtoken = localStorage.getItem('uc_token') || localStorage.getItem('mtoken');
                        
                        // Try to find real signing functions
                        let signature = null;
                        let p0 = null;
                        let k0 = null;
                        
                        // Look for MEXC signing functions
                        const signingFunctions = [
                            'signRequest', 'createSignature', 'sign', 'generateSign',
                            'signOrderRequest', 'getSignature', 'makeSignature',
                            'signData', 'createSign', 'generateSignature'
                        ];
                        
                        for (const funcName of signingFunctions) {
                            if (typeof window[funcName] === 'function') {
                                try {
                                    signature = window[funcName](orderData, nonce);
                                    if (signature) {
                                        console.log(`✅ Found signing function: ${funcName}`);
                                        break;
                                    }
                                } catch (e) {
                                    console.log(`❌ Failed with ${funcName}:`, e);
                                }
                            }
                        }
                        
                        // Look for parameter generation functions
                        const paramFunctions = ['generateP0', 'getP0', 'createP0'];
                        for (const funcName of paramFunctions) {
                            if (typeof window[funcName] === 'function') {
                                try {
                                    p0 = window[funcName]();
                                    if (p0) break;
                                } catch (e) {}
                            }
                        }
                        
                        const kFunctions = ['generateK0', 'getK0', 'createK0'];
                        for (const funcName of kFunctions) {
                            if (typeof window[funcName] === 'function') {
                                try {
                                    k0 = window[funcName]();
                                    if (k0) break;
                                } catch (e) {}
                            }
                        }
                        
                        // If no real functions found, try to intercept network requests
                        if (!signature) {
                            // Create a mock order request to capture real parameters
                            const mockResult = await this.interceptOrderRequest(orderData);
                            if (mockResult.signature) {
                                signature = mockResult.signature;
                                p0 = mockResult.p0;
                                k0 = mockResult.k0;
                            }
                        }
                        
                        return {
                            success: true,
                            orderData: orderData,
                            nonce: nonce,
                            signature: signature,
                            p0: p0,
                            k0: k0,
                            auth: auth,
                            mtoken: mtoken,
                            userAgent: navigator.userAgent,
                            timestamp: Date.now(),
                            hasRealSignature: signature !== null
                        };
                        
                    } catch (error) {
                        return {
                            success: false,
                            error: error.toString()
                        };
                    }
                },
                
                // Intercept real order request to capture parameters
                async interceptOrderRequest(orderData) {
                    return new Promise((resolve) => {
                        const originalFetch = window.fetch;
                        let captured = false;
                        
                        // Override fetch temporarily
                        window.fetch = function(...args) {
                            const [url, options] = args;
                            
                            if ((url.includes('order/create') || url.includes('order/submit')) && !captured) {
                                captured = true;
                                
                                // Extract headers and body
                                const headers = options.headers || {};
                                let body = null;
                                
                                try {
                                    if (options.body) {
                                        body = JSON.parse(options.body);
                                    }
                                } catch (e) {}
                                
                                // Restore original fetch
                                window.fetch = originalFetch;
                                
                                resolve({
                                    signature: headers['x-mxc-sign'],
                                    p0: body ? body.p0 : null,
                                    k0: body ? body.k0 : null,
                                    nonce: headers['x-mxc-nonce']
                                });
                                
                                // Don't actually send the request
                                return Promise.resolve(new Response('{"success":false,"code":999,"message":"Intercepted"}'));
                            }
                            
                            return originalFetch.apply(this, args);
                        };
                        
                        // Trigger a mock order (this might not work without UI interaction)
                        setTimeout(() => {
                            window.fetch = originalFetch;
                            resolve({ signature: null, p0: null, k0: null });
                        }, 1000);
                    });
                },
                
                // Test authentication
                async testAuth() {
                    try {
                        const response = await fetch('/api/v1/private/order/list/open_orders?page_num=1&page_size=5', {
                            credentials: 'include'
                        });
                        const data = await response.json();
                        return {
                            success: data.code === 0,
                            code: data.code,
                            message: data.message
                        };
                    } catch (error) {
                        return {
                            success: false,
                            error: error.toString()
                        };
                    }
                }
            };
            
            console.log('✅ MEXC Parameter Extractor functions injected');
        """
        
        self.page.evaluate(extraction_script)
        print("✅ Parameter extraction functions injected")
    
    def extract_parameters(self, symbol: str, side: int, price: float, volume: int = 1) -> Optional[Dict]:
        """Extract trading parameters for given order"""
        if not self.page:
            return None
        
        try:
            print(f"🔍 Extracting parameters for {symbol} @ ${price}")
            
            # Test authentication first
            auth_test = self.page.evaluate("() => window.mexcExtractor.testAuth()")
            if not auth_test.get('success'):
                print(f"❌ Authentication failed: {auth_test.get('message', 'Unknown error')}")
                return None
            
            print("✅ Authentication verified")
            
            # Extract parameters
            result = self.page.evaluate(f"""
                () => window.mexcExtractor.extractTradingParams('{symbol}', {side}, {price}, {volume})
            """)
            
            if result and result.get('success'):
                print("✅ Parameters extracted successfully")
                print(f"🔐 Has real signature: {result.get('hasRealSignature', False)}")
                return result
            else:
                print(f"❌ Parameter extraction failed: {result.get('error', 'Unknown error')}")
                return None
                
        except Exception as e:
            print(f"❌ Exception during parameter extraction: {e}")
            return None
    
    def get_market_price(self, symbol: str) -> float:
        """Get current market price"""
        if not self.page:
            return 0.0
        
        try:
            price = self.page.evaluate(f"() => window.mexcExtractor.getMarketPrice('{symbol}')")
            return float(price) if price else 0.0
        except Exception as e:
            print(f"❌ Market price error: {e}")
            return 0.0
    
    def start_http_service(self):
        """Start HTTP service for parameter requests"""
        
        class ParameterHandler(http.server.BaseHTTPRequestHandler):
            def __init__(self, extractor_service, *args, **kwargs):
                self.extractor_service = extractor_service
                super().__init__(*args, **kwargs)
            
            def do_GET(self):
                """Handle GET requests"""
                parsed_url = urlparse(self.path)
                
                if parsed_url.path == '/extract':
                    # Parse query parameters
                    params = parse_qs(parsed_url.query)
                    symbol = params.get('symbol', ['BTC_USDT'])[0]
                    side = int(params.get('side', ['1'])[0])
                    price = float(params.get('price', ['0'])[0])
                    volume = int(params.get('volume', ['1'])[0])
                    
                    # Extract parameters
                    result = self.extractor_service.extract_parameters(symbol, side, price, volume)
                    
                    # Send response
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.end_headers()
                    
                    response = {
                        'success': result is not None,
                        'data': result if result else None,
                        'timestamp': time.time()
                    }
                    
                    self.wfile.write(json.dumps(response, indent=2).encode())
                
                elif parsed_url.path == '/price':
                    # Get market price
                    params = parse_qs(parsed_url.query)
                    symbol = params.get('symbol', ['BTC_USDT'])[0]
                    
                    price = self.extractor_service.get_market_price(symbol)
                    
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.end_headers()
                    
                    response = {
                        'success': price > 0,
                        'symbol': symbol,
                        'price': price,
                        'timestamp': time.time()
                    }
                    
                    self.wfile.write(json.dumps(response, indent=2).encode())
                
                elif parsed_url.path == '/status':
                    # Service status
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.end_headers()
                    
                    response = {
                        'status': 'running',
                        'browser_connected': self.extractor_service.page is not None,
                        'timestamp': time.time()
                    }
                    
                    self.wfile.write(json.dumps(response, indent=2).encode())
                
                else:
                    self.send_response(404)
                    self.end_headers()
            
            def log_message(self, format, *args):
                # Suppress default logging
                pass
        
        # Create handler with extractor service reference
        handler = lambda *args, **kwargs: ParameterHandler(self, *args, **kwargs)
        
        try:
            with socketserver.TCPServer(("", self.port), handler) as httpd:
                print(f"🌐 HTTP service started on port {self.port}")
                print(f"📡 Available endpoints:")
                print(f"   GET /extract?symbol=BTC_USDT&side=1&price=50000&volume=1")
                print(f"   GET /price?symbol=BTC_USDT")
                print(f"   GET /status")
                
                self.running = True
                httpd.serve_forever()
                
        except Exception as e:
            print(f"❌ HTTP service failed: {e}")
    
    def run(self):
        """Run the parameter extractor service"""
        print("🚀 Starting MEXC Parameter Extractor Service...")
        
        # Start browser
        if not self.start_browser():
            return
        
        # Start HTTP service
        try:
            self.start_http_service()
        except KeyboardInterrupt:
            print("\n🛑 Service stopped by user")
        finally:
            self.stop()
    
    def stop(self):
        """Stop the service"""
        print("🛑 Stopping service...")
        self.running = False
        
        if self.browser:
            self.browser.close()
        if self.playwright:
            self.playwright.stop()
        
        print("✅ Service stopped")

def main():
    """Main function"""
    print("="*60)
    print("MEXC PARAMETER EXTRACTOR SERVICE")
    print("="*60)

    service = MEXCParameterExtractorService(port=8888)

    try:
        service.run()
    except KeyboardInterrupt:
        print("\n🛑 Service interrupted")
    finally:
        service.stop()

if __name__ == '__main__':
    main()
