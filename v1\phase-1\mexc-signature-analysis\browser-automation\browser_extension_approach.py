#!/usr/bin/env python3
"""
BROWSER EXTENSION APPROACH
Create a browser extension with deeper access to crack the signature
"""

import json
import time
import os
from playwright.sync_api import sync_playwright
from dotenv import dotenv_values

class BrowserExtensionApproach:
    """Use browser extension capabilities for deeper access"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        
        print("🔌 BROWSER EXTENSION APPROACH")
        print("="*35)
        print("🎯 CREATING DEEP ACCESS EXTENSION")
    
    def create_extension_manifest(self):
        """Create extension manifest"""
        
        manifest = {
            "manifest_version": 3,
            "name": "MEXC Signature Analyzer",
            "version": "1.0",
            "description": "Deep analysis of MEXC signature generation",
            "permissions": [
                "activeTab",
                "scripting",
                "webRequest",
                "webRequestBlocking",
                "storage",
                "debugger"
            ],
            "host_permissions": [
                "https://futures.mexc.com/*",
                "https://www.mexc.com/*"
            ],
            "background": {
                "service_worker": "background.js"
            },
            "content_scripts": [
                {
                    "matches": ["https://futures.mexc.com/*"],
                    "js": ["content.js"],
                    "run_at": "document_start"
                }
            ],
            "action": {
                "default_popup": "popup.html"
            }
        }
        
        # Create extension directory
        os.makedirs('mexc_extension', exist_ok=True)
        
        with open('mexc_extension/manifest.json', 'w') as f:
            json.dump(manifest, f, indent=2)
        
        print("✅ Created extension manifest")
    
    def create_content_script(self):
        """Create content script with deep hooks"""
        
        content_script = """
// MEXC Signature Analyzer Content Script
console.log('🔌 MEXC Signature Analyzer loaded');

// Global data storage
window.mexcAnalyzer = {
    signatures: [],
    entropy: [],
    functions: [],
    memory: [],
    network: []
};

// Hook into ALL possible signature generation points
(function() {
    'use strict';
    
    // 1. Hook ALL object property access
    const originalDefineProperty = Object.defineProperty;
    Object.defineProperty = function(obj, prop, descriptor) {
        // Check if this might be signature-related
        if (typeof prop === 'string' && 
            (prop.includes('sign') || prop.includes('hash') || prop.includes('crypto'))) {
            console.log('🔥 Property defined:', prop, descriptor);
            
            window.mexcAnalyzer.functions.push({
                type: 'property_define',
                property: prop,
                descriptor: descriptor,
                timestamp: Date.now()
            });
        }
        
        return originalDefineProperty.apply(this, arguments);
    };
    
    // 2. Hook function creation and execution
    const originalFunction = window.Function;
    window.Function = function(...args) {
        const func = originalFunction.apply(this, args);
        const funcStr = func.toString();
        
        // Check for crypto-related functions
        if (funcStr.includes('md5') || funcStr.includes('sha') || funcStr.includes('sign') || 
            funcStr.includes('hash') || funcStr.includes('crypto') || funcStr.includes('hmac')) {
            
            console.log('🔥 Crypto function created:', funcStr.substring(0, 200));
            
            window.mexcAnalyzer.functions.push({
                type: 'function_creation',
                source: funcStr,
                timestamp: Date.now()
            });
            
            // Hook the function execution
            const originalFunc = func;
            return function(...args) {
                console.log('🔥 Crypto function called:', args);
                
                const result = originalFunc.apply(this, args);
                
                console.log('🔥 Crypto function result:', result);
                
                window.mexcAnalyzer.functions.push({
                    type: 'function_call',
                    args: args,
                    result: result,
                    timestamp: Date.now()
                });
                
                return result;
            };
        }
        
        return func;
    };
    
    // 3. Hook ALL string operations that might be signatures
    const stringMethods = ['charAt', 'charCodeAt', 'concat', 'indexOf', 'slice', 'substring', 'toLowerCase', 'toUpperCase', 'replace'];
    
    stringMethods.forEach(method => {
        const original = String.prototype[method];
        String.prototype[method] = function(...args) {
            const result = original.apply(this, args);
            
            // Check if this string looks like a signature
            if (typeof this === 'string' && this.length === 32 && /^[a-f0-9]+$/i.test(this)) {
                console.log(`🔥 Signature string operation: ${method}`, this, '=>', result);
                
                window.mexcAnalyzer.signatures.push({
                    type: 'string_operation',
                    method: method,
                    input: this.toString(),
                    args: args,
                    result: result,
                    timestamp: Date.now(),
                    stack: new Error().stack
                });
            }
            
            return result;
        };
    });
    
    // 4. Hook ALL network requests with detailed analysis
    const originalXHROpen = XMLHttpRequest.prototype.open;
    const originalXHRSend = XMLHttpRequest.prototype.send;
    const originalXHRSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;
    
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        this._mexc_method = method;
        this._mexc_url = url;
        this._mexc_headers = {};
        return originalXHROpen.apply(this, arguments);
    };
    
    XMLHttpRequest.prototype.setRequestHeader = function(name, value) {
        this._mexc_headers[name] = value;
        
        if (name.toLowerCase() === 'x-mxc-sign') {
            console.log('🎉🎉🎉 SIGNATURE HEADER INTERCEPTED! 🎉🎉🎉');
            console.log('Signature:', value);
            console.log('URL:', this._mexc_url);
            console.log('All headers:', this._mexc_headers);
            
            // Capture the exact moment and context
            const capture = {
                type: 'signature_header',
                signature: value,
                url: this._mexc_url,
                method: this._mexc_method,
                headers: {...this._mexc_headers},
                timestamp: Date.now(),
                stack: new Error().stack,
                // Capture current page state
                pageUrl: window.location.href,
                userAgent: navigator.userAgent,
                // Try to capture recent function calls
                recentFunctions: window.mexcAnalyzer.functions.slice(-10),
                recentEntropy: window.mexcAnalyzer.entropy.slice(-10)
            };
            
            window.mexcAnalyzer.signatures.push(capture);
            
            // Try to trace the signature generation
            console.log('🔍 Tracing signature generation...');
            
            // Look for the signature value in global scope
            function findSignatureInGlobals(sig) {
                const results = [];
                
                function search(obj, path = '', depth = 0) {
                    if (depth > 3) return;
                    
                    try {
                        for (const key in obj) {
                            try {
                                const val = obj[key];
                                
                                if (typeof val === 'string' && val === sig) {
                                    results.push(`${path}.${key}`);
                                } else if (typeof val === 'object' && val !== null && depth < 2) {
                                    search(val, `${path}.${key}`, depth + 1);
                                }
                            } catch (e) {
                                // Ignore access errors
                            }
                        }
                    } catch (e) {
                        // Ignore access errors
                    }
                }
                
                search(window, 'window');
                return results;
            }
            
            const signatureLocations = findSignatureInGlobals(value);
            if (signatureLocations.length > 0) {
                console.log('🎯 Signature found in globals:', signatureLocations);
                capture.globalLocations = signatureLocations;
            }
        }
        
        return originalXHRSetRequestHeader.apply(this, arguments);
    };
    
    XMLHttpRequest.prototype.send = function(data) {
        if (this._mexc_url && this._mexc_url.includes('order')) {
            console.log('🚀 Order request:', this._mexc_url, data);
            
            window.mexcAnalyzer.network.push({
                type: 'xhr_order',
                url: this._mexc_url,
                method: this._mexc_method,
                headers: this._mexc_headers,
                data: data,
                timestamp: Date.now()
            });
        }
        
        return originalXHRSend.apply(this, arguments);
    };
    
    // 5. Hook crypto operations with extreme detail
    if (window.crypto && window.crypto.getRandomValues) {
        const originalGetRandomValues = window.crypto.getRandomValues;
        window.crypto.getRandomValues = function(array) {
            const result = originalGetRandomValues.apply(this, arguments);
            
            const randomData = Array.from(array);
            const randomHex = randomData.map(b => b.toString(16).padStart(2, '0')).join('');
            
            console.log('🎲 crypto.getRandomValues:', randomHex);
            
            window.mexcAnalyzer.entropy.push({
                type: 'crypto_random',
                data: randomData,
                hex: randomHex,
                length: array.length,
                timestamp: Date.now(),
                stack: new Error().stack
            });
            
            return result;
        };
    }
    
    // 6. Hook memory operations
    const originalArrayBuffer = window.ArrayBuffer;
    window.ArrayBuffer = function(length) {
        const buffer = new originalArrayBuffer(length);
        
        if (length >= 32) {
            console.log('🧠 ArrayBuffer created:', length);
            
            window.mexcAnalyzer.memory.push({
                type: 'array_buffer',
                length: length,
                timestamp: Date.now(),
                stack: new Error().stack
            });
        }
        
        return buffer;
    };
    
    // 7. Hook eval and dynamic code execution
    const originalEval = window.eval;
    window.eval = function(code) {
        if (typeof code === 'string' && 
            (code.includes('sign') || code.includes('hash') || code.includes('crypto'))) {
            
            console.log('🔥 Crypto eval:', code.substring(0, 200));
            
            window.mexcAnalyzer.functions.push({
                type: 'eval_crypto',
                code: code,
                timestamp: Date.now(),
                stack: new Error().stack
            });
        }
        
        return originalEval.apply(this, arguments);
    };
    
    console.log('✅ Deep hooks installed successfully!');
})();

// Export data for analysis
window.getMexcAnalyzerData = function() {
    return window.mexcAnalyzer;
};

// Auto-export data every 10 seconds
setInterval(() => {
    if (window.mexcAnalyzer.signatures.length > 0) {
        console.log('📊 Current analysis data:', window.mexcAnalyzer);
    }
}, 10000);
"""
        
        with open('mexc_extension/content.js', 'w') as f:
            f.write(content_script)
        
        print("✅ Created content script")
    
    def create_background_script(self):
        """Create background service worker"""
        
        background_script = """
// MEXC Signature Analyzer Background Script
console.log('🔌 MEXC Background script loaded');

// Listen for network requests
chrome.webRequest.onBeforeRequest.addListener(
    function(details) {
        if (details.url.includes('mexc.com') && details.url.includes('order')) {
            console.log('🚀 Order request intercepted:', details);
        }
    },
    {urls: ["https://futures.mexc.com/*"]},
    ["requestBody"]
);

// Listen for response headers
chrome.webRequest.onHeadersReceived.addListener(
    function(details) {
        if (details.url.includes('mexc.com') && details.url.includes('order')) {
            console.log('📥 Order response headers:', details.responseHeaders);
        }
    },
    {urls: ["https://futures.mexc.com/*"]},
    ["responseHeaders"]
);

// Store captured data
chrome.storage.local.set({
    mexcAnalysisStarted: Date.now()
});
"""
        
        with open('mexc_extension/background.js', 'w') as f:
            f.write(background_script)
        
        print("✅ Created background script")
    
    def create_popup_html(self):
        """Create popup HTML"""
        
        popup_html = """
<!DOCTYPE html>
<html>
<head>
    <style>
        body { width: 300px; padding: 10px; }
        .status { margin: 10px 0; }
        .data { font-family: monospace; font-size: 12px; }
        button { margin: 5px; padding: 5px 10px; }
    </style>
</head>
<body>
    <h3>🔌 MEXC Signature Analyzer</h3>
    
    <div class="status">
        <strong>Status:</strong> <span id="status">Active</span>
    </div>
    
    <div class="status">
        <strong>Signatures:</strong> <span id="sigCount">0</span>
    </div>
    
    <div class="status">
        <strong>Entropy:</strong> <span id="entropyCount">0</span>
    </div>
    
    <button id="exportData">Export Data</button>
    <button id="clearData">Clear Data</button>
    
    <div id="recentData" class="data"></div>
    
    <script src="popup.js"></script>
</body>
</html>
"""
        
        with open('mexc_extension/popup.html', 'w') as f:
            f.write(popup_html)
        
        print("✅ Created popup HTML")
    
    def create_popup_script(self):
        """Create popup JavaScript"""
        
        popup_script = """
// MEXC Signature Analyzer Popup Script
document.addEventListener('DOMContentLoaded', function() {
    
    // Get current tab and inject data retrieval
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        const tab = tabs[0];
        
        if (tab.url.includes('mexc.com')) {
            // Execute script to get analyzer data
            chrome.scripting.executeScript({
                target: {tabId: tab.id},
                function: getAnalyzerData
            }, function(results) {
                if (results && results[0] && results[0].result) {
                    updatePopup(results[0].result);
                }
            });
        }
    });
    
    // Export data button
    document.getElementById('exportData').addEventListener('click', function() {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            chrome.scripting.executeScript({
                target: {tabId: tabs[0].id},
                function: exportAnalyzerData
            });
        });
    });
    
    // Clear data button
    document.getElementById('clearData').addEventListener('click', function() {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            chrome.scripting.executeScript({
                target: {tabId: tabs[0].id},
                function: clearAnalyzerData
            });
        });
    });
});

function getAnalyzerData() {
    if (window.mexcAnalyzer) {
        return window.mexcAnalyzer;
    }
    return null;
}

function exportAnalyzerData() {
    if (window.mexcAnalyzer) {
        const data = JSON.stringify(window.mexcAnalyzer, null, 2);
        const blob = new Blob([data], {type: 'application/json'});
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `mexc_analysis_${Date.now()}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
    }
}

function clearAnalyzerData() {
    if (window.mexcAnalyzer) {
        window.mexcAnalyzer = {
            signatures: [],
            entropy: [],
            functions: [],
            memory: [],
            network: []
        };
    }
}

function updatePopup(data) {
    if (data) {
        document.getElementById('sigCount').textContent = data.signatures.length;
        document.getElementById('entropyCount').textContent = data.entropy.length;
        
        // Show recent signatures
        if (data.signatures.length > 0) {
            const recent = data.signatures.slice(-3);
            const recentHtml = recent.map(sig => 
                `<div>🔐 ${sig.signature.substring(0, 16)}...</div>`
            ).join('');
            document.getElementById('recentData').innerHTML = recentHtml;
        }
    }
}
"""
        
        with open('mexc_extension/popup.js', 'w') as f:
            f.write(popup_script)
        
        print("✅ Created popup script")
    
    def load_extension_in_browser(self):
        """Load the extension in the browser"""
        
        print(f"\n🔌 LOADING EXTENSION IN BROWSER")
        print("="*40)
        
        try:
            self.playwright = sync_playwright().start()
            
            # Launch browser with extension
            context = self.playwright.chromium.launch_persistent_context(
                user_data_dir="./browser_data",
                headless=False,
                args=[
                    f"--load-extension=./mexc_extension",
                    "--disable-extensions-except=./mexc_extension",
                    "--remote-debugging-port=9222"
                ]
            )
            
            # Navigate to MEXC
            page = context.new_page()
            page.goto('https://futures.mexc.com/exchange/BTC_USDT', wait_until='domcontentloaded')
            
            print("✅ Extension loaded in browser")
            print("🎯 Navigate to MEXC and place orders to capture signatures")
            
            # Wait for user interaction
            input("Press Enter when you've placed orders and want to analyze data...")
            
            # Get captured data
            analyzer_data = page.evaluate("() => window.mexcAnalyzer || {}")
            
            if analyzer_data.get('signatures'):
                print(f"\n🎉 CAPTURED {len(analyzer_data['signatures'])} SIGNATURES!")
                
                # Analyze the captured data
                self.analyze_extension_data(analyzer_data)
                return True
            else:
                print(f"\n❌ No signatures captured")
                return False
            
        except Exception as e:
            print(f"❌ Extension loading failed: {e}")
            return False
        
        finally:
            try:
                if hasattr(self, 'playwright'):
                    self.playwright.stop()
            except:
                pass
    
    def analyze_extension_data(self, data):
        """Analyze data captured by the extension"""
        
        print(f"\n🔍 ANALYZING EXTENSION DATA")
        print("="*35)
        
        signatures = data.get('signatures', [])
        entropy = data.get('entropy', [])
        functions = data.get('functions', [])
        
        print(f"📊 Captured data:")
        print(f"   🔐 Signatures: {len(signatures)}")
        print(f"   🎲 Entropy: {len(entropy)}")
        print(f"   🔧 Functions: {len(functions)}")
        
        # Analyze signatures
        for i, sig_data in enumerate(signatures):
            if sig_data.get('type') == 'signature_header':
                print(f"\n🔐 Signature #{i+1}: {sig_data['signature']}")
                
                # Try to correlate with recent entropy
                sig_time = sig_data['timestamp']
                recent_entropy = [
                    e for e in entropy
                    if abs(e['timestamp'] - sig_time) < 30000
                ]
                
                print(f"   Recent entropy: {len(recent_entropy)} values")
                
                # Try to crack with this data
                if self.crack_with_extension_data(sig_data, recent_entropy):
                    return True
        
        return False
    
    def crack_with_extension_data(self, sig_data, entropy_data):
        """Try to crack signature with extension data"""
        
        signature = sig_data['signature']
        nonce = sig_data['headers'].get('x-mxc-nonce', 0)
        
        print(f"   🧪 Cracking signature: {signature}")
        print(f"   🧪 Nonce: {nonce}")
        
        # Test with entropy data
        for entropy in entropy_data:
            if entropy['type'] == 'crypto_random':
                entropy_hex = entropy['hex']
                
                # Test combinations
                import hashlib
                test_patterns = [
                    entropy_hex + str(nonce),
                    str(nonce) + entropy_hex,
                    self.auth + entropy_hex + str(nonce),
                    entropy_hex + self.auth + str(nonce),
                ]
                
                for pattern in test_patterns:
                    test_sig = hashlib.md5(pattern.encode()).hexdigest()
                    if test_sig == signature:
                        print(f"🎉🎉🎉 EXTENSION SIGNATURE CRACKED! 🎉🎉🎉")
                        print(f"   Algorithm: MD5({pattern})")
                        print(f"   Entropy: {entropy_hex}")
                        return True
        
        return False
    
    def run_extension_approach(self):
        """Run the browser extension approach"""
        
        print("="*60)
        print("🔌 BROWSER EXTENSION APPROACH")
        print("="*60)
        
        # Create extension files
        self.create_extension_manifest()
        self.create_content_script()
        self.create_background_script()
        self.create_popup_html()
        self.create_popup_script()
        
        print(f"\n✅ Extension created in: mexc_extension/")
        print(f"📋 Extension includes:")
        print(f"   - Deep content script hooks")
        print(f"   - Network request interception")
        print(f"   - Memory and crypto monitoring")
        print(f"   - Real-time data export")
        
        # Load extension in browser
        return self.load_extension_in_browser()

def main():
    """Main function"""
    
    approach = BrowserExtensionApproach()
    if approach.run_extension_approach():
        print("\n🎉 SIGNATURE CRACKED WITH EXTENSION!")
    else:
        print("\n🔍 Extension approach completed - check captured data")

if __name__ == '__main__':
    main()
