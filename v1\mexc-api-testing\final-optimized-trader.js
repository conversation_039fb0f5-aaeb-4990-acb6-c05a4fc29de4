const { chromium } = require('playwright');

class FinalOptimizedTrader {
    constructor() {
        this.browser = null;
        this.page = null;
        this.startTime = null;
    }

    async connectToRemoteBrowser() {
        console.log('🔗 Connecting...');
        
        try {
            this.browser = await chromium.connectOverCDP('http://localhost:9222');
            const contexts = this.browser.contexts();
            
            if (contexts.length > 0) {
                const context = contexts[0];
                const pages = context.pages();
                this.page = pages.length > 0 ? pages[0] : await context.newPage();
            } else {
                const context = await this.browser.newContext();
                this.page = await context.newPage();
            }
            
            console.log('✅ Connected');
            return true;
        } catch (error) {
            console.error('❌ Connection failed:', error.message);
            return false;
        }
    }

    async executeOptimizedOrder() {
        this.startTime = Date.now();
        console.log('🚀 EXECUTING OPTIMIZED ORDER...');
        
        try {
            // Ensure correct page
            const url = this.page.url();
            if (!url.includes('mexc.com') || !url.includes('TRU')) {
                await this.page.goto('https://www.mexc.com/futures/TRU_USDT', { 
                    waitUntil: 'domcontentloaded',
                    timeout: 3000 
                });
                await this.page.waitForTimeout(500);
            }

            // Based on previous successful execution, use the exact working selectors
            
            // STEP 1: Click Buy (using the selector that worked before)
            console.log('📈 Clicking Buy...');
            let buySuccess = false;
            
            try {
                // This selector worked in the previous successful run
                await this.page.locator('button:has-text("Buy")').first().click({ timeout: 1000 });
                buySuccess = true;
                console.log('✅ Buy clicked');
            } catch (error) {
                // Try alternative selectors
                const buySelectors = [
                    'button:has-text("Long")',
                    '.buy-btn',
                    '[class*="buy"]:not(input)',
                    'button[class*="buy"]'
                ];
                
                for (const selector of buySelectors) {
                    try {
                        await this.page.locator(selector).first().click({ timeout: 500 });
                        buySuccess = true;
                        console.log(`✅ Buy clicked with: ${selector}`);
                        break;
                    } catch (error) {
                        continue;
                    }
                }
            }

            if (!buySuccess) {
                throw new Error('Could not click Buy button');
            }

            // Small delay for UI update
            await this.page.waitForTimeout(200);

            // STEP 2: Market order (if available)
            console.log('📊 Selecting Market...');
            try {
                await this.page.locator('button:has-text("Market")').first().click({ timeout: 500 });
                console.log('✅ Market selected');
            } catch (error) {
                console.log('⚠️ Market button not found or already selected');
            }

            await this.page.waitForTimeout(100);

            // STEP 3: Enter quantity
            console.log('🔢 Entering quantity...');
            let quantitySuccess = false;
            
            const quantitySelectors = [
                'input[placeholder*="amount"]',
                'input[placeholder*="quantity"]',
                'input[placeholder*="size"]',
                'input[type="number"]'
            ];

            for (const selector of quantitySelectors) {
                try {
                    const input = this.page.locator(selector).first();
                    await input.click({ timeout: 300 });
                    await input.fill('40');
                    quantitySuccess = true;
                    console.log('✅ Quantity entered: 40');
                    break;
                } catch (error) {
                    continue;
                }
            }

            if (!quantitySuccess) {
                console.log('⚠️ Trying percentage buttons...');
                try {
                    await this.page.locator('button:has-text("25%")').first().click({ timeout: 300 });
                    quantitySuccess = true;
                    console.log('✅ Used 25% button');
                } catch (error) {
                    console.log('⚠️ Could not set quantity');
                }
            }

            await this.page.waitForTimeout(100);

            // STEP 4: Submit order
            console.log('🚀 Submitting order...');
            let submitSuccess = false;
            
            const submitSelectors = [
                'button:has-text("Buy")',
                'button:has-text("Long")',
                'button:has-text("Place")',
                'button:has-text("Submit")',
                'button:has-text("Confirm")',
                '.submit-btn',
                '.place-order-btn'
            ];

            for (const selector of submitSelectors) {
                try {
                    await this.page.locator(selector).first().click({ timeout: 500 });
                    submitSuccess = true;
                    console.log(`✅ Order submitted with: ${selector}`);
                    break;
                } catch (error) {
                    continue;
                }
            }

            const executionTime = Date.now() - this.startTime;

            // Quick confirmation check
            let confirmationFound = false;
            try {
                const confirmationSelectors = [
                    'text=successfully',
                    'text=Success',
                    'text=Purchased',
                    'text=placed',
                    '.success',
                    '.toast'
                ];

                for (const selector of confirmationSelectors) {
                    try {
                        const isVisible = await this.page.locator(selector).first().isVisible({ timeout: 500 });
                        if (isVisible) {
                            const text = await this.page.locator(selector).first().textContent();
                            console.log(`🎉 Confirmation: ${text}`);
                            confirmationFound = true;
                            break;
                        }
                    } catch (error) {
                        continue;
                    }
                }
            } catch (error) {
                // Non-blocking
            }

            console.log('\n🏆 FINAL OPTIMIZED RESULTS:');
            console.log('===========================');
            console.log(`⏱️ Execution time: ${executionTime}ms`);
            console.log(`🎯 Under 2 seconds: ${executionTime < 2000 ? '🏆 SUCCESS!' : '❌ NO'}`);
            console.log(`🎯 Under 1.5 seconds: ${executionTime < 1500 ? '🏆 EXCELLENT!' : '⚠️ NO'}`);
            console.log(`🎯 Under 1 second: ${executionTime < 1000 ? '🏆 LIGHTNING!' : '⚠️ NO'}`);
            console.log(`📋 Order submitted: ${submitSuccess ? '✅ YES' : '❌ NO'}`);
            console.log(`🎉 Confirmation: ${confirmationFound ? '✅ YES' : '⚠️ CHECKING...'}`);

            const result = {
                success: submitSuccess,
                executionTime,
                targetAchieved: executionTime < 2000,
                excellentTime: executionTime < 1500,
                lightningTime: executionTime < 1000,
                confirmationDetected: confirmationFound,
                timestamp: new Date().toISOString()
            };

            require('fs').writeFileSync('final-optimized-results.json', JSON.stringify(result, null, 2));

            return result;

        } catch (error) {
            const executionTime = Date.now() - this.startTime;
            console.error(`❌ Optimized order failed after ${executionTime}ms:`, error.message);
            
            return {
                success: false,
                executionTime,
                error: error.message
            };
        }
    }

    async verifyOrderSuccess() {
        console.log('🔍 Final verification...');
        
        try {
            await this.page.waitForTimeout(2000);
            
            const successIndicators = [
                'text=Purchased successfully',
                'text=Order placed successfully',
                'text=Success',
                'text=Filled',
                'text=Executed',
                '.order-success',
                '.success-message',
                '.toast-success'
            ];

            for (const indicator of successIndicators) {
                try {
                    const element = this.page.locator(indicator).first();
                    const isVisible = await element.isVisible({ timeout: 1000 });
                    if (isVisible) {
                        const text = await element.textContent();
                        console.log(`✅ VERIFIED: ${text}`);
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }

            console.log('⚠️ No verification message found');
            return false;
        } catch (error) {
            console.log('❌ Verification error:', error.message);
            return false;
        }
    }
}

async function runFinalOptimizedTrader() {
    const trader = new FinalOptimizedTrader();
    
    try {
        const connected = await trader.connectToRemoteBrowser();
        if (!connected) {
            throw new Error('Could not connect to remote browser');
        }

        console.log('\n🎯 FINAL OPTIMIZED EXECUTION');
        console.log('============================');
        console.log('⚡ Target: Sub-2 second execution');
        console.log('🏆 Stretch: Sub-1.5 second execution');
        console.log('⚡ Lightning: Sub-1 second execution');
        console.log('📊 Symbol: TRUUSDT');
        console.log('📈 Side: Buy/Long');
        console.log('🔢 Quantity: 40');
        console.log('\nExecuting in 1 second...');
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const result = await trader.executeOptimizedOrder();
        
        // Final verification
        const verified = await trader.verifyOrderSuccess();
        
        if (result.success && result.lightningTime) {
            console.log('\n⚡ LIGHTNING SPEED ACHIEVED!');
            console.log('🏆 ORDER PLACED UNDER 1 SECOND!');
        } else if (result.success && result.excellentTime) {
            console.log('\n🏆 EXCELLENT SPEED!');
            console.log('⚡ ORDER PLACED UNDER 1.5 SECONDS!');
        } else if (result.success && result.targetAchieved) {
            console.log('\n🎯 TARGET ACHIEVED!');
            console.log('✅ ORDER PLACED UNDER 2 SECONDS!');
        } else if (result.success) {
            console.log('\n✅ Order placed successfully!');
            console.log(`⏱️ Time: ${result.executionTime}ms`);
        } else {
            console.log('\n❌ Order placement failed');
        }

        if (verified) {
            console.log('🎉 ORDER PLACEMENT VERIFIED!');
        }
        
        return result;
        
    } catch (error) {
        console.error('💥 Final optimized trader failed:', error.message);
        return { success: false, error: error.message };
    }
}

if (require.main === module) {
    console.log('🎯 MEXC FINAL OPTIMIZED TRADER');
    console.log('==============================');
    console.log('🚀 Maximum optimization for speed');
    console.log('⚡ Target: Sub-2 second execution');
    console.log('🏆 Stretch: Sub-1.5 second execution');
    console.log('⚡ Lightning: Sub-1 second execution');
    console.log('📋 Requires: Chrome with --remote-debugging-port=9222');
    console.log('');
    
    runFinalOptimizedTrader()
        .then(result => {
            console.log('\n🏁 Final optimized session completed');
            process.exit(result.success ? 0 : 1);
        })
        .catch(error => {
            console.error('💥 Session crashed:', error);
            process.exit(1);
        });
}

module.exports = FinalOptimizedTrader;
