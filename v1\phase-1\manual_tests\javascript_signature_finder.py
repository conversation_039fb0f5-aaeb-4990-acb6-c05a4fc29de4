#!/usr/bin/env python3
"""
JavaScript Signature Function Finder
Searches for the actual signature generation function in the browser
"""

import json
import time
from playwright.sync_api import sync_playwright
from dotenv import dotenv_values

class JavaScriptSignatureFinder:
    """Find the actual signature generation function in browser JavaScript"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        
        print("🔍 JavaScript Signature Function Finder")
        print("="*45)
        print("🎯 Goal: Find the actual signature generation code")
    
    def setup_browser(self):
        """Setup browser for JavaScript analysis"""
        
        print("\n🌐 Setting up browser for JavaScript analysis...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                print("❌ No browser contexts found")
                return False
            
            self.context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                mexc_page = self.context.new_page()
                mexc_page.goto('https://futures.mexc.com/exchange/BTC_USDT', wait_until='domcontentloaded')
            
            self.page = mexc_page
            
            print("✅ Browser setup completed")
            return True
            
        except Exception as e:
            print(f"❌ Browser setup failed: {e}")
            return False
    
    def search_for_signature_functions(self):
        """Search for signature-related functions in the page"""
        
        print("\n🔍 Searching for signature functions...")
        
        # Inject search script
        search_results = self.page.evaluate("""
            () => {
                const results = {
                    functions: [],
                    variables: [],
                    strings: [],
                    objects: []
                };
                
                // Search window object for signature-related items
                function searchObject(obj, path = 'window', depth = 0) {
                    if (depth > 3) return; // Limit depth to avoid infinite recursion
                    
                    try {
                        for (const key in obj) {
                            if (typeof key === 'string') {
                                const lowerKey = key.toLowerCase();
                                
                                // Look for signature-related names
                                if (lowerKey.includes('sign') || lowerKey.includes('hash') || 
                                    lowerKey.includes('crypto') || lowerKey.includes('auth') ||
                                    lowerKey.includes('token') || lowerKey.includes('nonce')) {
                                    
                                    const value = obj[key];
                                    const fullPath = `${path}.${key}`;
                                    
                                    if (typeof value === 'function') {
                                        results.functions.push({
                                            name: key,
                                            path: fullPath,
                                            source: value.toString().substring(0, 200)
                                        });
                                    } else if (typeof value === 'string' && value.length > 10) {
                                        results.strings.push({
                                            name: key,
                                            path: fullPath,
                                            value: value.substring(0, 100)
                                        });
                                    } else if (typeof value === 'object' && value !== null) {
                                        results.objects.push({
                                            name: key,
                                            path: fullPath,
                                            type: Array.isArray(value) ? 'array' : 'object'
                                        });
                                        
                                        // Recursively search objects
                                        if (depth < 2) {
                                            searchObject(value, fullPath, depth + 1);
                                        }
                                    }
                                }
                            }
                        }
                    } catch (e) {
                        // Ignore errors accessing properties
                    }
                }
                
                // Search window object
                searchObject(window);
                
                // Search for specific patterns in all scripts
                const scripts = Array.from(document.querySelectorAll('script'));
                const scriptContents = [];
                
                scripts.forEach((script, index) => {
                    if (script.src) {
                        scriptContents.push({
                            type: 'external',
                            src: script.src,
                            index: index
                        });
                    } else if (script.textContent) {
                        const content = script.textContent;
                        if (content.includes('sign') || content.includes('hash') || 
                            content.includes('crypto') || content.includes('md5') ||
                            content.includes('sha') || content.includes('hmac')) {
                            scriptContents.push({
                                type: 'inline',
                                content: content.substring(0, 500),
                                index: index,
                                length: content.length
                            });
                        }
                    }
                });
                
                results.scripts = scriptContents;
                
                return results;
            }
        """)
        
        print(f"✅ Search completed!")
        print(f"   Found {len(search_results['functions'])} signature-related functions")
        print(f"   Found {len(search_results['strings'])} signature-related strings")
        print(f"   Found {len(search_results['objects'])} signature-related objects")
        print(f"   Found {len(search_results['scripts'])} relevant scripts")
        
        return search_results
    
    def analyze_search_results(self, results):
        """Analyze the search results for signature clues"""
        
        print(f"\n📊 ANALYZING SEARCH RESULTS")
        print("="*35)
        
        # Analyze functions
        if results['functions']:
            print(f"\n🔧 SIGNATURE-RELATED FUNCTIONS ({len(results['functions'])}):")
            for func in results['functions'][:10]:  # Show first 10
                print(f"   📍 {func['path']}")
                print(f"      Source: {func['source']}...")
        
        # Analyze strings
        if results['strings']:
            print(f"\n📝 SIGNATURE-RELATED STRINGS ({len(results['strings'])}):")
            for string in results['strings'][:10]:  # Show first 10
                print(f"   📍 {string['path']}")
                print(f"      Value: {string['value']}...")
        
        # Analyze objects
        if results['objects']:
            print(f"\n📦 SIGNATURE-RELATED OBJECTS ({len(results['objects'])}):")
            for obj in results['objects'][:10]:  # Show first 10
                print(f"   📍 {obj['path']} ({obj['type']})")
        
        # Analyze scripts
        if results['scripts']:
            print(f"\n📜 RELEVANT SCRIPTS ({len(results['scripts'])}):")
            for script in results['scripts'][:5]:  # Show first 5
                if script['type'] == 'external':
                    print(f"   🌐 External: {script['src']}")
                else:
                    print(f"   📄 Inline script #{script['index']} ({script['length']} chars)")
                    print(f"      Content: {script['content'][:200]}...")
    
    def try_to_call_signature_functions(self):
        """Try to call found signature functions with test data"""
        
        print(f"\n🧪 TESTING SIGNATURE FUNCTIONS")
        print("="*35)
        
        test_results = self.page.evaluate("""
            () => {
                const results = [];
                
                // Test data
                const testAuth = 'WEBd98cd3a6eecf378454e859e5b4b680aeae16c4bd38492179d5a4f4afa81f43b6';
                const testNonce = '1754926000000';
                const testData = {
                    symbol: 'BTC_USDT',
                    side: 1,
                    price: '1000',
                    vol: 1
                };
                
                // Look for common signature function names
                const possibleFunctions = [
                    'sign', 'signature', 'createSignature', 'generateSignature',
                    'hash', 'createHash', 'md5', 'sha256', 'hmac',
                    'encrypt', 'encode', 'auth', 'token'
                ];
                
                // Search in window and common namespaces
                const searchSpaces = [window];
                
                // Add common library namespaces if they exist
                if (window.crypto) searchSpaces.push(window.crypto);
                if (window.CryptoJS) searchSpaces.push(window.CryptoJS);
                if (window.forge) searchSpaces.push(window.forge);
                if (window.sjcl) searchSpaces.push(window.sjcl);
                
                for (const space of searchSpaces) {
                    for (const funcName of possibleFunctions) {
                        try {
                            if (typeof space[funcName] === 'function') {
                                // Try calling with different parameter combinations
                                const testCalls = [
                                    [testAuth, testNonce],
                                    [testNonce, testAuth],
                                    [testAuth, testNonce, JSON.stringify(testData)],
                                    [JSON.stringify(testData), testNonce, testAuth],
                                    [testData, testNonce, testAuth],
                                    [testAuth],
                                    [testNonce],
                                    [JSON.stringify(testData)]
                                ];
                                
                                for (let i = 0; i < testCalls.length; i++) {
                                    try {
                                        const result = space[funcName](...testCalls[i]);
                                        if (result && typeof result === 'string' && result.length === 32) {
                                            results.push({
                                                function: funcName,
                                                namespace: space === window ? 'window' : 'library',
                                                params: testCalls[i].map(p => typeof p === 'string' ? p.substring(0, 20) + '...' : typeof p),
                                                result: result,
                                                paramIndex: i
                                            });
                                        }
                                    } catch (e) {
                                        // Function call failed, continue
                                    }
                                }
                            }
                        } catch (e) {
                            // Property access failed, continue
                        }
                    }
                }
                
                return results;
            }
        """)
        
        if test_results:
            print(f"✅ Found {len(test_results)} potential signature functions!")
            for result in test_results:
                print(f"   🎯 {result['namespace']}.{result['function']}()")
                print(f"      Params: {result['params']}")
                print(f"      Result: {result['result']}")
                print(f"      Param combo: #{result['paramIndex']}")
        else:
            print("❌ No working signature functions found")
        
        return test_results
    
    def run_analysis(self):
        """Run the complete JavaScript analysis"""
        
        print("="*60)
        print("JAVASCRIPT SIGNATURE FUNCTION ANALYSIS")
        print("="*60)
        
        # Setup browser
        if not self.setup_browser():
            return False
        
        try:
            # Search for signature functions
            search_results = self.search_for_signature_functions()
            
            # Analyze results
            self.analyze_search_results(search_results)
            
            # Try to call functions
            function_results = self.try_to_call_signature_functions()
            
            if function_results:
                print("\n🎉 POTENTIAL SIGNATURE FUNCTIONS FOUND!")
                print("These functions might be used for signature generation.")
                return True
            else:
                print("\n🔍 No obvious signature functions found.")
                print("The signature generation might be:")
                print("- Obfuscated or minified")
                print("- In external libraries")
                print("- Generated server-side")
                return False
            
        finally:
            # Cleanup
            if hasattr(self, 'browser'):
                self.browser.close()
            if hasattr(self, 'playwright'):
                self.playwright.stop()

def main():
    """Main function"""
    
    finder = JavaScriptSignatureFinder()
    success = finder.run_analysis()
    
    if success:
        print("\n🚀 JAVASCRIPT ANALYSIS SUCCESSFUL!")
        print("Potential signature functions identified!")
    else:
        print("\n🔧 JavaScript analysis complete")
        print("May need alternative approaches for signature generation")

if __name__ == '__main__':
    main()
