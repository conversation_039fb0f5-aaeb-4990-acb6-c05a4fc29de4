const { chromium } = require('playwright');

class DualBrowserTrader {
    constructor() {
        this.openBrowser = null;
        this.closeBrowser = null;
        this.openPage = null;
        this.closePage = null;
    }

    async connectToBrowsers() {
        console.log('🔗 Connecting to dual browsers...');
        
        try {
            // Connect to Open browser (port 9222)
            this.openBrowser = await chromium.connectOverCDP('http://localhost:9222');
            const openContexts = this.openBrowser.contexts();
            if (openContexts.length > 0) {
                const openPages = openContexts[0].pages();
                this.openPage = openPages.length > 0 ? openPages[0] : await openContexts[0].newPage();
            } else {
                const context = await this.openBrowser.newContext();
                this.openPage = await context.newPage();
            }
            console.log('✅ Connected to OPEN browser (port 9222)');

            // Connect to Close browser (port 9223)
            this.closeBrowser = await chromium.connectOverCDP('http://localhost:9223');
            const closeContexts = this.closeBrowser.contexts();
            if (closeContexts.length > 0) {
                const closePages = closeContexts[0].pages();
                this.closePage = closePages.length > 0 ? closePages[0] : await closeContexts[0].newPage();
            } else {
                const context = await this.closeBrowser.newContext();
                this.closePage = await context.newPage();
            }
            console.log('✅ Connected to CLOSE browser (port 9223)');

            return true;
        } catch (error) {
            console.error('❌ Browser connection failed:', error.message);
            return false;
        }
    }

    async retryFindElement(page, selectors, maxRetries = 5, timeout = 300) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            for (const selector of selectors) {
                try {
                    const element = page.locator(selector).first();
                    const isVisible = await element.isVisible({ timeout });
                    
                    if (isVisible) {
                        console.log(`✅ Found element: ${selector} (attempt ${attempt})`);
                        return element;
                    }
                } catch (error) {
                    continue;
                }
            }
            
            if (attempt < maxRetries) {
                console.log(`⚠️ Retry ${attempt}/${maxRetries} - Element not found, retrying...`);
                await new Promise(resolve => setTimeout(resolve, 200));
            }
        }
        
        throw new Error(`Element not found after ${maxRetries} attempts`);
    }

    async executeOpenTrade() {
        const startTime = Date.now();
        console.log('📈 EXECUTING OPEN TRADE (Browser 9222)...');
        
        try {
            // Ensure we're on the right page
            const url = this.openPage.url();
            if (!url.includes('mexc.com/futures/TRU_USDT')) {
                await this.openPage.goto('https://www.mexc.com/futures/TRU_USDT');
                await this.openPage.waitForTimeout(2000);
            }

            // Step 1: Fill Quantity with retry
            console.log('🔢 Step 1: Filling quantity...');
            const quantitySelectors = [
                'text=Quantity(USDT) >> xpath=following::input[1]',
                'text=Quantity >> xpath=following::input[1]',
                'input[placeholder*="quantity"]',
                'input[placeholder*="amount"]',
                'input[type="number"]'
            ];

            const quantityField = await this.retryFindElement(this.openPage, quantitySelectors);
            await quantityField.click();
            await quantityField.fill('0.3600');
            console.log('✅ Quantity filled: 0.3600');

            // Step 2: Click Open Long with retry
            console.log('📈 Step 2: Clicking Open Long...');
            const openLongSelectors = [
                'button:has-text("Open Long")',
                'text=Open Long',
                '.open-long',
                'button[class*="long"]'
            ];

            const openLongButton = await this.retryFindElement(this.openPage, openLongSelectors);
            await openLongButton.click();
            console.log('✅ Open Long clicked');

            // Step 3: Handle popup with retry
            console.log('✅ Step 3: Handling popup...');
            const confirmClicked = await this.handleConfirmPopup(this.openPage, 'OPEN');

            const executionTime = Date.now() - startTime;

            // Verify success
            const verified = await this.verifyTrade(this.openPage, 'OPEN');

            console.log(`⚡ OPEN trade completed in ${executionTime}ms`);
            console.log(`🎉 OPEN verified: ${verified ? '✅ YES' : '❌ NO'}`);

            return {
                success: verified,
                executionTime,
                verified,
                type: 'OPEN'
            };

        } catch (error) {
            const executionTime = Date.now() - startTime;
            console.error(`❌ OPEN trade failed after ${executionTime}ms:`, error.message);
            return { success: false, executionTime, error: error.message, type: 'OPEN' };
        }
    }

    async executeCloseTrade() {
        const startTime = Date.now();
        console.log('📉 EXECUTING CLOSE TRADE (Browser 9223)...');
        
        try {
            // Ensure we're on the right page
            const url = this.closePage.url();
            if (!url.includes('mexc.com/futures/TRU_USDT')) {
                await this.closePage.goto('https://www.mexc.com/futures/TRU_USDT');
                await this.closePage.waitForTimeout(2000);
            }

            // Step 1: Make sure we're in Close mode
            console.log('🔄 Step 1: Switching to Close mode...');
            const closeModeSelectors = [
                'button:has-text("Close")',
                'text=Close',
                '.close-tab',
                '[data-tab="close"]'
            ];

            try {
                const closeModeButton = await this.retryFindElement(this.closePage, closeModeSelectors, 3);
                await closeModeButton.click();
                console.log('✅ Switched to Close mode');
                await this.closePage.waitForTimeout(500);
            } catch (error) {
                console.log('⚠️ Close mode button not found, assuming already in close mode');
            }

            // Step 2: Fill Quantity with retry
            console.log('🔢 Step 2: Filling quantity...');
            const quantitySelectors = [
                'text=Quantity(USDT) >> xpath=following::input[1]',
                'text=Quantity >> xpath=following::input[1]',
                'input[placeholder*="quantity"]',
                'input[placeholder*="amount"]',
                'input[type="number"]'
            ];

            const quantityField = await this.retryFindElement(this.closePage, quantitySelectors);
            await quantityField.click();
            await quantityField.fill('0.3600');
            console.log('✅ Quantity filled: 0.3600');

            // Step 3: Click Close Long (red button) with retry
            console.log('📉 Step 3: Clicking Close Long...');
            const closeLongSelectors = [
                'button:has-text("Close Long")',
                'text=Close Long',
                '.close-long',
                'button[class*="close"][class*="long"]',
                // Alternative: might be "Sell" in close mode
                'button:has-text("Sell")',
                '.sell-btn'
            ];

            const closeLongButton = await this.retryFindElement(this.closePage, closeLongSelectors);
            await closeLongButton.click();
            console.log('✅ Close Long clicked');

            // Step 4: Handle popup with retry
            console.log('✅ Step 4: Handling popup...');
            const confirmClicked = await this.handleConfirmPopup(this.closePage, 'CLOSE');

            const executionTime = Date.now() - startTime;

            // Verify success
            const verified = await this.verifyTrade(this.closePage, 'CLOSE');

            console.log(`⚡ CLOSE trade completed in ${executionTime}ms`);
            console.log(`🎉 CLOSE verified: ${verified ? '✅ YES' : '❌ NO'}`);

            return {
                success: verified,
                executionTime,
                verified,
                type: 'CLOSE'
            };

        } catch (error) {
            const executionTime = Date.now() - startTime;
            console.error(`❌ CLOSE trade failed after ${executionTime}ms:`, error.message);
            return { success: false, executionTime, error: error.message, type: 'CLOSE' };
        }
    }

    async handleConfirmPopup(page, tradeType) {
        const confirmSelectors = [
            'button:has-text("Confirm")',
            'text=Confirm',
            '.confirm-btn',
            'button[class*="confirm"]'
        ];

        // Aggressive popup handling with retry
        for (let attempt = 1; attempt <= 20; attempt++) {
            for (const selector of confirmSelectors) {
                try {
                    const element = page.locator(selector).first();
                    const isVisible = await element.isVisible({ timeout: 100 });
                    
                    if (isVisible) {
                        await element.click();
                        console.log(`✅ ${tradeType}: Confirm clicked (attempt ${attempt})`);
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }
            await new Promise(resolve => setTimeout(resolve, 50));
        }

        console.log(`⚠️ ${tradeType}: Confirm button not found after 20 attempts`);
        return false;
    }

    async verifyTrade(page, tradeType) {
        try {
            const successSelectors = [
                'text=Purchased successfully',
                'text=Success',
                'text=success',
                'text=completed',
                '.success',
                '.toast-success'
            ];

            for (const selector of successSelectors) {
                try {
                    const element = page.locator(selector).first();
                    const isVisible = await element.isVisible({ timeout: 500 });
                    if (isVisible) {
                        const text = await element.textContent();
                        console.log(`✅ ${tradeType} success: ${text}`);
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }
            return false;
        } catch (error) {
            return false;
        }
    }
}

async function executeCommand(command) {
    const trader = new DualBrowserTrader();
    
    try {
        console.log('🎯 MEXC DUAL BROWSER TRADER');
        console.log('============================');
        console.log('📈 Port 9222: Open trades');
        console.log('📉 Port 9223: Close trades');
        console.log('⚡ Target: <2 seconds per trade');
        console.log('🔄 Retry logic: 5 attempts per element');
        console.log('');

        const connected = await trader.connectToBrowsers();
        if (!connected) {
            throw new Error('Failed to connect to browsers');
        }

        let result;
        
        switch (command) {
            case 'open':
                result = await trader.executeOpenTrade();
                break;
                
            case 'close':
                result = await trader.executeCloseTrade();
                break;
                
            case 'both':
                console.log('🔄 EXECUTING BOTH TRADES...');
                const openResult = await trader.executeOpenTrade();
                await new Promise(resolve => setTimeout(resolve, 1000));
                const closeResult = await trader.executeCloseTrade();
                
                const totalTime = openResult.executionTime + closeResult.executionTime;
                
                console.log('\n🏆 BOTH TRADES SUMMARY:');
                console.log('=======================');
                console.log(`📈 OPEN: ${openResult.success ? '✅' : '❌'} (${openResult.executionTime}ms)`);
                console.log(`📉 CLOSE: ${closeResult.success ? '✅' : '❌'} (${closeResult.executionTime}ms)`);
                console.log(`⏱️ Total: ${totalTime}ms`);
                console.log(`🎯 Both <2s: ${openResult.executionTime < 2000 && closeResult.executionTime < 2000 ? '✅' : '❌'}`);
                
                result = { 
                    success: openResult.success && closeResult.success,
                    openResult, 
                    closeResult, 
                    totalTime 
                };
                break;
                
            default:
                console.log('📋 USAGE:');
                console.log('=========');
                console.log('node dual-browser-trader.js open   - Execute OPEN trade (port 9222)');
                console.log('node dual-browser-trader.js close  - Execute CLOSE trade (port 9223)');
                console.log('node dual-browser-trader.js both   - Execute BOTH trades');
                console.log('');
                console.log('🚀 Prerequisites:');
                console.log('Start browsers with:');
                console.log('chrome.exe --remote-debugging-port=9222 --user-data-dir="./browser_data"');
                console.log('chrome.exe --remote-debugging-port=9223 --user-data-dir="./browser_data2"');
                return;
        }

        // Final results
        if (result.success) {
            if (result.executionTime && result.executionTime < 2000) {
                console.log('\n🏆 TARGET ACHIEVED!');
                console.log(`⚡ Executed in ${result.executionTime}ms (<2 seconds)`);
            } else {
                console.log('\n✅ Trade completed successfully!');
            }
        } else {
            console.log('\n❌ Trade failed');
            if (result.error) console.log(`Error: ${result.error}`);
        }

        // Save results
        require('fs').writeFileSync('dual-browser-results.json', JSON.stringify(result, null, 2));
        
        process.exit(result.success ? 0 : 1);
        
    } catch (error) {
        console.error('💥 Dual browser trader failed:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    const command = process.argv[2];
    executeCommand(command);
}

module.exports = DualBrowserTrader;
