// Simple test to verify webhook integration works
const { spawn } = require('child_process');
const path = require('path');

async function testTradeExecution() {
    console.log('🧪 TESTING TRADE EXECUTION');
    console.log('===========================');
    
    return new Promise((resolve, reject) => {
        const startTime = Date.now();
        
        // Test our working-trader directly
        const traderPath = path.join(__dirname, 'working-trader.js');
        const args = [traderPath, 'Open Long'];
        
        console.log(`🔧 Testing: node ${args.join(' ')}`);
        
        const child = spawn('node', args, {
            cwd: __dirname,
            stdio: ['pipe', 'pipe', 'pipe']
        });
        
        let stdout = '';
        let stderr = '';
        
        child.stdout.on('data', (data) => {
            const output = data.toString();
            stdout += output;
            console.log(output.trim());
        });
        
        child.stderr.on('data', (data) => {
            const output = data.toString();
            stderr += output;
            console.error(output.trim());
        });
        
        child.on('close', (code) => {
            const executionTime = Date.now() - startTime;
            
            console.log('\n📊 TEST RESULTS:');
            console.log('================');
            console.log(`Exit code: ${code}`);
            console.log(`Execution time: ${executionTime}ms`);
            console.log(`Success: ${code === 0 ? '✅ YES' : '❌ NO'}`);
            
            resolve({
                success: code === 0,
                executionTime: executionTime,
                exitCode: code,
                stdout: stdout,
                stderr: stderr
            });
        });
        
        child.on('error', (error) => {
            const executionTime = Date.now() - startTime;
            console.error(`💥 Process error: ${error.message}`);
            
            resolve({
                success: false,
                executionTime: executionTime,
                error: error.message
            });
        });
        
        // Timeout after 15 seconds
        setTimeout(() => {
            if (!child.killed) {
                child.kill();
                const executionTime = Date.now() - startTime;
                
                resolve({
                    success: false,
                    executionTime: executionTime,
                    error: 'Execution timeout (15s)'
                });
            }
        }, 15000);
    });
}

async function simulateWebhookSignal() {
    console.log('\n🎯 SIMULATING WEBHOOK SIGNAL');
    console.log('=============================');
    
    const signal = {
        symbol: 'TRUUSDT',
        trade: 'buy',
        last_price: '0.03295',
        leverage: '2'
    };
    
    console.log('📡 Signal:', JSON.stringify(signal, null, 2));
    
    // Map signal to order type
    let orderType;
    switch (signal.trade) {
        case 'buy':
            orderType = 'Open Long';
            break;
        case 'sell':
            orderType = 'Open Short';
            break;
        case 'close':
            orderType = 'Close Long';
            break;
        default:
            throw new Error(`Unknown trade action: ${signal.trade}`);
    }
    
    console.log(`🎯 Mapped to: ${orderType}`);
    
    return new Promise((resolve, reject) => {
        const startTime = Date.now();
        
        const traderPath = path.join(__dirname, 'working-trader.js');
        const args = [traderPath, orderType];
        
        console.log(`🚀 Executing: node ${args.join(' ')}`);
        
        const child = spawn('node', args, {
            cwd: __dirname,
            stdio: ['pipe', 'pipe', 'pipe']
        });
        
        let stdout = '';
        let stderr = '';
        
        child.stdout.on('data', (data) => {
            const output = data.toString();
            stdout += output;
            console.log(output.trim());
        });
        
        child.stderr.on('data', (data) => {
            const output = data.toString();
            stderr += output;
            console.error(output.trim());
        });
        
        child.on('close', (code) => {
            const executionTime = Date.now() - startTime;
            
            console.log('\n📊 WEBHOOK SIMULATION RESULTS:');
            console.log('===============================');
            console.log(`Signal: ${signal.trade} → ${orderType}`);
            console.log(`Exit code: ${code}`);
            console.log(`Execution time: ${executionTime}ms`);
            console.log(`Success: ${code === 0 ? '✅ YES' : '❌ NO'}`);
            console.log(`Target achieved: ${executionTime < 2000 ? '🏆 YES' : '⚠️ NO'} (<2000ms)`);
            
            resolve({
                success: code === 0,
                executionTime: executionTime,
                signal: signal,
                orderType: orderType,
                targetAchieved: executionTime < 2000
            });
        });
        
        child.on('error', (error) => {
            const executionTime = Date.now() - startTime;
            console.error(`💥 Webhook simulation error: ${error.message}`);
            
            resolve({
                success: false,
                executionTime: executionTime,
                error: error.message,
                signal: signal,
                orderType: orderType
            });
        });
        
        // Timeout after 15 seconds
        setTimeout(() => {
            if (!child.killed) {
                child.kill();
                const executionTime = Date.now() - startTime;
                
                resolve({
                    success: false,
                    executionTime: executionTime,
                    error: 'Execution timeout (15s)',
                    signal: signal,
                    orderType: orderType
                });
            }
        }, 15000);
    });
}

async function main() {
    try {
        console.log('🎯 MEXC WEBHOOK INTEGRATION TEST');
        console.log('=================================');
        console.log('Testing the integration between webhook signals and trade execution');
        console.log('');
        
        // Test 1: Direct trade execution
        const directResult = await testTradeExecution();
        
        // Test 2: Webhook signal simulation
        const webhookResult = await simulateWebhookSignal();
        
        // Summary
        console.log('\n🏆 FINAL SUMMARY:');
        console.log('==================');
        console.log(`Direct execution: ${directResult.success ? '✅ SUCCESS' : '❌ FAILED'} (${directResult.executionTime}ms)`);
        console.log(`Webhook simulation: ${webhookResult.success ? '✅ SUCCESS' : '❌ FAILED'} (${webhookResult.executionTime}ms)`);
        
        if (directResult.success && webhookResult.success) {
            console.log('\n🎉 ALL TESTS PASSED!');
            console.log('✅ Webhook integration is ready for production');
            console.log('⚡ Both executions achieved target performance');
        } else {
            console.log('\n⚠️ Some tests failed');
            if (!directResult.success) console.log(`❌ Direct execution failed: ${directResult.error}`);
            if (!webhookResult.success) console.log(`❌ Webhook simulation failed: ${webhookResult.error}`);
        }
        
        // Save results
        const results = {
            directExecution: directResult,
            webhookSimulation: webhookResult,
            timestamp: new Date().toISOString()
        };
        
        require('fs').writeFileSync('webhook-test-results.json', JSON.stringify(results, null, 2));
        console.log('\n📄 Results saved to: webhook-test-results.json');
        
        process.exit(directResult.success && webhookResult.success ? 0 : 1);
        
    } catch (error) {
        console.error('💥 Test failed:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = { testTradeExecution, simulateWebhookSignal };
