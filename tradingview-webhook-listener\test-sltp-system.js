const axios = require('axios');

async function testSLTPSystem() {
    console.log('🧪 Testing Complete SL/TP System\n');

    try {
        // 1. Enable SL/TP Configuration
        console.log('1️⃣ Configuring SL/TP settings...');
        const slTpConfig = {
            slTpEnabled: true,
            atrLength: 10,
            atrSmoothing: 'RMA',
            slMultiplier: 1.5,
            
            tp1Enabled: true,
            tp1Reward: 2,
            tp1Percent: 50,
            
            tp2Enabled: true,
            tp2Reward: 4,
            tp2Percent: 30,
            
            tp3Enabled: false,
            tp3Reward: 6,
            tp3Percent: 20,
            
            maxExecutionTime: 10000, // 10 seconds
            maxPriceDifference: 2, // 2%
            
            moneyManagementEnabled: false,
            defaultQuantity: '0.0001'
        };

        const configResponse = await axios.post('http://localhost:4000/api/config', slTpConfig);
        console.log('✅ SL/TP Configuration saved:', configResponse.data.success);

        // 2. Test Market Data
        console.log('\n2️⃣ Testing market data...');
        const marketData = await axios.get('http://localhost:4000/api/market-data');
        console.log('✅ Current TRUUSDT Price:', marketData.data.currentPrice.price);
        console.log('✅ ATR Value:', marketData.data.atr);

        // 3. Test Webhook with SL/TP
        console.log('\n3️⃣ Sending test webhook signal...');
        const testSignal = {
            symbol: "TRUUSDT",
            trade: "open",
            last_price: marketData.data.currentPrice.price.toString(),
            leverage: "2"
        };

        const webhookResponse = await axios.post('http://localhost:4000/webhook', testSignal);
        console.log('✅ Webhook Response:', webhookResponse.data.success ? 'SUCCESS' : 'FAILED');
        
        if (webhookResponse.data.success) {
            console.log('   📊 Trade executed successfully');
            console.log('   ⏱️  Execution time:', webhookResponse.data.executionTime, 'ms');
            
            // 4. Check positions
            console.log('\n4️⃣ Checking active positions...');
            const positions = await axios.get('http://localhost:4000/api/positions');
            console.log('✅ Active Positions:', positions.data.statistics.activePositions);
            
            if (positions.data.positions.length > 0) {
                const position = positions.data.positions[0];
                console.log('   📍 Position ID:', position.id);
                console.log('   💰 Entry Price:', position.entryPrice);
                console.log('   🛑 Stop Loss:', position.stopLoss);
                console.log('   🎯 Take Profits:', position.takeProfits.length);
                console.log('   📈 ATR:', position.atr);
                
                position.takeProfits.forEach((tp, index) => {
                    console.log(`   🎯 TP${tp.level}: ${tp.price} (${tp.percent}%)`);
                });
            }
        } else {
            console.log('❌ Trade failed:', webhookResponse.data.message);
            if (webhookResponse.data.skipped) {
                console.log('   📋 Reason:', webhookResponse.data.reason);
                console.log('   📋 Details:', JSON.stringify(webhookResponse.data.details, null, 2));
            }
        }

        // 5. Test Statistics
        console.log('\n5️⃣ Checking system statistics...');
        const status = await axios.get('http://localhost:4000/api/status');
        console.log('✅ Total Signals:', status.data.totalSignalsReceived);
        console.log('✅ Total Trades:', status.data.totalTradesExecuted);
        console.log('✅ Trades Skipped:', status.data.totalTradesSkipped);
        console.log('✅ Success Rate:', status.data.successRate + '%');
        console.log('✅ Active Positions:', status.data.activePositions);

        // 6. Test Price Validation (should skip)
        console.log('\n6️⃣ Testing price validation (should skip)...');
        const invalidPriceSignal = {
            symbol: "TRUUSDT",
            trade: "open",
            last_price: (marketData.data.currentPrice.price * 1.05).toString(), // 5% difference
            leverage: "2"
        };

        const validationTest = await axios.post('http://localhost:4000/webhook', invalidPriceSignal);
        if (validationTest.data.skipped) {
            console.log('✅ Price validation working - trade skipped');
            console.log('   📋 Reason:', validationTest.data.reason);
        } else {
            console.log('❌ Price validation failed - trade should have been skipped');
        }

        console.log('\n🎉 SL/TP System Test Complete!');
        console.log('\n📊 Final Statistics:');
        const finalStatus = await axios.get('http://localhost:4000/api/status');
        console.log('   📈 Signals Received:', finalStatus.data.totalSignalsReceived);
        console.log('   ⚡ Trades Executed:', finalStatus.data.totalTradesExecuted);
        console.log('   ⏭️  Trades Skipped:', finalStatus.data.totalTradesSkipped);
        console.log('   📍 Active Positions:', finalStatus.data.activePositions);

    } catch (error) {
        console.error('❌ Test failed:', error.response?.data || error.message);
    }
}

testSLTPSystem();
