# MEXC Signature Analysis - Git Ignore Configuration

# ============================================================================
# CRITICAL: PRESERVE RESEARCH DATA
# The following files contain our breakthrough research data and MUST be kept
# ============================================================================

# KEEP: Core research data (75 signatures + 57 entropy values)
!data/captured_data.json
!data/api_specifications.json
!data/signature_patterns.json

# KEEP: All analysis scripts and documentation
!signature-analysis/
!browser-automation/
!entropy-analysis/
!api-testing/
!data-capture/
!*.py
!*.md
!requirements.txt

# ============================================================================
# EXCLUDE: Sensitive authentication data
# ============================================================================

# Environment files with real tokens
.env
.env.local
.env.production
.env.development

# Browser session data
browser_data/
temp/
chrome_data/
user_data/

# Authentication tokens and cookies
*token*
*auth*
*session*
*cookie*
mexc_session_*
auth_tokens.json

# ============================================================================
# EXCLUDE: Standard development files
# ============================================================================

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
.npm
.yarn-integrity

# Trading system artifacts
browser_data_open/
browser_data_close/
result-*.json
test-results-*.json
production-test-results.json

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Jupyter Notebook
.ipynb_checkpoints

# PyCharm
.idea/

# ============================================================================
# EXCLUDE: Browser and automation artifacts
# ============================================================================

# Playwright
playwright-report/
test-results/
.playwright/

# Chrome/Browser artifacts
*.crx
*.pem
chrome_debug.log
browser_console.log

# Screenshots and recordings
screenshots/
recordings/
*.png
*.jpg
*.gif
*.mp4
*.webm

# ============================================================================
# EXCLUDE: Analysis output and temporary files
# ============================================================================

# Analysis results (regeneratable)
analysis_results/
output/
results/
temp_analysis/

# Log files
*.log
logs/
mexc_analysis.log
debug.log
error.log

# Temporary files
tmp/
temp/
*.tmp
*.temp
*.bak
*.backup

# CSV and Excel files (unless specifically needed)
*.csv
*.xlsx
*.xls

# ============================================================================
# EXCLUDE: System and OS files
# ============================================================================

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ============================================================================
# EXCLUDE: Network and debugging artifacts
# ============================================================================

# Network captures
*.pcap
*.pcapng
*.har
network_logs/

# Debugging artifacts
core
*.core
*.dmp
crash_dumps/

# ============================================================================
# INCLUDE: Essential project files
# ============================================================================

# Force include critical documentation
!README.md
!TECHNICAL_ANALYSIS.md
!LICENSE
!.gitignore
!requirements.txt
!.env.template

# Force include all Python scripts
!*.py

# Force include all subdirectory README files
!*/README.md

# Force include data directory structure
!data/
!data/README.md

# ============================================================================
# RESEARCH DATA PRESERVATION NOTES
# ============================================================================

# CRITICAL: The following files represent hundreds of hours of research:
# - data/captured_data.json (75 signatures + 57 entropy values)
# - All Python analysis scripts
# - Complete documentation and README files
# - API specifications and pattern analysis
#
# These files enable future researchers to continue from our 95% completion
# point rather than starting from scratch. They must be preserved exactly
# as captured to maintain research integrity and reproducibility.
#
# DO NOT add data/captured_data.json to .gitignore - it contains our
# breakthrough research data that cannot be regenerated.
# ============================================================================
