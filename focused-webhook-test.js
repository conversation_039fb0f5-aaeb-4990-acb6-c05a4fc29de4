const axios = require('axios');
const fs = require('fs');

class FocusedWebhookTester {
    constructor() {
        this.webhookUrl = 'http://localhost:4000/webhook';
        this.configUrl = 'http://localhost:4000/api/config';
        this.statusUrl = 'http://localhost:4000/api/status';
        this.testResults = [];
        this.currentTRUPrice = 0.03225; // Current TRUUSDT price
    }

    log(message, data = null) {
        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] ${message}`);
        if (data) {
            console.log(JSON.stringify(data, null, 2));
        }
    }

    addTestResult(testName, success, details = null) {
        const result = {
            testName,
            success,
            details,
            timestamp: new Date().toISOString()
        };
        this.testResults.push(result);
        
        const status = success ? '✅' : '❌';
        this.log(`${status} ${testName}`, details);
    }

    async getCurrentConfig() {
        try {
            const response = await axios.get(this.configUrl, { timeout: 5000 });
            return response.data;
        } catch (error) {
            this.log('Failed to get current config:', error.message);
            return null;
        }
    }

    async updateConfig(newConfig) {
        try {
            const response = await axios.post(this.configUrl, newConfig, { timeout: 10000 });
            return response.data;
        } catch (error) {
            this.log('Failed to update config:', error.message);
            return null;
        }
    }

    async setupTestingEnvironment() {
        this.log('🔧 Setting up testing environment...');
        
        // Get current configuration
        const currentConfig = await this.getCurrentConfig();
        if (!currentConfig) {
            this.addTestResult('Get Current Config', false, 'Failed to retrieve config');
            return false;
        }

        // Modify configuration for testing
        const testConfig = {
            ...currentConfig,
            // Disable price difference validation for testing
            maxPriceDifference: 100, // Allow 100% price difference
            // Enable money management for testing
            moneyManagementEnabled: true,
            moneyManagementMode: 'percentage',
            positionSizePercentage: 50,
            // Set a default quantity for when balance is 0
            defaultQuantity: '2.29',
            // Enable SL/TP for testing
            slTpEnabled: true,
            tp1Enabled: true,
            tp1Reward: 2,
            tp1Percent: 50,
            tp2Enabled: true,
            tp2Reward: 4,
            tp2Percent: 30
        };

        const updateResult = await this.updateConfig(testConfig);
        if (updateResult) {
            this.addTestResult('Setup Testing Environment', true, 'Configuration updated for testing');
            return true;
        } else {
            this.addTestResult('Setup Testing Environment', false, 'Failed to update configuration');
            return false;
        }
    }

    async testWebhookFormats() {
        this.log('📡 Testing Webhook Format Processing...');

        const testCases = [
            {
                name: 'Buy Signal (TradingView Format)',
                signal: {
                    symbol: "TRUUSDT",
                    trade: "buy",
                    last_price: this.currentTRUPrice.toString(),
                    leverege: "2"
                },
                expectedOrderType: 'Open Long'
            },
            {
                name: 'Sell Signal (TradingView Format)',
                signal: {
                    symbol: "TRUUSDT",
                    trade: "sell",
                    last_price: this.currentTRUPrice.toString(),
                    leverege: "2"
                },
                expectedOrderType: 'Open Short'
            },
            {
                name: 'Close Signal (TradingView Format)',
                signal: {
                    symbol: "TRUUSDT",
                    trade: "close",
                    last_price: this.currentTRUPrice.toString(),
                    leverege: "2"
                },
                expectedOrderType: 'Close Long'
            },
            {
                name: 'Open Long Signal (Direct Format)',
                signal: {
                    symbol: "TRUUSDT",
                    trade: "open",
                    last_price: this.currentTRUPrice.toString(),
                    leverege: "2"
                },
                expectedOrderType: 'Open Long'
            }
        ];

        for (const testCase of testCases) {
            await this.testSingleWebhookFormat(testCase);
            await this.sleep(1000);
        }
    }

    async testSingleWebhookFormat(testCase) {
        try {
            const response = await axios.post(this.webhookUrl, testCase.signal, { 
                timeout: 15000,
                headers: { 'Content-Type': 'application/json' }
            });
            
            const success = response.data.processedSignal?.orderType === testCase.expectedOrderType;
            
            this.addTestResult(testCase.name, success, {
                expectedOrderType: testCase.expectedOrderType,
                actualOrderType: response.data.processedSignal?.orderType,
                success: response.data.success,
                message: response.data.message,
                executionTime: response.data.executionTime,
                skipped: response.data.skipped,
                reason: response.data.reason
            });

        } catch (error) {
            this.addTestResult(testCase.name, false, { 
                error: error.message,
                response: error.response?.data 
            });
        }
    }

    async testMoneyManagementModes() {
        this.log('💰 Testing Money Management Configurations...');

        const configurations = [
            {
                name: 'Percentage Mode - 25%',
                config: {
                    moneyManagementEnabled: true,
                    moneyManagementMode: 'percentage',
                    positionSizePercentage: 25,
                    defaultQuantity: '2.29'
                }
            },
            {
                name: 'Percentage Mode - 50%',
                config: {
                    moneyManagementEnabled: true,
                    moneyManagementMode: 'percentage',
                    positionSizePercentage: 50,
                    defaultQuantity: '2.29'
                }
            },
            {
                name: 'Percentage Mode - 100%',
                config: {
                    moneyManagementEnabled: true,
                    moneyManagementMode: 'percentage',
                    positionSizePercentage: 100,
                    defaultQuantity: '2.29'
                }
            },
            {
                name: 'Fixed Amount Mode - 1.0 USDT',
                config: {
                    moneyManagementEnabled: true,
                    moneyManagementMode: 'fixed',
                    fixedTradeAmount: 1.0,
                    defaultQuantity: '2.29'
                }
            },
            {
                name: 'Money Management Disabled',
                config: {
                    moneyManagementEnabled: false,
                    defaultQuantity: '0.5000'
                }
            }
        ];

        for (const config of configurations) {
            await this.testMoneyManagementConfig(config);
            await this.sleep(2000);
        }
    }

    async testMoneyManagementConfig(configTest) {
        this.log(`💼 Testing: ${configTest.name}`);

        try {
            // Update configuration
            await this.updateConfig(configTest.config);
            await this.sleep(1000); // Wait for config to apply

            // Send test signal
            const testSignal = {
                symbol: "TRUUSDT",
                trade: "buy",
                last_price: this.currentTRUPrice.toString(),
                leverege: "2"
            };

            const response = await axios.post(this.webhookUrl, testSignal, { timeout: 15000 });
            
            this.addTestResult(`${configTest.name} - Processing`, response.data.success !== false, {
                config: configTest.config,
                calculatedQuantity: response.data.processedSignal?.quantity,
                message: response.data.message,
                success: response.data.success,
                skipped: response.data.skipped,
                reason: response.data.reason
            });

        } catch (error) {
            this.addTestResult(`${configTest.name} - Processing`, false, { 
                error: error.message,
                config: configTest.config
            });
        }
    }

    async testSLTPCalculations() {
        this.log('🎯 Testing SL/TP Calculation Logic...');

        const slTpConfigs = [
            {
                name: 'Single TP1 Configuration',
                config: {
                    slTpEnabled: true,
                    tp1Enabled: true,
                    tp1Reward: 2,
                    tp1Percent: 100,
                    tp2Enabled: false,
                    tp3Enabled: false,
                    slMultiplier: 1.5,
                    atrLength: 10
                }
            },
            {
                name: 'Multiple TP Configuration',
                config: {
                    slTpEnabled: true,
                    tp1Enabled: true,
                    tp1Reward: 2,
                    tp1Percent: 50,
                    tp2Enabled: true,
                    tp2Reward: 4,
                    tp2Percent: 30,
                    tp3Enabled: true,
                    tp3Reward: 6,
                    tp3Percent: 20,
                    slMultiplier: 1.5,
                    atrLength: 10
                }
            },
            {
                name: 'SL/TP Disabled',
                config: {
                    slTpEnabled: false
                }
            }
        ];

        for (const config of slTpConfigs) {
            await this.testSLTPConfig(config);
            await this.sleep(2000);
        }
    }

    async testSLTPConfig(configTest) {
        this.log(`🎯 Testing SL/TP: ${configTest.name}`);

        try {
            // Update configuration
            await this.updateConfig(configTest.config);
            await this.sleep(1000);

            // Send test signal
            const testSignal = {
                symbol: "TRUUSDT",
                trade: "buy",
                last_price: this.currentTRUPrice.toString(),
                leverege: "2"
            };

            const response = await axios.post(this.webhookUrl, testSignal, { timeout: 15000 });
            
            this.addTestResult(`${configTest.name} - SL/TP Calculation`, true, {
                config: configTest.config,
                slTpEnabled: configTest.config.slTpEnabled,
                response: {
                    success: response.data.success,
                    message: response.data.message,
                    slTpLevels: response.data.slTpLevels,
                    positionAdded: response.data.positionAdded
                }
            });

        } catch (error) {
            this.addTestResult(`${configTest.name} - SL/TP Calculation`, false, { 
                error: error.message,
                config: configTest.config
            });
        }
    }

    async testExecutionTimes() {
        this.log('⏱️ Testing Execution Performance...');

        const testSignal = {
            symbol: "TRUUSDT",
            trade: "buy",
            last_price: this.currentTRUPrice.toString(),
            leverege: "2"
        };

        const executionTimes = [];

        for (let i = 0; i < 5; i++) {
            const startTime = Date.now();
            
            try {
                const response = await axios.post(this.webhookUrl, testSignal, { timeout: 15000 });
                const totalTime = Date.now() - startTime;
                
                executionTimes.push({
                    attempt: i + 1,
                    totalTime,
                    success: response.data.success !== false,
                    tradeExecutionTime: response.data.executionTime,
                    message: response.data.message
                });

                this.log(`Execution ${i + 1}: ${totalTime}ms total`);
                
            } catch (error) {
                const totalTime = Date.now() - startTime;
                executionTimes.push({
                    attempt: i + 1,
                    totalTime,
                    success: false,
                    error: error.message
                });
            }

            await this.sleep(2000);
        }

        const avgTime = executionTimes.reduce((sum, t) => sum + t.totalTime, 0) / executionTimes.length;
        const successfulExecutions = executionTimes.filter(t => t.success).length;

        this.addTestResult('Execution Performance', successfulExecutions >= 3, {
            averageTime: `${avgTime.toFixed(0)}ms`,
            successfulExecutions: `${successfulExecutions}/5`,
            allExecutions: executionTimes
        });
    }

    async generateReport() {
        this.log('📊 Generating Test Report...');

        const report = {
            summary: {
                totalTests: this.testResults.length,
                passed: this.testResults.filter(r => r.success).length,
                failed: this.testResults.filter(r => !r.success).length,
                successRate: `${Math.round((this.testResults.filter(r => r.success).length / this.testResults.length) * 100)}%`
            },
            testResults: this.testResults,
            timestamp: new Date().toISOString(),
            environment: {
                webhookListenerRunning: true,
                mexcTraderRunning: false,
                currentTRUPrice: this.currentTRUPrice
            }
        };

        fs.writeFileSync('focused-webhook-test-report.json', JSON.stringify(report, null, 2));
        
        this.log('📋 Test Summary:');
        this.log(`Total Tests: ${report.summary.totalTests}`);
        this.log(`Passed: ${report.summary.passed}`);
        this.log(`Failed: ${report.summary.failed}`);
        this.log(`Success Rate: ${report.summary.successRate}`);
        
        return report;
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async runFocusedTest() {
        this.log('🚀 Starting Focused Webhook System Test...');
        
        try {
            const setupSuccess = await this.setupTestingEnvironment();
            if (!setupSuccess) {
                throw new Error('Failed to setup testing environment');
            }

            await this.testWebhookFormats();
            await this.testMoneyManagementModes();
            await this.testSLTPCalculations();
            await this.testExecutionTimes();
            
            const report = await this.generateReport();
            
            this.log('✅ Focused testing completed!');
            this.log('📄 Report saved to: focused-webhook-test-report.json');
            
            return report;
            
        } catch (error) {
            this.log('❌ Testing failed:', error.message);
            throw error;
        }
    }
}

// Run the test
if (require.main === module) {
    const tester = new FocusedWebhookTester();
    tester.runFocusedTest()
        .then(report => {
            console.log('\n🎉 Testing completed successfully!');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n💥 Testing failed:', error.message);
            process.exit(1);
        });
}

module.exports = FocusedWebhookTester;
