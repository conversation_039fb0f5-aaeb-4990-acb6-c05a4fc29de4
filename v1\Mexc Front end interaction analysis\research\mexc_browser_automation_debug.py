#!/usr/bin/env python3
"""
MEXC Browser Automation Debug Script
Comprehensive browser automation for MEXC trading with detailed debugging and issue documentation.

This script provides:
1. Browser integration with existing session management
2. Authentication workflow detection and handling
3. Trading execution with comprehensive logging
4. Visual debugging with screenshots
5. Network request/response monitoring
6. Issue documentation and edge case identification

Configuration:
- Local server port: 8080 (configurable)
- Debug port: 9222 (matches your existing setup)
- Trade parameters: Configurable via command line or script variables
"""

import os
import sys
import json
import time
import logging
import argparse
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from playwright.sync_api import sync_playwright, <PERSON>, Browser, BrowserContext
import http.server
import socketserver
import threading
from urllib.parse import urlparse, parse_qs

# Configuration
@dataclass
class TradeConfig:
    symbol: str = "TRU_USDT"
    side: str = "BUY"  # BUY or SELL
    order_type: str = "MARKET"  # MARKET or LIMIT
    quantity: float = 10.0  # Amount in USDT
    price: Optional[float] = None  # For limit orders
    leverage: int = 1
    reduce_only: bool = False

@dataclass
class ServerConfig:
    port: int = 8080
    debug_port: int = 9222
    log_level: str = "DEBUG"
    screenshot_dir: str = "./debug_screenshots"
    log_dir: str = "./debug_logs"

class DebugLogger:
    """Enhanced logging with file and console output"""
    
    def __init__(self, config: ServerConfig):
        self.config = config
        self.setup_logging()
        self.issues = []
        self.timing_data = {}
        
    def setup_logging(self):
        """Setup comprehensive logging"""
        os.makedirs(self.config.log_dir, exist_ok=True)
        os.makedirs(self.config.screenshot_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = f"{self.config.log_dir}/mexc_debug_{timestamp}.log"
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, self.config.log_level),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"🚀 MEXC Browser Automation Debug Session Started")
        self.logger.info(f"📁 Log file: {log_file}")
        self.logger.info(f"📸 Screenshots: {self.config.screenshot_dir}")
    
    def log_issue(self, category: str, description: str, severity: str = "INFO", screenshot_path: str = None):
        """Log an issue with categorization"""
        issue = {
            "timestamp": datetime.now().isoformat(),
            "category": category,
            "description": description,
            "severity": severity,
            "screenshot": screenshot_path
        }
        self.issues.append(issue)
        self.logger.log(getattr(logging, severity), f"[{category}] {description}")
    
    def start_timing(self, operation: str):
        """Start timing an operation"""
        self.timing_data[operation] = time.time()
    
    def end_timing(self, operation: str):
        """End timing and log duration"""
        if operation in self.timing_data:
            duration = time.time() - self.timing_data[operation]
            self.logger.info(f"⏱️ {operation}: {duration:.2f}s")
            return duration
        return 0
    
    def save_debug_report(self):
        """Save comprehensive debug report"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"{self.config.log_dir}/debug_report_{timestamp}.json"
        
        report = {
            "session_info": {
                "timestamp": datetime.now().isoformat(),
                "total_issues": len(self.issues),
                "timing_data": self.timing_data
            },
            "issues": self.issues,
            "recommendations": self.generate_recommendations()
        }
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        self.logger.info(f"📊 Debug report saved: {report_file}")
        return report_file
    
    def generate_recommendations(self) -> List[str]:
        """Generate recommendations based on observed issues"""
        recommendations = []
        
        # Analyze issues and generate recommendations
        auth_issues = [i for i in self.issues if i['category'] == 'AUTHENTICATION']
        if auth_issues:
            recommendations.append("Consider implementing automatic 2FA handling")
        
        timing_issues = [i for i in self.issues if i['category'] == 'TIMING']
        if timing_issues:
            recommendations.append("Implement adaptive wait times based on network conditions")
        
        element_issues = [i for i in self.issues if i['category'] == 'ELEMENT_DETECTION']
        if element_issues:
            recommendations.append("Use more robust element selectors with fallback strategies")
        
        return recommendations

class LocalDebugServer:
    """Local HTTP server for debugging interface"""
    
    def __init__(self, port: int, debug_data: Dict[str, Any]):
        self.port = port
        self.debug_data = debug_data
        self.server = None
        self.thread = None
    
    def start(self):
        """Start the debug server"""
        handler = self.create_handler()
        self.server = socketserver.TCPServer(("", self.port), handler)
        self.thread = threading.Thread(target=self.server.serve_forever)
        self.thread.daemon = True
        self.thread.start()
        print(f"🌐 Debug server started at http://localhost:{self.port}")
    
    def create_handler(self):
        """Create request handler with access to debug data"""
        debug_data = self.debug_data
        
        class DebugHandler(http.server.SimpleHTTPRequestHandler):
            def do_GET(self):
                if self.path == '/debug':
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps(debug_data, indent=2).encode())
                elif self.path == '/':
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html')
                    self.end_headers()
                    html = self.generate_debug_interface()
                    self.wfile.write(html.encode())
                else:
                    super().do_GET()
            
            def generate_debug_interface(self):
                return """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>MEXC Browser Automation Debug</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
                        .issue { margin: 10px 0; padding: 10px; background: #f5f5f5; }
                        .error { background: #ffebee; }
                        .warning { background: #fff3e0; }
                        .info { background: #e3f2fd; }
                        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
                    </style>
                </head>
                <body>
                    <h1>🚀 MEXC Browser Automation Debug Interface</h1>
                    <div id="content">Loading debug data...</div>
                    <script>
                        fetch('/debug')
                            .then(response => response.json())
                            .then(data => {
                                document.getElementById('content').innerHTML = 
                                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                            });
                    </script>
                </body>
                </html>
                """
        
        return DebugHandler
    
    def stop(self):
        """Stop the debug server"""
        if self.server:
            self.server.shutdown()
            self.thread.join()

class MEXCBrowserAutomation:
    """Main browser automation class with comprehensive debugging"""
    
    def __init__(self, trade_config: TradeConfig, server_config: ServerConfig):
        self.trade_config = trade_config
        self.server_config = server_config
        self.logger = DebugLogger(server_config)
        self.debug_data = {"session_start": datetime.now().isoformat()}
        self.debug_server = LocalDebugServer(server_config.port, self.debug_data)
        
        # Browser components
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        
        # Network monitoring
        self.network_requests = []
        self.network_responses = []
        
        self.logger.logger.info(f"🎯 Trade Configuration: {trade_config}")
        self.logger.logger.info(f"⚙️ Server Configuration: {server_config}")
    
    def start_debug_server(self):
        """Start the local debug server"""
        self.debug_server.start()
        return f"http://localhost:{self.server_config.port}"
    
    def take_screenshot(self, name: str, description: str = "") -> str:
        """Take a screenshot for debugging"""
        if not self.page:
            return ""

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{self.server_config.screenshot_dir}/screenshot_{name}_{timestamp}.png"

        try:
            self.page.screenshot(path=filename, full_page=True)
            self.logger.logger.info(f"📸 Screenshot saved: {filename} - {description}")
            return filename
        except Exception as e:
            self.logger.log_issue("SCREENSHOT", f"Failed to take screenshot {name}: {e}", "ERROR")
            return ""

    def setup_network_monitoring(self):
        """Setup network request/response monitoring"""
        if not self.page:
            return

        def handle_request(request):
            request_data = {
                "timestamp": datetime.now().isoformat(),
                "url": request.url,
                "method": request.method,
                "headers": dict(request.headers),
                "post_data": request.post_data
            }
            self.network_requests.append(request_data)

            # Log trading-related requests
            if any(keyword in request.url.lower() for keyword in ['order', 'trade', 'position', 'balance']):
                self.logger.logger.info(f"🌐 Trading Request: {request.method} {request.url}")

        def handle_response(response):
            response_data = {
                "timestamp": datetime.now().isoformat(),
                "url": response.url,
                "status": response.status,
                "headers": dict(response.headers)
            }

            # Try to capture response body for trading endpoints
            if any(keyword in response.url.lower() for keyword in ['order', 'trade', 'position', 'balance']):
                try:
                    response_data["body"] = response.text()
                    self.logger.logger.info(f"📥 Trading Response: {response.status} {response.url}")
                except:
                    response_data["body"] = "Could not capture response body"

            self.network_responses.append(response_data)

        self.page.on("request", handle_request)
        self.page.on("response", handle_response)
        self.logger.logger.info("🔍 Network monitoring enabled")

    def connect_to_browser(self) -> bool:
        """Connect to existing browser session"""
        self.logger.start_timing("browser_connection")

        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp(f'http://127.0.0.1:{self.server_config.debug_port}')

            if not self.browser.contexts:
                self.logger.log_issue("BROWSER_CONNECTION", "No browser contexts found", "ERROR")
                return False

            self.context = self.browser.contexts[0]

            # Find existing MEXC page or create new one
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break

            if not mexc_page:
                mexc_page = self.context.new_page()
                mexc_page.goto('https://futures.mexc.com/exchange/TRU_USDT', wait_until='domcontentloaded')
                self.logger.log_issue("BROWSER_CONNECTION", "Created new MEXC page", "INFO")
            else:
                self.logger.log_issue("BROWSER_CONNECTION", f"Found existing MEXC page: {mexc_page.url}", "INFO")

            self.page = mexc_page

            # Setup monitoring
            self.setup_network_monitoring()

            # Take initial screenshot
            self.take_screenshot("initial_connection", "Browser connected successfully")

            self.logger.end_timing("browser_connection")
            self.logger.logger.info("✅ Browser connection established")
            return True

        except Exception as e:
            self.logger.log_issue("BROWSER_CONNECTION", f"Failed to connect to browser: {e}", "ERROR")
            self.logger.end_timing("browser_connection")
            return False

    def check_authentication_status(self) -> Dict[str, Any]:
        """Check if user is authenticated and detect authentication state"""
        self.logger.start_timing("auth_check")

        auth_status = {
            "is_authenticated": False,
            "needs_login": False,
            "needs_2fa": False,
            "user_info": None,
            "issues": []
        }

        try:
            # Take screenshot of current state
            screenshot = self.take_screenshot("auth_check", "Checking authentication status")

            # Check for login indicators
            login_selectors = [
                'button:has-text("Log In")',
                'a:has-text("Log In")',
                '.login-btn',
                '[data-testid="login"]',
                'input[type="email"]',
                'input[type="password"]'
            ]

            for selector in login_selectors:
                try:
                    if self.page.locator(selector).is_visible(timeout=2000):
                        auth_status["needs_login"] = True
                        self.logger.log_issue("AUTHENTICATION", f"Login required - found: {selector}", "INFO", screenshot)
                        break
                except:
                    continue

            # Check for 2FA indicators
            twofa_selectors = [
                'input[placeholder*="verification"]',
                'input[placeholder*="code"]',
                '.verification-code',
                '.totp-input',
                'text="Enter verification code"'
            ]

            for selector in twofa_selectors:
                try:
                    if self.page.locator(selector).is_visible(timeout=2000):
                        auth_status["needs_2fa"] = True
                        self.logger.log_issue("AUTHENTICATION", f"2FA required - found: {selector}", "WARNING", screenshot)
                        break
                except:
                    continue

            # Check for authenticated state indicators
            auth_selectors = [
                '.user-info',
                '.account-balance',
                '.trading-panel',
                'text="Available Balance"',
                '[data-testid="user-menu"]'
            ]

            for selector in auth_selectors:
                try:
                    if self.page.locator(selector).is_visible(timeout=2000):
                        auth_status["is_authenticated"] = True
                        self.logger.log_issue("AUTHENTICATION", f"Authenticated - found: {selector}", "INFO", screenshot)
                        break
                except:
                    continue

            # Try to extract user information if authenticated
            if auth_status["is_authenticated"]:
                try:
                    # Look for balance information
                    balance_text = self.page.locator('text=/Available.*Balance/').first.text_content(timeout=5000)
                    auth_status["user_info"] = {"balance_text": balance_text}
                except:
                    pass

            self.logger.end_timing("auth_check")
            self.debug_data["auth_status"] = auth_status

            return auth_status

        except Exception as e:
            self.logger.log_issue("AUTHENTICATION", f"Error checking auth status: {e}", "ERROR")
            auth_status["issues"].append(str(e))
            self.logger.end_timing("auth_check")
            return auth_status

    def navigate_to_trading_pair(self) -> bool:
        """Navigate to the specified trading pair"""
        self.logger.start_timing("navigation")

        try:
            target_url = f"https://futures.mexc.com/exchange/{self.trade_config.symbol}"
            current_url = self.page.url

            if self.trade_config.symbol.replace('_', '') not in current_url:
                self.logger.logger.info(f"🧭 Navigating to {target_url}")
                self.page.goto(target_url, wait_until='domcontentloaded')
                time.sleep(3)  # Allow page to fully load

                screenshot = self.take_screenshot("navigation", f"Navigated to {self.trade_config.symbol}")
                self.logger.log_issue("NAVIGATION", f"Navigated to {target_url}", "INFO", screenshot)
            else:
                self.logger.logger.info(f"✅ Already on correct trading pair: {current_url}")

            # Wait for trading interface to load
            trading_selectors = [
                '.trading-panel',
                '.order-form',
                '[data-testid="buy-button"]',
                '[data-testid="sell-button"]',
                'button:has-text("Buy")',
                'button:has-text("Sell")'
            ]

            interface_loaded = False
            for selector in trading_selectors:
                try:
                    if self.page.locator(selector).is_visible(timeout=10000):
                        interface_loaded = True
                        self.logger.log_issue("NAVIGATION", f"Trading interface loaded - found: {selector}", "INFO")
                        break
                except:
                    continue

            if not interface_loaded:
                screenshot = self.take_screenshot("navigation_failed", "Trading interface not found")
                self.logger.log_issue("NAVIGATION", "Trading interface not detected", "ERROR", screenshot)
                return False

            self.logger.end_timing("navigation")
            return True

        except Exception as e:
            screenshot = self.take_screenshot("navigation_error", f"Navigation error: {e}")
            self.logger.log_issue("NAVIGATION", f"Navigation failed: {e}", "ERROR", screenshot)
            self.logger.end_timing("navigation")
            return False

    def execute_trade(self) -> Dict[str, Any]:
        """Execute the configured trade with comprehensive logging"""
        self.logger.start_timing("trade_execution")

        trade_result = {
            "success": False,
            "order_id": None,
            "error": None,
            "steps_completed": [],
            "issues_encountered": []
        }

        try:
            # Take pre-trade screenshot
            screenshot = self.take_screenshot("pre_trade", "Before trade execution")

            # Step 1: Locate trading form
            self.logger.logger.info("🎯 Step 1: Locating trading form")

            # Common selectors for trading forms
            form_selectors = [
                '.order-form',
                '.trading-form',
                '[data-testid="order-form"]',
                '.buy-sell-panel'
            ]

            trading_form = None
            for selector in form_selectors:
                try:
                    form = self.page.locator(selector).first
                    if form.is_visible(timeout=5000):
                        trading_form = form
                        trade_result["steps_completed"].append(f"Found trading form: {selector}")
                        break
                except:
                    continue

            if not trading_form:
                error_msg = "Trading form not found"
                trade_result["error"] = error_msg
                trade_result["issues_encountered"].append(error_msg)
                screenshot = self.take_screenshot("form_not_found", error_msg)
                self.logger.log_issue("TRADE_EXECUTION", error_msg, "ERROR", screenshot)
                return trade_result

            # Step 2: Select order side (Buy/Sell)
            self.logger.logger.info(f"🎯 Step 2: Selecting {self.trade_config.side} side")

            side_selectors = [
                f'button:has-text("{self.trade_config.side.title()}")',
                f'[data-testid="{self.trade_config.side.lower()}-button"]',
                f'.{self.trade_config.side.lower()}-btn',
                f'.btn-{self.trade_config.side.lower()}'
            ]

            side_selected = False
            for selector in side_selectors:
                try:
                    button = self.page.locator(selector).first
                    if button.is_visible(timeout=3000):
                        button.click()
                        side_selected = True
                        trade_result["steps_completed"].append(f"Selected {self.trade_config.side} side")
                        time.sleep(1)  # Allow UI to update
                        break
                except Exception as e:
                    trade_result["issues_encountered"].append(f"Failed to click {selector}: {e}")
                    continue

            if not side_selected:
                error_msg = f"Could not select {self.trade_config.side} side"
                trade_result["error"] = error_msg
                screenshot = self.take_screenshot("side_selection_failed", error_msg)
                self.logger.log_issue("TRADE_EXECUTION", error_msg, "ERROR", screenshot)
                return trade_result

            # Step 3: Select order type
            self.logger.logger.info(f"🎯 Step 3: Selecting {self.trade_config.order_type} order type")

            if self.trade_config.order_type == "LIMIT":
                type_selectors = [
                    'button:has-text("Limit")',
                    '[data-testid="limit-order"]',
                    '.limit-order-btn'
                ]

                for selector in type_selectors:
                    try:
                        button = self.page.locator(selector).first
                        if button.is_visible(timeout=3000):
                            button.click()
                            trade_result["steps_completed"].append("Selected LIMIT order type")
                            time.sleep(1)
                            break
                    except:
                        continue

            # Step 4: Enter quantity
            self.logger.logger.info(f"🎯 Step 4: Entering quantity: {self.trade_config.quantity}")

            quantity_selectors = [
                'input[placeholder*="quantity"]',
                'input[placeholder*="amount"]',
                'input[placeholder*="size"]',
                '[data-testid="quantity-input"]',
                '.quantity-input'
            ]

            quantity_entered = False
            for selector in quantity_selectors:
                try:
                    input_field = self.page.locator(selector).first
                    if input_field.is_visible(timeout=3000):
                        input_field.clear()
                        input_field.fill(str(self.trade_config.quantity))
                        quantity_entered = True
                        trade_result["steps_completed"].append(f"Entered quantity: {self.trade_config.quantity}")
                        break
                except Exception as e:
                    trade_result["issues_encountered"].append(f"Failed to enter quantity in {selector}: {e}")
                    continue

            if not quantity_entered:
                error_msg = "Could not enter quantity"
                trade_result["error"] = error_msg
                screenshot = self.take_screenshot("quantity_entry_failed", error_msg)
                self.logger.log_issue("TRADE_EXECUTION", error_msg, "ERROR", screenshot)
                return trade_result

            # Step 5: Enter price (for limit orders)
            if self.trade_config.order_type == "LIMIT" and self.trade_config.price:
                self.logger.logger.info(f"🎯 Step 5: Entering price: {self.trade_config.price}")

                price_selectors = [
                    'input[placeholder*="price"]',
                    '[data-testid="price-input"]',
                    '.price-input'
                ]

                for selector in price_selectors:
                    try:
                        input_field = self.page.locator(selector).first
                        if input_field.is_visible(timeout=3000):
                            input_field.clear()
                            input_field.fill(str(self.trade_config.price))
                            trade_result["steps_completed"].append(f"Entered price: {self.trade_config.price}")
                            break
                    except Exception as e:
                        trade_result["issues_encountered"].append(f"Failed to enter price in {selector}: {e}")
                        continue

            # Take screenshot before submitting
            screenshot = self.take_screenshot("before_submit", "Form filled, ready to submit")

            # Step 6: Submit order (with confirmation handling)
            self.logger.logger.info("🎯 Step 6: Submitting order")

            submit_selectors = [
                f'button:has-text("{self.trade_config.side.title()}")',
                'button:has-text("Submit")',
                'button:has-text("Place Order")',
                '[data-testid="submit-order"]',
                '.submit-btn'
            ]

            order_submitted = False
            for selector in submit_selectors:
                try:
                    button = self.page.locator(selector).first
                    if button.is_visible(timeout=3000) and button.is_enabled():
                        button.click()
                        order_submitted = True
                        trade_result["steps_completed"].append("Order submitted")
                        break
                except Exception as e:
                    trade_result["issues_encountered"].append(f"Failed to submit with {selector}: {e}")
                    continue

            if not order_submitted:
                error_msg = "Could not submit order"
                trade_result["error"] = error_msg
                screenshot = self.take_screenshot("submit_failed", error_msg)
                self.logger.log_issue("TRADE_EXECUTION", error_msg, "ERROR", screenshot)
                return trade_result

            # Wait for confirmation or error
            time.sleep(2)
            screenshot = self.take_screenshot("after_submit", "After order submission")

            # Check for success/error messages
            success_indicators = [
                'text="Order placed successfully"',
                'text="Success"',
                '.success-message',
                '.order-success'
            ]

            error_indicators = [
                'text="Error"',
                'text="Failed"',
                '.error-message',
                '.order-error'
            ]

            # Check for success
            for selector in success_indicators:
                try:
                    if self.page.locator(selector).is_visible(timeout=5000):
                        trade_result["success"] = True
                        trade_result["steps_completed"].append("Order confirmed successful")
                        self.logger.log_issue("TRADE_EXECUTION", "Order placed successfully", "INFO", screenshot)
                        break
                except:
                    continue

            # Check for errors
            for selector in error_indicators:
                try:
                    if self.page.locator(selector).is_visible(timeout=3000):
                        error_text = self.page.locator(selector).text_content()
                        trade_result["error"] = error_text
                        trade_result["issues_encountered"].append(f"Order error: {error_text}")
                        self.logger.log_issue("TRADE_EXECUTION", f"Order failed: {error_text}", "ERROR", screenshot)
                        break
                except:
                    continue

            # If no clear success/error, assume success but log uncertainty
            if trade_result["success"] is False and not trade_result["error"]:
                trade_result["success"] = True  # Assume success if no error detected
                trade_result["issues_encountered"].append("Order status unclear - assumed successful")
                self.logger.log_issue("TRADE_EXECUTION", "Order status unclear", "WARNING", screenshot)

            self.logger.end_timing("trade_execution")
            self.debug_data["trade_result"] = trade_result

            return trade_result

        except Exception as e:
            trade_result["error"] = str(e)
            screenshot = self.take_screenshot("trade_exception", f"Trade execution exception: {e}")
            self.logger.log_issue("TRADE_EXECUTION", f"Trade execution failed: {e}", "ERROR", screenshot)
            self.logger.end_timing("trade_execution")
            return trade_result

    def run_full_automation(self) -> Dict[str, Any]:
        """Run the complete automation workflow"""
        self.logger.logger.info("🚀 Starting full MEXC browser automation workflow")

        workflow_result = {
            "success": False,
            "steps": {},
            "total_duration": 0,
            "debug_server_url": None
        }

        start_time = time.time()

        try:
            # Start debug server
            debug_url = self.start_debug_server()
            workflow_result["debug_server_url"] = debug_url
            self.logger.logger.info(f"🌐 Debug interface available at: {debug_url}")

            # Step 1: Connect to browser
            self.logger.logger.info("📋 Step 1: Connecting to browser")
            if not self.connect_to_browser():
                workflow_result["steps"]["browser_connection"] = "FAILED"
                return workflow_result
            workflow_result["steps"]["browser_connection"] = "SUCCESS"

            # Step 2: Check authentication
            self.logger.logger.info("📋 Step 2: Checking authentication status")
            auth_status = self.check_authentication_status()
            workflow_result["steps"]["authentication_check"] = auth_status

            if not auth_status["is_authenticated"]:
                if auth_status["needs_login"]:
                    self.logger.logger.warning("⚠️ Manual login required")
                    self.logger.log_issue("WORKFLOW", "Manual login required", "WARNING")
                    input("Please log in manually in the browser, then press Enter to continue...")

                    # Re-check authentication after manual login
                    auth_status = self.check_authentication_status()
                    if not auth_status["is_authenticated"]:
                        workflow_result["steps"]["manual_login"] = "FAILED"
                        return workflow_result
                    workflow_result["steps"]["manual_login"] = "SUCCESS"

                if auth_status["needs_2fa"]:
                    self.logger.logger.warning("⚠️ 2FA verification required")
                    self.logger.log_issue("WORKFLOW", "2FA verification required", "WARNING")
                    input("Please complete 2FA verification in the browser, then press Enter to continue...")

                    # Re-check authentication after 2FA
                    auth_status = self.check_authentication_status()
                    if not auth_status["is_authenticated"]:
                        workflow_result["steps"]["2fa_verification"] = "FAILED"
                        return workflow_result
                    workflow_result["steps"]["2fa_verification"] = "SUCCESS"

            # Step 3: Navigate to trading pair
            self.logger.logger.info("📋 Step 3: Navigating to trading pair")
            if not self.navigate_to_trading_pair():
                workflow_result["steps"]["navigation"] = "FAILED"
                return workflow_result
            workflow_result["steps"]["navigation"] = "SUCCESS"

            # Step 4: Execute trade
            self.logger.logger.info("📋 Step 4: Executing trade")
            trade_result = self.execute_trade()
            workflow_result["steps"]["trade_execution"] = trade_result

            if trade_result["success"]:
                workflow_result["success"] = True
                self.logger.logger.info("✅ Full automation workflow completed successfully")
            else:
                self.logger.logger.error(f"❌ Trade execution failed: {trade_result.get('error', 'Unknown error')}")

        except Exception as e:
            self.logger.log_issue("WORKFLOW", f"Workflow exception: {e}", "ERROR")
            workflow_result["steps"]["exception"] = str(e)

        finally:
            # Calculate total duration
            workflow_result["total_duration"] = time.time() - start_time

            # Update debug data
            self.debug_data.update({
                "workflow_result": workflow_result,
                "network_requests": len(self.network_requests),
                "network_responses": len(self.network_responses),
                "total_issues": len(self.logger.issues)
            })

            # Generate and save debug report
            report_file = self.logger.save_debug_report()
            workflow_result["debug_report"] = report_file

            self.logger.logger.info(f"⏱️ Total workflow duration: {workflow_result['total_duration']:.2f}s")
            self.logger.logger.info(f"📊 Debug report: {report_file}")
            self.logger.logger.info(f"🌐 Debug server: {debug_url}")

        return workflow_result

    def cleanup(self):
        """Clean up resources"""
        try:
            if self.debug_server:
                self.debug_server.stop()
            if self.playwright:
                self.playwright.stop()
        except Exception as e:
            self.logger.logger.error(f"Cleanup error: {e}")

def main():
    """Main entry point with command-line interface"""
    parser = argparse.ArgumentParser(description="MEXC Browser Automation Debug Script")

    # Trade configuration
    parser.add_argument("--symbol", default="TRU_USDT", help="Trading symbol (default: TRU_USDT)")
    parser.add_argument("--side", choices=["BUY", "SELL"], default="BUY", help="Order side (default: BUY)")
    parser.add_argument("--type", choices=["MARKET", "LIMIT"], default="MARKET", help="Order type (default: MARKET)")
    parser.add_argument("--quantity", type=float, default=10.0, help="Order quantity in USDT (default: 10.0)")
    parser.add_argument("--price", type=float, help="Order price for limit orders")
    parser.add_argument("--leverage", type=int, default=1, help="Leverage (default: 1)")

    # Server configuration
    parser.add_argument("--port", type=int, default=8080, help="Debug server port (default: 8080)")
    parser.add_argument("--debug-port", type=int, default=9222, help="Browser debug port (default: 9222)")
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"], default="DEBUG", help="Log level (default: DEBUG)")

    args = parser.parse_args()

    # Create configurations
    trade_config = TradeConfig(
        symbol=args.symbol,
        side=args.side,
        order_type=args.type,
        quantity=args.quantity,
        price=args.price,
        leverage=args.leverage
    )

    server_config = ServerConfig(
        port=args.port,
        debug_port=args.debug_port,
        log_level=args.log_level
    )

    # Create and run automation
    automation = MEXCBrowserAutomation(trade_config, server_config)

    try:
        print(f"""
🚀 MEXC Browser Automation Debug Script
=====================================

Trade Configuration:
  Symbol: {trade_config.symbol}
  Side: {trade_config.side}
  Type: {trade_config.order_type}
  Quantity: {trade_config.quantity} USDT
  Price: {trade_config.price or 'Market Price'}
  Leverage: {trade_config.leverage}x

Server Configuration:
  Debug Server Port: {server_config.port}
  Browser Debug Port: {server_config.debug_port}
  Log Level: {server_config.log_level}

Starting automation...
        """)

        result = automation.run_full_automation()

        print(f"""
📊 Automation Results:
=====================
Success: {result['success']}
Duration: {result['total_duration']:.2f}s
Debug Server: {result['debug_server_url']}
Debug Report: {result.get('debug_report', 'N/A')}

Steps Completed:
{json.dumps(result['steps'], indent=2)}
        """)

        if result['success']:
            print("✅ Automation completed successfully!")
        else:
            print("❌ Automation failed. Check debug report for details.")

        # Keep debug server running
        if result.get('debug_server_url'):
            print(f"\n🌐 Debug server is running at: {result['debug_server_url']}")
            print("Press Ctrl+C to stop the debug server and exit.")

            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n👋 Shutting down...")

    except KeyboardInterrupt:
        print("\n👋 Interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
    finally:
        automation.cleanup()

if __name__ == "__main__":
    main()
