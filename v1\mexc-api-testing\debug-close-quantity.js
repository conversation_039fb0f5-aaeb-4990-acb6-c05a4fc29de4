const { chromium } = require('playwright');

async function debugCloseQuantityField() {
    console.log('🔍 DEBUGGING CLOSE QUANTITY FIELD (Port 9223)');
    console.log('==============================================');
    
    try {
        const browser = await chromium.connectOverCDP('http://localhost:9223');
        const contexts = browser.contexts();
        
        if (contexts.length > 0) {
            const pages = contexts[0].pages();
            const page = pages.length > 0 ? pages[0] : await contexts[0].newPage();
            
            // Navigate to the page
            const url = page.url();
            if (!url.includes('mexc.com/futures/TRU_USDT')) {
                console.log('🌐 Navigating to TRU_USDT...');
                await page.goto('https://www.mexc.com/futures/TRU_USDT');
                await page.waitForTimeout(3000);
            }
            
            // Try to set Close mode
            console.log('\n🔄 Setting Close mode...');
            try {
                const closeBtn = page.locator('button:has-text("Close")').first();
                if (await closeBtn.isVisible({ timeout: 1000 })) {
                    await closeBtn.click();
                    await page.waitForTimeout(500);
                    console.log('✅ Close mode activated');
                } else {
                    console.log('⚠️ Close mode button not found');
                }
            } catch (error) {
                console.log('❌ Error setting Close mode:', error.message);
            }
            
            // Find all input fields
            console.log('\n📋 ALL INPUT FIELDS:');
            const allInputs = await page.locator('input').all();
            for (let i = 0; i < allInputs.length; i++) {
                try {
                    const input = allInputs[i];
                    if (await input.isVisible({ timeout: 100 })) {
                        const type = await input.getAttribute('type');
                        const placeholder = await input.getAttribute('placeholder');
                        const className = await input.getAttribute('class');
                        const value = await input.inputValue();
                        const disabled = await input.isDisabled();
                        const readonly = await input.getAttribute('readonly');
                        
                        console.log(`  ${i + 1}. Type: ${type || 'none'}, Placeholder: "${placeholder || 'none'}", Value: "${value}", Disabled: ${disabled}, Readonly: ${readonly !== null}`);
                        console.log(`     Class: ${className || 'none'}`);
                    }
                } catch (error) {
                    console.log(`  ${i + 1}. Error reading input: ${error.message}`);
                }
            }
            
            // Test the specific quantity field strategies
            console.log('\n🎯 TESTING QUANTITY FIELD STRATEGIES:');
            
            // Strategy 1: XPath following Quantity(USDT)
            console.log('\n1️⃣ Strategy 1: XPath following Quantity(USDT)');
            try {
                const input = page.locator('text=Quantity(USDT) >> xpath=following::input[1]').first();
                if (await input.isVisible({ timeout: 500 })) {
                    console.log('✅ Field found');
                    
                    const type = await input.getAttribute('type');
                    const disabled = await input.isDisabled();
                    const readonly = await input.getAttribute('readonly');
                    const currentValue = await input.inputValue();
                    
                    console.log(`   Type: ${type}, Disabled: ${disabled}, Readonly: ${readonly !== null}, Current: "${currentValue}"`);
                    
                    // Try to interact with it
                    try {
                        await input.click();
                        console.log('✅ Click successful');
                        
                        // Try clearing
                        await input.press('Control+a');
                        await input.press('Delete');
                        await input.fill('');
                        console.log('✅ Clear successful');
                        
                        // Try filling
                        await input.fill('0.3600');
                        const newValue = await input.inputValue();
                        console.log(`✅ Fill attempt: "${newValue}"`);
                        
                        if (newValue === '0.3600') {
                            console.log('🎉 SUCCESS: Field populated correctly!');
                        } else {
                            console.log('❌ FAILED: Field not populated correctly');
                            
                            // Try alternative method
                            console.log('🔄 Trying alternative method...');
                            await input.click();
                            await input.press('Control+a');
                            await input.type('0.3600');
                            const altValue = await input.inputValue();
                            console.log(`   Alternative result: "${altValue}"`);
                        }
                        
                    } catch (error) {
                        console.log(`❌ Interaction failed: ${error.message}`);
                    }
                } else {
                    console.log('❌ Field not found');
                }
            } catch (error) {
                console.log(`❌ Strategy 1 failed: ${error.message}`);
            }
            
            // Strategy 2: XPath following Quantity
            console.log('\n2️⃣ Strategy 2: XPath following Quantity');
            try {
                const input = page.locator('text=Quantity >> xpath=following::input[1]').first();
                if (await input.isVisible({ timeout: 300 })) {
                    const type = await input.getAttribute('type');
                    if (type !== 'checkbox') {
                        console.log('✅ Field found (non-checkbox)');
                        
                        const disabled = await input.isDisabled();
                        const readonly = await input.getAttribute('readonly');
                        const currentValue = await input.inputValue();
                        
                        console.log(`   Type: ${type}, Disabled: ${disabled}, Readonly: ${readonly !== null}, Current: "${currentValue}"`);
                        
                        try {
                            await input.click();
                            await input.fill('0.3600');
                            const newValue = await input.inputValue();
                            console.log(`✅ Fill result: "${newValue}"`);
                        } catch (error) {
                            console.log(`❌ Fill failed: ${error.message}`);
                        }
                    } else {
                        console.log('⚠️ Field is checkbox, skipping');
                    }
                } else {
                    console.log('❌ Field not found');
                }
            } catch (error) {
                console.log(`❌ Strategy 2 failed: ${error.message}`);
            }
            
            // Check for any special attributes or behaviors
            console.log('\n🔬 DETAILED FIELD ANALYSIS:');
            try {
                const quantityInputs = await page.locator('input[type="text"], input[type="number"], input:not([type])').all();
                for (let i = 0; i < quantityInputs.length; i++) {
                    const input = quantityInputs[i];
                    if (await input.isVisible({ timeout: 100 })) {
                        const boundingBox = await input.boundingBox();
                        const computedStyle = await input.evaluate(el => {
                            const style = window.getComputedStyle(el);
                            return {
                                display: style.display,
                                visibility: style.visibility,
                                pointerEvents: style.pointerEvents,
                                opacity: style.opacity
                            };
                        });
                        
                        console.log(`Input ${i + 1}:`);
                        console.log(`  Position: ${boundingBox ? `${boundingBox.x},${boundingBox.y}` : 'unknown'}`);
                        console.log(`  Style: ${JSON.stringify(computedStyle)}`);
                    }
                }
            } catch (error) {
                console.log(`❌ Analysis failed: ${error.message}`);
            }
            
        }
    } catch (error) {
        console.error('❌ Debug failed:', error.message);
    }
    
    console.log('\n🏁 Debug completed');
}

if (require.main === module) {
    debugCloseQuantityField().catch(console.error);
}

module.exports = debugCloseQuantityField;
