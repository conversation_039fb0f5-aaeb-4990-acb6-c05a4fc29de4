# MEXC Trading System - Testing Guide

This guide covers testing procedures for the MEXC High-Speed Futures Trading System.

## Pre-Testing Setup

### 1. Environment Configuration

```bash
# Copy and configure environment
cp .env.template .env

# Essential configurations for testing
ENVIRONMENT=development
DEBUG_MODE=true
LOG_LEVEL=DEBUG
HEADLESS_MODE=false  # For visual testing
MAX_CONCURRENT_TRADES=2  # Limit for testing
```

### 2. Test Dependencies

```bash
# Install test dependencies
pip install pytest pytest-asyncio pytest-mock httpx

# Install development tools
pip install black flake8 mypy
```

## Testing Phases

### Phase 1: Unit Tests

```bash
# Run unit tests
pytest tests/unit/ -v

# Test specific components
pytest tests/unit/test_session_manager.py -v
pytest tests/unit/test_trading_engine.py -v
pytest tests/unit/test_telegram_bot.py -v
```

### Phase 2: Integration Tests

```bash
# Run integration tests
pytest tests/integration/ -v

# Test API endpoints
pytest tests/integration/test_webhook_api.py -v
pytest tests/integration/test_management_api.py -v
```

### Phase 3: End-to-End Tests

```bash
# Run E2E tests (requires MEXC test account)
pytest tests/e2e/ -v --slow

# Test complete trading flow
pytest tests/e2e/test_trading_flow.py -v
```

## Manual Testing Procedures

### 1. System Startup

```bash
# Start in development mode
./start.sh dev

# Verify startup logs
tail -f logs/trading_system.log

# Check health endpoint
curl http://localhost:8000/health
```

Expected output:
```json
{
  "status": "healthy",
  "components": {
    "session_manager": true,
    "trading_engine": true,
    "telegram_bot": true
  }
}
```

### 2. Session Management Testing

#### Test Session Creation
1. Access dashboard: http://localhost:8000
2. Navigate to Sessions tab
3. Verify 3 sessions are created
4. Check session health scores

#### Test Session Authentication
1. Click on a session with low health
2. Browser window should open to MEXC login
3. Manually log in and complete 2FA
4. Verify session becomes healthy

#### Test Session Expiry Warnings
```bash
# Simulate session expiry (modify database)
# Should trigger Telegram notifications
```

### 3. Trading Engine Testing

#### Test Webhook Reception
```bash
# Test webhook endpoint
curl -X POST http://localhost:8000/webhook/test \
  -H "Content-Type: application/json" \
  -d '{
    "action": "buy",
    "symbol": "BTC_USDT",
    "side": "long",
    "quantity": 0.001,
    "price": 45000,
    "leverage": 1,
    "order_type": "limit"
  }'
```

Expected response:
```json
{
  "success": true,
  "message": "Test webhook validated: buy BTC_USDT",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

#### Test Trade Execution (Dry Run)
```bash
# Manual trade endpoint
curl -X POST http://localhost:8000/webhook/manual \
  -H "Content-Type: application/json" \
  -d '{
    "action": "buy",
    "symbol": "BTC_USDT",
    "side": "long",
    "quantity": 0.001,
    "price": 50000,
    "leverage": 1,
    "order_type": "post_only"
  }'
```

### 4. Dashboard Testing

#### Navigation Tests
1. Access main dashboard: http://localhost:8000
2. Verify all metrics display correctly
3. Test navigation to all sections:
   - Sessions: http://localhost:8000/dashboard/sessions
   - Trades: http://localhost:8000/dashboard/trades
   - Config: http://localhost:8000/dashboard/config

#### Functionality Tests
1. Test session refresh functionality
2. Test emergency stop button
3. Test webhook test button
4. Verify real-time updates

### 5. API Testing

#### Management API
```bash
# Get system status
curl http://localhost:8000/api/status

# Get sessions
curl http://localhost:8000/api/sessions

# Get trades
curl http://localhost:8000/api/trades

# Get metrics
curl http://localhost:8000/api/metrics
```

#### Webhook API
```bash
# TradingView webhook
curl -X POST http://localhost:8000/webhook/tradingview \
  -H "Content-Type: application/json" \
  -d '{
    "action": "sell",
    "symbol": "ETH_USDT",
    "side": "short",
    "quantity": 0.1,
    "leverage": 5
  }'
```

## Performance Testing

### 1. Load Testing

```bash
# Install load testing tool
pip install locust

# Run load test
locust -f tests/load/locustfile.py --host=http://localhost:8000
```

### 2. Concurrent Trading Test

```bash
# Send multiple simultaneous webhooks
for i in {1..10}; do
  curl -X POST http://localhost:8000/webhook/test \
    -H "Content-Type: application/json" \
    -d '{"action":"buy","symbol":"BTC_USDT","side":"long","quantity":0.001}' &
done
wait
```

### 3. Session Pool Stress Test

```bash
# Test session pool under load
# Monitor session health during high activity
# Verify session rotation and recovery
```

## Security Testing

### 1. Webhook Signature Verification

```bash
# Test with invalid signature
curl -X POST http://localhost:8000/webhook/tradingview \
  -H "Content-Type: application/json" \
  -H "X-Signature: invalid_signature" \
  -d '{"action":"buy","symbol":"BTC_USDT","side":"long","quantity":0.001}'
```

Expected: 401 Unauthorized

### 2. Input Validation

```bash
# Test invalid symbol
curl -X POST http://localhost:8000/webhook/test \
  -H "Content-Type: application/json" \
  -d '{"action":"buy","symbol":"INVALID_SYMBOL","side":"long","quantity":0.001}'
```

Expected: 400 Bad Request

### 3. Rate Limiting (if configured)

```bash
# Send rapid requests to test rate limiting
for i in {1..100}; do
  curl http://localhost:8000/api/status &
done
```

## Error Handling Testing

### 1. Network Failures

```bash
# Simulate network issues
# Test system recovery
# Verify error notifications
```

### 2. MEXC API Errors

```bash
# Test with invalid orders
# Verify error handling and logging
# Check Telegram notifications
```

### 3. Session Failures

```bash
# Force session closure
# Test automatic session recreation
# Verify failover mechanisms
```

## Monitoring Testing

### 1. Telegram Notifications

Verify notifications are sent for:
- System startup/shutdown
- Trade executions
- Session expiry warnings
- Error conditions
- Health check failures

### 2. Logging

Check log files contain:
- Structured log entries
- Trade execution details
- Error messages with stack traces
- Session management events
- Performance metrics

### 3. Health Checks

```bash
# Test health endpoint
curl http://localhost:8000/health

# Test metrics endpoint
curl http://localhost:8000/metrics

# Verify Prometheus metrics format
```

## Production Testing Checklist

Before deploying to production:

- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] Performance benchmarks met
- [ ] Security tests pass
- [ ] Error handling verified
- [ ] Monitoring configured
- [ ] Backup procedures tested
- [ ] Recovery procedures tested
- [ ] Documentation updated

## Test Data Cleanup

```bash
# Clean test data
rm -rf data/test_*
rm -rf browser_data/test_*
rm -rf logs/test_*

# Reset database
rm -f data/trading_system.db
```

## Continuous Testing

Set up automated testing:

```bash
# GitHub Actions workflow
# Run tests on every commit
# Deploy to staging environment
# Run integration tests
# Deploy to production on success
```

## Test Results Documentation

Document test results:
- Performance benchmarks
- Error rates
- Response times
- Resource usage
- Success rates

This ensures consistent quality and helps identify regressions in future updates.
