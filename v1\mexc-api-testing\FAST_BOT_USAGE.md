# 🚀 Fast MEXC Futures Trading Bot

## ⚡ Speed-Optimized Browser Automation

This bot is designed for **sub-2 second** futures order execution on MEXC using browser automation.

## 🎯 Key Features

- ⚡ **Ultra-fast execution** (target: <2 seconds)
- 🔄 **Session persistence** (login once, reuse session)
- 🚫 **Resource blocking** (images, CSS, fonts disabled for speed)
- 🎭 **Headless operation** (can run in background)
- 🔐 **Auto-login** with saved sessions
- 📊 **Real-time execution metrics**

## 📦 Installation

### Node.js Version

```bash
# Install dependencies
npm install

# Install Playwright browser
npm run install-playwright

# Run the bot
npm run fast-bot
```

### Python Version

```bash
# Install dependencies
pip install -r requirements-python.txt

# Install Playwright browser
playwright install chromium

# Run the bot
python fast_futures_bot.py
```

## ⚙️ Configuration

Make sure your `.env` file contains:

```env
MEXC_EMAIL=<EMAIL>
MEXC_PASSWORD=your_password
```

## 🎯 Usage Examples

### Basic Market Order (Node.js)

```javascript
const FastMEXCFuturesBot = require('./fast-futures-bot');

const bot = new FastMEXCFuturesBot();

async function quickTrade() {
    await bot.initialize();
    await bot.login();
    
    const result = await bot.placeFuturesOrder({
        symbol: 'TRU_USDT',
        side: 'buy',
        type: 'market',
        quantity: '40',
        leverage: '1'
    });
    
    console.log('Execution time:', result.executionTime + 'ms');
    await bot.close();
}

quickTrade();
```

### Basic Market Order (Python)

```python
import asyncio
from fast_futures_bot import FastMEXCFuturesBot

async def quick_trade():
    bot = FastMEXCFuturesBot()
    await bot.initialize()
    await bot.login()
    
    result = await bot.place_futures_order({
        'symbol': 'TRU_USDT',
        'side': 'buy',
        'type': 'market',
        'quantity': '40',
        'leverage': '1'
    })
    
    print(f'Execution time: {result["execution_time"]}ms')
    await bot.close()

asyncio.run(quick_trade())
```

## 🔧 Order Configuration

```javascript
const orderConfig = {
    symbol: 'BTC_USDT',     // Trading pair
    side: 'buy',            // 'buy' or 'sell'
    type: 'market',         // 'market' or 'limit'
    quantity: '0.001',      // Order size
    price: '50000',         // Only for limit orders
    leverage: '10'          // Leverage (1-125)
};
```

## ⚡ Speed Optimizations

### Browser Optimizations
- Disabled images, CSS, fonts, media
- Minimal viewport size
- Disabled GPU acceleration
- No sandbox mode
- Background throttling disabled

### Session Management
- Cookies saved locally
- Auto-login on subsequent runs
- Session validation before trading

### Selector Strategy
- Multiple fallback selectors
- Timeout-optimized element finding
- Parallel element searches

## 📊 Performance Metrics

Expected execution times:
- **Cold start** (first login): 5-10 seconds
- **Warm start** (with session): 1-3 seconds
- **Order placement**: 0.5-2 seconds
- **Total execution**: <2 seconds (target)

## 🛡️ Safety Features

### Built-in Safeguards
- 3-second confirmation delay
- Order parameter validation
- Error handling and recovery
- Execution time monitoring

### Risk Management
- Small default quantities
- Conservative leverage settings
- Order confirmation detection
- Session timeout handling

## 🔍 Debugging

### Enable Visual Mode
```javascript
// Node.js
const bot = new FastMEXCFuturesBot();
bot.config.headless = false; // Show browser

# Python
bot = FastMEXCFuturesBot()
bot.config['headless'] = False  # Show browser
```

### Common Issues

1. **Login Failed**
   - Check email/password in .env
   - Verify MEXC account status
   - Clear session file: `rm mexc-session.json`

2. **Slow Execution**
   - Check internet connection
   - Verify browser installation
   - Monitor system resources

3. **Order Not Placed**
   - Check account balance
   - Verify symbol format (TRU_USDT not TRUUSDT)
   - Ensure futures trading is enabled

## 📈 Advanced Usage

### Webhook Integration

```javascript
const express = require('express');
const app = express();

app.post('/webhook', async (req, res) => {
    const signal = req.body;
    
    const result = await bot.placeFuturesOrder({
        symbol: signal.symbol,
        side: signal.action,
        type: 'market',
        quantity: signal.quantity
    });
    
    res.json(result);
});
```

### Multiple Symbol Trading

```javascript
const symbols = ['BTC_USDT', 'ETH_USDT', 'TRU_USDT'];

for (const symbol of symbols) {
    await bot.placeFuturesOrder({
        symbol: symbol,
        side: 'buy',
        type: 'market',
        quantity: '0.001'
    });
}
```

## 🚨 Important Notes

1. **Real Money**: This bot places REAL orders with REAL money
2. **Speed vs Safety**: Faster execution = less validation
3. **Session Security**: Keep session files secure
4. **Rate Limits**: MEXC may have rate limits on web interface
5. **Market Hours**: Some features may be limited during maintenance

## 📞 Support

If you encounter issues:
1. Check the execution logs
2. Verify browser installation
3. Test with small amounts first
4. Monitor MEXC system status

## ⚖️ Legal Disclaimer

- Use at your own risk
- Test with small amounts first
- Monitor your account closely
- Comply with MEXC terms of service
- Not financial advice

---

**Target Performance**: Sub-2 second order execution ⚡  
**Status**: Ready for testing 🚀  
**Risk Level**: High - Real money trading 🚨
