#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MEXC UI Analysis Script
Analyze the complete MEXC futures trading interface to identify all UI elements and selectors.
"""

import os
import json
import time
from datetime import datetime
from playwright.sync_api import sync_playwright

class MEXCUIAnalyzer:
    """Analyze MEXC futures trading interface"""
    
    def __init__(self):
        self.playwright = None
        self.browser = None
        self.page = None
        self.analysis_data = {}
    
    def connect_to_browser(self) -> bool:
        """Connect to existing browser session"""
        try:
            print("🔌 Starting Playwright...")
            self.playwright = sync_playwright().start()

            print("🌐 Connecting to browser...")
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')

            if not self.browser.contexts:
                print("❌ No browser contexts found")
                return False

            print(f"📋 Found {len(self.browser.contexts)} browser context(s)")
            context = self.browser.contexts[0]

            print(f"📄 Found {len(context.pages)} page(s)")
            for i, page in enumerate(context.pages):
                print(f"  Page {i+1}: {page.url}")

            # Find MEXC page
            mexc_page = None
            for page in context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    print(f"🎯 Found MEXC page: {page.url}")
                    break

            if not mexc_page:
                print("❌ No MEXC page found in existing tabs")
                print("🔄 Creating new MEXC page...")
                mexc_page = context.new_page()
                mexc_page.goto('https://futures.mexc.com/exchange/TRU_USDT', wait_until='domcontentloaded')
                print(f"✅ Created new MEXC page: {mexc_page.url}")

            self.page = mexc_page
            print(f"✅ Connected to MEXC page: {self.page.url}")
            print(f"📄 Page title: {self.page.title()}")
            return True

        except Exception as e:
            print(f"❌ Browser connection failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def capture_page_source(self):
        """Capture complete HTML source"""
        print("📄 Capturing page source...")
        
        try:
            html_content = self.page.content()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"mexc_page_source_{timestamp}.html"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"✅ Page source saved: {filename}")
            self.analysis_data["page_source_file"] = filename
            self.analysis_data["page_source_size"] = len(html_content)
            
        except Exception as e:
            print(f"❌ Failed to capture page source: {e}")
    
    def analyze_trading_elements(self):
        """Analyze all trading-related UI elements"""
        print("🔍 Analyzing trading elements...")
        
        elements = {}
        
        # Pop-up and modal elements
        popup_selectors = [
            '.popup', '.modal', '.dialog', '.overlay',
            '[class*="popup"]', '[class*="modal"]', '[class*="dialog"]',
            'button[class*="close"]', '.close-btn', '[aria-label="close"]'
        ]
        
        # Trade type tabs (Open/Close)
        trade_type_selectors = [
            'button:has-text("Open")', 'button:has-text("Close")',
            '.tab:has-text("Open")', '.tab:has-text("Close")',
            '[data-testid*="open"]', '[data-testid*="close"]'
        ]
        
        # Margin mode elements
        margin_selectors = [
            'button:has-text("Isolated")', 'button:has-text("Cross")',
            '.margin-mode', '[class*="margin"]',
            'input[type="radio"][value="isolated"]', 'input[type="radio"][value="cross"]'
        ]
        
        # Leverage elements
        leverage_selectors = [
            'button:has-text("20x")', '[class*="leverage"]',
            'input[placeholder*="leverage"]', '.leverage-input',
            'button[class*="leverage"]'
        ]
        
        # Order type elements
        order_type_selectors = [
            'button:has-text("Limit")', 'button:has-text("Market")', 'button:has-text("Trigger")',
            '.order-type', '[class*="order-type"]',
            'select[class*="order"]', '.dropdown'
        ]
        
        # Input fields
        input_selectors = [
            'input[placeholder*="price"]', 'input[placeholder*="quantity"]',
            'input[placeholder*="amount"]', 'input[placeholder*="trigger"]',
            '.price-input', '.quantity-input', '.amount-input'
        ]
        
        # Action buttons
        action_selectors = [
            'button:has-text("Buy")', 'button:has-text("Sell")',
            'button:has-text("Open Long")', 'button:has-text("Open Short")',
            'button:has-text("Submit")', 'button:has-text("Confirm")',
            '.buy-btn', '.sell-btn', '.submit-btn'
        ]
        
        # TP/SL elements
        tpsl_selectors = [
            'input[type="checkbox"]:has-text("TP")', 'input[type="checkbox"]:has-text("SL")',
            '.tp-sl', '[class*="take-profit"]', '[class*="stop-loss"]'
        ]
        
        all_selectors = {
            "popups": popup_selectors,
            "trade_types": trade_type_selectors,
            "margin_modes": margin_selectors,
            "leverage": leverage_selectors,
            "order_types": order_type_selectors,
            "inputs": input_selectors,
            "actions": action_selectors,
            "tpsl": tpsl_selectors
        }
        
        for category, selectors in all_selectors.items():
            elements[category] = []
            print(f"\n🔍 Checking {category}:")
            
            for selector in selectors:
                try:
                    element = self.page.locator(selector).first
                    if element.is_visible(timeout=1000):
                        element_info = {
                            "selector": selector,
                            "text": element.text_content()[:100] if element.text_content() else "",
                            "is_enabled": element.is_enabled(),
                            "bounding_box": element.bounding_box()
                        }
                        elements[category].append(element_info)
                        print(f"  ✅ {selector}: {element_info['text'][:50]}")
                    else:
                        print(f"  ⚠️ {selector}: not visible")
                except Exception as e:
                    print(f"  ❌ {selector}: {str(e)[:50]}")
        
        self.analysis_data["elements"] = elements
        return elements
    
    def capture_element_screenshots(self):
        """Capture screenshots of key UI areas"""
        print("📸 Capturing element screenshots...")
        
        areas_to_capture = [
            ("full_page", "body"),
            ("trading_panel", ".trading-panel, .order-form, [class*='trading']"),
            ("order_buttons", "button:has-text('Buy'), button:has-text('Sell')"),
            ("leverage_area", "[class*='leverage'], button:has-text('20x')"),
            ("margin_area", "[class*='margin'], button:has-text('Isolated')"),
        ]
        
        screenshots = {}
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        for name, selector in areas_to_capture:
            try:
                filename = f"ui_analysis_{name}_{timestamp}.png"
                
                if name == "full_page":
                    self.page.screenshot(path=filename, full_page=True)
                else:
                    element = self.page.locator(selector).first
                    if element.is_visible(timeout=2000):
                        element.screenshot(path=filename)
                    else:
                        continue
                
                screenshots[name] = filename
                print(f"  ✅ {name}: {filename}")
                
            except Exception as e:
                print(f"  ❌ {name}: {e}")
        
        self.analysis_data["screenshots"] = screenshots
        return screenshots
    
    def extract_page_structure(self):
        """Extract detailed page structure"""
        print("🏗️ Extracting page structure...")
        
        try:
            # Get all interactive elements
            interactive_elements = self.page.evaluate("""
                () => {
                    const elements = [];
                    const selectors = ['button', 'input', 'select', 'a', '[role="button"]', '[class*="btn"]'];
                    
                    selectors.forEach(selector => {
                        document.querySelectorAll(selector).forEach(el => {
                            if (el.offsetParent !== null) { // visible elements
                                elements.push({
                                    tag: el.tagName,
                                    text: el.textContent.trim().substring(0, 100),
                                    className: el.className,
                                    id: el.id,
                                    type: el.type || '',
                                    placeholder: el.placeholder || '',
                                    selector: el.tagName.toLowerCase() + 
                                             (el.id ? '#' + el.id : '') + 
                                             (el.className ? '.' + el.className.split(' ').join('.') : '')
                                });
                            }
                        });
                    });
                    
                    return elements;
                }
            """)
            
            self.analysis_data["interactive_elements"] = interactive_elements
            print(f"✅ Found {len(interactive_elements)} interactive elements")
            
            # Filter trading-related elements
            trading_elements = [
                el for el in interactive_elements 
                if any(keyword in el['text'].lower() for keyword in 
                      ['buy', 'sell', 'open', 'close', 'limit', 'market', 'trigger', 'leverage', 'isolated', 'cross'])
            ]
            
            self.analysis_data["trading_elements"] = trading_elements
            print(f"✅ Found {len(trading_elements)} trading-related elements")
            
        except Exception as e:
            print(f"❌ Failed to extract page structure: {e}")
    
    def save_analysis_report(self):
        """Save complete analysis report"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"mexc_ui_analysis_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.analysis_data, f, indent=2, ensure_ascii=False)
            
            print(f"📊 Analysis report saved: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ Failed to save analysis report: {e}")
            return None
    
    def run_complete_analysis(self):
        """Run complete UI analysis"""
        print("🚀 Starting MEXC UI Analysis")
        print("=" * 40)
        
        if not self.connect_to_browser():
            return False
        
        # Capture current state
        self.capture_page_source()
        self.capture_element_screenshots()
        self.analyze_trading_elements()
        self.extract_page_structure()
        
        # Save report
        report_file = self.save_analysis_report()
        
        print(f"\n📊 Analysis Complete!")
        print(f"Report: {report_file}")
        print(f"Screenshots: {len(self.analysis_data.get('screenshots', {}))}")
        print(f"Interactive elements: {len(self.analysis_data.get('interactive_elements', []))}")
        print(f"Trading elements: {len(self.analysis_data.get('trading_elements', []))}")
        
        return True
    
    def cleanup(self):
        """Clean up resources"""
        try:
            if self.playwright:
                self.playwright.stop()
        except Exception as e:
            print(f"Cleanup error: {e}")

if __name__ == "__main__":
    analyzer = MEXCUIAnalyzer()
    try:
        analyzer.run_complete_analysis()
    except KeyboardInterrupt:
        print("\n👋 Analysis interrupted")
    finally:
        analyzer.cleanup()
