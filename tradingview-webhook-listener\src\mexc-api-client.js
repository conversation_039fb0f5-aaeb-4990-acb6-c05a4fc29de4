const axios = require('axios');
const crypto = require('crypto');

class MexcApiClient {
    constructor(configManager) {
        this.configManager = configManager;
        this.baseURL = 'https://api.mexc.com';
        this.connected = false;
        this.lastBalance = null;
        this.lastBalanceUpdate = null;
    }

    updateConfig(configManager) {
        this.configManager = configManager;
        this.connected = false; // Reset connection status
    }

    isConnected() {
        return this.connected && this.hasValidCredentials();
    }

    hasValidCredentials() {
        const config = this.configManager.getConfig();
        return !!(config.mexcApiKey && config.mexcSecretKey);
    }

    generateSignature(queryString, secretKey) {
        return crypto
            .createHmac('sha256', secretKey)
            .update(queryString)
            .digest('hex');
    }

    async makeRequest(method, endpoint, params = {}, isPrivate = false) {
        const config = this.configManager.getConfig();
        
        if (isPrivate && !this.hasValidCredentials()) {
            throw new Error('MEXC API credentials not configured');
        }

        const timestamp = Date.now();
        let url = `${this.baseURL}${endpoint}`;
        let queryString = '';
        let headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'TradingView-Webhook-Listener/1.0.0'
        };

        if (isPrivate) {
            // Add timestamp and API key
            params.timestamp = timestamp;
            params.recvWindow = 5000;

            // Create query string
            queryString = Object.keys(params)
                .sort()
                .map(key => `${key}=${encodeURIComponent(params[key])}`)
                .join('&');

            // Generate signature
            const signature = this.generateSignature(queryString, config.mexcSecretKey);
            queryString += `&signature=${signature}`;

            headers['X-MEXC-APIKEY'] = config.mexcApiKey;
        } else {
            // For public endpoints, add params to query string
            if (Object.keys(params).length > 0) {
                queryString = Object.keys(params)
                    .map(key => `${key}=${encodeURIComponent(params[key])}`)
                    .join('&');
            }
        }

        if (queryString) {
            url += `?${queryString}`;
        }

        try {
            const response = await axios({
                method,
                url,
                headers,
                timeout: 10000
            });

            if (isPrivate) {
                this.connected = true;
            }

            return response.data;
        } catch (error) {
            if (error.response) {
                const errorMsg = error.response.data?.msg || error.response.data?.message || error.response.statusText;
                throw new Error(`MEXC API Error: ${errorMsg} (${error.response.status})`);
            } else if (error.request) {
                throw new Error('MEXC API: Network error - unable to reach server');
            } else {
                throw new Error(`MEXC API: ${error.message}`);
            }
        }
    }

    async testConnection() {
        try {
            // Test with account info endpoint
            const accountInfo = await this.makeRequest('GET', '/api/v3/account', {}, true);
            this.connected = true;
            return {
                success: true,
                message: 'Successfully connected to MEXC API',
                accountInfo: {
                    canTrade: accountInfo.canTrade,
                    canWithdraw: accountInfo.canWithdraw,
                    canDeposit: accountInfo.canDeposit,
                    updateTime: accountInfo.updateTime
                }
            };
        } catch (error) {
            this.connected = false;
            throw error;
        }
    }

    async getBalance() {
        try {
            const accountInfo = await this.makeRequest('GET', '/api/v3/account', {}, true);
            
            // Find USDT balance
            const usdtBalance = accountInfo.balances.find(balance => balance.asset === 'USDT');
            
            const balanceInfo = {
                asset: 'USDT',
                free: parseFloat(usdtBalance?.free || '0'),
                locked: parseFloat(usdtBalance?.locked || '0'),
                total: parseFloat(usdtBalance?.free || '0') + parseFloat(usdtBalance?.locked || '0'),
                updateTime: accountInfo.updateTime,
                timestamp: new Date().toISOString()
            };

            this.lastBalance = balanceInfo;
            this.lastBalanceUpdate = Date.now();
            this.connected = true; // Mark as connected on successful API call

            return balanceInfo;
        } catch (error) {
            this.connected = false; // Mark as disconnected on API failure
            throw new Error(`Failed to get balance: ${error.message}`);
        }
    }

    async getFuturesBalance() {
        try {
            // For futures trading, we need to use the futures API
            const futuresAccount = await this.makeRequest('GET', '/api/v3/account', {}, true);
            
            // This is a simplified version - in reality, MEXC futures API might be different
            // You may need to adjust this based on MEXC's actual futures API endpoints
            
            const usdtBalance = futuresAccount.balances.find(balance => balance.asset === 'USDT');
            
            return {
                asset: 'USDT',
                availableBalance: parseFloat(usdtBalance?.free || '0'),
                totalWalletBalance: parseFloat(usdtBalance?.free || '0') + parseFloat(usdtBalance?.locked || '0'),
                unrealizedPnl: 0, // Would need to get this from positions
                updateTime: futuresAccount.updateTime,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            throw new Error(`Failed to get futures balance: ${error.message}`);
        }
    }

    async getSymbolInfo(symbol = 'TRUUSDT') {
        try {
            const exchangeInfo = await this.makeRequest('GET', '/api/v3/exchangeInfo');
            const symbolInfo = exchangeInfo.symbols.find(s => s.symbol === symbol);
            
            if (!symbolInfo) {
                throw new Error(`Symbol ${symbol} not found`);
            }

            return {
                symbol: symbolInfo.symbol,
                status: symbolInfo.status,
                baseAsset: symbolInfo.baseAsset,
                quoteAsset: symbolInfo.quoteAsset,
                minQty: symbolInfo.filters.find(f => f.filterType === 'LOT_SIZE')?.minQty,
                maxQty: symbolInfo.filters.find(f => f.filterType === 'LOT_SIZE')?.maxQty,
                stepSize: symbolInfo.filters.find(f => f.filterType === 'LOT_SIZE')?.stepSize,
                minNotional: symbolInfo.filters.find(f => f.filterType === 'MIN_NOTIONAL')?.minNotional
            };
        } catch (error) {
            throw new Error(`Failed to get symbol info: ${error.message}`);
        }
    }

    async getCurrentPrice(symbol = 'TRUUSDT') {
        try {
            const ticker = await this.makeRequest('GET', '/api/v3/ticker/price', { symbol });
            return {
                symbol: ticker.symbol,
                price: parseFloat(ticker.price),
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            throw new Error(`Failed to get current price: ${error.message}`);
        }
    }

    // Get cached balance if recent, otherwise fetch new
    async getCachedBalance(maxAgeMs = 30000) { // 30 seconds default
        if (this.lastBalance && this.lastBalanceUpdate && 
            (Date.now() - this.lastBalanceUpdate) < maxAgeMs) {
            return this.lastBalance;
        }
        
        return await this.getBalance();
    }

    // Validate API credentials without making a trade
    async validateCredentials() {
        try {
            await this.testConnection();
            return { valid: true, message: 'API credentials are valid' };
        } catch (error) {
            return { valid: false, message: error.message };
        }
    }

    // Get account trading status
    async getTradingStatus() {
        try {
            const accountInfo = await this.makeRequest('GET', '/api/v3/account', {}, true);
            return {
                canTrade: accountInfo.canTrade,
                canWithdraw: accountInfo.canWithdraw,
                canDeposit: accountInfo.canDeposit,
                tradingEnabled: accountInfo.canTrade,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            throw new Error(`Failed to get trading status: ${error.message}`);
        }
    }
}

module.exports = MexcApiClient;
