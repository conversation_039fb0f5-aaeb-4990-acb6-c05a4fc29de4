const MexcFuturesTrader = require('./mexc-futures-trader/src/trader');

async function quickOptimizationTest() {
    console.log('⚡ Quick Optimization Test');
    console.log('=' .repeat(40));

    const trader = new MexcFuturesTrader(9223);
    
    try {
        // Test 1: Connection and Background Monitoring
        console.log('🔗 Test 1: Connection and Background Monitoring');
        const connected = await trader.connectToBrowser();
        
        if (!connected) {
            console.error('❌ Connection failed');
            return;
        }
        
        console.log('✅ Connected successfully');
        console.log('✅ Background monitoring should be active');
        
        // Test 2: Background Cleanup
        console.log('\n🧹 Test 2: Manual Background Cleanup');
        try {
            await trader.backgroundCleanup();
            console.log('✅ Background cleanup executed successfully');
        } catch (error) {
            console.log('⚠️ Background cleanup failed:', error.message);
        }
        
        // Test 3: Default Open Tab Positioning Logic
        console.log('\n🎯 Test 3: Default Open Tab Positioning Logic');
        try {
            await trader.prepareForNextTrade('Open Long');
            console.log('✅ Post-execution positioning to Open tab completed');

            await trader.prepareForNextTrade('Close Long');
            console.log('✅ Post-execution positioning to Open tab completed');

            await trader.ensureOpenTabDefault();
            console.log('✅ Background Open tab default positioning completed');
        } catch (error) {
            console.log('⚠️ Tab positioning failed:', error.message);
        }
        
        // Test 4: Execution State Management
        console.log('\n🚦 Test 4: Execution State Management');
        console.log(`Initial execution state: ${trader.isExecutingTrade ? 'EXECUTING' : 'IDLE'}`);
        
        trader.isExecutingTrade = true;
        console.log(`After setting flag: ${trader.isExecutingTrade ? 'EXECUTING' : 'IDLE'}`);
        
        trader.isExecutingTrade = false;
        console.log(`After clearing flag: ${trader.isExecutingTrade ? 'EXECUTING' : 'IDLE'}`);
        console.log('✅ Execution state management working');
        
        // Test 5: Optimized Methods Exist
        console.log('\n🔧 Test 5: Optimized Methods');
        const methods = [
            'selectTabOptimized',
            'prepareForNextTrade',
            'fillQuantityWithOptimizedHandling',
            'backgroundCleanup',
            'ensureOpenTabDefault',
            'checkAndClearQuantityField',
            'checkAndClosePopups',
            'startBackgroundMonitoring',
            'stopBackgroundMonitoring'
        ];
        
        methods.forEach(method => {
            if (typeof trader[method] === 'function') {
                console.log(`✅ ${method} method exists`);
            } else {
                console.log(`❌ ${method} method missing`);
            }
        });
        
        // Test 6: Properties
        console.log('\n📊 Test 6: New Properties');
        const properties = [
            'lastExecutedOrderType',
            'backgroundMonitoringInterval',
            'isExecutingTrade',
            'monitoringIntervalMs'
        ];
        
        properties.forEach(prop => {
            if (trader.hasOwnProperty(prop)) {
                console.log(`✅ ${prop}: ${trader[prop]}`);
            } else {
                console.log(`❌ ${prop} property missing`);
            }
        });
        
        console.log('\n🎉 Quick optimization test completed!');
        console.log('All optimization features are properly implemented.');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    } finally {
        // Cleanup
        try {
            await trader.disconnect();
            console.log('\n🛑 Cleaned up successfully');
        } catch (error) {
            console.log('⚠️ Cleanup error:', error.message);
        }
    }
}

// Run the test
if (require.main === module) {
    quickOptimizationTest().catch(console.error);
}

module.exports = { quickOptimizationTest };
