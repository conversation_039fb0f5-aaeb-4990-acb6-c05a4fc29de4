const { chromium } = require('playwright');

class MEXCPageAnalyzer {
    constructor() {
        this.browser = null;
        this.page = null;
    }

    async connectToRemoteBrowser() {
        console.log('🔗 Connecting to analyze MEXC page...');
        
        try {
            this.browser = await chromium.connectOverCDP('http://localhost:9222');
            const contexts = this.browser.contexts();
            
            if (contexts.length > 0) {
                const context = contexts[0];
                const pages = context.pages();
                this.page = pages.length > 0 ? pages[0] : await context.newPage();
            } else {
                const context = await this.browser.newContext();
                this.page = await context.newPage();
            }
            
            console.log('✅ Connected');
            return true;
        } catch (error) {
            console.error('❌ Connection failed:', error.message);
            return false;
        }
    }

    async analyzePage() {
        console.log('🔍 ANALYZING MEXC PAGE ELEMENTS...');
        console.log('==================================');
        
        try {
            // Get basic page info
            const title = await this.page.title();
            const url = this.page.url();
            console.log(`📄 Page Title: ${title}`);
            console.log(`🔗 URL: ${url}`);
            console.log('');

            // Navigate to TRU_USDT if not already there
            if (!url.includes('TRU')) {
                console.log('🌐 Navigating to TRU_USDT...');
                await this.page.goto('https://www.mexc.com/futures/TRU_USDT', {
                    waitUntil: 'domcontentloaded',
                    timeout: 10000
                });
                await this.page.waitForTimeout(3000);
                console.log('✅ Navigation complete');
            }

            // Analyze all buttons on the page
            console.log('🔍 ANALYZING ALL BUTTONS:');
            console.log('=========================');
            
            const buttons = await this.page.locator('button').all();
            console.log(`Found ${buttons.length} buttons total`);
            
            for (let i = 0; i < Math.min(buttons.length, 20); i++) {
                try {
                    const button = buttons[i];
                    const text = await button.textContent();
                    const isVisible = await button.isVisible();
                    const classes = await button.getAttribute('class');
                    const dataTestId = await button.getAttribute('data-testid');
                    
                    if (isVisible && text && (text.toLowerCase().includes('buy') || text.toLowerCase().includes('sell') || text.toLowerCase().includes('long') || text.toLowerCase().includes('short') || text.toLowerCase().includes('market') || text.toLowerCase().includes('limit'))) {
                        console.log(`Button ${i + 1}:`);
                        console.log(`  Text: "${text.trim()}"`);
                        console.log(`  Classes: ${classes || 'none'}`);
                        console.log(`  Data-testid: ${dataTestId || 'none'}`);
                        console.log(`  Visible: ${isVisible}`);
                        console.log('');
                    }
                } catch (error) {
                    // Skip problematic buttons
                }
            }

            // Analyze input fields
            console.log('🔍 ANALYZING INPUT FIELDS:');
            console.log('==========================');
            
            const inputs = await this.page.locator('input').all();
            console.log(`Found ${inputs.length} input fields total`);
            
            for (let i = 0; i < Math.min(inputs.length, 15); i++) {
                try {
                    const input = inputs[i];
                    const placeholder = await input.getAttribute('placeholder');
                    const type = await input.getAttribute('type');
                    const classes = await input.getAttribute('class');
                    const isVisible = await input.isVisible();
                    
                    if (isVisible && (placeholder || type === 'number')) {
                        console.log(`Input ${i + 1}:`);
                        console.log(`  Placeholder: "${placeholder || 'none'}"`);
                        console.log(`  Type: ${type || 'text'}`);
                        console.log(`  Classes: ${classes || 'none'}`);
                        console.log(`  Visible: ${isVisible}`);
                        console.log('');
                    }
                } catch (error) {
                    // Skip problematic inputs
                }
            }

            // Look for specific trading-related elements
            console.log('🔍 LOOKING FOR SPECIFIC TRADING ELEMENTS:');
            console.log('=========================================');
            
            const tradingSelectors = [
                // Buy/Sell buttons
                'button:has-text("Buy")',
                'button:has-text("Sell")',
                'button:has-text("Long")',
                'button:has-text("Short")',
                '.buy-btn',
                '.sell-btn',
                '.long-btn',
                '.short-btn',
                '[data-testid*="buy"]',
                '[data-testid*="sell"]',
                
                // Order type buttons
                'button:has-text("Market")',
                'button:has-text("Limit")',
                '.market-btn',
                '.limit-btn',
                '[data-testid*="market"]',
                '[data-testid*="limit"]',
                
                // Amount inputs
                'input[placeholder*="amount"]',
                'input[placeholder*="quantity"]',
                'input[placeholder*="size"]',
                'input[type="number"]',
                
                // Submit buttons
                'button:has-text("Place")',
                'button:has-text("Submit")',
                'button:has-text("Confirm")',
                '.submit-btn',
                '.place-order-btn'
            ];

            for (const selector of tradingSelectors) {
                try {
                    const elements = await this.page.locator(selector).all();
                    if (elements.length > 0) {
                        console.log(`✅ Found ${elements.length} elements for: ${selector}`);
                        
                        // Get details of first visible element
                        for (const element of elements) {
                            const isVisible = await element.isVisible();
                            if (isVisible) {
                                const text = await element.textContent();
                                const classes = await element.getAttribute('class');
                                console.log(`   First visible: "${text?.trim()}" (classes: ${classes})`);
                                break;
                            }
                        }
                    }
                } catch (error) {
                    // Element not found
                }
            }

            // Get page HTML structure (limited)
            console.log('\n🔍 PAGE STRUCTURE ANALYSIS:');
            console.log('===========================');
            
            try {
                // Look for trading container
                const tradingContainers = await this.page.locator('[class*="trading"], [class*="order"], [class*="trade"]').all();
                console.log(`Found ${tradingContainers.length} potential trading containers`);
                
                for (let i = 0; i < Math.min(tradingContainers.length, 3); i++) {
                    const container = tradingContainers[i];
                    const classes = await container.getAttribute('class');
                    const isVisible = await container.isVisible();
                    if (isVisible) {
                        console.log(`Trading container ${i + 1}: ${classes}`);
                    }
                }
            } catch (error) {
                console.log('Could not analyze trading containers');
            }

            console.log('\n✅ Page analysis complete!');
            return true;

        } catch (error) {
            console.error('❌ Page analysis failed:', error.message);
            return false;
        }
    }

    async testElementInteraction() {
        console.log('\n🧪 TESTING ELEMENT INTERACTIONS:');
        console.log('=================================');
        
        try {
            // Test clicking on various elements to see what works
            const testSelectors = [
                'button:has-text("Buy")',
                'button:has-text("Long")',
                '.buy-btn',
                '[class*="buy"]',
                'button:has-text("Market")',
                '.market-btn'
            ];

            for (const selector of testSelectors) {
                try {
                    const elements = await this.page.locator(selector).all();
                    if (elements.length > 0) {
                        const element = elements[0];
                        const isVisible = await element.isVisible();
                        const isEnabled = await element.isEnabled();
                        const text = await element.textContent();
                        
                        console.log(`Testing: ${selector}`);
                        console.log(`  Text: "${text?.trim()}"`);
                        console.log(`  Visible: ${isVisible}`);
                        console.log(`  Enabled: ${isEnabled}`);
                        
                        if (isVisible && isEnabled) {
                            console.log(`  ✅ Ready for interaction`);
                        } else {
                            console.log(`  ❌ Not ready for interaction`);
                        }
                        console.log('');
                    }
                } catch (error) {
                    console.log(`❌ Error testing ${selector}: ${error.message}`);
                }
            }

        } catch (error) {
            console.error('❌ Element interaction test failed:', error.message);
        }
    }
}

async function runPageAnalyzer() {
    const analyzer = new MEXCPageAnalyzer();
    
    try {
        const connected = await analyzer.connectToRemoteBrowser();
        if (!connected) {
            throw new Error('Could not connect to remote browser');
        }

        await analyzer.analyzePage();
        await analyzer.testElementInteraction();
        
        console.log('\n🎯 ANALYSIS COMPLETE!');
        console.log('Use the findings above to optimize the trading bot.');
        
        return true;
        
    } catch (error) {
        console.error('💥 Page analyzer failed:', error.message);
        return false;
    }
}

if (require.main === module) {
    console.log('🔍 MEXC PAGE ANALYZER');
    console.log('=====================');
    console.log('📋 Analyzing MEXC trading interface elements');
    console.log('🎯 Finding optimal selectors for fast trading');
    console.log('');
    
    runPageAnalyzer()
        .then(result => {
            console.log('\n🏁 Analysis session completed');
            process.exit(result ? 0 : 1);
        })
        .catch(error => {
            console.error('💥 Analysis crashed:', error);
            process.exit(1);
        });
}

module.exports = MEXCPageAnalyzer;
