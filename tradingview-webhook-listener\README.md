# TradingView Webhook Listener

Advanced TradingView webhook listener with MEXC API integration, smart money management, and responsive web interface. Automatically executes TRUUSDT futures trades based on TradingView signals with customizable position sizing.

## 🚀 Features

- **TradingView Integration**: Receives webhook signals from TradingView alerts
- **MEXC API Integration**: Real-time balance monitoring and account management
- **Smart Money Management**: 
  - Percentage-based position sizing (use X% of balance)
  - Fixed amount trading (use fixed $X per trade)
  - Intelligent fallback when balance < fixed amount
- **Responsive Web Interface**: Configure settings from any device
- **Real-time Monitoring**: Live balance, trade history, and statistics
- **Risk Management**: Min/max trade limits and remaining balance protection
- **High-Speed Execution**: Integrates with MEXC Futures Trader service
- **Comprehensive Logging**: Full audit trail and error tracking

## 📋 Prerequisites

- **Node.js** 16.0.0 or higher
- **MEXC Futures Trader Service** running on port 3000
- **MEXC Account** with API access enabled
- **TradingView** account with webhook alerts

## 🛠️ Installation

1. **Navigate to the service directory:**
   ```bash
   cd tradingview-webhook-listener
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Start the service:**
   ```bash
   npm start
   ```

4. **Access the dashboard:**
   ```
   http://localhost:4000
   ```

## 🌐 Configuration

### Step 1: Web Interface Setup

1. **Open the dashboard:** `http://localhost:4000`
2. **Configure MEXC API:**
   - Enter your MEXC API Key
   - Enter your MEXC Secret Key
   - Click "Test Connection" to verify

3. **Set up Money Management:**
   - **Enable/Disable:** Toggle money management on/off
   - **Mode Selection:**
     - **Percentage:** Use X% of available balance
     - **Fixed Amount:** Use fixed $X per trade
   - **Configure limits:** Min/max trade amounts

4. **Activate the Bot:**
   - Toggle "Bot Active" to start receiving signals
   - Save settings

### Step 2: TradingView Webhook Setup

1. **Create TradingView Alert**
2. **Set Webhook URL:** `http://your-server:4000/webhook`
3. **Configure Alert Message:**
   ```json
   {
       "symbol": "TRUUSDT",
       "trade": "open",
       "last_price": "{{close}}",
       "leverage": "2"
   }
   ```

   For close signals:
   ```json
   {
       "symbol": "TRUUSDT", 
       "trade": "close",
       "last_price": "{{close}}",
       "leverage": "2"
   }
   ```

## 💰 Money Management Modes

### Mode 1: Percentage-Based
- **Setting:** 50% → Uses half of available balance
- **Setting:** 100% → Uses entire available balance
- **Example:** Balance: $1000, Setting: 50% → Trade: $500

### Mode 2: Fixed Amount
- **Setting:** $100 fixed amount
- **If balance > $100:** Uses exactly $100
- **If balance < $100 AND percentage = 100%:** Uses entire balance
- **If balance < $100 AND percentage < 100%:** Uses percentage of balance

### Examples:
| Balance | Fixed Amount | Percentage | Result |
|---------|--------------|------------|---------|
| $1000 | $100 | 50% | $100 (fixed amount) |
| $50 | $100 | 100% | $50 (entire balance) |
| $50 | $100 | 50% | $25 (50% of balance) |

## 🔗 API Endpoints

### Webhook Endpoint
```bash
POST /webhook
Content-Type: application/json

{
  "symbol": "TRUUSDT",
  "trade": "open",
  "last_price": "0.*********", 
  "leverage": "2"
}
```

### Configuration Endpoints
```bash
# Get current configuration
GET /api/config

# Update configuration
POST /api/config
{
  "botActive": true,
  "moneyManagementEnabled": true,
  "positionSizePercentage": 50,
  "fixedTradeAmount": 100
}

# Get system status
GET /api/status

# Get account balance
GET /api/balance

# Get trade history
GET /api/trades

# Test webhook
POST /api/test-webhook
```

## 📊 Dashboard Features

### Real-time Status Indicators
- **Bot Status:** Active/Inactive/Not Configured
- **MEXC API:** Connected/Disconnected
- **Trading Service:** Ready/Not Ready
- **Balance:** Live USDT balance

### Configuration Panels
- **Bot Control:** Activate/deactivate trading
- **API Configuration:** MEXC credentials setup
- **Money Management:** Position sizing settings
- **Trade Limits:** Risk management controls

### Statistics & Monitoring
- **Signals Received:** Total TradingView signals
- **Trades Executed:** Successful trade count
- **Success Rate:** Trade success percentage
- **Recent Trades:** Live trade history with details

## 🔧 Integration with MEXC Futures Trader

This service connects to the MEXC Futures Trader service:

1. **Ensure MEXC Futures Trader is running:**
   ```bash
   cd ../mexc-futures-trader
   npm start  # Should be running on port 3000
   ```

2. **Verify connection:**
   - Dashboard will show "Trading Service: Ready" when connected
   - Test webhook will execute actual trades

## 🧪 Testing

### Test Webhook Locally
```bash
curl -X POST http://localhost:4000/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "TRUUSDT",
    "trade": "open", 
    "last_price": "0.*********",
    "leverage": "2"
  }'
```

### Test via Dashboard
1. Open dashboard: `http://localhost:4000`
2. Click "Test Webhook" button
3. Monitor trade execution in real-time

## 🔒 Security Features

- **API Key Protection:** Keys are masked in UI
- **Rate Limiting:** Prevents webhook spam
- **Input Validation:** Comprehensive payload validation
- **Error Handling:** Graceful failure management
- **Audit Logging:** Complete trade audit trail

## 📝 Supported Signals

### Signal Format
```json
{
  "symbol": "TRUUSDT",        // Only TRUUSDT supported
  "trade": "open|close",      // Action type
  "last_price": "0.*********", // Current price
  "leverage": "2"             // Leverage (informational)
}
```

### Signal Processing
- **"open"** → Executes "Open Long" order
- **"close"** → Executes "Close Long" order
- Only TRUUSDT symbol supported
- Automatic position size calculation based on money management settings

## 🚨 Troubleshooting

### Common Issues

1. **"MEXC API not configured"**
   - Enter API credentials in dashboard
   - Test connection to verify

2. **"Trading Service not available"**
   - Ensure MEXC Futures Trader is running on port 3000
   - Check browser setup (ports 9222, 9223)

3. **"Insufficient balance"**
   - Check MEXC account balance
   - Adjust money management settings

4. **Webhook not received**
   - Verify TradingView webhook URL
   - Check firewall/network settings
   - Ensure service is running on port 4000

### Debug Steps
1. **Check service logs:** Monitor console output
2. **Test API connection:** Use dashboard test button
3. **Verify webhook format:** Ensure JSON matches expected format
4. **Check balance:** Ensure sufficient USDT balance

## 🎯 Production Deployment

### Environment Variables
```bash
PORT=4000                    # Service port
LOG_LEVEL=info              # Logging level
MEXC_TRADER_URL=http://localhost:3000  # MEXC Trader service URL
```

### Process Management
```bash
# Using PM2
npm install -g pm2
pm2 start src/server.js --name "tradingview-webhook"
pm2 save
pm2 startup
```

## 📈 Performance

- **Webhook Response:** < 100ms
- **Trade Execution:** 3-8 seconds (via MEXC Trader)
- **Balance Updates:** Real-time via MEXC API
- **Dashboard Refresh:** Every 5 seconds

---

**⚡ Ready to receive TradingView signals and execute high-speed MEXC trades!** 🚀
