const axios = require('axios');

async function checkServices() {
    console.log('🔍 Checking if services are running...\n');

    const services = [
        { name: 'Webhook Listener', url: 'http://localhost:4000/api/status' },
        { name: 'MEXC Trader', url: 'http://localhost:3000/health' },
        { name: 'Webhook Endpoint', url: 'http://localhost:4000/webhook' }
    ];

    for (const service of services) {
        try {
            const response = await axios.get(service.url, { timeout: 5000 });
            console.log(`✅ ${service.name}: Running (${response.status})`);
            if (response.data) {
                console.log(`   Data:`, response.data);
            }
        } catch (error) {
            if (error.code === 'ECONNREFUSED') {
                console.log(`❌ ${service.name}: Not running (Connection refused)`);
            } else if (error.response) {
                console.log(`⚠️ ${service.name}: Responding but with error (${error.response.status})`);
                console.log(`   Error:`, error.response.data);
            } else {
                console.log(`❌ ${service.name}: ${error.message}`);
            }
        }
        console.log('');
    }

    // Test a simple webhook call
    console.log('📡 Testing webhook endpoint...');
    try {
        const testPayload = {
            symbol: "TRUUSDT",
            trade: "buy",
            last_price: "0.000012064",
            leverege: "2"
        };

        const response = await axios.post('http://localhost:4000/webhook', testPayload, {
            timeout: 10000,
            headers: { 'Content-Type': 'application/json' }
        });

        console.log('✅ Webhook test successful!');
        console.log('Response:', JSON.stringify(response.data, null, 2));
    } catch (error) {
        console.log('❌ Webhook test failed:');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Data:', error.response.data);
        } else {
            console.log('Error:', error.message);
        }
    }
}

checkServices();
