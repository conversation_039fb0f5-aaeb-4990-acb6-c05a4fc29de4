#!/usr/bin/env python3
"""
Option B: Real-time Browser Integration
Uses browser to generate real parameters and executes via direct API
"""

import json
import time
import hashlib
import hmac
import random
import string
from playwright.sync_api import sync_playwright
from curl_cffi import requests
from dotenv import dotenv_values

class RealTimeBrowserIntegration:
    """Real-time browser integration for parameter extraction and API execution"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        self.uc_token = vals.get('uc_token')
        self.session = requests.Session(impersonate='chrome124')
        
        print("🔗 Real-time Browser Integration System")
        print("="*45)
        print("🎯 Strategy: Browser generates real parameters → Direct API execution")
    
    def setup_browser_session(self):
        """Setup browser session with parameter extraction capabilities"""
        
        print("\n🌐 Setting up browser session...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                print("❌ No browser contexts found")
                return False
            
            self.context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                mexc_page = self.context.new_page()
                mexc_page.goto('https://futures.mexc.com/exchange/BTC_USDT', wait_until='domcontentloaded')
            
            self.page = mexc_page
            
            # Inject session tokens
            self.page.evaluate(f"""
                () => {{
                    localStorage.setItem('authorization', '{self.auth}');
                    localStorage.setItem('u_id', '{self.auth}');
                    {f"localStorage.setItem('uc_token', '{self.uc_token}');" if self.uc_token else ""}
                }}
            """)
            
            # Reload to apply session
            self.page.reload(wait_until='domcontentloaded')
            time.sleep(3)
            
            # Setup network interception
            self.setup_network_interception()

            # Inject parameter extraction system
            self._inject_parameter_extraction_system()

            print("✅ Browser session setup completed")
            return True
            
        except Exception as e:
            print(f"❌ Browser setup failed: {e}")
            return False
    
    def _inject_parameter_extraction_system(self):
        """Inject advanced parameter extraction system into browser"""

        print("🔧 Injecting parameter extraction system...")

        extraction_system = """
            // Real-time Parameter Extraction System
            window.mexcRealTimeExtractor = {
                // Capture real network requests
                capturedRequests: [],
                originalFetch: window.fetch,
                originalXHR: window.XMLHttpRequest,

                // Initialize request interception
                init() {
                    const self = this;

                    // Override fetch to capture real requests
                    window.fetch = function(...args) {
                        const [url, options] = args;

                        // Log all requests for debugging
                        console.log('🌐 Fetch request:', url, options);

                        // Capture order-related requests
                        if (url.includes('order/create') || url.includes('order/submit') || url.includes('order/place')) {
                            const headers = options.headers || {};
                            let body = null;

                            try {
                                if (options.body) {
                                    if (typeof options.body === 'string') {
                                        body = JSON.parse(options.body);
                                    } else {
                                        body = options.body;
                                    }
                                }
                            } catch (e) {
                                body = options.body;
                            }

                            self.capturedRequests.push({
                                url: url,
                                headers: headers,
                                body: body,
                                timestamp: Date.now()
                            });

                            console.log('🎯 Captured real order request:', {
                                signature: headers['x-mxc-sign'],
                                nonce: headers['x-mxc-nonce'],
                                mtoken: headers['mtoken'],
                                authorization: headers['authorization']
                            });
                        }

                        return self.originalFetch.apply(this, args);
                    };

                    // Also override XMLHttpRequest
                    window.XMLHttpRequest = function() {
                        const xhr = new self.originalXHR();
                        const originalOpen = xhr.open;
                        const originalSend = xhr.send;
                        const originalSetRequestHeader = xhr.setRequestHeader;

                        let requestHeaders = {};
                        let requestUrl = '';
                        let requestBody = null;

                        xhr.open = function(method, url, ...args) {
                            requestUrl = url;
                            return originalOpen.apply(this, [method, url, ...args]);
                        };

                        xhr.setRequestHeader = function(name, value) {
                            requestHeaders[name] = value;
                            return originalSetRequestHeader.apply(this, [name, value]);
                        };

                        xhr.send = function(body) {
                            requestBody = body;

                            // Capture order requests
                            if (requestUrl.includes('order/create') || requestUrl.includes('order/submit') || requestUrl.includes('order/place')) {
                                let parsedBody = null;
                                try {
                                    if (body && typeof body === 'string') {
                                        parsedBody = JSON.parse(body);
                                    } else {
                                        parsedBody = body;
                                    }
                                } catch (e) {
                                    parsedBody = body;
                                }

                                self.capturedRequests.push({
                                    url: requestUrl,
                                    headers: requestHeaders,
                                    body: parsedBody,
                                    timestamp: Date.now(),
                                    method: 'XHR'
                                });

                                console.log('🎯 Captured XHR order request:', {
                                    signature: requestHeaders['x-mxc-sign'],
                                    nonce: requestHeaders['x-mxc-nonce'],
                                    mtoken: requestHeaders['mtoken'],
                                    authorization: requestHeaders['authorization']
                                });
                            }

                            return originalSend.apply(this, [body]);
                        };

                        return xhr;
                    };

                    console.log('✅ Real-time extraction system initialized');
                },
                
                // Simulate order to capture real parameters
                async simulateOrder(symbol, side, price, volume) {
                    try {
                        console.log('🎯 Starting order simulation for', symbol, side, price, volume);

                        // Clear previous captures
                        this.capturedRequests = [];

                        // Get current timestamp for nonce
                        const nonce = Date.now().toString();

                        // Prepare order data with realistic structure
                        const orderData = {
                            symbol: symbol,
                            side: side,
                            openType: 1,
                            type: '2',
                            vol: volume,
                            leverage: 1,
                            marketCeiling: false,
                            price: price.toString(),
                            priceProtect: '0'
                        };

                        // Try to find and analyze existing order functions in the page
                        console.log('🔍 Analyzing page for order functions...');

                        // Look for React components or Vue instances that might handle orders
                        const reactElements = document.querySelectorAll('[data-reactroot], [data-react-checksum]');
                        console.log('Found React elements:', reactElements.length);

                        // Look for order-related buttons and forms
                        const orderButtons = document.querySelectorAll('button, input[type="submit"]');
                        for (const button of orderButtons) {
                            const text = button.textContent || button.value || '';
                            if (text.toLowerCase().includes('order') || text.toLowerCase().includes('buy') || text.toLowerCase().includes('sell')) {
                                console.log('Found order button:', text);
                            }
                        }

                        // Try to access window objects that might contain order functions
                        const windowKeys = Object.keys(window);
                        const orderRelatedKeys = windowKeys.filter(key =>
                            key.toLowerCase().includes('order') ||
                            key.toLowerCase().includes('trade') ||
                            key.toLowerCase().includes('mexc')
                        );
                        console.log('Order-related window objects:', orderRelatedKeys);

                        // Generate signature using available information
                        const auth = localStorage.getItem('authorization') || '';
                        const signature = this.generateAdvancedSignature(orderData, nonce, auth);

                        // Try to make a test request to capture real parameters
                        console.log('🚀 Making test request...');

                        try {
                            const testResponse = await fetch('/api/v1/private/order/create', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'Accept': 'application/json',
                                    'x-mxc-nonce': nonce,
                                    'x-mxc-sign': signature,
                                    'authorization': auth,
                                    'mtoken': localStorage.getItem('uc_token') || '',
                                    'x-language': 'en_US'
                                },
                                body: JSON.stringify(orderData),
                                credentials: 'include'
                            });

                            console.log('Test response status:', testResponse.status);

                            if (testResponse.status === 200) {
                                const responseData = await testResponse.json();
                                console.log('Test response data:', responseData);
                            }

                        } catch (e) {
                            console.log('Test request error (expected):', e.message);
                        }

                        // Check if we captured any real requests
                        if (this.capturedRequests.length > 0) {
                            const captured = this.capturedRequests[0];
                            console.log('✅ Captured real request parameters');
                            return {
                                success: true,
                                nonce: captured.headers['x-mxc-nonce'],
                                signature: captured.headers['x-mxc-sign'],
                                mtoken: captured.headers['mtoken'],
                                authorization: captured.headers['authorization'],
                                orderData: captured.body,
                                hasRealSignature: true,
                                captureMethod: captured.method || 'fetch'
                            };
                        } else {
                            // Return our generated parameters
                            console.log('📝 Using generated parameters');
                            return {
                                success: true,
                                nonce: nonce,
                                signature: signature,
                                mtoken: localStorage.getItem('uc_token'),
                                authorization: auth,
                                orderData: orderData,
                                hasRealSignature: false,
                                captureMethod: 'generated'
                            };
                        }

                    } catch (error) {
                        console.error('❌ Order simulation error:', error);
                        return {
                            success: false,
                            error: error.toString()
                        };
                    }
                },
                
                // Advanced signature generation (reverse engineered)
                generateAdvancedSignature(orderData, nonce, auth) {
                    try {
                        // Method 1: Try to use crypto.subtle if available
                        if (window.crypto && window.crypto.subtle) {
                            // We'll use a synchronous approach for now
                        }

                        // Method 2: Enhanced hash-based signature
                        const jsonStr = JSON.stringify(orderData, Object.keys(orderData).sort());

                        // Create signature string in the format that MEXC likely uses
                        // Common patterns: auth + nonce + body, or nonce + body + auth
                        const signatureStrings = [
                            auth + nonce + jsonStr,
                            nonce + jsonStr + auth,
                            auth + jsonStr + nonce,
                            jsonStr + nonce + auth
                        ];

                        // Try different hash algorithms
                        let bestSignature = '';

                        for (const sigStr of signatureStrings) {
                            // Enhanced hash function
                            let hash = 0x811c9dc5; // FNV offset basis
                            for (let i = 0; i < sigStr.length; i++) {
                                hash ^= sigStr.charCodeAt(i);
                                hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);
                            }

                            const signature = Math.abs(hash).toString(16).padStart(32, '0');
                            if (signature.length >= 32) {
                                bestSignature = signature;
                                break;
                            }
                        }

                        // Fallback to MD5-like hash if available
                        if (!bestSignature && typeof CryptoJS !== 'undefined') {
                            bestSignature = CryptoJS.MD5(auth + nonce + jsonStr).toString();
                        }

                        // Final fallback
                        if (!bestSignature) {
                            bestSignature = this.simpleHash(auth + nonce + jsonStr);
                        }

                        console.log('Generated signature:', bestSignature);
                        return bestSignature;

                    } catch (error) {
                        console.error('Signature generation error:', error);
                        return this.simpleHash(auth + nonce + JSON.stringify(orderData));
                    }
                },

                // Simple hash function as fallback
                simpleHash(str) {
                    let hash = 0;
                    if (str.length === 0) return hash.toString(16);

                    for (let i = 0; i < str.length; i++) {
                        const char = str.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash & hash; // Convert to 32-bit integer
                    }

                    return Math.abs(hash).toString(16).padStart(32, '0');
                },
                
                // Test authentication
                async testAuth() {
                    try {
                        console.log('🔐 Testing authentication...');

                        // Try multiple endpoints to test auth
                        const testEndpoints = [
                            '/api/v1/private/order/list/open_orders?page_num=1&page_size=5',
                            '/api/v1/private/account/assets',
                            '/api/v1/private/position/list/open_positions'
                        ];

                        for (const endpoint of testEndpoints) {
                            try {
                                const response = await this.originalFetch(endpoint, {
                                    credentials: 'include',
                                    headers: {
                                        'authorization': localStorage.getItem('authorization'),
                                        'mtoken': localStorage.getItem('uc_token')
                                    }
                                });

                                console.log(`Auth test ${endpoint}:`, response.status);

                                if (response.status === 200) {
                                    const data = await response.json();
                                    console.log(`Auth test result:`, data);

                                    return {
                                        success: data.code === 0,
                                        code: data.code,
                                        message: data.message || 'OK',
                                        endpoint: endpoint
                                    };
                                }
                            } catch (e) {
                                console.log(`Auth test ${endpoint} failed:`, e.message);
                            }
                        }

                        return {
                            success: false,
                            error: 'All auth tests failed'
                        };

                    } catch (error) {
                        return {
                            success: false,
                            error: error.toString()
                        };
                    }
                }
            };
            
            // Initialize the system
            window.mexcRealTimeExtractor.init();
            console.log('🚀 MEXC Real-time Parameter Extraction System ready');
        """
        
        self.page.evaluate(extraction_system)
        print("✅ Parameter extraction system injected")
    
    def setup_network_interception(self):
        """Setup network request interception to capture real signatures"""

        print("🕸️ Setting up network interception...")

        self.captured_requests = []

        def handle_request(request):
            """Handle intercepted requests"""
            if 'order/create' in request.url or 'order/submit' in request.url:
                print(f"🎯 Intercepted order request: {request.url}")
                print(f"📋 Headers: {request.headers}")

                # Store the request details
                self.captured_requests.append({
                    'url': request.url,
                    'method': request.method,
                    'headers': dict(request.headers),
                    'post_data': request.post_data,
                    'timestamp': time.time()
                })

        def handle_response(response):
            """Handle intercepted responses"""
            if 'order/create' in response.url or 'order/submit' in response.url:
                print(f"📥 Intercepted order response: {response.status}")

        # Enable request interception
        self.page.on('request', handle_request)
        self.page.on('response', handle_response)

        print("✅ Network interception enabled")

    def extract_real_parameters(self, symbol: str, side: int, price: float, volume: int = 1):
        """Extract real parameters using browser"""

        print(f"\n🔍 Extracting real parameters for {symbol} @ ${price}")

        try:
            # Clear previous captures
            self.captured_requests = []

            # Test if our extractor is available
            extractor_available = self.page.evaluate("() => typeof window.mexcRealTimeExtractor !== 'undefined'")

            if not extractor_available:
                print("⚠️ Extractor not available, using direct approach")
                return self._extract_parameters_direct(symbol, side, price, volume)

            # Test authentication first
            try:
                auth_test = self.page.evaluate("() => window.mexcRealTimeExtractor.testAuth()")
                if not auth_test.get('success'):
                    print(f"❌ Browser authentication failed: {auth_test}")
                    return self._extract_parameters_direct(symbol, side, price, volume)

                print("✅ Browser authentication successful")
            except Exception as e:
                print(f"⚠️ Auth test failed: {e}, using direct approach")
                return self._extract_parameters_direct(symbol, side, price, volume)

            # Extract parameters using JavaScript
            try:
                result = self.page.evaluate(f"""
                    () => window.mexcRealTimeExtractor.simulateOrder('{symbol}', {side}, {price}, {volume})
                """)

                if result and result.get('success'):
                    print("✅ Parameters extracted successfully")
                    print(f"🔐 Has real signature: {result.get('hasRealSignature', False)}")
                    return result
                else:
                    print(f"❌ Parameter extraction failed: {result}")
                    return self._extract_parameters_direct(symbol, side, price, volume)
            except Exception as e:
                print(f"❌ JavaScript extraction error: {e}")
                return self._extract_parameters_direct(symbol, side, price, volume)

        except Exception as e:
            print(f"❌ Parameter extraction error: {e}")
            return self._extract_parameters_direct(symbol, side, price, volume)

    def _extract_parameters_direct(self, symbol: str, side: int, price: float, volume: int = 1):
        """Direct parameter extraction without JavaScript"""

        print("🔧 Using direct parameter extraction...")

        try:
            # First, try to trigger a real order interface to capture requests
            print("🎯 Attempting to trigger real order interface...")

            # Navigate to the trading page
            self.page.goto(f'https://futures.mexc.com/exchange/{symbol}', wait_until='domcontentloaded')
            time.sleep(3)

            # Try to interact with order form elements
            try:
                # Look for price input
                price_inputs = self.page.query_selector_all('input[type="text"], input[type="number"]')
                for input_elem in price_inputs:
                    placeholder = input_elem.get_attribute('placeholder') or ''
                    if 'price' in placeholder.lower():
                        print(f"🎯 Found price input: {placeholder}")
                        input_elem.fill(str(price))
                        break

                # Look for volume input
                for input_elem in price_inputs:
                    placeholder = input_elem.get_attribute('placeholder') or ''
                    if 'amount' in placeholder.lower() or 'quantity' in placeholder.lower():
                        print(f"🎯 Found volume input: {placeholder}")
                        input_elem.fill(str(volume))
                        break

                # Look for buy/sell buttons
                buttons = self.page.query_selector_all('button')
                order_button = None
                for button in buttons:
                    text = button.text_content() or ''
                    if ('buy' in text.lower() and side == 1) or ('sell' in text.lower() and side == 2):
                        order_button = button
                        print(f"🎯 Found order button: {text}")
                        break

                # Clear captured requests
                self.captured_requests = []

                # Click the order button (but don't actually submit)
                if order_button:
                    print("🔍 Clicking order button to capture requests...")
                    # Just hover to trigger any preparation
                    order_button.hover()
                    time.sleep(1)

                    # Try to click but intercept before actual submission
                    try:
                        order_button.click(timeout=2000)
                    except:
                        pass  # Expected to fail or timeout

                # Check if we captured any requests
                if self.captured_requests:
                    print(f"✅ Captured {len(self.captured_requests)} real requests!")
                    captured = self.captured_requests[0]

                    # Parse the captured request
                    headers = captured.get('headers', {})
                    return {
                        'success': True,
                        'nonce': headers.get('x-mxc-nonce'),
                        'signature': headers.get('x-mxc-sign'),
                        'mtoken': headers.get('mtoken'),
                        'authorization': headers.get('authorization'),
                        'orderData': json.loads(captured.get('post_data', '{}')),
                        'hasRealSignature': True,
                        'captureMethod': 'real_browser_capture'
                    }

            except Exception as e:
                print(f"⚠️ Order interface interaction failed: {e}")

            # Fallback to token-based generation
            print("🔄 Falling back to token-based generation...")

            # Get authentication tokens from browser
            auth_tokens = self.page.evaluate("""
                () => ({
                    authorization: localStorage.getItem('authorization'),
                    uc_token: localStorage.getItem('uc_token'),
                    u_id: localStorage.getItem('u_id'),
                    fingerprint: localStorage.getItem('mexc_fingerprint_visitorId') || document.querySelector('meta[name="fingerprint"]')?.content
                })
            """)

            # Generate nonce
            nonce = str(int(time.time() * 1000))

            # Prepare order data with additional fields that might be required
            order_data = {
                'symbol': symbol,
                'side': side,
                'openType': 1,
                'type': '2',
                'vol': volume,
                'leverage': 1,
                'marketCeiling': False,
                'price': str(price),
                'priceProtect': '0'
            }

            # Add fingerprint if available
            if auth_tokens.get('fingerprint'):
                order_data['fingerprint'] = auth_tokens['fingerprint']

            # Generate signature using Python
            signature = self._generate_signature(order_data, nonce, auth_tokens.get('authorization', ''))

            return {
                'success': True,
                'nonce': nonce,
                'signature': signature,
                'mtoken': auth_tokens.get('uc_token'),
                'authorization': auth_tokens.get('authorization'),
                'orderData': order_data,
                'hasRealSignature': False,
                'captureMethod': 'python_generated'
            }

        except Exception as e:
            print(f"❌ Direct extraction error: {e}")
            return None

    def _generate_signature(self, order_data: dict, nonce: str, auth: str) -> str:
        """Generate signature using Python (reverse engineered)"""

        import hashlib
        import hmac

        # Try different signature algorithms
        json_str = json.dumps(order_data, separators=(',', ':'), sort_keys=True)

        # Method 1: Simple concatenation + hash
        content1 = f"{auth}{nonce}{json_str}"
        signature1 = hashlib.md5(content1.encode()).hexdigest()

        # Method 2: Different order
        content2 = f"{nonce}{json_str}{auth}"
        signature2 = hashlib.sha256(content2.encode()).hexdigest()[:32]

        # Method 3: HMAC approach
        try:
            signature3 = hmac.new(auth.encode(), f"{nonce}{json_str}".encode(), hashlib.sha256).hexdigest()[:32]
        except:
            signature3 = signature1

        # Return the most likely candidate (we'll test all)
        print(f"🔐 Generated signatures: {signature1[:8]}..., {signature2[:8]}..., {signature3[:8]}...")

        return signature1  # Start with MD5 approach
    
    def execute_order_direct(self, params: dict):
        """Execute order using extracted parameters via direct API"""

        if not params or not params.get('success'):
            return {'success': False, 'error': 'Invalid parameters'}

        print(f"\n🚀 Executing order with real-time parameters...")

        order_data = params.get('orderData', {})
        nonce = params.get('nonce')
        auth = params.get('authorization', self.auth)

        print(f"📋 Order data: {json.dumps(order_data, indent=2)}")
        print(f"🎯 Real signature: {params.get('hasRealSignature', False)}")

        # Try multiple signature approaches if we don't have a real one
        signatures_to_try = []

        if params.get('hasRealSignature'):
            signatures_to_try.append(params.get('signature'))
        else:
            # Generate multiple signature candidates
            signatures_to_try.extend(self._generate_multiple_signatures(order_data, nonce, auth))

        # Try each signature
        for i, signature in enumerate(signatures_to_try):
            print(f"\n🔐 Trying signature {i+1}/{len(signatures_to_try)}: {signature[:16]}...")

            # Get additional headers from browser
            browser_headers = self.page.evaluate("""
                () => ({
                    userAgent: navigator.userAgent,
                    fingerprint: localStorage.getItem('mexc_fingerprint_visitorId'),
                    deviceId: localStorage.getItem('mexc_device_id')
                })
            """)

            # Prepare headers with all required fields
            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br, zstd',
                'Origin': 'https://futures.mexc.com',
                'Referer': f'https://futures.mexc.com/exchange/{order_data.get("symbol", "BTC_USDT")}',
                'Content-Type': 'application/json',
                'User-Agent': browser_headers.get('userAgent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
                'authorization': auth,
                'x-mxc-nonce': nonce,
                'x-mxc-sign': signature,
                'x-language': 'en_US',
            }

            # Add optional headers
            if params.get('mtoken'):
                headers['mtoken'] = params.get('mtoken')

            if browser_headers.get('fingerprint'):
                headers['x-mxc-fingerprint'] = browser_headers['fingerprint']

            if browser_headers.get('deviceId'):
                headers['x-mxc-device-id'] = browser_headers['deviceId']

            # Try create endpoint
            try:
                mhash = ''.join(random.choices(string.ascii_lowercase + string.digits, k=32))
                url = f'https://futures.mexc.com/api/v1/private/order/create?mhash={mhash}'

                r = self.session.post(url, json=order_data, headers=headers)

                print(f"Response status: {r.status_code}")

                if r.status_code == 200:
                    try:
                        result = r.json()
                        print(f"Response: {json.dumps(result, indent=2)}")

                        if result.get('success') and result.get('code') == 0:
                            print(f"🎉 SUCCESS with signature {i+1}!")
                            return {
                                'success': True,
                                'result': result,
                                'order_id': result.get('data', {}).get('orderId'),
                                'working_signature': signature
                            }
                        else:
                            error_code = result.get('code')
                            error_msg = result.get('message', '')
                            print(f"❌ Order failed: {error_code} - {error_msg}")

                            # If it's a signature error, try next signature
                            if 'sign' in error_msg.lower() or error_code in [10001, 10002, 10003]:
                                continue
                            else:
                                # Other error, don't try more signatures
                                return {'success': False, 'error': f'{error_code}: {error_msg}'}
                    except json.JSONDecodeError:
                        print(f"❌ Invalid JSON response")
                        continue
                else:
                    print(f"❌ HTTP {r.status_code}")
                    if r.status_code == 401 or r.status_code == 403:
                        continue  # Try next signature
                    else:
                        return {'success': False, 'error': f'HTTP {r.status_code}'}

            except Exception as e:
                print(f"❌ Exception: {e}")
                continue

        return {'success': False, 'error': 'All signature attempts failed'}

    def _generate_multiple_signatures(self, order_data: dict, nonce: str, auth: str) -> list:
        """Generate multiple signature candidates"""

        signatures = []
        json_str = json.dumps(order_data, separators=(',', ':'), sort_keys=True)

        # Method 1: MD5 variants
        content1 = f"{auth}{nonce}{json_str}"
        signatures.append(hashlib.md5(content1.encode()).hexdigest())

        content2 = f"{nonce}{json_str}{auth}"
        signatures.append(hashlib.md5(content2.encode()).hexdigest())

        content3 = f"{json_str}{nonce}{auth}"
        signatures.append(hashlib.md5(content3.encode()).hexdigest())

        # Method 2: SHA256 variants
        signatures.append(hashlib.sha256(content1.encode()).hexdigest()[:32])
        signatures.append(hashlib.sha256(content2.encode()).hexdigest()[:32])
        signatures.append(hashlib.sha256(content3.encode()).hexdigest()[:32])

        # Method 3: HMAC variants
        try:
            signatures.append(hmac.new(auth.encode(), f"{nonce}{json_str}".encode(), hashlib.md5).hexdigest())
            signatures.append(hmac.new(auth.encode(), f"{json_str}{nonce}".encode(), hashlib.md5).hexdigest())
            signatures.append(hmac.new(auth.encode(), f"{nonce}{json_str}".encode(), hashlib.sha256).hexdigest()[:32])
        except:
            pass

        # Remove duplicates while preserving order
        unique_signatures = []
        for sig in signatures:
            if sig not in unique_signatures:
                unique_signatures.append(sig)

        print(f"🔐 Generated {len(unique_signatures)} signature candidates")
        return unique_signatures
    
    def debug_browser_state(self):
        """Debug browser authentication and state"""

        print("\n🔍 Debugging browser state...")

        try:
            # Check current URL
            current_url = self.page.url
            print(f"📍 Current URL: {current_url}")

            # Check authentication tokens
            auth_info = self.page.evaluate("""
                () => {
                    return {
                        authorization: localStorage.getItem('authorization'),
                        uc_token: localStorage.getItem('uc_token'),
                        u_id: localStorage.getItem('u_id'),
                        cookies: document.cookie
                    };
                }
            """)

            print(f"🔐 Auth tokens: {auth_info}")

            # Test basic API access
            api_test = self.page.evaluate("""
                async () => {
                    try {
                        const response = await fetch('/api/v1/contract/ticker?symbol=BTC_USDT', {
                            credentials: 'include'
                        });
                        const data = await response.json();
                        return {
                            status: response.status,
                            data: data
                        };
                    } catch (error) {
                        return {
                            error: error.toString()
                        };
                    }
                }
            """)

            print(f"🌐 API test result: {api_test}")

            return api_test

        except Exception as e:
            print(f"❌ Debug error: {e}")
            return None

    def test_complete_workflow(self, symbol: str = 'BTC_USDT'):
        """Test the complete real-time browser integration workflow"""

        print("="*60)
        print("REAL-TIME BROWSER INTEGRATION TEST")
        print("="*60)

        # Setup browser
        if not self.setup_browser_session():
            return False

        try:
            # Debug browser state first
            debug_result = self.debug_browser_state()

            # Get market data with better error handling
            market_price = self.page.evaluate(f"""
                async () => {{
                    try {{
                        console.log('Fetching market data for {symbol}...');
                        const response = await fetch('/api/v1/contract/ticker?symbol={symbol}', {{
                            credentials: 'include'
                        }});
                        console.log('Response status:', response.status);
                        const data = await response.json();
                        console.log('Response data:', data);

                        if (data.code === 0 && data.data) {{
                            const ticker = Array.isArray(data.data) ? data.data[0] : data.data;
                            const price = parseFloat(ticker.lastPrice || 0);
                            console.log('Extracted price:', price);
                            return price;
                        }}
                        return 0;
                    }} catch (error) {{
                        console.error('Market data error:', error);
                        return 0;
                    }}
                }}
            """)

            if market_price <= 0:
                print(f"❌ Could not get market price for {symbol}")
                # Try alternative approach
                print("🔄 Trying alternative market data approach...")

                # Navigate to the trading page and wait
                self.page.goto(f'https://futures.mexc.com/exchange/{symbol}', wait_until='domcontentloaded')
                time.sleep(5)

                # Try to get price from page elements
                market_price = self.page.evaluate("""
                    () => {
                        // Try various selectors for price
                        const selectors = [
                            '[data-testid="price"]',
                            '.price',
                            '.last-price',
                            '.current-price'
                        ];

                        for (const selector of selectors) {
                            const element = document.querySelector(selector);
                            if (element) {
                                const price = parseFloat(element.textContent.replace(/[^0-9.]/g, ''));
                                if (price > 0) return price;
                            }
                        }

                        // Fallback: use a reasonable test price
                        return 50000; // Reasonable BTC price for testing
                    }
                """)

            test_price = round(market_price * 0.3, 2)  # 70% below market

            print(f"✅ Market price: ${market_price:,.2f}")
            print(f"🎯 Test price: ${test_price:,.2f}")

            # Extract parameters
            params = self.extract_real_parameters(symbol, 1, test_price, 1)
            if not params:
                return False

            # Execute order
            order_result = self.execute_order_direct(params)

            if order_result.get('success'):
                print("🎉 ORDER PLACED SUCCESSFULLY!")
                print("✅ Real-time Browser Integration WORKS!")
                return True
            else:
                print(f"❌ Order execution failed: {order_result.get('error')}")
                return False

        finally:
            # Cleanup
            if hasattr(self, 'browser'):
                self.browser.close()
            if hasattr(self, 'playwright'):
                self.playwright.stop()

def main():
    """Main test function"""
    
    integration = RealTimeBrowserIntegration()
    success = integration.test_complete_workflow()
    
    if success:
        print("\n🚀 OPTION B SUCCESSFUL!")
        print("Real-time Browser Integration is ready for production!")
    else:
        print("\n❌ OPTION B NEEDS REFINEMENT")
        print("Further development required for production use.")

if __name__ == '__main__':
    main()
