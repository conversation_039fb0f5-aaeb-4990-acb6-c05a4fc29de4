#!/usr/bin/env python3
"""
Exact Replica Test: Use the exact captured request structure
"""

import json
import time
from curl_cffi import requests
from dotenv import dotenv_values

class ExactReplicaTest:
    """Test using exact captured request structure"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        self.session = requests.Session(impersonate='chrome124')
        
        print("🔬 Exact Replica Test")
        print("="*30)
        print("🎯 Using exact captured request structure")
    
    def test_exact_replica(self):
        """Test with exact captured request data"""
        
        print("\n🧪 Testing with exact captured request structure...")
        
        # Exact captured request data
        captured_headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Origin': 'https://futures.mexc.com',
            'Referer': 'https://futures.mexc.com/exchange/TRU_USDT',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Authorization': self.auth,
            'mtoken': 'b03MOmeXoiZid75ogtwP',
            'x-mxc-sign': 'ec2e0051aaa08af9fe4f22568726fdc5',  # Real captured signature
            'x-mxc-nonce': '1754926229710',  # Real captured nonce
            'x-language': 'en_US',
        }
        
        # Exact captured request body (modified for safety)
        captured_body = {
            "symbol": "BTC_USDT",  # Changed to BTC for testing
            "side": 1,
            "openType": 1,
            "type": "2",
            "vol": 1,
            "leverage": 1,
            "marketCeiling": False,
            "price": "1000.0",  # Very low price for safety
            "priceProtect": "0",
            "p0": "AfP1RrE1NFB0xVGUVu4dE3EswEH+qf4/xUt8zYUiTyq04zSDWVpLDa7JgyZednLiCWt757ygSTT2cCzuY8KYcomQwg3FjT+4nFEP4tA9N0UCTUrZgknvkt1el9gEh55cK5HmBXiG8G+kX20octO8t/YYN8zhElhbfnkI+TfJ8JSBOtP93aF73gAWIyVWca1nhWKM56VGBtLTNYB9+A/n4dGvgQvUJF+eD0ajjQDyfp1xvC5vvaxf4AaY2w1kOF45+MN/xGbl8G9b+8vW/eDphB/+t4H+oYIpTO8Y5kUPdXVdPU+BYWlASP1nQoCr8lWJZuzdyFwDhYzYZI=",
            "k0": "bd8w0vzDjkwcrqu0/WWrvB4Ce2dBc1V7Ct/jbwwH7yfuj1tdamKljm/j6m8yclru510t1gNrL/Cn59vUQMBi//UtWZ5WS9CMJeCt8auua0Qf1ANVmYFFcUJ1xdrvN8qCvMFAsRJ+hH98asX7k7E3COa2JJezLt9Y11VC3YisWtoUMOn2V/Z8NhKSUwnt7rrDD5h0I9McniEqDyqHN/KMuuOG8x2X5f4rq3hLB9gt509ilGEUJlYmWtV2ulC6W0JGBrlK6i83TqaweLWXm4ch463PAlMpc+Iyhq9fCdC1a+mRKlpltn7tIYFUsMNe+fqMtzYuD/HB2lBmOO6fRp/uQyexw==",
            "chash": "d6c64d28e362f314071b3f9d78ff7494d9cd7177ae0465e772d1840e9f7905dd8",
            "mtoken": "b03MOmeXoiZid75ogtwP",
            "ts": int(time.time() * 1000),  # Current timestamp
            "mhash": "85723e9fb269ff0e1e19525050842a3c"
        }
        
        print(f"📋 Request body: {json.dumps(captured_body, indent=2)}")
        print(f"🔐 Using real signature: {captured_headers['x-mxc-sign']}")
        
        # Make the request
        try:
            url = f'https://futures.mexc.com/api/v1/private/order/create?mhash={captured_body["mhash"]}'
            
            r = self.session.post(url, json=captured_body, headers=captured_headers)
            
            print(f"\nResponse status: {r.status_code}")
            
            if r.status_code == 200:
                try:
                    result = r.json()
                    print(f"Response: {json.dumps(result, indent=2)}")
                    
                    if result.get('success') and result.get('code') == 0:
                        print("🎉 SUCCESS! Order placed with exact replica!")
                        return True
                    else:
                        error_code = result.get('code')
                        error_msg = result.get('message', '')
                        print(f"❌ Order failed: {error_code} - {error_msg}")
                        
                        # Analyze the error
                        if error_code == 602:
                            print("🔍 Signature verification failed - need to understand signature algorithm")
                        elif error_code == 601:
                            print("🔍 Parameter error - need to adjust parameters")
                        else:
                            print(f"🔍 Other error: {error_code}")
                        
                        return False
                
                except json.JSONDecodeError:
                    print(f"❌ Invalid JSON response: {r.text[:200]}...")
                    return False
            
            else:
                print(f"❌ HTTP error: {r.status_code}")
                try:
                    print(f"Response: {r.text[:200]}...")
                except:
                    pass
                return False
                
        except Exception as e:
            print(f"❌ Request error: {e}")
            return False
    
    def test_minimal_request(self):
        """Test with minimal request structure"""
        
        print("\n🧪 Testing with minimal request structure...")
        
        # Minimal headers
        headers = {
            'Content-Type': 'application/json',
            'Authorization': self.auth,
            'x-mxc-nonce': str(int(time.time() * 1000)),
            'x-mxc-sign': 'test_signature',
        }
        
        # Minimal body
        body = {
            "symbol": "BTC_USDT",
            "side": 1,
            "type": "2",
            "vol": 1,
            "price": "1000.0"
        }
        
        try:
            url = 'https://futures.mexc.com/api/v1/private/order/create'
            r = self.session.post(url, json=body, headers=headers)
            
            print(f"Response status: {r.status_code}")
            
            if r.status_code == 200:
                result = r.json()
                print(f"Response: {json.dumps(result, indent=2)}")
            else:
                print(f"Response: {r.text[:200]}...")
                
        except Exception as e:
            print(f"Error: {e}")
    
    def run_tests(self):
        """Run all tests"""
        
        print("="*50)
        print("EXACT REPLICA TESTING")
        print("="*50)
        
        # Test 1: Exact replica
        print("\n📋 Test 1: Exact Replica")
        success1 = self.test_exact_replica()
        
        # Test 2: Minimal request
        print("\n📋 Test 2: Minimal Request")
        self.test_minimal_request()
        
        if success1:
            print("\n🎉 EXACT REPLICA SUCCESSFUL!")
            return True
        else:
            print("\n🔍 Need to analyze signature algorithm further")
            return False

def main():
    """Main function"""
    
    tester = ExactReplicaTest()
    success = tester.run_tests()
    
    if success:
        print("\n✅ Tests completed successfully!")
    else:
        print("\n🔧 Further analysis needed")

if __name__ == '__main__':
    main()
