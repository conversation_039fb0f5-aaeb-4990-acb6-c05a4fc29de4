#!/usr/bin/env python3
"""
Test Python MEXC packages for futures trading
"""

import os
import sys
import json
import time
import asyncio
from datetime import datetime

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

API_KEY = os.getenv('MEXC_API_KEY')
API_SECRET = os.getenv('MEXC_API_SECRET')

def print_header(title):
    print(f"\n{'='*50}")
    print(f"🔍 {title}")
    print(f"{'='*50}")

def print_result(success, message, details=None):
    status = "✅" if success else "❌"
    print(f"{status} {message}")
    if details:
        print(f"   Details: {details}")

async def test_pymexc():
    """Test the pymexc library"""
    print_header("Testing pymexc Library")
    
    try:
        # Try to import pymexc
        try:
            from pymexc import MEXC
            print_result(True, "pymexc imported successfully")
        except ImportError as e:
            print_result(False, f"pymexc import failed: {e}")
            print("💡 Install with: pip install pymexc")
            return {"success": False, "error": "Import failed", "library": "pymexc"}

        # Initialize client
        client = MEXC(api_key=API_KEY, api_secret=API_SECRET)
        print_result(True, "pymexc client initialized")

        # Test connection
        try:
            server_time = await client.get_server_time()
            print_result(True, f"Server time: {server_time}")
        except Exception as e:
            print_result(False, f"Server time failed: {e}")

        # Test account info
        try:
            account = await client.get_account_info()
            print_result(True, "Account info retrieved")
        except Exception as e:
            print_result(False, f"Account info failed: {e}")

        # Test futures functionality
        try:
            positions = await client.get_futures_positions()
            print_result(True, f"Futures positions: {len(positions) if positions else 0}")
        except Exception as e:
            print_result(False, f"Futures positions failed: {e}")

        # Test order placement (dry run)
        try:
            order_params = {
                'symbol': 'TRUUSDT',
                'side': 'buy',
                'type': 'market',
                'quantity': 40
            }
            print(f"🎯 Testing order placement with params: {order_params}")
            
            # Note: Uncomment to place real order
            # order = await client.create_futures_order(**order_params)
            # print_result(True, f"Order placed: {order}")
            
            print_result(True, "Order parameters validated (not placed)")
            
            return {"success": True, "library": "pymexc", "features": ["futures", "async"]}
            
        except Exception as e:
            print_result(False, f"Order test failed: {e}")
            return {"success": False, "library": "pymexc", "error": str(e)}

    except Exception as e:
        print_result(False, f"pymexc test failed: {e}")
        return {"success": False, "library": "pymexc", "error": str(e)}

def test_ccxt_python():
    """Test CCXT Python library"""
    print_header("Testing CCXT Python Library")
    
    try:
        # Try to import ccxt
        try:
            import ccxt
            print_result(True, "ccxt imported successfully")
        except ImportError as e:
            print_result(False, f"ccxt import failed: {e}")
            print("💡 Install with: pip install ccxt")
            return {"success": False, "error": "Import failed", "library": "ccxt-python"}

        # Initialize exchange
        exchange = ccxt.mexc({
            'apiKey': API_KEY,
            'secret': API_SECRET,
            'sandbox': False,
            'enableRateLimit': True,
        })
        print_result(True, "CCXT MEXC exchange initialized")

        # Load markets
        markets = exchange.load_markets()
        print_result(True, f"Markets loaded: {len(markets)} total")

        # Check for TRU futures
        futures_symbol = 'TRU/USDT:USDT'
        if futures_symbol in markets:
            print_result(True, f"Found futures symbol: {futures_symbol}")
        else:
            print_result(False, f"Futures symbol {futures_symbol} not found")

        # Test balance
        try:
            balance = exchange.fetch_balance({'type': 'swap'})
            usdt_balance = balance.get('USDT', {})
            print_result(True, f"USDT balance: {usdt_balance}")
        except Exception as e:
            print_result(False, f"Balance fetch failed: {e}")

        # Test market data
        try:
            ticker = exchange.fetch_ticker(futures_symbol)
            print_result(True, f"Ticker: Price={ticker['last']}, Volume={ticker['baseVolume']}")
        except Exception as e:
            print_result(False, f"Ticker fetch failed: {e}")

        # Test order placement
        try:
            print("🎯 Testing order placement...")
            
            # Calculate order size
            current_price = ticker['last']
            order_value = 1.5  # $1.5
            quantity = int(order_value / current_price)
            
            print(f"   Symbol: {futures_symbol}")
            print(f"   Price: {current_price}")
            print(f"   Quantity: {quantity}")
            
            # Place market order
            order = exchange.create_market_buy_order(futures_symbol, quantity)
            print_result(True, f"🎉 ORDER PLACED SUCCESSFULLY! Order ID: {order['id']}")
            
            # Try to fetch order status
            try:
                order_status = exchange.fetch_order(order['id'], futures_symbol)
                print_result(True, f"Order status: {order_status['status']}")
            except Exception as status_error:
                print_result(False, f"Order status fetch failed: {status_error}")
            
            return {
                "success": True, 
                "library": "ccxt-python", 
                "features": ["futures", "order_placed"],
                "order": order
            }
            
        except Exception as e:
            print_result(False, f"Order placement failed: {e}")
            return {"success": False, "library": "ccxt-python", "error": str(e)}

    except Exception as e:
        print_result(False, f"CCXT Python test failed: {e}")
        return {"success": False, "library": "ccxt-python", "error": str(e)}

def test_custom_python_api():
    """Test custom Python implementation"""
    print_header("Testing Custom Python API")

    try:
        import requests
        import hmac
        import hashlib
        import time

        class CustomMexcAPI:
            def __init__(self, api_key, api_secret):
                self.api_key = api_key
                self.api_secret = api_secret
                self.base_url = 'https://contract.mexc.com'

            def _generate_signature(self, query_string):
                return hmac.new(
                    self.api_secret.encode('utf-8'),
                    query_string.encode('utf-8'),
                    hashlib.sha256
                ).hexdigest()

            def _make_request(self, method, endpoint, params=None):
                if params is None:
                    params = {}

                params['timestamp'] = int(time.time() * 1000)

                query_string = '&'.join([f"{k}={v}" for k, v in sorted(params.items())])
                signature = self._generate_signature(query_string)
                params['signature'] = signature

                headers = {
                    'ApiKey': self.api_key,
                    'Request-Time': str(params['timestamp']),
                    'Content-Type': 'application/json',
                    'source': 'CCXT',
                    'Signature': signature
                }

                url = f"{self.base_url}{endpoint}"

                if method == 'GET':
                    response = requests.get(url, params=params, headers=headers, timeout=30)
                else:
                    response = requests.post(url, json=params, headers=headers, timeout=30)

                return response.json()

            def get_account_assets(self):
                return self._make_request('GET', '/api/v1/private/account/assets')

            def create_order(self, symbol, side, order_type, vol, price=None):
                params = {
                    'symbol': symbol,
                    'side': side,
                    'type': order_type,
                    'vol': vol,
                    'openType': 2  # Cross margin
                }
                if price:
                    params['price'] = price

                return self._make_request('POST', '/api/v1/private/order/submit', params)

        print_result(True, "Custom Python API initialized")

        # Initialize client
        client = CustomMexcAPI(API_KEY, API_SECRET)

        # Test account assets
        try:
            assets = client.get_account_assets()
            if assets.get('success'):
                print_result(True, f"Account assets retrieved: {len(assets.get('data', []))} currencies")
            else:
                print_result(False, f"Account assets failed: {assets}")
        except Exception as e:
            print_result(False, f"Account assets failed: {e}")

        # Test order placement
        try:
            print("🎯 Testing order placement...")

            order_params = {
                'symbol': 'TRU_USDT',
                'side': 1,  # 1 = buy
                'order_type': 5,  # 5 = market order
                'vol': 40
            }

            print(f"   Parameters: {order_params}")

            # Place order
            order = client.create_order(**order_params)

            if order.get('success'):
                print_result(True, f"🎉 ORDER PLACED SUCCESSFULLY! Order ID: {order.get('data', {}).get('orderId')}")
                return {
                    "success": True,
                    "library": "custom-python-api",
                    "features": ["futures", "order_placed"],
                    "order": order
                }
            else:
                print_result(False, f"Order placement failed: {order}")
                return {"success": False, "library": "custom-python-api", "error": str(order)}

        except Exception as e:
            print_result(False, f"Order placement failed: {e}")
            return {"success": False, "library": "custom-python-api", "error": str(e)}

    except Exception as e:
        print_result(False, f"Custom Python API test failed: {e}")
        return {"success": False, "library": "custom-python-api", "error": str(e)}

async def main():
    """Main test runner"""
    print("🚀 MEXC Python Package Testing Suite")
    print("====================================")
    print(f"API Key: {API_KEY[:10]}..." if API_KEY else "No API Key")
    print(f"Testing Symbol: TRUUSDT")
    print(f"Balance: ~$2 USDT")
    print("")
    
    print("⚠️  WARNING: This will attempt to place REAL orders!")
    print("⚠️  Starting tests in 5 seconds... Press Ctrl+C to cancel")
    
    try:
        time.sleep(5)
    except KeyboardInterrupt:
        print("\n❌ Tests cancelled by user")
        return

    results = []
    
    # Test 1: pymexc (async)
    try:
        result = await test_pymexc()
        results.append(result)
    except Exception as e:
        results.append({"success": False, "library": "pymexc", "error": f"Test crashed: {e}"})
    
    time.sleep(2)
    
    # Test 2: CCXT Python
    try:
        result = test_ccxt_python()
        results.append(result)
    except Exception as e:
        results.append({"success": False, "library": "ccxt-python", "error": f"Test crashed: {e}"})
    
    time.sleep(2)
    
    # Test 3: Custom Python API
    try:
        result = test_custom_python_api()
        results.append(result)
    except Exception as e:
        results.append({"success": False, "library": "custom-python-api", "error": f"Test crashed: {e}"})

    # Generate summary
    print_header("FINAL RESULTS SUMMARY")
    
    successful = [r for r in results if r.get('success')]
    failed = [r for r in results if not r.get('success')]
    
    print(f"✅ Successful: {len(successful)}/{len(results)}")
    print(f"❌ Failed: {len(failed)}/{len(results)}")
    
    if successful:
        print("\n🎉 WORKING PACKAGES:")
        for result in successful:
            print(f"  ✓ {result['library']}")
            if 'order' in result:
                print(f"    🎯 ORDER PLACED SUCCESSFULLY!")
            if 'features' in result:
                print(f"    Features: {', '.join(result['features'])}")
    
    if failed:
        print("\n💔 FAILED PACKAGES:")
        for result in failed:
            print(f"  ✗ {result['library']}: {result.get('error', 'Unknown error')}")
    
    # Save report
    report = {
        'timestamp': datetime.now().isoformat(),
        'summary': {
            'total': len(results),
            'successful': len(successful),
            'failed': len(failed)
        },
        'results': results
    }
    
    with open('python-packages-test-report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Report saved to: python-packages-test-report.json")
    
    # Exit with success if any package worked
    return len(successful) > 0

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite crashed: {e}")
        sys.exit(1)
