{"name": "mexc-futures-trader", "version": "1.0.0", "description": "High-speed MEXC futures trading service with sub-2 second execution", "main": "src/trader.js", "scripts": {"start": "node src/server.js", "test": "node tests/test-all-orders.js", "trade": "node src/trader.js", "setup": "npm install && echo 'Setup complete! See README.md for browser setup instructions.'"}, "keywords": ["mexc", "futures", "trading", "automation", "high-speed", "playwright"], "author": "MEXC Trading System", "license": "MIT", "dependencies": {"axios": "^1.6.0", "cors": "^2.8.5", "express": "^4.21.2", "helmet": "^7.1.0", "playwright": "^1.40.0", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}