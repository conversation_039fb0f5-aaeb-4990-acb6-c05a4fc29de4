const axios = require('axios');

async function testWebhook() {
    const webhookUrl = 'http://localhost:4000/webhook';
    
    const testSignal = {
        symbol: "TRUUSDT",
        trade: "open",
        last_price: "0.000012064",
        leverage: "2"
    };

    console.log('🚀 Sending test webhook signal...');
    console.log('Signal:', JSON.stringify(testSignal, null, 2));
    
    try {
        const response = await axios.post(webhookUrl, testSignal, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 30000
        });
        
        console.log('✅ Webhook Response:');
        console.log(JSON.stringify(response.data, null, 2));
        
    } catch (error) {
        console.log('❌ Webhook Error:');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Data:', error.response.data);
        } else {
            console.log('Error:', error.message);
        }
    }
}

testWebhook();
