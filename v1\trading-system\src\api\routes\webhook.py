"""
Webhook API routes for TradingView and external signals
"""

import asyncio
import hashlib
import hmac
import json
from datetime import datetime
from typing import Dict, Any, Optional

from fastapi import APIRouter, HTTPException, Request, BackgroundTasks, Depends
from pydantic import BaseModel, validator

from src.config import settings
from src.core.trading_engine import TradingEngine, TradeRequest, OrderType
from src.integration.enhanced_integration import (
    enhanced_trading_system,
    process_enhanced_webhook,
    execute_enhanced_manual_trade
)
from src.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()

# Global trading engine instance (will be injected)
trading_engine: Optional[TradingEngine] = None


class TradingViewWebhook(BaseModel):
    """TradingView webhook payload model"""
    action: str  # buy, sell, close
    symbol: str
    side: str = "long"  # long, short
    quantity: float
    price: Optional[float] = None
    leverage: int = 1
    order_type: str = "limit"
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    
    # Optional metadata
    strategy: Optional[str] = None
    timeframe: Optional[str] = None
    timestamp: Optional[str] = None
    
    @validator("action")
    def validate_action(cls, v):
        if v.lower() not in ["buy", "sell", "close"]:
            raise ValueError("Action must be 'buy', 'sell', or 'close'")
        return v.lower()
    
    @validator("side")
    def validate_side(cls, v):
        if v.lower() not in ["long", "short"]:
            raise ValueError("Side must be 'long' or 'short'")
        return v.lower()
    
    @validator("symbol")
    def validate_symbol(cls, v):
        # Ensure symbol is in correct format
        symbol = v.upper().replace("/", "_").replace("-", "_")
        if symbol not in settings.SUPPORTED_SYMBOLS:
            raise ValueError(f"Symbol {symbol} not supported")
        return symbol
    
    @validator("quantity")
    def validate_quantity(cls, v):
        if v <= 0:
            raise ValueError("Quantity must be positive")
        return v
    
    @validator("leverage")
    def validate_leverage(cls, v):
        if v < 1 or v > 100:
            raise ValueError("Leverage must be between 1 and 100")
        return v
    
    @validator("order_type")
    def validate_order_type(cls, v):
        valid_types = ["market", "limit", "post_only"]
        if v.lower() not in valid_types:
            raise ValueError(f"Order type must be one of: {valid_types}")
        return v.lower()


class WebhookResponse(BaseModel):
    """Webhook response model"""
    success: bool
    message: str
    trade_id: Optional[int] = None
    order_id: Optional[str] = None
    timestamp: str
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "Trade executed successfully",
                "trade_id": 123,
                "order_id": "order_456789",
                "timestamp": "2024-01-01T12:00:00Z"
            }
        }


def verify_webhook_signature(request: Request, payload: bytes) -> bool:
    """
    Verify webhook signature for security
    
    Args:
        request: FastAPI request object
        payload: Raw request payload
        
    Returns:
        True if signature is valid
    """
    if not settings.WEBHOOK_SECRET:
        return True  # Skip verification if no secret configured
    
    signature = request.headers.get("X-Signature")
    if not signature:
        return False
    
    # Calculate expected signature
    expected_signature = hmac.new(
        settings.WEBHOOK_SECRET.encode(),
        payload,
        hashlib.sha256
    ).hexdigest()
    
    return hmac.compare_digest(signature, expected_signature)


async def get_trading_engine() -> TradingEngine:
    """Get trading engine instance"""
    global trading_engine
    if not trading_engine:
        raise HTTPException(status_code=503, detail="Trading engine not available")
    return trading_engine


@router.post("/tradingview/enhanced", response_model=WebhookResponse)
async def tradingview_webhook_enhanced(
    webhook_data: TradingViewWebhook,
    background_tasks: BackgroundTasks,
    request: Request
):
    """
    Handle TradingView webhook signals using ENHANCED automation with blur prevention

    This endpoint uses the proven browser automation system that achieves 100% success rate.

    Args:
        webhook_data: TradingView webhook payload
        background_tasks: FastAPI background tasks
        request: HTTP request object

    Returns:
        Webhook response
    """
    try:
        # Log incoming webhook
        logger.info(
            "Received TradingView webhook (ENHANCED)",
            action=webhook_data.action,
            symbol=webhook_data.symbol,
            side=webhook_data.side,
            quantity=webhook_data.quantity,
            price=webhook_data.price,
            strategy=webhook_data.strategy
        )

        # Verify signature if configured
        if settings.WEBHOOK_SECRET:
            body = await request.body()
            if not verify_webhook_signature(request, body):
                logger.warning("Invalid webhook signature")
                raise HTTPException(status_code=401, detail="Invalid signature")

        # Convert webhook data to dictionary for enhanced processing
        webhook_dict = {
            "action": webhook_data.action,
            "symbol": webhook_data.symbol,
            "side": webhook_data.side,
            "quantity": webhook_data.quantity,
            "price": webhook_data.price,
            "leverage": webhook_data.leverage,
            "order_type": webhook_data.order_type,
            "stop_loss": webhook_data.stop_loss,
            "take_profit": webhook_data.take_profit,
            "strategy": webhook_data.strategy,
            "timeframe": webhook_data.timeframe,
            "timestamp": webhook_data.timestamp,
            "source": "tradingview_enhanced"
        }

        # Execute trade using enhanced automation in background
        background_tasks.add_task(execute_enhanced_trade_async, webhook_dict)

        return WebhookResponse(
            success=True,
            message=f"ENHANCED trade request received: {webhook_data.action} {webhook_data.symbol} (blur prevention active)",
            timestamp=datetime.utcnow().isoformat()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Enhanced webhook processing failed: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tradingview", response_model=WebhookResponse)
async def tradingview_webhook(
    webhook_data: TradingViewWebhook,
    background_tasks: BackgroundTasks,
    request: Request,
    engine: TradingEngine = Depends(get_trading_engine)
):
    """
    Handle TradingView webhook signals
    
    Args:
        webhook_data: TradingView webhook payload
        background_tasks: FastAPI background tasks
        request: HTTP request object
        engine: Trading engine instance
        
    Returns:
        Webhook response
    """
    try:
        # Log incoming webhook
        logger.info(
            "Received TradingView webhook",
            action=webhook_data.action,
            symbol=webhook_data.symbol,
            side=webhook_data.side,
            quantity=webhook_data.quantity,
            price=webhook_data.price,
            strategy=webhook_data.strategy
        )
        
        # Verify signature if configured
        if settings.WEBHOOK_SECRET:
            body = await request.body()
            if not verify_webhook_signature(request, body):
                logger.warning("Invalid webhook signature")
                raise HTTPException(status_code=401, detail="Invalid signature")
        
        # Convert to trade request
        order_type_mapping = {
            "market": OrderType.MARKET,
            "limit": OrderType.LIMIT,
            "post_only": OrderType.POST_ONLY
        }
        
        trade_request = TradeRequest(
            action=webhook_data.action,
            symbol=webhook_data.symbol,
            side=webhook_data.side,
            quantity=webhook_data.quantity,
            price=webhook_data.price,
            leverage=webhook_data.leverage,
            order_type=order_type_mapping.get(webhook_data.order_type, OrderType.LIMIT),
            stop_loss=webhook_data.stop_loss,
            take_profit=webhook_data.take_profit,
            strategy=webhook_data.strategy,
            timeframe=webhook_data.timeframe,
            source="tradingview"
        )
        
        # Execute trade in background
        background_tasks.add_task(execute_trade_async, engine, trade_request)
        
        return WebhookResponse(
            success=True,
            message=f"Trade request received: {webhook_data.action} {webhook_data.symbol}",
            timestamp=datetime.utcnow().isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Webhook processing failed: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


class TestWebhook(BaseModel):
    """Test webhook payload model with relaxed validation"""
    action: str = "buy"
    symbol: str = "BTCUSDT"
    side: str = "long"
    quantity: float = 0.001
    price: Optional[float] = None
    leverage: int = 1
    order_type: str = "limit"
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    strategy: Optional[str] = "test"
    timeframe: Optional[str] = "1h"
    timestamp: Optional[str] = None


@router.post("/test", response_model=WebhookResponse)
async def test_webhook(
    webhook_data: TestWebhook = None,
    engine: TradingEngine = Depends(get_trading_engine)
):
    """
    Test webhook endpoint for development and testing

    Args:
        webhook_data: Test webhook payload (optional, uses defaults if not provided)
        engine: Trading engine instance

    Returns:
        Test response
    """
    try:
        # Use default test data if none provided
        if webhook_data is None:
            webhook_data = TestWebhook()

        logger.info(
            "Test webhook received",
            action=webhook_data.action,
            symbol=webhook_data.symbol,
            side=webhook_data.side,
            quantity=webhook_data.quantity
        )

        # Create a test trade request without strict validation
        trade_request = TradeRequest(
            action=webhook_data.action,
            symbol=webhook_data.symbol,
            side=webhook_data.side,
            quantity=webhook_data.quantity,
            price=webhook_data.price,
            leverage=webhook_data.leverage,
            source="test"
        )
        
        return WebhookResponse(
            success=True,
            message=f"Test webhook validated: {webhook_data.action} {webhook_data.symbol}",
            timestamp=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Test webhook failed: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/test", response_model=WebhookResponse)
async def test_webhook_get():
    """
    Simple GET test endpoint for webhook testing
    """
    try:
        logger.info("Test webhook GET request received")

        return WebhookResponse(
            success=True,
            message="Webhook endpoint is working! Use POST with JSON payload for full testing.",
            timestamp=datetime.utcnow().isoformat()
        )

    except Exception as e:
        logger.error(f"Test webhook GET failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/manual/enhanced", response_model=WebhookResponse)
async def manual_trade_enhanced(
    webhook_data: TradingViewWebhook,
    background_tasks: BackgroundTasks
):
    """
    Manual trade execution endpoint using ENHANCED automation with blur prevention

    This endpoint uses the proven browser automation system that achieves 100% success rate.

    Args:
        webhook_data: Manual trade payload
        background_tasks: FastAPI background tasks

    Returns:
        Trade response
    """
    try:
        logger.info(
            "Manual trade request (ENHANCED)",
            action=webhook_data.action,
            symbol=webhook_data.symbol,
            side=webhook_data.side,
            quantity=webhook_data.quantity
        )

        # Execute enhanced manual trade in background
        background_tasks.add_task(
            execute_enhanced_manual_trade_async,
            webhook_data.action,
            webhook_data.symbol,
            webhook_data.side,
            webhook_data.quantity,
            webhook_data.price,
            webhook_data.leverage,
            webhook_data.order_type,
            webhook_data.stop_loss,
            webhook_data.take_profit
        )

        return WebhookResponse(
            success=True,
            message=f"ENHANCED manual trade queued: {webhook_data.action} {webhook_data.symbol} (blur prevention active)",
            timestamp=datetime.utcnow().isoformat()
        )

    except Exception as e:
        logger.error(f"Enhanced manual trade failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/manual", response_model=WebhookResponse)
async def manual_trade(
    webhook_data: TradingViewWebhook,
    background_tasks: BackgroundTasks,
    engine: TradingEngine = Depends(get_trading_engine)
):
    """
    Manual trade execution endpoint
    
    Args:
        webhook_data: Manual trade payload
        background_tasks: FastAPI background tasks
        engine: Trading engine instance
        
    Returns:
        Trade response
    """
    try:
        logger.info(
            "Manual trade request",
            action=webhook_data.action,
            symbol=webhook_data.symbol,
            side=webhook_data.side,
            quantity=webhook_data.quantity
        )
        
        # Convert to trade request
        trade_request = TradeRequest(
            action=webhook_data.action,
            symbol=webhook_data.symbol,
            side=webhook_data.side,
            quantity=webhook_data.quantity,
            price=webhook_data.price,
            leverage=webhook_data.leverage,
            stop_loss=webhook_data.stop_loss,
            take_profit=webhook_data.take_profit,
            source="manual"
        )
        
        # Execute trade in background
        background_tasks.add_task(execute_trade_async, engine, trade_request)
        
        return WebhookResponse(
            success=True,
            message=f"Manual trade queued: {webhook_data.action} {webhook_data.symbol}",
            timestamp=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Manual trade failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def execute_trade_async(engine: TradingEngine, trade_request: TradeRequest):
    """
    Execute trade asynchronously
    
    Args:
        engine: Trading engine instance
        trade_request: Trade request to execute
    """
    try:
        result = await engine.execute_trade(trade_request)
        
        if result["success"]:
            logger.info(
                "Trade executed successfully",
                trade_id=result.get("trade_id"),
                order_id=result.get("order_id"),
                symbol=trade_request.symbol,
                action=trade_request.action
            )
        else:
            logger.error(
                "Trade execution failed",
                error=result.get("error"),
                symbol=trade_request.symbol,
                action=trade_request.action
            )
            
    except Exception as e:
        logger.error(f"Async trade execution failed: {e}", exc_info=True)


async def execute_enhanced_trade_async(webhook_data: Dict[str, Any]):
    """
    Execute enhanced trade asynchronously using proven automation

    Args:
        webhook_data: Webhook data dictionary
    """
    try:
        logger.info("Executing enhanced trade with blur prevention...")

        result = await process_enhanced_webhook(webhook_data)

        if result["success"]:
            logger.info(
                "ENHANCED trade executed successfully",
                trade_id=result.get("trade_id"),
                order_id=result.get("order_id"),
                symbol=webhook_data.get("symbol"),
                action=webhook_data.get("action"),
                automation_data=result.get("automation_data", {})
            )
        else:
            logger.error(
                "ENHANCED trade execution failed",
                error=result.get("error"),
                symbol=webhook_data.get("symbol"),
                action=webhook_data.get("action")
            )

    except Exception as e:
        logger.error(f"Enhanced async trade execution failed: {e}", exc_info=True)


async def execute_enhanced_manual_trade_async(
    action: str,
    symbol: str,
    side: str,
    quantity: float,
    price: Optional[float] = None,
    leverage: int = 1,
    order_type: str = "limit",
    stop_loss: Optional[float] = None,
    take_profit: Optional[float] = None
):
    """
    Execute enhanced manual trade asynchronously

    Args:
        action: Trade action
        symbol: Trading symbol
        side: Position side
        quantity: Order quantity
        price: Order price
        leverage: Leverage multiplier
        order_type: Order type
        stop_loss: Stop loss price
        take_profit: Take profit price
    """
    try:
        logger.info("Executing enhanced manual trade with blur prevention...")

        result = await execute_enhanced_manual_trade(
            action=action,
            symbol=symbol,
            side=side,
            quantity=quantity,
            price=price,
            leverage=leverage,
            order_type=order_type,
            stop_loss=stop_loss,
            take_profit=take_profit
        )

        if result["success"]:
            logger.info(
                "ENHANCED manual trade executed successfully",
                trade_id=result.get("trade_id"),
                order_id=result.get("order_id"),
                symbol=symbol,
                action=action
            )
        else:
            logger.error(
                "ENHANCED manual trade execution failed",
                error=result.get("error"),
                symbol=symbol,
                action=action
            )

    except Exception as e:
        logger.error(f"Enhanced async manual trade execution failed: {e}", exc_info=True)


def set_trading_engine(engine: TradingEngine):
    """Set the global trading engine instance"""
    global trading_engine
    trading_engine = engine
