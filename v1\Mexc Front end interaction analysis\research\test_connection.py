#!/usr/bin/env python3
"""
Simple connection test for Chrome remote debugging
"""

import requests
import json
import time

def test_debug_port():
    """Test if Chrome debug port is accessible"""
    print("🔍 Testing Chrome debug port 9222...")
    
    try:
        response = requests.get('http://127.0.0.1:9222/json', timeout=5)
        
        if response.status_code == 200:
            tabs = response.json()
            print(f"✅ Browser accessible with {len(tabs)} tabs")
            
            for i, tab in enumerate(tabs):
                print(f"  Tab {i+1}: {tab.get('title', 'No title')[:50]}")
                print(f"         URL: {tab.get('url', 'No URL')}")
                print(f"         Type: {tab.get('type', 'No type')}")
                print()
            
            # Look for MEXC tabs
            mexc_tabs = [tab for tab in tabs if 'mexc.com' in tab.get('url', '')]
            if mexc_tabs:
                print(f"🎯 Found {len(mexc_tabs)} MEXC tab(s)")
                for tab in mexc_tabs:
                    print(f"  MEXC: {tab.get('url', '')}")
            else:
                print("⚠️ No MEXC tabs found")
            
            return True
        else:
            print(f"❌ Browser not accessible: HTTP {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to port 9222")
        print("💡 Make sure Chrome is running with: --remote-debugging-port=9222")
        return False
    except Exception as e:
        print(f"❌ Error testing debug port: {e}")
        return False

def test_playwright_connection():
    """Test Playwright connection"""
    print("\n🎭 Testing Playwright connection...")
    
    try:
        from playwright.sync_api import sync_playwright
        
        print("🔌 Starting Playwright...")
        playwright = sync_playwright().start()
        
        print("🌐 Connecting to browser...")
        browser = playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
        
        print(f"📋 Connected! Found {len(browser.contexts)} context(s)")
        
        if browser.contexts:
            context = browser.contexts[0]
            print(f"📄 Context has {len(context.pages)} page(s)")
            
            for i, page in enumerate(context.pages):
                try:
                    print(f"  Page {i+1}: {page.url}")
                    print(f"           Title: {page.title()}")
                except:
                    print(f"  Page {i+1}: Could not get details")
        
        playwright.stop()
        print("✅ Playwright connection successful")
        return True
        
    except Exception as e:
        print(f"❌ Playwright connection failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run connection tests"""
    print("🚀 Chrome Remote Debugging Connection Test")
    print("=" * 45)
    
    # Test 1: HTTP connection to debug port
    http_ok = test_debug_port()
    
    # Test 2: Playwright connection
    playwright_ok = test_playwright_connection()
    
    print(f"\n📊 Test Results:")
    print(f"HTTP Debug Port: {'✅ OK' if http_ok else '❌ FAILED'}")
    print(f"Playwright Connection: {'✅ OK' if playwright_ok else '❌ FAILED'}")
    
    if http_ok and playwright_ok:
        print("\n🎉 All tests passed! Browser automation is ready.")
    else:
        print("\n⚠️ Some tests failed. Please restart Chrome with remote debugging.")
        print("\nCommand to restart Chrome:")
        print('"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe" --remote-debugging-port=9222 --user-data-dir="./browser_data" --disable-web-security')

if __name__ == "__main__":
    main()
